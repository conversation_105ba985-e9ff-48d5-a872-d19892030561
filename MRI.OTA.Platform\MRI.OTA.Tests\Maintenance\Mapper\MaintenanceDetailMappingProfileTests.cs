using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.Common.Constants;

namespace MRI.OTA.UnitTestCases.Maintenance.Mapper
{
    public class MaintenanceDetailMappingProfileTests
    {
        private readonly IMapper _mapper;

        public MaintenanceDetailMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<MaintenanceDetailMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        #region MaintenanceJobResponse to MaintenanceDetail Tests

        [Fact]
        public void Should_Map_MaintenanceJobResponse_To_MaintenanceDetail()
        {
            // Arrange
            var maintenanceJob = new MaintenanceJobResponse
            {
                JobId = "JOB123",
                JobSummary = "Fix leaky faucet",
                JobStatus = "In Progress",
                RequestId = "REQ456",
                RequestSummary = "Tenant reported leaky faucet",
                RequestStatus = "Open",
                RequestRaisedBy = "John Doe",
                RequestRaisedDate = new DateTime(2023, 1, 15),
                ImageLink = "https://example.com/image.jpg"
            };

            // Act
            var result = _mapper.Map<MaintenanceDetail>(maintenanceJob);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(maintenanceJob.JobId, result.SRCJobId);
            Assert.Equal(maintenanceJob.JobSummary, result.JobSummary);
            Assert.Equal(maintenanceJob.RequestId, result.SRCRequestId);
            Assert.Equal(maintenanceJob.RequestSummary, result.RequestSummary);
            Assert.Equal(maintenanceJob.RequestRaisedBy, result.RequestRaisedBy);
            Assert.Equal(maintenanceJob.RequestRaisedDate, result.RequestRaisedDate);
            Assert.Equal(maintenanceJob.ImageLink, result.ImageLink);
            Assert.Equal(-1, result.PropertyId); // Default value
            Assert.Equal(0, result.MaintenanceDetailId); // Ignored
        }

        [Fact]
        public void Should_Map_MaintenanceJobResponse_JobStatus_Using_Constants()
        {
            // Arrange
            var maintenanceJob = new MaintenanceJobResponse
            {
                JobId = "JOB123",
                JobStatus = "In Progress",
                RequestStatus = "Open"
            };

            // Act
            var result = _mapper.Map<MaintenanceDetail>(maintenanceJob);

            // Assert
            Assert.NotNull(result);
            // The actual status mapping depends on Constants.JobStatusDic
            // This test verifies the mapping method is called
            Assert.True(result.JobStatus == "In Progress");
            Assert.True(result.RequestStatus == "Open");
        }

        [Fact]
        public void Should_Handle_Null_Status_Values()
        {
            // Arrange
            var maintenanceJob = new MaintenanceJobResponse
            {
                JobId = "JOB123",
                JobStatus = null!,
                RequestStatus = ""
            };

            // Act
            var result = _mapper.Map<MaintenanceDetail>(maintenanceJob);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(null!, result.JobStatus); // Should return 0 for null/empty
            Assert.Equal("", result.RequestStatus); // Should return 0 for null/empty
        }

        #endregion

        #region MaintenanceDetailResponse to List<MaintenanceDetail> Tests

        [Fact]
        public void Should_Map_MaintenanceDetailResponse_To_List_MaintenanceDetail()
        {
            // Arrange
            var response = new MaintenanceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                MaintenanceJobs = new List<MaintenanceJobResponse>
                {
                    new MaintenanceJobResponse
                    {
                        JobId = "JOB001",
                        JobSummary = "Fix door",
                        JobStatus = "Open",
                        RequestId = "REQ001",
                        RequestSummary = "Door won't close",
                        RequestStatus = "New"
                    },
                    new MaintenanceJobResponse
                    {
                        JobId = "JOB002",
                        JobSummary = "Paint walls",
                        JobStatus = "In Progress",
                        RequestId = "REQ002",
                        RequestSummary = "Walls need painting",
                        RequestStatus = "Approved"
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<MaintenanceDetail>>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Check first maintenance detail
            var first = result[0];
            Assert.Equal("AGENCY123", first.SRCManagementId);
            Assert.Equal("PROP456", first.SRCPropertyId);
            Assert.Equal("JOB001", first.SRCJobId);
            Assert.Equal("Fix door", first.JobSummary);
            Assert.Equal("REQ001", first.SRCRequestId);
            Assert.Equal("Door won't close", first.RequestSummary);

            // Check second maintenance detail
            var second = result[1];
            Assert.Equal("AGENCY123", second.SRCManagementId);
            Assert.Equal("PROP456", second.SRCPropertyId);
            Assert.Equal("JOB002", second.SRCJobId);
            Assert.Equal("Paint walls", second.JobSummary);
            Assert.Equal("REQ002", second.SRCRequestId);
            Assert.Equal("Walls need painting", second.RequestSummary);
        }

        [Fact]
        public void Should_Return_Empty_List_When_No_MaintenanceJobs()
        {
            // Arrange
            var response = new MaintenanceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                MaintenanceJobs = null!
            };

            // Act
            var result = _mapper.Map<List<MaintenanceDetail>>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void Should_Return_Empty_List_When_Empty_MaintenanceJobs()
        {
            // Arrange
            var response = new MaintenanceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                MaintenanceJobs = new List<MaintenanceJobResponse>()
            };

            // Act
            var result = _mapper.Map<List<MaintenanceDetail>>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        #endregion

        #region List<MaintenanceDetailResponse> to List<MaintenanceDetail> Tests

        [Fact]
        public void Should_Map_List_MaintenanceDetailResponse_To_List_MaintenanceDetail()
        {
            // Arrange
            var responses = new List<MaintenanceDetailResponse>
            {
                new MaintenanceDetailResponse
                {
                    ManagementId = "AGENCY1",
                    PropertyId = "PROP1",
                    MaintenanceJobs = new List<MaintenanceJobResponse>
                    {
                        new MaintenanceJobResponse { JobId = "JOB1", JobSummary = "Job 1" },
                        new MaintenanceJobResponse { JobId = "JOB2", JobSummary = "Job 2" }
                    }
                },
                new MaintenanceDetailResponse
                {
                    ManagementId = "AGENCY2",
                    PropertyId = "PROP2",
                    MaintenanceJobs = new List<MaintenanceJobResponse>
                    {
                        new MaintenanceJobResponse { JobId = "JOB3", JobSummary = "Job 3" }
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<MaintenanceDetail>>(responses);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(3, result.Count); // 2 jobs from first response + 1 job from second response

            // Verify jobs from first response
            Assert.Contains(result, x => x.SRCJobId == "JOB1" && x.SRCManagementId == "AGENCY1" && x.SRCPropertyId == "PROP1");
            Assert.Contains(result, x => x.SRCJobId == "JOB2" && x.SRCManagementId == "AGENCY1" && x.SRCPropertyId == "PROP1");

            // Verify job from second response
            Assert.Contains(result, x => x.SRCJobId == "JOB3" && x.SRCManagementId == "AGENCY2" && x.SRCPropertyId == "PROP2");
        }

        [Fact]
        public void Should_Return_Empty_List_When_No_Responses()
        {
            // Arrange
            var responses = new List<MaintenanceDetailResponse>();

            // Act
            var result = _mapper.Map<List<MaintenanceDetail>>(responses);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void Should_Return_Empty_List_When_Null_Responses()
        {
            // Arrange
            List<MaintenanceDetailResponse> responses = null;

            // Act
            var result = _mapper.Map<List<MaintenanceDetail>>(responses);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void Should_Handle_Mixed_Responses_With_Some_Empty_Jobs()
        {
            // Arrange
            var responses = new List<MaintenanceDetailResponse>
            {
                new MaintenanceDetailResponse
                {
                    ManagementId = "AGENCY1",
                    PropertyId = "PROP1",
                    MaintenanceJobs = new List<MaintenanceJobResponse>
                    {
                        new MaintenanceJobResponse { JobId = "JOB1", JobSummary = "Job 1" }
                    }
                },
                new MaintenanceDetailResponse
                {
                    ManagementId = "AGENCY2",
                    PropertyId = "PROP2",
                    MaintenanceJobs = null! // No jobs
                },
                new MaintenanceDetailResponse
                {
                    ManagementId = "AGENCY3",
                    PropertyId = "PROP3",
                    MaintenanceJobs = new List<MaintenanceJobResponse>
                    {
                        new MaintenanceJobResponse { JobId = "JOB3", JobSummary = "Job 3" }
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<MaintenanceDetail>>(responses);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count); // Only jobs from first and third responses
            Assert.Contains(result, x => x.SRCJobId == "JOB1" && x.SRCManagementId == "AGENCY1");
            Assert.Contains(result, x => x.SRCJobId == "JOB3" && x.SRCManagementId == "AGENCY3");
        }

        #endregion

        #region Legacy Mapping Tests

        [Fact]
        public void Should_Map_MaintenanceDetailResponse_To_Single_MaintenanceDetail_Legacy()
        {
            // Arrange
            var response = new MaintenanceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                MaintenanceJobs = new List<MaintenanceJobResponse>
                {
                    new MaintenanceJobResponse
                    {
                        JobId = "JOB001",
                        JobSummary = "First job",
                        JobStatus = "Open"
                    },
                    new MaintenanceJobResponse
                    {
                        JobId = "JOB002",
                        JobSummary = "Second job",
                        JobStatus = "Closed"
                    }
                }
            };

            // Act - This should use the legacy mapping that only takes the first job
            var result = _mapper.Map<MaintenanceDetail>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("AGENCY123", result.SRCManagementId);
            Assert.Equal("PROP456", result.SRCPropertyId);
            Assert.Equal("JOB001", result.SRCJobId); // Should only take the first job
            Assert.Equal("First job", result.JobSummary);
        }

        [Fact]
        public void Should_Handle_Empty_Jobs_In_Legacy_Mapping()
        {
            // Arrange
            var response = new MaintenanceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                MaintenanceJobs = null!
            };

            // Act
            var result = _mapper.Map<MaintenanceDetail>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("AGENCY123", result.SRCManagementId);
            Assert.Equal("PROP456", result.SRCPropertyId);
            Assert.Null(result.SRCJobId); // Should be null when no jobs
            Assert.Null(result.JobSummary);
        }

        #endregion
    }
}
