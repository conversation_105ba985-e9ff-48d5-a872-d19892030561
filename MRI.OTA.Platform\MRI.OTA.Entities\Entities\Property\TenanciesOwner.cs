﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class TenanciesOwner
    {
        [ExcludeColumn]
        public int PropertyFinancialInformationId { get; set; }
        public string SRCAgencyId { get; set; }
        [ExcludeColumn]
        public string SRCUserId { get; set; }
        public string SRCManagementId { get; set; }
        public int PropertyId { get; set; }
        public string SRCPropertyId { get; set; }
        public string TenancyStatus { get; set; }
        public string SRCTenancyId { get; set; }
        public string TenancyName { get; set; }
        public DateTime? LeaseStart { get; set; }
        public DateTime? LeaseEnd { get; set; }
        public decimal? Rent { get; set; }
        public bool IsActive { get; set; }
        public decimal? OwnershipTotalAvailableBalance { get; set; }
        public decimal? PropertyOutstandingFees { get; set; }
        public decimal? PropertyOutstandingInvoices { get; set; }
        public decimal? PropertyOverdueInvoices { get; set; }
        public decimal? LastPaymentAmount { get; set; }

        public int? RentPeriod { get; set; }

        public string? Currency { get; set; }
        public DateTime? LastStatementDate { get; set; }
        public TenanciesPropertyManagerDetails TenanciesPropertyManagerDetails { get; set; }
    }
}
