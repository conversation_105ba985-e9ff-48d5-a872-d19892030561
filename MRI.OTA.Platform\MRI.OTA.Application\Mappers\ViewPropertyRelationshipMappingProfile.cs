﻿using AutoMapper;
using MRI.OTA.Application.Models.Master;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.Application.Mappers
{
    public class ViewPropertyRelationshipMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for propertyRelationship
        /// </summary>
        public ViewPropertyRelationshipMappingProfile() : base("ViewPropertyRelationshipMappingProfile")
        {
            CreateMap<PropertyRelationship, PropertyRelationshipsModel>()
            .ReverseMap();
        }
    }
}
