﻿using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using MRI.OTA.Common.Models;

namespace MRI.OTA.API.ExceptionHandler
{
    public class ConflictExceptionHandler : IExceptionHandler
    {
        private readonly ILogger<ConflictExceptionHandler> _logger;

        public ConflictExceptionHandler(ILogger<ConflictExceptionHandler> logger)
        {
            _logger = logger;
        }

        public async ValueTask<bool> TryHandleAsync(
            HttpContext httpContext,
            Exception exception,
            CancellationToken cancellationToken)
        {
            if (exception is not ConflictException conflictException)
            {
                return false;
            }

            _logger.LogError(
                conflictException,
                "Exception occurred: {Message}",
                conflictException.Message);

            var response = new ApiResponse<object>(false, "Conflict.", data: null!, StatusCodes.Status409Conflict, new List<string> { exception.Message });

            httpContext.Response.ContentType = "application/json";
            httpContext.Response.StatusCode = StatusCodes.Status409Conflict;
            await httpContext.Response.WriteAsJsonAsync(JsonSerializer.Serialize(response), cancellationToken);

            return true;
        }
    }
}
