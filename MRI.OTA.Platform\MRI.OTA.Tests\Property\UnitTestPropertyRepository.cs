using Dapper;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.DBCore.Repositories;
using System.Data;

namespace MRI.OTA.UnitTestCases.Property
{
    public class UnitTestPropertyRepository
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<PropertyRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<IDbTransaction> _mockTransaction;
        private readonly PropertyRepository _repository;
        private readonly Mock<IAPITrackingRepository> _mockapiTrackingRepository;

        public UnitTestPropertyRepository()
        {
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<PropertyRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();
            _mockTransaction = new Mock<IDbTransaction>();
            _mockapiTrackingRepository = new Mock<IAPITrackingRepository>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);
            _mockConnection.Setup(c => c.BeginTransaction()).Returns(_mockTransaction.Object);

            _repository = new PropertyRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object, _mockapiTrackingRepository.Object);
        }

        #region GetAllProperties Tests

        [Fact]
        public async Task GetAllProperties_Should_Return_Properties_With_Pagination()
        {
            // Arrange
            int userId = 1;
            int? offset = 0;
            int? limit = 10;
            bool? showAllRecords = false;

            var expectedProperties = new List<ViewUserProperties>
            {
                new ViewUserProperties
                {
                    PropertyId = 1,
                    UserId = userId,
                    PropertyName = "Test Property 1"
                },
                new ViewUserProperties
                {
                    PropertyId = 2,
                    UserId = userId,
                    PropertyName = "Test Property 2"
                }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewUserProperties>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains(Constants.UserPropertyTableName)),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == userId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedProperties);

            // Act
            var result = await _repository.GetAllProperties(userId, offset, limit, showAllRecords);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedProperties.Count, result.Count);
            Assert.Equal(expectedProperties[0].PropertyId, result[0].PropertyId);
            Assert.Equal(expectedProperties[0].UserId, result[0].UserId);
            Assert.Equal(expectedProperties[0].PropertyName, result[0].PropertyName);
            Assert.Equal(expectedProperties[1].PropertyId, result[1].PropertyId);
        }

        [Fact]
        public async Task GetAllProperties_Should_Return_All_Properties_Without_Pagination()
        {
            // Arrange
            int userId = 1;
            int? offset = 0;
            int? limit = 10;
            bool? showAllRecords = true;

            var expectedProperties = new List<ViewUserProperties>
            {
                new ViewUserProperties { PropertyId = 1, UserId = userId, PropertyName = "Test Property 1" },
                new ViewUserProperties { PropertyId = 2, UserId = userId, PropertyName = "Test Property 2" },
                new ViewUserProperties { PropertyId = 3, UserId = userId, PropertyName = "Test Property 3" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewUserProperties>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains(Constants.UserPropertyTableName) && !q.Contains("OFFSET")),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == userId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedProperties);

            // Act
            var result = await _repository.GetAllProperties(userId, offset, limit, showAllRecords);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedProperties.Count, result.Count);
            Assert.Equal(3, result.Count);
        }

        [Fact]
        public async Task GetAllProperties_Should_Return_Empty_List_When_No_Properties_Found()
        {
            // Arrange
            int userId = 999; // User with no properties
            int? offset = 0;
            int? limit = 10;
            bool? showAllRecords = false;

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewUserProperties>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(new List<ViewUserProperties>());

            // Act
            var result = await _repository.GetAllProperties(userId, offset, limit, showAllRecords);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        #endregion

        #region GetPropertyById Tests

        [Fact]
        public async Task GetPropertyById_Should_Return_Property_When_Found()
        {
            // Arrange
            int userId = 1;
            int propertyId = 2;

            var expectedProperty = new ViewUserProperties
            {
                PropertyId = propertyId,
                UserId = userId,
                PropertyName = "Test Property",
                PropertyOwnershipDetailsId = 10,
                IsActive = true
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<ViewUserProperties>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == userId &&
                        p.GetType().GetProperty("PropertyId") != null &&
                        (int)p.GetType().GetProperty("PropertyId")!.GetValue(p)! == propertyId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedProperty);

            // Act
            var result = await _repository.GetPropertyById(userId, propertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedProperty.PropertyId, result.PropertyId);
            Assert.Equal(expectedProperty.UserId, result.UserId);
            Assert.Equal(expectedProperty.PropertyName, result.PropertyName);
        }

        [Fact]
        public async Task GetPropertyById_Should_Return_Null_When_Not_Found()
        {
            // Arrange
            int userId = 1;
            int propertyId = 999; // Non-existent property

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<ViewUserProperties>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((ViewUserProperties)null!);

            // Act
            var result = await _repository.GetPropertyById(userId, propertyId);

            // Assert
            Assert.Null(result);
        }

        #endregion

        #region GetPropertyImages Tests

        [Fact]
        public async Task GetPropertyImages_Should_Return_Images_When_Found()
        {
            // Arrange
            var propertyIds = new List<int> { 1, 2 };

            var expectedImages = new List<PropertyImages>
            {
                new PropertyImages { PropertyImagesId = 1, PropertyId = 1, ImageBlobUrl = "url1.jpg" },
                new PropertyImages { PropertyImagesId = 2, PropertyId = 1, ImageBlobUrl = "url2.jpg" },
                new PropertyImages { PropertyImagesId = 3, PropertyId = 2, ImageBlobUrl = "url3.jpg" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<PropertyImages>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains(Constants.PropertyImagesTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedImages);

            // Act
            var result = await _repository.GetPropertyImages(propertyIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedImages.Count, result.Count);
            Assert.Equal(expectedImages[0].PropertyImagesId, result[0].PropertyImagesId);
            Assert.Equal(expectedImages[0].PropertyId, result[0].PropertyId);
            Assert.Equal(expectedImages[0].ImageBlobUrl, result[0].ImageBlobUrl);
        }

        [Fact]
        public async Task GetPropertyImages_Should_Return_Empty_List_When_PropertyIds_Empty()
        {
            // Arrange
            var propertyIds = new List<int>();

            // Act
            var result = await _repository.GetPropertyImages(propertyIds);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);

            // Verify that no database call was made
            _mockDapperWrapper.Verify(
                d => d.QueryAsync<PropertyImages>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()),
                Times.Never);
        }

        [Fact]
        public async Task GetPropertyImages_Should_Return_Empty_List_When_PropertyIds_Null()
        {
            // Arrange
            List<int> propertyIds = null!;

            // Act
            var result = await _repository.GetPropertyImages(propertyIds);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);

            // Verify that no database call was made
            _mockDapperWrapper.Verify(
                d => d.QueryAsync<PropertyImages>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()),
                Times.Never);
        }

        #endregion

        #region GetPropertyRelations Tests

        [Fact]
        public async Task GetPropertyRelations_Should_Return_Relations_When_Found()
        {
            // Arrange
            int userPropertiesNickNameId = 1;

            var expectedRelations = new List<ViewUserPropertiesNickName>
            {
                new ViewUserPropertiesNickName
                {
                    PropertyId = 1,
                    UserId = 1,
                    PropertyName = "Property 1",
                    UserPropertiesNickNameId = userPropertiesNickNameId,
                    PropertyRelationshipId = 1
                },
                new ViewUserPropertiesNickName
                {
                    PropertyId = 2,
                    UserId = 1,
                    PropertyName = "Property 2",
                    UserPropertiesNickNameId = userPropertiesNickNameId,
                    PropertyRelationshipId = 2
                }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewUserPropertiesNickName>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserPropertiesNickNameId") != null &&
                        (int)p.GetType().GetProperty("UserPropertiesNickNameId")!.GetValue(p)! == userPropertiesNickNameId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRelations);

            // Act
            var result = await _repository.GetPropertyRelations(userPropertiesNickNameId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRelations.Count, result.Count);
            Assert.Equal(expectedRelations[0].PropertyId, result[0].PropertyId);
            Assert.Equal(expectedRelations[0].UserPropertiesNickNameId, result[0].UserPropertiesNickNameId);
            Assert.Equal(expectedRelations[1].PropertyId, result[1].PropertyId);
        }

        [Fact]
        public async Task GetPropertyRelations_Should_Return_Empty_List_When_Not_Found()
        {
            // Arrange
            int userPropertiesNickNameId = 999; // Non-existent ID

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewUserPropertiesNickName>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(new List<ViewUserPropertiesNickName>());

            // Act
            var result = await _repository.GetPropertyRelations(userPropertiesNickNameId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        #endregion

        #region GetPropertyNickNames Tests

        [Fact]
        public async Task GetPropertyNickNames_Should_Return_NickNames_When_Found()
        {
            // Arrange
            int userId = 1;

            var expectedNickNames = new List<ViewUserPropertiesNickName>
            {
                new ViewUserPropertiesNickName
                {
                    UserId = userId,
                    UserPropertiesNickNameId = 1,
                    PropertyNickName = "Home"
                },
                new ViewUserPropertiesNickName
                {
                    UserId = userId,
                    UserPropertiesNickNameId = 2,
                    PropertyNickName = "Vacation House"
                }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewUserPropertiesNickName>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == userId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedNickNames);

            // Act
            var result = await _repository.GetPropertyNickNames(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedNickNames.Count, result.Count);
            Assert.Equal(expectedNickNames[0].UserId, result[0].UserId);
            Assert.Equal(expectedNickNames[0].UserPropertiesNickNameId, result[0].UserPropertiesNickNameId);
            Assert.Equal(expectedNickNames[0].PropertyNickName, result[0].PropertyNickName);
        }

        [Fact]
        public async Task GetPropertyNickNames_Should_Return_Empty_List_When_Not_Found()
        {
            // Arrange
            int userId = 999; // Non-existent user

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewUserPropertiesNickName>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(new List<ViewUserPropertiesNickName>());

            // Act
            var result = await _repository.GetPropertyNickNames(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        #endregion

        #region AddProperty Tests

        [Fact]
        public async Task AddProperty_Should_Return_PropertyId_When_Successful()
        {
            // Arrange
            int expectedPropertyId = 123;
            int expectedUserDataSourceId = 789;
            int expectedNickNameId = 456;

            var userProperty = new UserProperties
            {
                UserId = 1,
                PropertyName = "Test Property",
                DataSourceId = 1,
                PropertyManagerInformation = new MRI.OTA.DBCore.Entities.Property.PropertyManagerInformation(),
                PropertyFinancialInformation = new MRI.OTA.DBCore.Entities.Property.PropertyFinancialInformation(),
            };

            // Setup for UserDataSource
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserDataSourceTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedUserDataSourceId);

            // Setup for UserPropertiesNickName
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserPropertiesNickNameTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedNickNameId);
            // Setup for UserProperties
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserPropertyTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedPropertyId);

            // Setup for PropertyManagerInformation
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.PropertyManagerInformationTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Setup for PropertyFinancialInformation
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.PropertyFinancialInformationTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.AddProperty(userProperty);

            // Assert
            Assert.Equal(expectedPropertyId, result);
        }

        [Fact]
        public async Task AddProperty_Should_Return_Error_When_Failed()
        {
            // Arrange
            var userProperty = new UserProperties
            {
                UserId = 1,
                PropertyName = "Test Property",
                DataSourceId = 1
            };

            // Setup for UserDataSource - succeeds
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserDataSourceTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Setup for UserPropertiesNickName - succeeds
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserPropertiesNickNameTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Setup for UserProperties - fails (returns 0 to simulate failure)
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserPropertyTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(0); // Return 0 to simulate failure

            // Act
            var result = await _repository.AddProperty(userProperty);

            // Assert
            Assert.Equal(Constants.Error, result);
        }

        [Fact]
        public async Task AddProperty_Should_Handle_Exception()
        {
            // Arrange
            var userProperty = new UserProperties
            {
                UserId = 1,
                PropertyName = "Test Property",
                DataSourceId = 1
            };

            // Setup exception on first call
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _repository.AddProperty(userProperty));
        }

        #endregion

        #region UpdateProperty Tests

        [Fact]
        public async Task UpdateProperty_Should_Handle_Exception()
        {
            // Arrange
            int propertyId = 123;

            var userProperty = new UserProperties
            {
                PropertyId = propertyId,
                UserId = 1,
                PropertyName = "Updated Property",
                PropertyFinancialInformation = new MRI.OTA.DBCore.Entities.Property.PropertyFinancialInformation()
            };

            // Setup NullReferenceException which matches the actual exception being thrown
            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new NullReferenceException("Object reference not set to an instance of an object"));            // Act & Assert
            // Change to expect NullReferenceException instead of generic Exception
            await Assert.ThrowsAsync<NullReferenceException>(() => _repository.UpdateProperty(userProperty));
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(), // Still using Exception here as base type for verification
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce); // Changed from Times.Once to Times.AtLeastOnce to account for multiple log calls
        }

        [Fact]
        public async Task AddProperty_Should_Add_PropertyImage_When_DataSourceId_IsNotEqualTo_SelfSource()
        {
            // Arrange
            int expectedPropertyId = 123;
            int expectedUserDataSourceId = 789;
            int expectedNickNameId = 456;

            var userProperty = new UserProperties
            {
                UserId = 1,
                PropertyName = "Test Property",
                DataSourceId = 2,
                PropertyManagerInformation = new MRI.OTA.DBCore.Entities.Property.PropertyManagerInformation(),
                PropertyFinancialInformation = new MRI.OTA.DBCore.Entities.Property.PropertyFinancialInformation(),
                DefaultImageLink = "image.jpg",
            };

            // Setup for UserDataSource
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserDataSourceTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedUserDataSourceId);

            // Setup for UserPropertiesNickName
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserPropertiesNickNameTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedNickNameId);
            // Setup for UserProperties
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserPropertyTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedPropertyId);

            // Setup for PropertyManagerInformation
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.PropertyManagerInformationTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Setup for PropertyFinancialInformation
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.PropertyFinancialInformationTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.AddProperty(userProperty);

            // Assert
            Assert.Equal(expectedPropertyId, result);
        }

        #endregion

        #region DeleteProperty Tests

        [Fact]
        public async Task DeleteProperty_Should_Return_RowsAffected()
        {
            // Arrange
            int propertyId = 123;
            int expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UPDATE") && q.Contains(Constants.UserPropertyTableName) && q.Contains("IsActive = 0")),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("PropertyId") != null &&
                        (int)p.GetType().GetProperty("PropertyId")!.GetValue(p)! == propertyId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.DeleteProperty(propertyId);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
        }

        [Fact]
        public async Task DeleteProperty_Should_Return_Zero_When_No_Property_Found()
        {
            // Arrange
            int propertyId = 999; // Non-existent property
            int expectedRowsAffected = 0;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.DeleteProperty(propertyId);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
        }

        #endregion

        #region UpdatePropertyStatus Tests

        [Fact]
        public async Task UpdatePropertyStatus_Should_Return_RowsAffected()
        {
            // Arrange
            int userId = 1;
            int propertyId = 123;
            bool isActive = false;
            int expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UPDATE") && q.Contains(Constants.UserPropertyTableName) && q.Contains("IsActive =")),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("PropertyId") != null &&
                        (int)p.GetType().GetProperty("PropertyId")!.GetValue(p)! == propertyId &&
                        p.GetType().GetProperty("UserId") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == userId &&
                        p.GetType().GetProperty("IsActive") != null &&
                        (bool)p.GetType().GetProperty("IsActive")!.GetValue(p)! == isActive),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdatePropertyStatus(userId, propertyId, isActive);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
        }


        [Fact]
        public async Task UpdatePropertyStatus_Should_Return_Zero_When_No_Property_Found()
        {
            // Arrange
            int userId = 1;
            int propertyId = 999; // Non-existent property
            bool isActive = true;
            int expectedRowsAffected = 0;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdatePropertyStatus(userId, propertyId, isActive);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
        }

        #endregion

        #region GetPropertyCountsByAgency Tests

        [Fact]
        public async Task GetPropertyCountsByAgency_Should_Return_Counts_When_Found()
        {
            // Arrange
            int userId = 1;
            var expectedCounts = new List<ViewAgencyPropertyCount>
            {
                new ViewAgencyPropertyCount
                {
                    AgencyId = "AGENCY1",
                    BusinessRegisteredName = "SelfSource Agency",
                    ActiveCount = 3,
                    InactiveCount = 1
                },
                new ViewAgencyPropertyCount
                {
                    AgencyId = "SELF_SOURCE",
                    ActiveCount = 2,
                    InactiveCount = 0
                }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewAgencyPropertyCount>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == userId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedCounts);

            // Act
            var result = await _repository.GetPropertyCountsByAgency(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedCounts.Count, result.Count);
            Assert.Equal(expectedCounts[0].AgencyId, result[0].AgencyId);
            Assert.Equal(expectedCounts[0].BusinessRegisteredName, result[0].BusinessRegisteredName);
            Assert.Equal(expectedCounts[0].ActiveCount, result[0].ActiveCount);
            Assert.Equal(expectedCounts[0].InactiveCount, result[0].InactiveCount);
        }


        [Fact]
        public async Task GetPropertyCountsByAgency_Should_Return_Empty_List_When_Exception_Occurs()
        {
            // Arrange
            int userId = 1;

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewAgencyPropertyCount>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _repository.GetPropertyCountsByAgency(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);

            // Verify the error was logged - using AtLeastOnce because both the base repository
            // and property repository log the error
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }
        [Fact]
        public async Task GetPropertiesByUserIds_Should_Return_Properties_For_Valid_UserIds()
        {
            // Arrange
            var userIds = new[] { "provider1", "provider2" };
            var expectedProperties = new List<UserProperties>
            {
                new UserProperties { UserId = 1, PropertyName = "Property 1", SRCEntitytId = "E1", SRCAgencyId = "A1", SRCManagementId = "M1" },
                new UserProperties { UserId = 2, PropertyName = "Property 2", SRCEntitytId = "E2", SRCAgencyId = "A2", SRCManagementId = "M2" }
            };

            _mockDapperWrapper
              .Setup(d => d.QueryAsync<UserProperties>(
                  It.IsAny<IDbConnection>(),
                  It.Is<string>(q => q.Contains(Constants.UserPropertyTableName) && q.Contains(Constants.UserTableName)),
                  It.Is<DynamicParameters>(p => true),
                  It.IsAny<IDbTransaction>(),
                  It.IsAny<int?>(),
                  It.IsAny<CommandType?>()))
              .ReturnsAsync(expectedProperties);

            // Act
            var result = await _repository.GetPropertiesByUserIds(userIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedProperties.Count, result.Count);
            Assert.Equal(expectedProperties[0].UserId, result[0].UserId);
            Assert.Equal(expectedProperties[1].UserId, result[1].UserId);
        }

        [Fact]
        public async Task GetPropertiesByUserIds_Should_Return_Empty_List_When_UserIds_Is_Null_Or_Empty()
        {
            // Arrange
            string[]? nullUserIds = null;
            var emptyUserIds = new string[0];

            // Act
            var resultNull = await _repository.GetPropertiesByUserIds(nullUserIds!);
            var resultEmpty = await _repository.GetPropertiesByUserIds(emptyUserIds);

            // Assert
            Assert.NotNull(resultNull);
            Assert.Empty(resultNull);
            Assert.NotNull(resultEmpty);
            Assert.Empty(resultEmpty);
        }

        [Fact]
        public async Task BulkUpsertUserProperties_Should_Return_Zero_When_List_Is_Null_Or_Empty()
        {
            // Arrange
            List<UserProperties> nullList = null!;
            var emptyList = new List<UserProperties>();
            int dataSourceId = 1;

            // Act
            var resultNull = await _repository.BulkUpsertUserProperties(nullList, dataSourceId);
            var resultEmpty = await _repository.BulkUpsertUserProperties(emptyList, dataSourceId);

            // Assert
            Assert.Equal(null!, resultNull);
            Assert.Equal(null!, resultEmpty);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_Should_Return_Detail_When_Found()
        {
            // Arrange
            string tenancyId = "T1";
            string srcPropertyId = null;
            int? propertyId = null;

            var expectedResponse = new TenanciesTenantDetailResponse
            {
                SRCPropertyId = "P1",
                SRCAgencyId = "A1",
                SRCManagementId = "M1",
                SRCTenancyId = "T1",
                PropertyManagerName = "Manager",
                PropertyManagerMobile = "1234567890",
                PropertyManagerEmail = "<EMAIL>",
                BusinessRegistrationNumber = "BRN123"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<TenanciesTenantDetailResponse>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("SELECT TOP 1") && q.Contains("FROM " + Constants.UserPropertyTableName)),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("TenancyId") != null &&
                        (string?)p.GetType().GetProperty("TenancyId")!.GetValue(p)! == tenancyId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _repository.GetTenantOwnerDetail(tenancyId, srcPropertyId, propertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResponse.SRCPropertyId, result.SRCPropertyId);
            Assert.Equal(expectedResponse.SRCAgencyId, result.SRCAgencyId);
            Assert.Equal(expectedResponse.SRCManagementId, result.SRCManagementId);
            Assert.Equal(expectedResponse.SRCTenancyId, result.SRCTenancyId);
            Assert.Equal(expectedResponse.PropertyManagerName, result.PropertyManagerName);
            Assert.Equal(expectedResponse.PropertyManagerMobile, result.PropertyManagerMobile);
            Assert.Equal(expectedResponse.PropertyManagerEmail, result.PropertyManagerEmail);
            Assert.Equal(expectedResponse.BusinessRegistrationNumber, result.BusinessRegistrationNumber);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_Should_Return_Null_When_Not_Found()
        {
            // Arrange
            string tenancyId = "T999";
            string srcPropertyId = null;
            int? propertyId = null;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<TenanciesTenantDetailResponse>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((TenanciesTenantDetailResponse)null!);

            // Act
            var result = await _repository.GetTenantOwnerDetail(tenancyId, srcPropertyId, propertyId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_Should_Use_SRCPropertyId_When_TenancyId_Is_Null_And_SRCPropertyId_Is_Not_Null()
        {
            // Arrange
            string? tenancyId = null;
            string? srcPropertyId = "SRC123";
            int? propertyId = null;

            var expectedResponse = new TenanciesTenantDetailResponse
            {
                SRCPropertyId = srcPropertyId,
                PropertyId = 10,
                SRCAgencyId = "AG1",
                SRCManagementId = "MG1",
                SRCTenancyId = "T1"
            };

            // Setup: Ensure the repository's GetByIdAsync is called with a query containing "UP.SRCEntitytId = @SRCPropertyId"
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<TenanciesTenantDetailResponse>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UP.SRCEntitytId = @SRCPropertyId")),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("SRCPropertyId") != null &&
                        (string?)p.GetType().GetProperty("SRCPropertyId")!.GetValue(p)! == srcPropertyId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _repository.GetTenantOwnerDetail(tenancyId, srcPropertyId, propertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResponse.SRCPropertyId, result.SRCPropertyId);
            Assert.Equal(expectedResponse.PropertyId, result.PropertyId);
            Assert.Equal(expectedResponse.SRCAgencyId, result.SRCAgencyId);
            Assert.Equal(expectedResponse.SRCManagementId, result.SRCManagementId);
            Assert.Equal(expectedResponse.SRCTenancyId, result.SRCTenancyId);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_Should_Use_PropertyId_When_TenancyId_And_SRCPropertyId_Are_Null_And_PropertyId_Is_Greater_Than_Zero()
        {
            // Arrange
            string? tenancyId = null;
            string? srcPropertyId = null;
            int? propertyId = 42;

            var expectedResponse = new TenanciesTenantDetailResponse
            {
                PropertyId = propertyId.Value,
                SRCPropertyId = "SRC42"
            };

            // Setup: Ensure the repository's GetByIdAsync is called with a query containing "UP.PropertyId = @PropertyId"
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<TenanciesTenantDetailResponse>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UP.PropertyId = @PropertyId")),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("PropertyId") != null &&
                        (int?)p.GetType().GetProperty("PropertyId")!.GetValue(p)! == propertyId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedResponse);

            // Act
            var result = await _repository.GetTenantOwnerDetail(tenancyId, srcPropertyId, propertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResponse.PropertyId, result.PropertyId);
            Assert.Equal(expectedResponse.SRCPropertyId, result.SRCPropertyId);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_Should_Use_ManagementId_When_Provided()
        {
            // Arrange
            string managementId = "MGMT1";
            int? propertyId = null;
            string? srcPropertyId = null;

            var expected = new PropertyFinancialWithAgencyDetails
            {
                SRCAgencyId = "AG1",
                SRCManagementId = managementId,
                SRCPropertyId = "SRC1",
                AgencyName = "Agency1"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyFinancialWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UP.SRCManagementId = @ManagementId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyFinancialInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.SRCManagementId, result.SRCManagementId);
            Assert.Equal(expected.AgencyName, result.AgencyName);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_Should_Use_PropertyId_When_Provided()
        {
            // Arrange
            string? managementId = null;
            int? propertyId = 123;
            string? srcPropertyId = null;

            var expected = new PropertyFinancialWithAgencyDetails
            {
                SRCAgencyId = "AG2",
                SRCManagementId = "MGMT2",
                SRCPropertyId = "SRC2",
                AgencyName = "Agency2"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyFinancialWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("PF.PropertyId = @PropertyId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyFinancialInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.SRCPropertyId, result.SRCPropertyId);
            Assert.Equal(expected.AgencyName, result.AgencyName);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_Should_Use_SRCPropertyId_When_Provided()
        {
            // Arrange
            string? managementId = null;
            int? propertyId = null;
            string? srcPropertyId = "SRC3";

            var expected = new PropertyFinancialWithAgencyDetails
            {
                SRCAgencyId = "AG3",
                SRCManagementId = "MGMT3",
                SRCPropertyId = srcPropertyId,
                AgencyName = "Agency3"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyFinancialWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UP.SRCEntitytId = @SRCPropertyId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyFinancialInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.SRCPropertyId, result.SRCPropertyId);
            Assert.Equal(expected.AgencyName, result.AgencyName);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_Should_Use_All_Parameters_When_All_Provided()
        {
            // Arrange
            string managementId = "MGMT4";
            int? propertyId = 444;
            string? srcPropertyId = "SRC4";

            var expected = new PropertyFinancialWithAgencyDetails
            {
                SRCAgencyId = "AG4",
                SRCManagementId = managementId,
                SRCPropertyId = srcPropertyId,
                AgencyName = "Agency4"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyFinancialWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UP.SRCManagementId = @ManagementId") && q.Contains("PF.PropertyId = @PropertyId") && q.Contains("UP.SRCEntitytId = @SRCPropertyId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyFinancialInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.SRCManagementId, result.SRCManagementId);
            Assert.Equal(expected.SRCPropertyId, result.SRCPropertyId);
            Assert.Equal(expected.AgencyName, result.AgencyName);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_Should_Return_Null_When_Not_Found()
        {
            // Arrange
            string? managementId = null;
            int? propertyId = null;
            string? srcPropertyId = null;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyFinancialWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((PropertyFinancialWithAgencyDetails)null!);

            // Act
            var result = await _repository.GetPropertyFinancialInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_Should_Use_ManagementId_When_Provided()
        {
            // Arrange
            string managementId = "MGMT1";
            int? propertyId = null;
            string? srcPropertyId = null;

            var expected = new PropertyManagerWithAgencyDetails
            {
                SRCManagementId = managementId,
                PropertyManagerName = "Manager1"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyManagerWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("PM.SRCManagementId = @ManagementId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyManagerInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.SRCManagementId, result.SRCManagementId);
            Assert.Equal(expected.PropertyManagerName, result.PropertyManagerName);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_Should_Use_PropertyId_When_Provided()
        {
            // Arrange
            string? managementId = null;
            int? propertyId = 123;
            string? srcPropertyId = null;

            var expected = new PropertyManagerWithAgencyDetails
            {
                SRCManagementId = "MGMT2",
                PropertyManagerInformationId = 123,
                PropertyManagerName = "Manager2"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyManagerWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("PM.PropertyId = @PropertyId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyManagerInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.PropertyManagerInformationId, result.PropertyManagerInformationId);
            Assert.Equal(expected.PropertyManagerName, result.PropertyManagerName);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_Should_Use_SRCPropertyId_When_Provided()
        {
            // Arrange
            string? managementId = null;
            int? propertyId = null;
            string? srcPropertyId = "SRC3";

            var expected = new PropertyManagerWithAgencyDetails
            {
                SRCPropertyId = srcPropertyId,
                PropertyManagerName = "Manager3"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyManagerWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("PM.SRCPropertyId = @SRCPropertyId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyManagerInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.SRCPropertyId, result.SRCPropertyId);
            Assert.Equal(expected.PropertyManagerName, result.PropertyManagerName);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_Should_Use_All_Parameters_When_All_Provided()
        {
            // Arrange
            string managementId = "MGMT4";
            int? propertyId = 444;
            string? srcPropertyId = "SRC4";

            var expected = new PropertyManagerWithAgencyDetails
            {
                SRCManagementId = managementId,
                PropertyManagerInformationId = 444,
                SRCPropertyId = srcPropertyId,
                PropertyManagerName = "Manager4"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyManagerWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("PM.SRCManagementId = @ManagementId") && q.Contains("PM.PropertyId = @PropertyId") && q.Contains("PM.SRCPropertyId = @SRCPropertyId")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expected);

            // Act
            var result = await _repository.GetPropertyManagerInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.SRCManagementId, result.SRCManagementId);
            Assert.Equal(expected.PropertyManagerInformationId, result.PropertyManagerInformationId);
            Assert.Equal(expected.SRCPropertyId, result.SRCPropertyId);
            Assert.Equal(expected.PropertyManagerName, result.PropertyManagerName);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_Should_Return_Null_When_Not_Found()
        {
            // Arrange
            string? managementId = null;
            int? propertyId = null;
            string? srcPropertyId = null;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyManagerWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((PropertyManagerWithAgencyDetails)null!);

            // Act
            var result = await _repository.GetPropertyManagerInformation(managementId, propertyId, srcPropertyId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetDocument_ReturnsDocuments_FilteredByTenancyId()
        {
            // Arrange
            var requestModel = new GetDocumentRequestModel
            {
                TenancyId = "T1",
                ManagementId = null,
                OffSet = 0,
                Limit = 10,
                ShowAllRecords = false
            };
            int userId = 1;
            var expected = new List<UserPropertyDocumentDetail>
            {
                new UserPropertyDocumentDetail { PropertyId = 1, UserId = userId, SRCTenancyId = "T1", DocumentName = "Doc1" }
            };

            var repo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            repo.Setup(r => r.GetAllAsync<UserPropertyDocumentDetail>(
                It.Is<string>(q => q.Contains("DD.SRCTenancyId = @TenancyId")),
                It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await repo.Object.GetDocument(requestModel, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("T1", result[0].SRCTenancyId);
        }

        [Fact]
        public async Task GetDocument_ReturnsDocuments_FilteredByManagementId()
        {
            // Arrange
            var requestModel = new GetDocumentRequestModel
            {
                TenancyId = null,
                ManagementId = "M1",
                OffSet = 0,
                Limit = 10,
                ShowAllRecords = false
            };
            int userId = 2;
            var expected = new List<UserPropertyDocumentDetail>
            {
                new UserPropertyDocumentDetail { PropertyId = 2, UserId = userId, SRCManagementId = "M1", DocumentName = "Doc2" }
            };

            var repo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            repo.Setup(r => r.GetAllAsync<UserPropertyDocumentDetail>(
                It.Is<string>(q => q.Contains("DD.SRCManagementId = @ManagementId")),
                It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await repo.Object.GetDocument(requestModel, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal("M1", result[0].SRCManagementId);
        }

        [Fact]
        public async Task GetDocument_ReturnsDocuments_FilteredByUserId_WhenNoTenancyOrManagementId()
        {
            // Arrange
            var requestModel = new GetDocumentRequestModel
            {
                TenancyId = null,
                ManagementId = null,
                OffSet = 0,
                Limit = 10,
                ShowAllRecords = false
            };
            int userId = 3;
            var expected = new List<UserPropertyDocumentDetail>
            {
                new UserPropertyDocumentDetail { PropertyId = 3, UserId = userId, DocumentName = "Doc3" }
            };

            var repo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            repo.Setup(r => r.GetAllAsync<UserPropertyDocumentDetail>(
                It.Is<string>(q => q.Contains("UP.UserId = @UserId")),
                It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await repo.Object.GetDocument(requestModel, userId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(userId, result[0].UserId);
        }

        [Fact]
        public async Task GetInspections_ReturnsInspectionDetails_WhenDataExists()
        {
            // Arrange
            var tenancyId = "T1";
            var propertyId = 123;
            var expected = new List<InspectionDetail>
            {
                new InspectionDetail
                {
                    InspectionsDetailId = 1,
                    SRCTenancyId = tenancyId,
                    SRCPropertyId = "SRC1",
                    PropertyId = propertyId,
                    SRCInspectionId = "INSP1",
                    InspectionStatus = "Completed",
                    InspectionDate = DateTime.UtcNow,
                    InspectionStartTime = DateTime.UtcNow.AddHours(-1),
                    InspectionEndTime = DateTime.UtcNow,
                    Summary = "Test summary"
                }
            };

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<InspectionDetail>(
                    It.Is<string>(q => q.Contains("FROM") && q.Contains("ORDER BY ID.InspectionDate DESC")),
                    It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await mockRepo.Object.GetInspections(propertyId, null, tenancyId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(propertyId, result[0].PropertyId);
            Assert.Equal(tenancyId, result[0].SRCTenancyId);
        }

        [Fact]
        public async Task GetInspections_ReturnsEmptyList_WhenNoData()
        {
            // Arrange
            var tenancyId = "T2";
            var propertyId = 456;

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<InspectionDetail>(
                    It.IsAny<string>(),
                    It.IsAny<object>()))
                .ReturnsAsync((List<InspectionDetail>)null!);

            // Act
            var result = await mockRepo.Object.GetInspections(propertyId, null, tenancyId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetCompliance_ReturnsComplianceDetails_WhenDataExists()
        {
            // Arrange
            var managementId = "M1";
            var propertyId = 101;
            var expected = new List<ComplianceDetail>
            {
                new ComplianceDetail
                {
                    ComplianceDetailId = 1,
                    SRCManagementId = managementId,
                    SRCPropertyId = "SRC1",
                    PropertyId = propertyId,
                    SRCComplianceId = "C1",
                    ComplianceName = "Smoke Alarm",
                    Status = "Valid",
                    ExpiryDate = DateTime.UtcNow.AddYears(1),
                    ServicedBy = "ServiceCo"
                }
            };

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<ComplianceDetail>(
                    It.Is<string>(q => q.Contains("FROM") && q.Contains("ORDER BY CD.ExpiryDate DESC")),
                    It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await mockRepo.Object.GetCompliance(managementId, propertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(propertyId, result[0].PropertyId);
            Assert.Equal(managementId, result[0].SRCManagementId);
        }

        [Fact]
        public async Task GetCompliance_ReturnsEmptyList_WhenNoData()
        {
            // Arrange
            var managementId = "M2";
            var propertyId = 202;

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<ComplianceDetail>(
                    It.IsAny<string>(),
                    It.IsAny<object>()))
                .ReturnsAsync((List<ComplianceDetail>)null!);

            // Act
            var result = await mockRepo.Object.GetCompliance(managementId, propertyId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetMaintenanceDetailsWithAgency_ReturnsResults_WhenDataExists()
        {
            // Arrange
            var managementId = "MGMT1";
            var propertyId = 123;
            var expected = new List<ViewMaintenanceDetail>
            {
                new ViewMaintenanceDetail
                {
                    PropertyId = propertyId,
                    SRCPropertyId = "SRC1",
                    SRCManagementId = managementId
                }
            };

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<ViewMaintenanceDetail>(
                    It.Is<string>(q => q.Contains("FROM") && q.Contains("ORDER BY MD.RequestRaisedDate DESC")),
                    It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await mockRepo.Object.GetMaintenanceDetailsWithAgency(propertyId, managementId, null);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(propertyId, result[0].PropertyId);
            Assert.Equal(managementId, result[0].SRCManagementId);
        }

        [Fact]
        public async Task GetMaintenanceDetailsWithAgency_ReturnsEmptyList_WhenNoData()
        {
            // Arrange
            var managementId = "MGMT2";
            var propertyId = 456;

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<ViewMaintenanceDetail>(
                    It.IsAny<string>(),
                    It.IsAny<object>()))
                .ReturnsAsync((List<ViewMaintenanceDetail>)null!);

            // Act
            var result = await mockRepo.Object.GetMaintenanceDetailsWithAgency(propertyId, managementId, null);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }
        [Fact]
        public async Task GetMaintenanceDetails_ReturnsMaintenanceDetails_WhenDataExists()
        {
            // Arrange
            var managementId = "MGMT1";
            var propertyId = 123;
            var expected = new List<ViewMaintenanceDetail>
            {
                new ViewMaintenanceDetail
                {
                    MaintenanceDetailId = 1,
                    PropertyId = propertyId,
                    SRCPropertyId = "SRC1",
                    SRCManagementId = managementId,
                    AgencyName = "Test Agency"
                }
            };

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<ViewMaintenanceDetail>(
                    It.Is<string>(q => q.Contains("FROM") && q.Contains("ORDER BY MD.RequestRaisedDate DESC")),
                    It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await mockRepo.Object.GetMaintenanceDetails(propertyId, managementId, null);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(propertyId, result[0].PropertyId);
            Assert.Equal(managementId, result[0].SRCManagementId);
            Assert.Equal("Test Agency", result[0].AgencyName);
        }

        [Fact]
        public async Task GetMaintenanceDetails_ReturnsEmptyList_WhenNoData()
        {
            // Arrange
            var managementId = "MGMT2";
            var propertyId = 456;

            var mockRepo = new Mock<PropertyRepository>(null, null, null, null) { CallBase = true };
            mockRepo
                .Setup(r => r.GetAllAsync<ViewMaintenanceDetail>(
                    It.IsAny<string>(),
                    It.IsAny<object>()))
                .ReturnsAsync((List<ViewMaintenanceDetail>)null!);

            // Act
            var result = await mockRepo.Object.GetMaintenanceDetails(propertyId, managementId, null);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        #endregion

        [Fact]
        public async Task UpdateProperty_Should_Throw_And_Log_When_Exception_Occurs()
        {
            // Arrange
            var userProperty = new UserProperties
            {
                PropertyId = 123,
                UserId = 1,
                PropertyName = "Updated Property",
            };

            var mockRepo = new Mock<PropertyRepository>(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object,
                _mockapiTrackingRepository.Object
            )
            { CallBase = true };

            mockRepo.Setup(r => r.ConvertToDictionary(It.IsAny<object>())).Throws(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => mockRepo.Object.UpdateProperty(userProperty));
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_Should_Return_Results_When_Found()
        {
            // Arrange
            int userId = 1;
            var expected = new List<(int, string, int, int)>
            {
                (1, "DS1", 2, 1),
                (2, "DS2", 3, 0)
            };

            var mockRepo = new Mock<PropertyRepository>(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object,
                _mockapiTrackingRepository.Object
            )
            { CallBase = true };

            mockRepo.Setup(r => r.QueryAsync<(int, string, int, int)>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(expected);

            // Act
            var result = await mockRepo.Object.GetPropertyCountsByDataSource(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expected.Count, result.Count);
            Assert.Equal(expected[0], result[0]);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            int userId = 1;

            var mockRepo = new Mock<PropertyRepository>(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object,
                _mockapiTrackingRepository.Object
            )
            { CallBase = true };

            mockRepo.Setup(r => r.QueryAsync<(int, string, int, int)>(It.IsAny<string>(), It.IsAny<object>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await mockRepo.Object.GetPropertyCountsByDataSource(userId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => true),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Fact]
        public async Task CreateTenancyDetails_Should_Return_When_TenanciesTenant_Is_Null()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                UserId = 1,
                PropertyId = 10,
                TenanciesTenant = null
            };

            // Act
            var method = typeof(PropertyRepository).GetMethod("CreateTenancyDetails", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)method.Invoke(_repository, new object[] { userProperties, _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockDapperWrapper.VerifyNoOtherCalls();
            _mockLogger.Verify(
                l => l.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Never);
        }

        [Fact]
        public async Task CreateTenancyDetails_Should_Add_Tenancy_And_Manager_When_TenanciesTenant_Is_Not_Null()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                UserId = 1,
                PropertyId = 10,
                TenanciesTenant = new TenanciesTenant
                {
                    PropertyId = 10,
                    TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails()
                }
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var method = typeof(PropertyRepository).GetMethod("CreateTenancyDetails", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)method.Invoke(_repository, new object[] { userProperties, _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Created Tenancy details for PropertyId")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateTenancyDetails_Should_Log_And_Track_When_Exception_Occurs()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                UserId = 1,
                PropertyId = 10,
                TenanciesTenant = new TenanciesTenant
                {
                    PropertyId = 10,
                    TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails()
                }
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var method = typeof(PropertyRepository).GetMethod("CreateTenancyDetails", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)method.Invoke(_repository, new object[] { userProperties, _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockapiTrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.Is<APITrackingDetail>(d =>
                    d.APIDetailId == (int)Constants.APIDetail.GetTenanciesTenant &&
                    d.UserId == userProperties.UserId &&
                    d.ErrorInfo == "Test exception" &&
                    d.AdditionalErrorInfo.Contains("CreateTenancyDetails"))),
                Times.Once);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error in creating tenancy details for PropertyId")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOwnerDetails_Should_Return_When_TenanciesOwner_Is_Null()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                UserId = 1,
                PropertyId = 10,
                TenanciesOwner = null
            };

            // Act
            var method = typeof(PropertyRepository).GetMethod("CreateOwnerDetails", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)method.Invoke(_repository, new object[] { userProperties, _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockDapperWrapper.VerifyNoOtherCalls();
            _mockLogger.Verify(
                l => l.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Never);
        }

        [Fact]
        public async Task CreateOwnerDetails_Should_Add_Owner_And_Manager_When_TenanciesOwner_Is_Not_Null()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                UserId = 1,
                PropertyId = 10,
                TenanciesOwner = new TenanciesOwner
                {
                    PropertyId = 10,
                    TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails()
                }
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var method = typeof(PropertyRepository).GetMethod("CreateOwnerDetails", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)method.Invoke(_repository, new object[] { userProperties, _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Created Owner details for PropertyId")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreateOwnerDetails_Should_Log_And_Track_When_Exception_Occurs()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                UserId = 1,
                PropertyId = 10,
                TenanciesOwner = new TenanciesOwner
                {
                    PropertyId = 10,
                    TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails()
                }
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var method = typeof(PropertyRepository).GetMethod("CreateOwnerDetails", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)method.Invoke(_repository, new object[] { userProperties, _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockapiTrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.Is<APITrackingDetail>(d =>
                    d.APIDetailId == (int)Constants.APIDetail.GetTenanciesOwner &&
                    d.UserId == userProperties.UserId &&
                    d.ErrorInfo == "Test exception" &&
                    d.AdditionalErrorInfo.Contains("CreateOwnerDetails"))),
                Times.Once);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error in creating tenancy details for PropertyId")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task CreatePropertyDetails_Should_Invoke_CreateDetailsList_And_DeleteDocumentList()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                PropertyId = 1,
                UserId = 2,
                MaintenanceDetailList = new List<MaintenanceDetail> { new MaintenanceDetail() },
                InspectionDetailList = new List<InspectionDetail> { new InspectionDetail() },
                ComplianceDetailList = new List<ComplianceDetail> { new ComplianceDetail() },
                DocumentDetailList = new List<DocumentDetail>
                {
                    new DocumentDetail { SRCDocumentId = "doc1", DocumentDetailId = "1", isRemove = false },
                    new DocumentDetail { SRCDocumentId = "doc2", DocumentDetailId = "2", isRemove = true }
                }
            };

            // Act
            var method = typeof(PropertyRepository).GetMethod("CreatePropertyDetails", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            await (Task)method.Invoke(_repository, new object[] { userProperties, _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Fact]
        public async Task DeleteDocumentList_Should_Return_If_Details_Is_Null_Or_Empty()
        {
            // Arrange
            var userProperties = new UserProperties { PropertyId = 1, UserId = 2 };
            var method = typeof(PropertyRepository).GetMethod("DeleteDocumentList", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            await (Task)method.Invoke(_repository, new object[] { null!, userProperties, Constants.DocumentDetailsTableName, "document", _mockConnection.Object, _mockTransaction.Object });
            await (Task)method.Invoke(_repository, new object[] { new List<DocumentDetail>(), userProperties, Constants.DocumentDetailsTableName, "document", _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockLogger.Verify(
                l => l.Log(
                    It.IsAny<LogLevel>(),
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Never);
        }

        [Fact]
        public async Task DeleteDocumentList_Should_Delete_And_Log_Info()
        {
            // Arrange
            var userProperties = new UserProperties { PropertyId = 1, UserId = 2 };
            var details = new List<DocumentDetail> { new DocumentDetail { SRCDocumentId = "doc1" } };
            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ReturnsAsync(1);

            var method = typeof(PropertyRepository).GetMethod("DeleteDocumentList", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            await (Task)method.Invoke(_repository, new object[] { details, userProperties, Constants.DocumentDetailsTableName, "document", _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Created 1 document details for PropertyId")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task DeleteDocumentList_Should_Log_And_Track_When_Exception_Occurs()
        {
            // Arrange
            var userProperties = new UserProperties { PropertyId = 1, UserId = 2 };
            var details = new List<DocumentDetail> { new DocumentDetail { SRCDocumentId = "doc1" } };
            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            var method = typeof(PropertyRepository).GetMethod("DeleteDocumentList", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            await (Task)method.Invoke(_repository, new object[] { details, userProperties, Constants.DocumentDetailsTableName, "document", _mockConnection.Object, _mockTransaction.Object });

            // Assert
            _mockapiTrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.Is<APITrackingDetail>(d =>
                    d.APIDetailId == (int)Constants.APIDetail.GetDocuments &&
                    d.UserId == userProperties.UserId &&
                    d.ErrorInfo == "Test exception" &&
                    d.AdditionalErrorInfo.Contains("Delete Document Process"))),
                Times.Once);

            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error in Delete Document Process for PropertyId")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AddPropertyOtherDetails_Should_Return_Success_When_All_Steps_Succeed()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                PropertyId = 1,
                PropertyName = "Test Property",
                UserId = 2,
                MaintenanceDetailList = new List<MaintenanceDetail>(),
                InspectionDetailList = new List<InspectionDetail>(),
                ComplianceDetailList = new List<ComplianceDetail>(),
                DocumentDetailList = new List<DocumentDetail>(),
                TenanciesTenant = new TenanciesTenant { PropertyId = 1, TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails() },
                TenanciesOwner = new TenanciesOwner { PropertyId = 1, TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails() }
            };

            // Setup all AddAsync calls to succeed
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.AddPropertyOtherDetails(userProperties);

            // Assert
            Assert.Equal(Constants.Success, result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully added property data")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AddPropertyOtherDetails_Should_Work_When_Tenancy_And_Owner_Null()
        {
            // Arrange
            var userProperties = new UserProperties
            {
                PropertyId = 1,
                PropertyName = "Test Property",
                UserId = 2,
                TenanciesTenant = null,
                TenanciesOwner = null,
                MaintenanceDetailList = new List<MaintenanceDetail>(),
                InspectionDetailList = new List<InspectionDetail>(),
                ComplianceDetailList = new List<ComplianceDetail>(),
                DocumentDetailList = new List<DocumentDetail>(),
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<System.Data.CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.AddPropertyOtherDetails(userProperties);

            // Assert
            Assert.Equal(Constants.Success, result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Successfully added property data")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_Should_Return_Success_When_Updating_Existing_Nickname()
        {
            // Arrange
            int userId = 1, propertyId = 2, nicknameId = 10;
            string? nickname = null;
            int expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UPDATE")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdatePropertyPortfolio(userId, propertyId, nicknameId, nickname);

            // Assert
            Assert.Equal(Constants.Success, result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_Should_Return_Error_When_Update_Fails()
        {
            // Arrange
            int userId = 1, propertyId = 2, nicknameId = 10;
            string? nickname = null;
            int expectedRowsAffected = 0;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdatePropertyPortfolio(userId, propertyId, nicknameId, nickname);

            // Assert
            Assert.Equal(Constants.Error, result);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_Should_Return_DuplicateError_When_Nickname_Exists()
        {
            // Arrange
            int userId = 1, propertyId = 2, nicknameId = 0;
            string nickname = "Home";

            // Mock the duplicate check to return 1 (duplicate exists)
            var repoMock = new Mock<PropertyRepository>(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object,
                _mockapiTrackingRepository.Object
            )
            { CallBase = true };

            repoMock
                .Setup(r => r.GetByIdAsync<int>(
                    It.Is<string>(q => q.Contains("SELECT COUNT(1)")),
                    It.Is<object>(p => (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == userId && (string)p.GetType().GetProperty("PropertyNickName")!.GetValue(p)! == nickname) , 
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<IDbConnection>()))
                .ReturnsAsync(1);

            // Also mock the update call so it doesn't throw
            repoMock
                .Setup(r => r.ExecuteAsync(
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<IDbConnection>()))
                .ReturnsAsync(1);

            // Act
            var result = await repoMock.Object.UpdatePropertyPortfolio(userId, propertyId, nicknameId, nickname);

            // Assert
            Assert.Equal(Constants.DuplicateError, result);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_Should_Create_New_Nickname_And_Update_Property()
        {
            // Arrange
            int userId = 1, propertyId = 2, nicknameId = 0;
            string nickname = "NewNick";

            // Simulate no duplicate found
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("SELECT COUNT(1)")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(0);

            // Simulate AddAsync for new nickname
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(99);

            // Simulate update property
            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UPDATE")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.UpdatePropertyPortfolio(userId, propertyId, nicknameId, nickname);

            // Assert
            Assert.Equal(Constants.Success, result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.IsAny<It.IsAnyType>(),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Exactly(2)); // One for nickname creation, one for property update
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_Should_Throw_And_LogError_On_Exception()
        {
            // Arrange
            int userId = 1, propertyId = 2, nicknameId = 0;
            string nickname = "ErrorNick";

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _repository.UpdatePropertyPortfolio(userId, propertyId, nicknameId, nickname));
        }

        [Fact]
        public async Task BulkUpsertUserProperties_Should_Return_Null_When_List_Is_Null_Or_Empty()
        {
            // Arrange
            List<UserProperties> nullList = null!;
            var emptyList = new List<UserProperties>();
            int dataSourceId = 1;

            // Act
            var resultNull = await _repository.BulkUpsertUserProperties(nullList, dataSourceId);
            var resultEmpty = await _repository.BulkUpsertUserProperties(emptyList, dataSourceId);

            // Assert
            Assert.Null(resultNull);
            Assert.Null(resultEmpty);
        }

        [Fact]
        public async Task BulkUpsertUserProperties_Should_Return_Results_When_Successful()
        {
            // Arrange
            var userPropertiesList = new List<UserProperties>
            {
                new UserProperties { UserId = 1, PropertyName = "Property 1", DataSourceId = 1, IsActive = true },
                new UserProperties { UserId = 2, PropertyName = "Property 2", DataSourceId = 2, IsActive = true }
            };
            int dataSourceId = 1;

            // Mock AddAsync for UserDataSource
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("INSERT") && q.Contains(Constants.UserDataSourceTableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(100);

            // Mock QueryAsync for merge
            var expectedResults = new List<SQLQueryMergeResult>
            {
                new SQLQueryMergeResult { SRCPropertyId = "SRC1", SRCManagementId = "MGMT1" },
                new SQLQueryMergeResult { SRCPropertyId = "SRC2", SRCManagementId = "MGMT2" }
            };
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("MERGE")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedResults);

            // Act
            var result = await _repository.BulkUpsertUserProperties(userPropertiesList, dataSourceId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResults.Count, result.Count);
            Assert.Equal(expectedResults[0].SRCPropertyId, result[0].SRCPropertyId);
            Assert.Equal(expectedResults[1].SRCPropertyId, result[1].SRCPropertyId);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Bulk upserted")),
                    null,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertUserProperties_Should_Throw_And_LogError_On_Exception()
        {
            // Arrange
            var userPropertiesList = new List<UserProperties>
            {
                new UserProperties { UserId = 1, PropertyName = "Property 1", DataSourceId = 1, IsActive = true }
            };
            int dataSourceId = 1;

            // Mock AddAsync for UserDataSource
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(100);

            // Mock QueryAsync for merge to throw
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("MERGE")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Merge error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _repository.BulkUpsertUserProperties(userPropertiesList, dataSourceId));
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error in bulk upsert")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
            _mockapiTrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.Is<APITrackingDetail>(d =>
                    d.ErrorInfo.Contains("Merge error") &&
                    d.IsDataSync == true)),
                Times.Once);
        }

        [Fact]
        public async Task GetMaintenanceDetails_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewMaintenanceDetail>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetMaintenanceDetails(1, "mgmt", "tenancy");

            // Assert
            Assert.Empty(result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting maintenance details")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetMaintenanceDetailsWithAgency_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewMaintenanceDetail>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetMaintenanceDetailsWithAgency(1, "mgmt", "tenancy");

            // Assert
            Assert.Empty(result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting maintenance details with agency")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetCompliance_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ComplianceDetail>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetCompliance("mgmt", 1);

            // Assert
            Assert.Empty(result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting compliance details")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetInspections_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<InspectionDetail>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetInspections(1, "mgmt", "tenancy");

            // Assert
            Assert.Empty(result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting inspections")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetDocument_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<UserPropertyDocumentDetail>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var requestModel = new MRI.OTA.Common.Models.Request.GetDocumentRequestModel
            {
                ManagementId = "mgmt",
                TenancyId = "tenancy",
                OffSet = 0,
                Limit = 10,
                ShowAllRecords = false
            };
            var result = await _repository.GetDocument(requestModel, 1);

            // Assert
            Assert.Empty(result);
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting documents")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_Should_Return_Null_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<PropertyManagerWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetPropertyManagerInformation("mgmt", 1, "srcProp");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_Should_Return_Null_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<PropertyFinancialWithAgencyDetails>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetPropertyFinancialInformation("mgmt", 1, "srcProp");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_Should_Return_Null_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<TenanciesTenantDetailResponse>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetTenantOwnerDetail("tenancy", "srcProp", 1);

            // Assert
            Assert.Null(result);
        }
        #region Helper Classes

        public class PropertyOwnershipDetails
        {
            // Minimal implementation for testing
            public int PropertyOwnershipDetailsId { get; set; }
            public decimal? PurchasePrice { get; set; }
            public DateTime? OwnershipStartDate { get; set; }
            public DateTime? OwnershipEndDate { get; set; }
            public DateTime? LeaseStartDate { get; set; }
            public DateTime? LeaseEndDate { get; set; }
            public decimal? RentAmount { get; set; }
            public decimal? SecurityDeposit { get; set; }
        }

        public class PropertyManagerInformation
        {
            // Minimal implementation for testing
            public int PropertyManagerInformationId { get; set; }
            public int PropertyId { get; set; }
            public string? ManagementType { get; set; }
            public string? AgencyName { get; set; }
            public string? PropertyManagerName { get; set; }
            public string? PropertyManagerMobile { get; set; }
            public string? PropertyManagerPhone { get; set; }
            public string? PropertyManagerEmail { get; set; }
        }

        public class PropertyFinancialInformation
        {
            // Minimal implementation for testing
            public int PropertyFinancialInformationId { get; set; }
            public int PropertyId { get; set; }
            public string? TenancyName { get; set; }
            public DateTime? LeaseStart { get; set; }
            public DateTime? LeaseEnd { get; set; }
            public decimal? Rent { get; set; }
        }

        public class UserDataSource
        {
            // Minimal implementation for testing
            public int UserDataSourceId { get; set; }
            public int DataSourceId { get; set; }
            public string? AccessKey { get; set; }
            public string? AccessToken { get; set; }
        }

        public class UserPropertiesNickName
        {
            // Minimal implementation for testing
            public int UserPropertiesNickNameId { get; set; }
            public int UserId { get; set; }
            public string? PropertyNickName { get; set; }
            public bool IsActive { get; set; }
        }

        #endregion
    }
}
