using MRI.OTA.Email;
using System.Text;
using Xunit;

namespace MRI.OTA.UnitTestCases.Email
{
    public class UnitTestEmailMessage
    {
        [Fact]
        public void Constructor_InitializesAllCollections()
        {
            // Act
            var message = new EmailMessage();

            // Assert
            Assert.NotNull(message.ToAddresses);
            Assert.NotNull(message.CcAddresses);
            Assert.NotNull(message.BccAddresses);
            Assert.NotNull(message.Attachments);
            Assert.Empty(message.ToAddresses);
            Assert.Empty(message.CcAddresses);
            Assert.Empty(message.BccAddresses);
            Assert.Empty(message.Attachments);
        }

        [Fact]
        public void Constructor_InitializesStringPropertiesToNull()
        {
            // Act
            var message = new EmailMessage();

            // Assert
            Assert.Null(message.Subject);
            Assert.Null(message.HtmlContent);
            Assert.Null(message.PlainTextContent);
            Assert.Null(message.FromAddress);
            Assert.Null(message.ReplyToAddress);
        }

        [Fact]
        public void Subject_Property_CanBeSet()
        {
            // Arrange
            var message = new EmailMessage();
            var subject = "Test Subject";

            // Act
            message.Subject = subject;

            // Assert
            Assert.Equal(subject, message.Subject);
        }

        [Fact]
        public void HtmlContent_Property_CanBeSet()
        {
            // Arrange
            var message = new EmailMessage();
            var htmlContent = "<h1>Test HTML Content</h1>";

            // Act
            message.HtmlContent = htmlContent;

            // Assert
            Assert.Equal(htmlContent, message.HtmlContent);
        }

        [Fact]
        public void PlainTextContent_Property_CanBeSet()
        {
            // Arrange
            var message = new EmailMessage();
            var plainTextContent = "Test plain text content";

            // Act
            message.PlainTextContent = plainTextContent;

            // Assert
            Assert.Equal(plainTextContent, message.PlainTextContent);
        }

        [Fact]
        public void FromAddress_Property_CanBeSet()
        {
            // Arrange
            var message = new EmailMessage();
            var fromAddress = new EmailAddress("<EMAIL>", "From User");

            // Act
            message.FromAddress = fromAddress;

            // Assert
            Assert.Equal(fromAddress, message.FromAddress);
        }

        [Fact]
        public void ReplyToAddress_Property_CanBeSet()
        {
            // Arrange
            var message = new EmailMessage();
            var replyToAddress = new EmailAddress("<EMAIL>", "Reply To User");

            // Act
            message.ReplyToAddress = replyToAddress;

            // Assert
            Assert.Equal(replyToAddress, message.ReplyToAddress);
        }

        [Fact]
        public void ToAddresses_CanAddMultipleAddresses()
        {
            // Arrange
            var message = new EmailMessage();
            var address1 = new EmailAddress("<EMAIL>", "To User 1");
            var address2 = new EmailAddress("<EMAIL>", "To User 2");

            // Act
            message.ToAddresses.Add(address1);
            message.ToAddresses.Add(address2);

            // Assert
            Assert.Equal(2, message.ToAddresses.Count);
            Assert.Contains(address1, message.ToAddresses);
            Assert.Contains(address2, message.ToAddresses);
        }

        [Fact]
        public void CcAddresses_CanAddMultipleAddresses()
        {
            // Arrange
            var message = new EmailMessage();
            var address1 = new EmailAddress("<EMAIL>", "CC User 1");
            var address2 = new EmailAddress("<EMAIL>", "CC User 2");

            // Act
            message.CcAddresses.Add(address1);
            message.CcAddresses.Add(address2);

            // Assert
            Assert.Equal(2, message.CcAddresses.Count);
            Assert.Contains(address1, message.CcAddresses);
            Assert.Contains(address2, message.CcAddresses);
        }

        [Fact]
        public void BccAddresses_CanAddMultipleAddresses()
        {
            // Arrange
            var message = new EmailMessage();
            var address1 = new EmailAddress("<EMAIL>", "BCC User 1");
            var address2 = new EmailAddress("<EMAIL>", "BCC User 2");

            // Act
            message.BccAddresses.Add(address1);
            message.BccAddresses.Add(address2);

            // Assert
            Assert.Equal(2, message.BccAddresses.Count);
            Assert.Contains(address1, message.BccAddresses);
            Assert.Contains(address2, message.BccAddresses);
        }

        [Fact]
        public void Attachments_CanAddMultipleAttachments()
        {
            // Arrange
            var message = new EmailMessage();
            var attachment1 = new EmailAttachment("file1.txt", Encoding.UTF8.GetBytes("Content 1"), "text/plain");
            var attachment2 = new EmailAttachment("file2.pdf", Encoding.UTF8.GetBytes("Content 2"), "application/pdf");

            // Act
            message.Attachments.Add(attachment1);
            message.Attachments.Add(attachment2);

            // Assert
            Assert.Equal(2, message.Attachments.Count);
            Assert.Contains(attachment1, message.Attachments);
            Assert.Contains(attachment2, message.Attachments);
        }

        [Fact]
        public void EmailMessage_ImplementsIEmailMessage()
        {
            // Arrange & Act
            var message = new EmailMessage();

            // Assert
            Assert.IsAssignableFrom<IEmailMessage>(message);
        }

        [Fact]
        public void Collections_AreReadOnlyProperties()
        {
            // Arrange
            var message = new EmailMessage();
            var originalToAddresses = message.ToAddresses;
            var originalCcAddresses = message.CcAddresses;
            var originalBccAddresses = message.BccAddresses;
            var originalAttachments = message.Attachments;

            // Act & Assert
            Assert.Same(originalToAddresses, message.ToAddresses);
            Assert.Same(originalCcAddresses, message.CcAddresses);
            Assert.Same(originalBccAddresses, message.BccAddresses);
            Assert.Same(originalAttachments, message.Attachments);
        }

        [Fact]
        public void EmailMessage_CanBeFullyPopulated()
        {
            // Arrange
            var message = new EmailMessage();
            var subject = "Complete Email Test";
            var htmlContent = "<h1>HTML Content</h1>";
            var plainTextContent = "Plain text content";
            var fromAddress = new EmailAddress("<EMAIL>", "From User");
            var replyToAddress = new EmailAddress("<EMAIL>", "Reply To User");
            var toAddress = new EmailAddress("<EMAIL>", "To User");
            var ccAddress = new EmailAddress("<EMAIL>", "CC User");
            var bccAddress = new EmailAddress("<EMAIL>", "BCC User");
            var attachment = new EmailAttachment("test.txt", Encoding.UTF8.GetBytes("Test content"), "text/plain");

            // Act
            message.Subject = subject;
            message.HtmlContent = htmlContent;
            message.PlainTextContent = plainTextContent;
            message.FromAddress = fromAddress;
            message.ReplyToAddress = replyToAddress;
            message.ToAddresses.Add(toAddress);
            message.CcAddresses.Add(ccAddress);
            message.BccAddresses.Add(bccAddress);
            message.Attachments.Add(attachment);

            // Assert
            Assert.Equal(subject, message.Subject);
            Assert.Equal(htmlContent, message.HtmlContent);
            Assert.Equal(plainTextContent, message.PlainTextContent);
            Assert.Equal(fromAddress, message.FromAddress);
            Assert.Equal(replyToAddress, message.ReplyToAddress);
            Assert.Single(message.ToAddresses);
            Assert.Single(message.CcAddresses);
            Assert.Single(message.BccAddresses);
            Assert.Single(message.Attachments);
        }
    }
}
