using System;
using System.Collections.Generic;
using System.IO;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Infrastructure.Middlewares;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares
{
    public class UnitTestTaskContextMiddleware
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<ILogger<TaskContextMiddleware>> _loggerMock;
        private readonly Mock<IUserRepository> _userRepositoryMock;
        private readonly TaskContextMiddleware _middleware;
        private readonly TaskContext _taskContext;

        public UnitTestTaskContextMiddleware()
        {
            _nextMock = new Mock<RequestDelegate>();
            _loggerMock = new Mock<ILogger<TaskContextMiddleware>>();
            _userRepositoryMock = new Mock<IUserRepository>();
            _taskContext = new TaskContext();

            _middleware = new TaskContextMiddleware(_nextMock.Object, _loggerMock.Object);
        }

        [Fact]
        public void Constructor_CreatesInstance_WhenValidParametersProvided()
        {
            // Arrange
            var nextMock = new Mock<RequestDelegate>();
            var loggerMock = new Mock<ILogger<TaskContextMiddleware>>();

            // Act
            var middleware = new TaskContextMiddleware(nextMock.Object, loggerMock.Object);

            // Assert
            Assert.NotNull(middleware);
        }

        [Fact]
        public async Task InvokeAsync_InitializesRequestStartTime()
        {
            // Arrange
            var context = CreateHttpContext();
            var beforeTime = DateTime.UtcNow;

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            var afterTime = DateTime.UtcNow;
            Assert.True(_taskContext.RequestStartTime >= beforeTime);
            Assert.True(_taskContext.RequestStartTime <= afterTime);
        }

        [Fact]
        public async Task InvokeAsync_CallsNextMiddleware()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_AuthenticatesWithApiKey_WhenBothKeyAndTokenPresent()
        {
            // Arrange
            var context = CreateHttpContextWithApiKeyClaims("test-key", "test-token");

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("test-key", _taskContext.AccessKey);
            Assert.Equal("test-token", _taskContext.AccessToken);
            _userRepositoryMock.Verify(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
        }

        [Fact]
        public async Task InvokeAsync_FallsBackToUserAuth_WhenApiKeyMissing()
        {
            // Arrange
            var context = CreateHttpContextWithUserClaims("<EMAIL>", "provider123");
            var user = new Users { UserId = 42, ProviderId = "provider123" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("<EMAIL>", _taskContext.Email);
            Assert.Equal(42, _taskContext.UserId);
        }

        [Fact]
        public async Task InvokeAsync_FallsBackToUserAuth_WhenApiTokenMissing()
        {
            // Arrange
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"] = "test-key",
                // Missing actor claim (token)
                ["emails"] = "<EMAIL>",
                ["sub"] = "provider123"
            });
            var user = new Users { UserId = 42, ProviderId = "provider123" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("<EMAIL>", _taskContext.Email);
            Assert.Equal(42, _taskContext.UserId);
            Assert.Equal("test-key", _taskContext.AccessKey); // Still sets access key
        }

        [Fact]
        public async Task InvokeAsync_SetsEmailOnly_WhenProviderIdMissing()
        {
            // Arrange
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["emails"] = "<EMAIL>"
                // No provider ID claims
            });

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("<EMAIL>", _taskContext.Email);
            Assert.Equal(0, _taskContext.UserId); // Default value
            _userRepositoryMock.Verify(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()), Times.Never);
        }

        [Fact]
        public async Task InvokeAsync_UsesSubClaim_ForProviderId()
        {
            // Arrange
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["emails"] = "<EMAIL>",
                ["sub"] = "sub-provider-id"
            });
            var user = new Users { UserId = 123, ProviderId = "sub-provider-id" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(123, _taskContext.UserId);
            _userRepositoryMock.Verify(r => r.GetByIdAsync<Users>(
                "SELECT userid FROM Users WHERE providerid = @ProviderId",
                It.Is<object>(p => p.GetType().GetProperty("ProviderId")!.GetValue(p)!.ToString() == "sub-provider-id")),
                Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_UsesNameIdentifierClaim_WhenSubMissing()
        {
            // Arrange
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["emails"] = "<EMAIL>",
                ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"] = "nameidentifier-provider-id"
            });
            var user = new Users { UserId = 456, ProviderId = "nameidentifier-provider-id" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(456, _taskContext.UserId);
        }

        [Fact]
        public async Task InvokeAsync_HandlesUserNotFound_Gracefully()
        {
            // Arrange
            var context = CreateHttpContextWithUserClaims("<EMAIL>", "nonexistent-provider");
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync((Users?)null);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("<EMAIL>", _taskContext.Email);
            Assert.Equal(0, _taskContext.UserId); // Default value when user not found
        }

        [Fact]
        public async Task InvokeAsync_HandlesDatabaseException_Gracefully()
        {
            // Arrange
            var context = CreateHttpContextWithUserClaims("<EMAIL>", "provider123");
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ThrowsAsync(new InvalidOperationException("Database error"));

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("<EMAIL>", _taskContext.Email);
            Assert.Equal(0, _taskContext.UserId); // Default value when exception occurs
        }

        [Fact]
        public async Task InvokeAsync_LogsAccessKey_WhenSet()
        {
            // Arrange
            var context = CreateHttpContextWithApiKeyClaims("logged-key", "test-token");

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Setting TaskContext.AccessKey: logged-key")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_HandlesEmptyClaimsCollection()
        {
            // Arrange
            var context = CreateHttpContext(); // No claims

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(string.Empty, _taskContext.AccessKey);
            Assert.Null(_taskContext.AccessToken);
            Assert.Equal(string.Empty, _taskContext.Email);
            Assert.Equal(0, _taskContext.UserId);
        }

        [Fact]
        public async Task InvokeAsync_PreservesExistingTaskContextProperties()
        {
            // Arrange
            var context = CreateHttpContext();
            var existingRequestId = _taskContext.RequestId;

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(existingRequestId, _taskContext.RequestId); // Should not change
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.Response.Body = new MemoryStream();
            return context;
        }

        private static HttpContext CreateHttpContextWithApiKeyClaims(string accessKey, string accessToken)
        {
            return CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"] = accessKey,
                ["http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor"] = accessToken
            });
        }

        private static HttpContext CreateHttpContextWithUserClaims(string email, string providerId)
        {
            return CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["emails"] = email,
                ["sub"] = providerId
            });
        }

        private static HttpContext CreateHttpContextWithClaims(Dictionary<string, string> claims)
        {
            var context = CreateHttpContext();
            var claimsList = new List<Claim>();

            foreach (var claim in claims)
            {
                claimsList.Add(new Claim(claim.Key, claim.Value));
            }

            var identity = new ClaimsIdentity(claimsList, "test");
            context.User = new ClaimsPrincipal(identity);

            return context;
        }

        [Fact]
        public async Task InvokeAsync_HandlesNullClaims_Gracefully()
        {
            // Arrange
            var context = CreateHttpContext();
            context.User = new ClaimsPrincipal(); // No identity

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(string.Empty, _taskContext.AccessKey);
            Assert.Null(_taskContext.AccessToken);
            Assert.Equal(string.Empty, _taskContext.Email);
            Assert.Equal(0, _taskContext.UserId);
        }

        [Fact]
        public async Task InvokeAsync_HandlesEmptyStringClaims()
        {
            // Arrange
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"] = "",
                ["http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor"] = "",
                ["emails"] = "",
                ["sub"] = ""
            });

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(string.Empty, _taskContext.AccessKey);
            Assert.Null(_taskContext.AccessToken); // Empty token means no API auth
            Assert.Equal(string.Empty, _taskContext.Email);
            Assert.Equal(0, _taskContext.UserId);
        }

        [Fact]
        public async Task InvokeAsync_HandlesWhitespaceOnlyClaims()
        {
            // Arrange
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"] = "   ",
                ["http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor"] = "   ",
                ["emails"] = "   ",
                ["sub"] = "   "
            });

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("   ", _taskContext.AccessKey);
            Assert.Equal("   ", _taskContext.AccessToken); // Whitespace passes IsNullOrEmpty check, so API auth succeeds
            Assert.Null(_taskContext.Email); // Email not set because API auth succeeded, no fallback to user auth
            Assert.Equal(0, _taskContext.UserId); // UserId not set because API auth succeeded
        }

        [Fact]
        public async Task InvokeAsync_PrefersSubOverNameIdentifier()
        {
            // Arrange
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["emails"] = "<EMAIL>",
                ["sub"] = "sub-provider",
                ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier"] = "nameidentifier-provider"
            });
            var user = new Users { UserId = 789, ProviderId = "sub-provider" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(789, _taskContext.UserId);
            _userRepositoryMock.Verify(r => r.GetByIdAsync<Users>(
                It.IsAny<string>(),
                It.Is<object>(p => p.GetType().GetProperty("ProviderId")!.GetValue(p)!.ToString() == "sub-provider")),
                Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_HandlesDuplicateClaims()
        {
            // Arrange
            var context = CreateHttpContext();
            var claimsList = new List<Claim>
            {
                new Claim("emails", "<EMAIL>"),
                new Claim("emails", "<EMAIL>"), // Duplicate claim type
                new Claim("sub", "provider123")
            };
            var identity = new ClaimsIdentity(claimsList, "test");
            context.User = new ClaimsPrincipal(identity);

            var user = new Users { UserId = 999, ProviderId = "provider123" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act & Assert
            // The middleware should handle duplicate claims gracefully
            // Since ToDictionary throws on duplicates, this tests the actual behavior
            await Assert.ThrowsAsync<ArgumentException>(() =>
                _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object));
        }

        [Theory]
        [InlineData("GET")]
        [InlineData("POST")]
        [InlineData("PUT")]
        [InlineData("DELETE")]
        [InlineData("PATCH")]
        [InlineData("HEAD")]
        [InlineData("OPTIONS")]
        public async Task InvokeAsync_WorksWithAllHttpMethods(string method)
        {
            // Arrange
            var context = CreateHttpContextWithApiKeyClaims("test-key", "test-token");
            context.Request.Method = method;

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("test-key", _taskContext.AccessKey);
            Assert.Equal("test-token", _taskContext.AccessToken);
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_HandlesLongClaimValues()
        {
            // Arrange
            var longValue = new string('a', 1000);
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name"] = longValue,
                ["http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor"] = longValue,
                ["emails"] = longValue,
                ["sub"] = longValue
            });

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(longValue, _taskContext.AccessKey);
            Assert.Equal(longValue, _taskContext.AccessToken);
        }

        [Fact]
        public async Task InvokeAsync_HandlesSpecialCharactersInClaims()
        {
            // Arrange
            var specialChars = "!@#$%^&*()_+-=[]{}|;':\",./<>?`~";
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["emails"] = $"user{specialChars}@example.com",
                ["sub"] = $"provider{specialChars}123"
            });
            var user = new Users { UserId = 555, ProviderId = $"provider{specialChars}123" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal($"user{specialChars}@example.com", _taskContext.Email);
            Assert.Equal(555, _taskContext.UserId);
        }

        [Fact]
        public async Task InvokeAsync_HandlesUnicodeCharactersInClaims()
        {
            // Arrange
            var unicodeChars = "测试用户@例子.com";
            var context = CreateHttpContextWithClaims(new Dictionary<string, string>
            {
                ["emails"] = unicodeChars,
                ["sub"] = "unicode-provider-测试"
            });
            var user = new Users { UserId = 777, ProviderId = "unicode-provider-测试" };
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal(unicodeChars, _taskContext.Email);
            Assert.Equal(777, _taskContext.UserId);
        }

        [Fact]
        public async Task InvokeAsync_MaintainsTaskContextState_AcrossMultipleCalls()
        {
            // Arrange
            var context1 = CreateHttpContextWithApiKeyClaims("key1", "token1");
            var context2 = CreateHttpContextWithApiKeyClaims("key2", "token2");
            var taskContext1 = new TaskContext();
            var taskContext2 = new TaskContext();

            // Act
            await _middleware.InvokeAsync(context1, taskContext1, _userRepositoryMock.Object);
            await _middleware.InvokeAsync(context2, taskContext2, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("key1", taskContext1.AccessKey);
            Assert.Equal("token1", taskContext1.AccessToken);
            Assert.Equal("key2", taskContext2.AccessKey);
            Assert.Equal("token2", taskContext2.AccessToken);
        }

        [Fact]
        public async Task InvokeAsync_HandlesRepositoryReturningUserWithNullProperties()
        {
            // Arrange
            var context = CreateHttpContextWithUserClaims("<EMAIL>", "provider123");
            var user = new Users { UserId = 0, ProviderId = null }; // User with null/default properties
            _userRepositoryMock.Setup(r => r.GetByIdAsync<Users>(It.IsAny<string>(), It.IsAny<object>()))
                .ReturnsAsync(user);

            // Act
            await _middleware.InvokeAsync(context, _taskContext, _userRepositoryMock.Object);

            // Assert
            Assert.Equal("<EMAIL>", _taskContext.Email);
            Assert.Equal(0, _taskContext.UserId); // Should use the user's UserId even if it's 0
        }
    }
}
