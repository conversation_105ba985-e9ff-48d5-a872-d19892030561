﻿namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// User business model
    /// </summary>
    public class UserModel
    {
        /// <summary>
        /// userid
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// ProviderId
        /// </summary>
        public string? ProviderId { get; set; }

        /// <summary>
        /// DisplayName
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// ProviderTypeId
        /// </summary>
        public int ProviderTypeId { get; set; }
        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }
    }
}
