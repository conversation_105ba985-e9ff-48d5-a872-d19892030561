﻿namespace MRI.OTA.Common.Models
{
    public class AcceptInvitationModel
    {
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// UserId
        /// </summary>
        public int? UserId { get; set; }

        /// <summary>
        /// ProviderId
        /// </summary>
        public string? ProviderId { get; set; }

        /// <summary>
        /// ProviderType
        /// </summary>
        public string? ProviderType { get; set; }

        /// <summary>
        /// InviteCode
        /// </summary>
        public string? InviteCode { get; set; }
    }
}
