name: apac-shared-platform-mri-ota-api

on:
  workflow_dispatch:
    inputs:
      deploy:
        description: 'Deploy to Azure after build?'
        required: false
        default: 'false'
      environment:
        description: 'Target environment (e.g., dev, prod)'
        required: false
        default: 'dev'
      imageTagOverride:
        description: 'Optional image tag override'
        required: false

  pull_request:
    branches:
      - develop
      - 'feature/**'
  push:
    branches:
      - develop

env:
  IMAGE_NAME: 'apacsharedplatform/mriota/api'

jobs:
  test-and-build:
    name: 'Run Unit Tests and Build'
    runs-on: ubuntu-latest
    if: ${{ github.event_name == 'pull_request' || github.event_name == 'push' || github.event_name == 'workflow_dispatch' }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      - name: Restore dependencies
        run: dotnet restore MRI.OTA.Platform/MRI.OTA.Tests/MRI.OTA.UnitTestCases.csproj

      - name: Build project
        run: dotnet build MRI.OTA.Platform/MRI.OTA.Tests/MRI.OTA.UnitTestCases.csproj --no-restore

      - name: Run Unit Tests with Coverage
        run: dotnet test MRI.OTA.Platform/MRI.OTA.Tests/MRI.OTA.UnitTestCases.csproj --configuration Release --collect:"XPlat Code Coverage" --logger "console;verbosity=detailed" --blame-hang-timeout 60s -- DataCollectionRunSettings.DataCollectors.DataCollector.Configuration.ExcludeByFile="**/MRI.OTA.Platform/MRI.OTA.Application/Models/**;**/MRI.OTA.Platform/MRI.OTA.DBCore/Entities/**;**/MRI.OTA.Platform/MRI.OTA.Entities/Entities/**;**/MRI.OTA.Platform/MRI.OTA.Common/Models/**;"

      - name: Install ReportGenerator
        run: dotnet tool install -g dotnet-reportgenerator-globaltool

      - name: Generate Coverage Report
        run: reportgenerator -reports:"**/coverage.cobertura.xml" -targetdir:"coveragereport" -reporttypes:Html -classfilters:"-MRI.OTA.Application.Models.*;-MRI.OTA.Core.Entities.*;-MRI.OTA.Entities.*;-MRI.OTA.Common.Models.*;-MRI.OTA.DBCore.Entities.*;"

      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coveragereport

      - name: Print Coverage Summary
        run: |
          echo "Coverage Summary:"
          cat coveragereport/Summary.txt || echo "Summary file not found"

      - name: Build Docker Image (local build, no push)
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile
          push: false
          tags: temp-image:latest

  push-and-deploy:
    name: 'Push to ACR and Deploy'
    runs-on: ubuntu-latest
    needs: test-and-build
    if: >
      github.event_name == 'push' && github.ref == 'refs/heads/develop' ||
      github.event_name == 'workflow_dispatch' && github.event.inputs.deploy == 'true'

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Replace placeholders in appsettings files
        shell: bash
        run: |
          for FILE in MRI.OTA.Platform/MRI.OTA.API/appsettings.json MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json; do
            sed -i "s|#{ota_db_connection_string}|${{ secrets.OTA_DB_CONNECTION_STRING }}|g" "$FILE"
            sed -i "s|#{ota_tenant_id}|${{ secrets.OTA_TENANT_ID }}|g" "$FILE"
            sed -i "s|#{ota_tenant_name}|${{ secrets.OTA_TENANT_NAME }}|g" "$FILE"
            sed -i "s|#{ota_client_id}|${{ secrets.OTA_CLIENT_ID }}|g" "$FILE"
            escaped_secret=$(printf '%s\n' "${{ secrets.OTA_CLIENT_SECRET }}" | sed 's/[&/]/\\&/g')
            sed -i "s|#{ota_client_secret}|$escaped_secret|g" "$FILE"
            sed -i "s|#{ota_b2c_instance}|${{ secrets.OTA_B2C_INSTANCE }}|g" "$FILE"
            sed -i "s|#{ota_b2c_domain}|${{ secrets.OTA_B2C_DOMAIN }}|g" "$FILE"
            sed -i "s|#{ota_signup_signin_policyid}|${{ secrets.OTA_SIGNUP_SIGNIN_POLICYID }}|g" "$FILE"
            sed -i "s|#{ota_api_allowed_origins}|${{ secrets.OTA_API_ALLOWED_ORIGINS }}|g" "$FILE"
            sed -i "s|#{ota_app_insight_connection_string}|${{ secrets.OTA_APP_INSIGHT_CONNECTION_STRING }}|g" "$FILE"
            sed -i "s|#{ota_storage_connection_string}|${{ secrets.ota_storage_connection_string }}|g" "$FILE"
            sed -i "s|#{ota_storage_container_name}|${{ secrets.ota_storage_container_name }}|g" "$FILE"
            sed -i "s|#{ota_azure_communication_connection_string}|${{ secrets.ota_azure_communication_connection_string }}|g" "$FILE"
            sed -i "s|#{ota_azure_communication_from_email}|${{ secrets.ota_azure_communication_from_email }}|g" "$FILE"
            sed -i "s|#{ota_redis_connection_string}|${{ secrets.ota_redis_connection_string }}|g" "$FILE"
            sed -i "s|#{ota_redis_default_expiration_minutes}|${{ secrets.ota_redis_default_expiration_minutes }}|g" "$FILE"
            sed -i "s|#{ota_base_url}|${{ secrets.ota_base_url }}|g" "$FILE"
            sed -i "s|#{ota_default_image_url}|${{ secrets.ota_default_image_url }}|g" "$FILE"
            echo "✅ Secrets replaced in $FILE"
          done

      - name: Docker Login to Azure
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_ENDPOINT }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile
          push: true
          tags: |
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:latest
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ github.event.inputs.imageTagOverride || format('revision-{0}', github.run_id) }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Deploy to Azure Container App
        run: |
          IMAGE="${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ github.event.inputs.imageTagOverride || format('revision-{0}', github.run_id) }}"
          echo "🚀 Deploying image: $IMAGE"

          az containerapp update \
            --name "${{ secrets.CONTAINER_APP_NAME }}" \
            --resource-group "${{ secrets.RESOURCE_GROUP }}" \
            --image "$IMAGE"