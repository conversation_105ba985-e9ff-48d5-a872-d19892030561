name: Deploy container image to Azure Function App

description: Deploys the Docker image to a Function App and updates app settings

inputs:
  image-tag:
    description: 'Docker image tag to deploy'
    required: true
  acr-endpoint:
    description: 'Azure Container Registry endpoint'
    required: true
  image-name:
    description: 'Docker image name'
    required: true

runs:
  using: "composite"
  steps:
    - name: Deploy Function App container
      shell: bash
      run: |
        set -e

        IMAGE="${{ inputs.acr-endpoint }}/${{ inputs.image-name }}:${{ inputs.image-tag }}"
        echo "📦 Deploying image: $IMAGE"

        # Debug logs for environment variables
        echo "::notice:: ENVIRONMENT=$ENVIRONMENT"
        echo "::notice:: APP_NAME=$APP_NAME"
        echo "::notice:: APP_RG=$APP_RG"
        echo "::notice:: AzureWebJobsStorage=${AzureWebJobsStorage:0:10}..."  # Truncated for security
        echo "::notice:: FUNCTIONS_WORKER_RUNTIME=$FUNCTIONS_WORKER_RUNTIME"
        echo "::notice:: PROPERTY_DATA_TIMER=$PROPERTY_DATA_TIMER"
        echo "::notice:: AGENCY_DATA_TIMER=$AGENCY_DATA_TIMER"

        # Optional: Fail fast if required env vars are missing
        : "${APP_NAME:?APP_NAME is required}"
        : "${APP_RG:?APP_RG is required}"
        : "${AzureWebJobsStorage:?AzureWebJobsStorage is required}"
        : "${FUNCTIONS_WORKER_RUNTIME:?FUNCTIONS_WORKER_RUNTIME is required}"
        : "${PROPERTY_DATA_TIMER:?PROPERTY_DATA_TIMER is required}"
        : "${AGENCY_DATA_TIMER:?AGENCY_DATA_TIMER is required}"

        # Set container image for Function App
        az functionapp config container set \
          --name "$APP_NAME" \
          --resource-group "$APP_RG" \
          --image "$IMAGE" \
          --output none

        # Explicitly set the runtime to dotnet-isolated
        az functionapp config set \
          --name "$APP_NAME" \
          --resource-group "$APP_RG" \
          --linux-fx-version "DOCKER|$IMAGE" \
          --output none

        # Show what app settings will be set
        echo "::notice:: Setting app settings:"
        echo "::notice:: PROPERTY_DATA_TIMER will be set to: $PROPERTY_DATA_TIMER"
        echo "::notice:: AGENCY_DATA_TIMER will be set to: $AGENCY_DATA_TIMER"

        # Set environment-specific app settings
        az functionapp config appsettings set \
          --name "$APP_NAME" \
          --resource-group "$APP_RG" \
          --settings \
            AzureWebJobsStorage="$AzureWebJobsStorage" \
            FUNCTIONS_WORKER_RUNTIME="$FUNCTIONS_WORKER_RUNTIME" \
            PROPERTY_DATA_TIMER="$PROPERTY_DATA_TIMER" \
            AGENCY_DATA_TIMER="$AGENCY_DATA_TIMER" \
            BaseUrl="$BaseUrl" \
            WorkerTimeOut="$WorkerTimeOut" \
            TenantName="$TenantName" \
            ClientId="$ClientId" \
            ClientSecret="$CLIENT_SECRET" \
            SignUpSignInPolicyId="$SignUpSignInPolicyId" \
            ApplicationInsights__ConnectionString="$ApplicationInsights__ConnectionString" \
          --output none

        # Restart Function App to ensure environment variables are loaded
        echo "🔄 Restarting Function App to load new settings..."
        az functionapp restart \
          --name "$APP_NAME" \
          --resource-group "$APP_RG" \
          --output none

        # Wait a moment for the restart to complete
        echo "⏳ Waiting for Function App to restart..."
        sleep 30
