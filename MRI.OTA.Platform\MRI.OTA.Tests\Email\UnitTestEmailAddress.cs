using MRI.OTA.Email;
using Xunit;

namespace MRI.OTA.UnitTestCases.Email
{
    public class UnitTestEmailAddress
    {
        [Fact]
        public void Constructor_WithEmailOnly_SetsEmailAndEmptyName()
        {
            // Arrange
            var email = "<EMAIL>";

            // Act
            var emailAddress = new EmailAddress(email);

            // Assert
            Assert.Equal(email, emailAddress.Email);
            Assert.Equal(string.Empty, emailAddress.Name);
        }

        [Fact]
        public void Constructor_WithEmailAndName_SetsBothProperties()
        {
            // Arrange
            var email = "<EMAIL>";
            var name = "Test User";

            // Act
            var emailAddress = new EmailAddress(email, name);

            // Assert
            Assert.Equal(email, emailAddress.Email);
            Assert.Equal(name, emailAddress.Name);
        }

        [Fact]
        public void Constructor_WithEmailAndNullName_SetsEmailAndEmptyName()
        {
            // Arrange
            var email = "<EMAIL>";
            string? name = null;

            // Act
            var emailAddress = new EmailAddress(email, name);

            // Assert
            Assert.Equal(email, emailAddress.Email);
            Assert.Equal(string.Empty, emailAddress.Name);
        }

        [Fact]
        public void Email_Property_CanBeSet()
        {
            // Arrange
            var emailAddress = new EmailAddress("<EMAIL>");
            var newEmail = "<EMAIL>";

            // Act
            emailAddress.Email = newEmail;

            // Assert
            Assert.Equal(newEmail, emailAddress.Email);
        }

        [Fact]
        public void Name_Property_CanBeSet()
        {
            // Arrange
            var emailAddress = new EmailAddress("<EMAIL>", "Initial Name");
            var newName = "Updated Name";

            // Act
            emailAddress.Name = newName;

            // Assert
            Assert.Equal(newName, emailAddress.Name);
        }

        [Theory]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData("invalid-email")]
        [InlineData("@example.com")]
        [InlineData("test@")]
        public void Constructor_WithInvalidEmail_StillSetsEmail(string invalidEmail)
        {
            // Arrange & Act
            var emailAddress = new EmailAddress(invalidEmail);

            // Assert
            Assert.Equal(invalidEmail, emailAddress.Email);
            Assert.Equal(string.Empty, emailAddress.Name);
        }

        [Fact]
        public void Constructor_WithEmptyStringName_SetsNameToEmptyString()
        {
            // Arrange
            var email = "<EMAIL>";
            var name = "";

            // Act
            var emailAddress = new EmailAddress(email, name);

            // Assert
            Assert.Equal(email, emailAddress.Email);
            Assert.Equal(string.Empty, emailAddress.Name);
        }

        [Fact]
        public void Constructor_WithWhitespaceOnlyName_SetsNameToWhitespace()
        {
            // Arrange
            var email = "<EMAIL>";
            var name = "   ";

            // Act
            var emailAddress = new EmailAddress(email, name);

            // Assert
            Assert.Equal(email, emailAddress.Email);
            Assert.Equal(name, emailAddress.Name);
        }
    }
}
