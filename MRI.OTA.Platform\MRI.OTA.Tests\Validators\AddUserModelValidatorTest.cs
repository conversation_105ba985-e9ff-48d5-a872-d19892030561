﻿using MRI.OTA.Application.Models;
using MRI.OTA.Application.Validators;

namespace MRI.OTA.UnitTestCases.Validators
{
    public class AddUserModelValidatorTest
    {
        private readonly AddUserModelValidator _validator = new AddUserModelValidator();

        [Fact]
        public void Should_Have_Error_When_UserEmail_Is_Empty()
        {
            var model = new AddUserModel { UserEmail = "", DisplayName = "Valid Name", ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e => e.PropertyName == "UserEmail" && e.ErrorMessage == "User email is required");
        }

        [Fact]
        public void Should_Have_Error_When_UserEmail_Is_Invalid()
        {
            var model = new AddUserModel { UserEmail = "invalid-email", DisplayName = "Valid Name", ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e => e.PropertyName == "UserEmail" && e.ErrorMessage == "Please provide a valid email address");
        }

        [Fact]
        public void Should_Not_Have_Error_When_UserEmail_Is_Valid()
        {
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Valid Name", ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "UserEmail");
        }

        [Fact]
        public void Should_Have_Error_When_DisplayName_Is_Empty()
        {
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "", ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e => e.PropertyName == "DisplayName" && e.ErrorMessage == "Display name is required");
        }

        [Fact]
        public void Should_Have_Error_When_DisplayName_Too_Long()
        {
            var name = new string('a', 101);
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = name, ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e => e.PropertyName == "DisplayName" && e.ErrorMessage == "Display name cannot exceed 100 characters");
        }

        [Fact]
        public void Should_Have_Error_When_DisplayName_Has_Invalid_Characters()
        {
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Invalid123", ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e => e.PropertyName == "DisplayName" && e.ErrorMessage == "Display name can only contain letters, spaces, hyphens, apostrophes, and periods");
        }

        [Fact]
        public void Should_Not_Have_Error_When_DisplayName_Is_Valid()
        {
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "John O'Connor-Smith Jr.", ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "DisplayName");
        }

        [Fact]
        public void Should_Have_Error_When_ProviderId_Is_Empty()
        {
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Valid Name", ProviderId = "" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e => e.PropertyName == "ProviderId" && e.ErrorMessage == "Provider ID is required");
        }

        [Fact]
        public void Should_Have_Error_When_ProviderId_Too_Long()
        {
            var providerId = new string('a', 256);
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Valid Name", ProviderId = providerId };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e => e.PropertyName == "ProviderId" && e.ErrorMessage == "Provider ID cannot exceed 255 characters");
        }

        [Fact]
        public void Should_Not_Have_Error_When_ProviderId_Is_Valid()
        {
            var model = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Valid Name", ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "ProviderId");
        }
    }
}
