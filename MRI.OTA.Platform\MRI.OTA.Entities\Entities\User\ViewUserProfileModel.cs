﻿namespace MRI.OTA.Core.Entities
{
    public class ViewUserProfileModel
    {
        /// <summary>
        /// UserId
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// DisplayName
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// ProviderTypeId
        /// </summary>
        public int ProviderTypeId { get; set; }

        /// <summary>
        /// ProviderName
        /// </summary>
        public string ProviderName { get; set; }

        /// <summary>
        /// ProviderId
        /// </summary>
        public string? ProviderId { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// PropertyCount
        /// </summary>
        public int PropertyCount { get; set; }

        /// <summary>
        /// UserEmail
        /// </summary>
        public string? IdpAccessToken { get; set; }

        /// <summary>
        /// TermsAndConditions
        /// </summary>
        public bool? TermsAndConditions { get; set; }

        /// <summary>
        /// PreferredContactEmail
        /// </summary>
        public string? PreferredContactEmail { get; set; }

        /// <summary>
        /// PushNotificationEnabled
        /// </summary>
        public bool? PushNotificationEnabled { get; set; }

        /// <summary>
        /// EmailNotificationEnabled
        /// </summary>
        public bool? EmailNotificationEnabled { get; set; }
    }
}
