﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Application.Models;
using MRI.OTA.Application.Models.Integration;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Integration.Models;
using MRI.OTA.Integration.Services;
using Newtonsoft.Json;
using System.Data;
using System.Text.Json;
using static MRI.OTA.Common.Constants.Constants;

namespace MRI.OTA.Application.Services.Integration
{
    /// <summary>
    /// Batch processing configuration for sync operations
    /// </summary>
    public class BatchProcessingConfig
    {
        public int BatchSize { get; init; }
        public int DelayBetweenBatches { get; init; }
        public int DelayBetweenRequests { get; init; }
        public string DataSourceName { get; init; } = string.Empty;
        public int DataSourceId { get; init; }
        public DataSourceManifest Manifest { get; init; } = null!;
    }

    /// <summary>
    /// Service for integrating with PropertyTree external API
    /// </summary>
    public class PropertTreeService : IPropertTreeService
    {
        private readonly ILogger<PropertTreeService> _logger;
        private readonly IMapper _mapper;
        private readonly IPropertyRepository _propertyRepository;
        private readonly IUserRepository _userRepository;
        private readonly IProxyService _proxyService;
        private readonly TaskContext _taskContext;
        private readonly IAPITrackingService _apiTrackingService;
        private readonly IDataSourceRepository _dataSourceRepository;
        private readonly IConfiguration _configuration;
        private readonly IIntegrationRepository _integrationRepository;
        private readonly INotificationService _notificationService;
        private readonly IBatchOperationLogger _batchLogger;

        // Configuration constants
        private const int DefaultBatchSize = 50;
        private const int DelayBetweenRequests = 50;
        private const int DelayBetweenBatches = 100;

        public PropertTreeService(
            ILogger<PropertTreeService> logger, 
            IMapper mapper, 
            IPropertyRepository propertyRepository, 
            IUserRepository userRepository,
            IProxyService proxyService, 
            TaskContext taskContext, 
            IAPITrackingService apiTrackingService, 
            IDataSourceRepository dataSourceRepository, 
            IConfiguration configuration,
            IIntegrationRepository integrationRepository,
            INotificationService notificationService,
            IBatchOperationLogger batchLogger)
        {
            _logger = logger;
            _mapper = mapper;
            _propertyRepository = propertyRepository;
            _userRepository = userRepository;
            _proxyService = proxyService;
            _taskContext = taskContext;
            _dataSourceRepository = dataSourceRepository;
            _apiTrackingService = apiTrackingService;
            _configuration = configuration;
            _integrationRepository = integrationRepository;
            _notificationService = notificationService;
            _batchLogger = batchLogger;
        }

        #region Endpoint Management

        /// <summary>
        /// Get endpoint string based on IntegrationEndPointsType
        /// </summary>
        /// <param name="sectionType">The integration endpoint type</param>
        /// <returns>Endpoint string</returns>
        /// <exception cref="ArgumentException">Thrown when endpoint type is not supported</exception>
        private static string GetEndpointFromDictionary(IntegrationEndPointsType sectionType)
        {
            var endpointKey = sectionType switch
            {
                IntegrationEndPointsType.OwnerPropertyList => "Properties-Owner",
                IntegrationEndPointsType.TenantPropertyList => "Properties-Tenant",
                IntegrationEndPointsType.TenanciesTenantList => "TenanciesTenant",
                IntegrationEndPointsType.ManagementList => "Management",
                IntegrationEndPointsType.TenanciesOwnerList => "TenanciesOwner",
                IntegrationEndPointsType.MaintenanceList => "Maintenance",
                IntegrationEndPointsType.InspectionList => "Inspections",
                IntegrationEndPointsType.ComplianceList => "Compliance",
                IntegrationEndPointsType.FinancialList => "Financials",
                IntegrationEndPointsType.DocumentList => "Documents",
                IntegrationEndPointsType.AgencyList => "AgencyDetails",
                IntegrationEndPointsType.AgencyPartnerList => "AgencyPartners",
                _ => throw new ArgumentException($"Unknown IntegrationEndPointsType: {sectionType}")
            };

            if (!Constants.integrationEndpoints.TryGetValue(endpointKey, out var endpoint))
            {
                throw new InvalidOperationException($"Endpoint '{endpointKey}' not found in constants configuration");
            }

            return endpoint;
        }

        /// <summary>
        /// Validates if endpoint exists in constants
        /// </summary>
        /// <param name="sectionType">The integration endpoint type</param>
        /// <returns>True if endpoint exists, false otherwise</returns>
        private static bool IsEndpointSupported(IntegrationEndPointsType sectionType)
        {
            try
            {
                var endpoint = GetEndpointFromDictionary(sectionType);
                return !string.IsNullOrEmpty(endpoint);
            }
            catch (ArgumentException)
            {
                return false;
            }
        }

        #endregion

        #region Proxy Request Factory Methods

        public ProxyRequestModel CreateAssociatePortfolioProxyRequest(DataSourceManifest manifest, string portfolioId, string? providerId, string? providerType)
        {
            ArgumentNullException.ThrowIfNull(manifest);
            ArgumentException.ThrowIfNullOrEmpty(portfolioId);

            if (!Constants.integrationEndpoints.TryGetValue("AssociatePortfolio", out var endpoint))
            {
                throw new InvalidOperationException("AssociatePortfolio endpoint not found in constants configuration");
            }

            var url = $"{manifest.BaseUrl}{endpoint}";
            
            var associatePortfolioModel = new AssociatePortfolioModel
            {
                PortfolioId = portfolioId,
                UserId = providerId,
                ReferrerServiceId = providerType
            };
            
            return CreateProxyRequest(url, "POST", associatePortfolioModel);
        }

        public ProxyRequestModel CreateProxyRequest(DataSourceManifest manifest, IntegrationEndPointsType sectionType, string? modifiedSince = null, string[]? managementIds = null, string[]? tenancyIds = null, string[]? agencyIds = null)
        {
            ArgumentNullException.ThrowIfNull(manifest);
            
            var endpoint = GetEndpointFromDictionary(sectionType);
            var url = $"{manifest.BaseUrl}{endpoint}";
            var requestBody = CreateRequestBody(sectionType, modifiedSince, managementIds, tenancyIds, agencyIds);
            
            return CreateProxyRequest(url, "POST", requestBody);
        }

        public ProxyRequestModel CreateProxyRequest(DataSourceManifest manifest, IntegrationEndPointsType sectionType, string[] userId, string? modifiedSince = null)
        {
            ArgumentNullException.ThrowIfNull(manifest);
            ArgumentNullException.ThrowIfNull(userId);
            
            var endpoint = GetEndpointFromDictionary(sectionType);
            var url = $"{manifest.BaseUrl}{endpoint}";
            
            var ptRequest = new PropertyTreeRequest
            {
                ModifiedSince = modifiedSince,
                UserIds = userId
            };
            
            return CreateProxyRequest(url, "POST", ptRequest);
        }

        private ProxyRequestModel CreateDisConnectUserProxyRequest(DataSourceManifest manifest, string providerId)
        {
            ArgumentNullException.ThrowIfNull(manifest);
            ArgumentException.ThrowIfNullOrEmpty(providerId);

            if (!Constants.integrationEndpoints.TryGetValue("DisconnectUser", out var endpoint))
            {
                throw new InvalidOperationException("DisconnectUser endpoint not found in constants configuration");
            }

            var url = $"{manifest.BaseUrl}{endpoint}?userId={providerId}";
            return CreateProxyRequest(url, "DELETE", null);
        }

        private ProxyRequestModel CreateAgencyPartnerProxyRequest(DataSourceManifest manifest, string agencyId)
        {
            ArgumentNullException.ThrowIfNull(manifest);
            ArgumentException.ThrowIfNullOrEmpty(agencyId);
            
            var endpoint = GetEndpointFromDictionary(IntegrationEndPointsType.AgencyPartnerList);
            var url = $"{manifest.BaseUrl}{endpoint}/{agencyId}";
            return CreateProxyRequest(url, "GET", null);
        }

        /// <summary>
        /// Factory method to create proxy requests with consistent structure
        /// </summary>
        private static ProxyRequestModel CreateProxyRequest(string url, string method, object? body)
        {
            return new ProxyRequestModel
            {
                FullUrl = url,
                Method = method,
                RequestId = Guid.NewGuid().ToString(),
                Body = body
            };
        }

        /// <summary>
        /// Factory method to create request bodies based on section type
        /// </summary>
        private static object CreateRequestBody(IntegrationEndPointsType sectionType, string? modifiedSince, string[]? managementIds, string[]? tenancyIds, string[]? agencyIds)
        {
            return sectionType switch
            {
                IntegrationEndPointsType.MaintenanceList or 
                IntegrationEndPointsType.ManagementList or 
                IntegrationEndPointsType.FinancialList or 
                IntegrationEndPointsType.ComplianceList => new ManagementRequestModel
                {
                    ManagementIds = managementIds ?? Array.Empty<string>(),
                    ModifiedSince = modifiedSince
                },
                IntegrationEndPointsType.InspectionList or 
                IntegrationEndPointsType.TenanciesTenantList or 
                IntegrationEndPointsType.TenanciesOwnerList => new TenancyRequestModel
                {
                    TenancyIds = tenancyIds ?? Array.Empty<string>(),
                    ModifiedSince = modifiedSince
                },
                IntegrationEndPointsType.DocumentList => new DocumentRequestModel
                {
                    ManagementIds = managementIds ?? Array.Empty<string>(),
                    TenancyIds = tenancyIds ?? Array.Empty<string>(),
                    ModifiedSince = modifiedSince
                },
                IntegrationEndPointsType.AgencyList => new AgencyRequestModel
                {
                    AgencyIds = agencyIds ?? Array.Empty<string>()
                },
                _ => throw new ArgumentException($"Unknown IntegrationEndPointsType: {sectionType}")
            };
        }

        #endregion

        #region Response Processing

        /// <summary>
        /// Validates and extracts content from API response
        /// </summary>
        private static (bool isValid, string content) ValidateAndExtractResponse(IActionResult response, string operationName)
        {
            if (response is not ObjectResult objectResult)
            {
                return (false, string.Empty);
            }

            if (objectResult.StatusCode != 200)
            {
                return (false, string.Empty);
            }

            var content = objectResult.Value switch
            {
                JsonDocument jsonDoc => jsonDoc.RootElement.ToString(),
                string str => str,
                _ => objectResult.Value?.ToString() ?? string.Empty
            };

            return (true, content);
        }

        public async Task<(bool, List<UserProperties>)> ProcessGetPropertiesProxyResponse(IActionResult _propertyResponse, DataSource dataSource, AcceptInvitationModel acceptInvitation)
        {
            var addedProperties = new List<UserProperties>();

            var (isValid, responseContent) = ValidateAndExtractResponse(_propertyResponse, "ProcessGetPropertiesProxyResponse");
            if (!isValid)
            {
                _logger.LogError("Failed to accept invitation. Invalid response received.");
                return (false, addedProperties);
            }
            
            try
            {
                var propertResponse = JsonConvert.DeserializeObject<List<PropertyTreeInviteResponse>>(responseContent);

                if (propertResponse == null || !propertResponse.Any())
                {
                    _logger.LogWarning("No properties found in the response");
                    return (true, addedProperties);
                }

                _logger.LogInformation($"Successfully retrieved {propertResponse.Count} properties for user");

                // Map properties based on data source
                addedProperties = MapDataWithAutoProfile<PropertyTreeInviteResponse, UserProperties>(
                    propertResponse, dataSource.Name);
                    
                _logger.LogInformation($"Successfully mapped {addedProperties.Count} properties");

                // Check if properties already exist for the user
                var SRCManagementId = addedProperties
                        .Where(p => !string.IsNullOrEmpty(p.SRCManagementId))
                        .Select(p => p.SRCManagementId)
                        .Distinct()
                        .ToArray();
                var SRCTenancyId = addedProperties
                        .Where(p => !string.IsNullOrEmpty(p.SRCTenancyId))
                        .Select(p => p.SRCTenancyId)
                        .Distinct()
                        .ToArray();
                
                var existingProperties = await _propertyRepository.CheckExsitingProperties(
                    acceptInvitation.UserId.GetValueOrDefault(),
                    addedProperties
                        .Where(p => !string.IsNullOrEmpty(p.SRCAgencyId))
                        .Select(p => p.SRCAgencyId)
                        .Distinct()
                        .ToArray() ?? null,
                    addedProperties
                        .Where(p => !string.IsNullOrEmpty(p.SRCEntitytId))
                        .Select(p => p.SRCEntitytId)
                        .Distinct()
                        .ToArray() ?? null,
                    addedProperties
                        .Where(p => p.PropertyRelationshipId > 0)
                        .Select(p => p.PropertyRelationshipId)
                        .Distinct()
                        .ToArray() ?? null,
                    SRCManagementId.Any() ? SRCManagementId : null,
                    SRCTenancyId.Any() ? SRCTenancyId : null
                );
                if (existingProperties != null && existingProperties.Any())
                {
                    _logger.LogInformation($"Found {existingProperties.Count} existing properties for user");
                    // Filter out existing properties
                    addedProperties = addedProperties
                        .Where(p => !existingProperties.Any(ep =>
                            ep.UserId == acceptInvitation.UserId.GetValueOrDefault() &&
                            ep.SRCAgencyId == p.SRCAgencyId &&
                            ep.SRCEntitytId == p.SRCEntitytId &&
                            ep.PropertyRelationshipId == p.PropertyRelationshipId &&
                            (ep.SRCManagementId == p.SRCManagementId ||
                            ep.SRCTenancyId == p.SRCTenancyId)))
                        .ToList();
                    _logger.LogInformation($"Filtered down to {addedProperties.Count} new properties to add");
                }
                
                if (addedProperties != null && addedProperties.Any())
                {
                    foreach (var property in addedProperties)
                    {
                        property.DataSourceId = dataSource.DataSourceId;
                        property.UserId = acceptInvitation.UserId.GetValueOrDefault();
                        property.PropertyId = await _propertyRepository.AddProperty(property);
                    }
                }
                
                return (true, addedProperties!);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error parsing properties response");
                return (false, new List<UserProperties>());
            }
        }

        private string GetResponseContent(ObjectResult objectResult)
        {
            if (objectResult.Value is JsonDocument jsonDoc)
            {
                return jsonDoc.RootElement.ToString();
            }

            return objectResult.Value?.ToString() ?? string.Empty;
        }

        #endregion

        #region Data Mapping

        private List<TDestination> MapData<TSource, TDestination>(
            List<TSource> sourceData,
            string dataSourceName,
            string propertyTreeProfileName)
        {
            return dataSourceName switch
            {
                "Property tree" => _mapper.Map<List<TDestination>>(
                    sourceData,
                    opts => opts.Items["ProfileName"] = propertyTreeProfileName),
                _ => _mapper.Map<List<TDestination>>(sourceData)
            };
        }

        private List<TDestination> MapDataWithAutoProfile<TSource, TDestination>(
            List<TSource> sourceData,
            string dataSourceName)
        {
            var profileName = GetProfileNameForType<TDestination>();
            return MapData<TSource, TDestination>(sourceData, dataSourceName, profileName);
        }

        private static string GetProfileNameForType<TDestination>()
        {
            return typeof(TDestination).Name switch
            {
                nameof(UserProperties) => "PropertyTreeUserPropertiesMappingProfile",
                nameof(TenanciesTenant) => "TenanciesTenantMappingProfile",
                nameof(TenanciesOwner) => "TenanciesOwnerMappingProfile",
                nameof(MaintenanceDetail) => "MaintenanceDetailMappingProfile",
                nameof(InspectionDetail) => "InspectionDetailMappingProfile",
                nameof(ComplianceDetail) => "ComplianceDetailMappingProfile",
                nameof(FinancialDetail) => "FinancialDetailMappingProfile",
                nameof(DocumentDetail) => "DocumentDetailMappingProfile",
                nameof(PropertyManagerInformation) => "ManagementMappingProfile",
                _ => throw new ArgumentException($"No profile mapping defined for type {typeof(TDestination).Name}")
            };
        }

        #endregion

        #region Validation

        /// <summary>
        /// Validates data and separates valid from invalid entries
        /// </summary>
        private static (List<T> validData, List<T> invalidData) ValidateData<T>(List<T> data)
        {
            var validData = new List<T>();
            var invalidData = new List<T>();

            foreach (var item in data)
            {
                if (IsValidData(item))
                {
                    validData.Add(item);
                }
                else
                {
                    invalidData.Add(item);
                }
            }

            return (validData, invalidData);
        }

        /// <summary>
        /// Validates individual data items based on their type
        /// </summary>
        private static bool IsValidData<T>(T item)
        {
            return item switch
            {
                MaintenanceDetail detail => (!string.IsNullOrEmpty(detail.SRCManagementId) || !string.IsNullOrEmpty(detail.SRCTenancyId)) && 
                                          !string.IsNullOrEmpty(detail.SRCPropertyId) && 
                                          !string.IsNullOrEmpty(detail.SRCJobId),
                ComplianceDetail detail => !string.IsNullOrEmpty(detail.SRCManagementId) && 
                                         !string.IsNullOrEmpty(detail.SRCPropertyId) && 
                                         !string.IsNullOrEmpty(detail.SRCComplianceId),
                TenanciesTenant detail => !string.IsNullOrEmpty(detail.SRCManagementId) && 
                                        !string.IsNullOrEmpty(detail.SRCPropertyId) && 
                                        !string.IsNullOrEmpty(detail.SRCTenancyId) && 
                                        !string.IsNullOrEmpty(detail.TenancyName),
                TenanciesOwner detail => !string.IsNullOrEmpty(detail.SRCManagementId) && 
                                       !string.IsNullOrEmpty(detail.SRCPropertyId) && 
                                       !string.IsNullOrEmpty(detail.SRCTenancyId) && 
                                       !string.IsNullOrEmpty(detail.TenancyName),
                InspectionDetail detail => !string.IsNullOrEmpty(detail.SRCInspectionId) && 
                                         !string.IsNullOrEmpty(detail.SRCPropertyId) &&
                                         (!string.IsNullOrEmpty(detail.SRCManagementId) || !string.IsNullOrEmpty(detail.SRCTenancyId)),
                PropertyManagerInformation detail => !string.IsNullOrEmpty(detail.SRCManagementId) && 
                                                   !string.IsNullOrEmpty(detail.SRCPropertyId) && 
                                                   !string.IsNullOrEmpty(detail.SRCAgencyId),
                FinancialDetail detail => !string.IsNullOrEmpty(detail.SRCManagementId) && 
                                        !string.IsNullOrEmpty(detail.SRCPropertyId),
                UserProperties property => !string.IsNullOrEmpty(property.SRCEntitytId) &&
                                         (!string.IsNullOrEmpty(property.SRCManagementId) || !string.IsNullOrEmpty(property.SRCTenancyId)) && 
                                         !string.IsNullOrEmpty(property.SRCAgencyId),
                AgencyDetails agency => !string.IsNullOrEmpty(agency.MriId) && !string.IsNullOrEmpty(agency.AgencyId),
                AgencyPartners partner => !string.IsNullOrEmpty(partner.AgencyId),
                DocumentDetail doc => !string.IsNullOrEmpty(doc.SRCDocumentId) && 
                                    !string.IsNullOrEmpty(doc.DocumentName) && 
                                    !string.IsNullOrEmpty(doc.DocumentLink),
                _ => true // Default to valid for unknown types
            };
        }

        /// <summary>
        /// Logs invalid data for tracking purposes
        /// </summary>
        private async Task LogInvalidData<T>(List<T> invalidData, APIDetail apiDetail, int batchIndex)
        {
            if (invalidData?.Any() == true)
            {
                await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)apiDetail,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = $"Data Sync Process Error: Invalid Data, Batch Index {batchIndex}",
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(invalidData)
                });
            }
        }

        #endregion

        #region Batch Processing Helpers

        /// <summary>
        /// Creates batch processing configuration from data source
        /// </summary>
        private BatchProcessingConfig CreateBatchConfig(DataSource dataSource)
        {
            var batchSize = _configuration.GetValue<int>("ApplicationOption:BatchSize", DefaultBatchSize);
            
            return new BatchProcessingConfig
            {
                BatchSize = batchSize,
                DelayBetweenBatches = DelayBetweenBatches,
                DelayBetweenRequests = DelayBetweenRequests,
                DataSourceName = dataSource.Name!,
                DataSourceId = dataSource.DataSourceId,
                Manifest = DataSourceManifest.FromJson(dataSource.ManifestJson)
            };
        }

        /// <summary>
        /// Executes a batch operation with consistent error handling and logging
        /// </summary>
        private async Task<bool> ExecuteBatchOperation(
            Func<BatchProcessingConfig, Task<bool>> operation,
            string operationName)
        {
            // Create batch operation context for Application Insights tracking
            var batchContext = _batchLogger.CreateBatchOperationContext(operationName);
            
            try
            {
                var dataSources = await _dataSourceRepository.GetAllDataSource();
                var supportedDataSources = dataSources.Where(ds => Constants.DataSourceNames.Contains(ds.Name)).ToList();

                if (!supportedDataSources.Any())
                {
                    _logger.LogWarning("No supported data sources found for {OperationName}. OperationId: {OperationId}", 
                        operationName, batchContext.OperationId);
                    _batchLogger.LogBatchOperationComplete(batchContext, true, 0, 0);
                    return true;
                }

                var tasks = supportedDataSources.Select(async dataSource =>
                {
                    var config = CreateBatchConfig(dataSource);
                    
                    // Log start of processing for this data source
                    _batchLogger.LogBatchProcessingStart(batchContext, dataSource.Name, 1);
                    
                    try
                    {
                        var result = await operation(config);
                        
                        // Log data source completion
                        _batchLogger.LogDataSourceMetrics(batchContext, dataSource.Name, 0, result ? 1 : 0, result ? 0 : 1);
                        
                        return result;
                    }
                    catch (Exception ex)
                    {
                        _batchLogger.LogBatchError(batchContext, 0, ex, $"Error processing data source: {dataSource.Name}");
                        return false;
                    }
                }).ToArray();

                var results = await Task.WhenAll(tasks);
                var success = results.All(r => r);
                
                var successfulDataSources = results.Count(r => r);
                var failedDataSources = results.Count(r => !r);

                // Log overall operation completion with detailed metrics
                _batchLogger.LogBatchOperationComplete(batchContext, success, 0, supportedDataSources.Count);

                return success;
            }
            catch (Exception ex)
            {
                _batchLogger.LogBatchError(batchContext, 0, ex, $"Critical error in {operationName}");
                _batchLogger.LogBatchOperationComplete(batchContext, false);
                return false;
            }
        }

        #endregion

        #region Main Interface Methods

        public async Task<int> ProcessGetPropertiesOtherDataResponse(PropertyTreeResponseBundle responseBundle, DataSource dataSource, List<UserProperties> userPropertiesList, string InviteCode = null!)
        {
            int apiStep = 0;
            try
            {
                var tenanciesTenantList = new List<TenanciesTenant>();
                var tenanciesOwnerList = new List<TenanciesOwner>();
                var maintenanceList = new List<MaintenanceDetail>();
                var inspectionList = new List<InspectionDetail>();
                var complianceList = new List<ComplianceDetail>();
                var financialList = new List<FinancialDetail>();
                var documentList = new List<DocumentDetail>();

                // Parse response
                try
                {
                    apiStep = (int)Constants.APIDetail.GetManagement;
                    string responseContentOwnership = GetResponseContent((ObjectResult)responseBundle.OwnershipResponse);
                    var managementResponse = JsonConvert.DeserializeObject<List<ManagementResponse>>(responseContentOwnership);

                    apiStep = (int)Constants.APIDetail.GetTenanciesOwner;
                    string responseContentTenanciesOwner = GetResponseContent((ObjectResult)responseBundle.TenanciesOwnerResponse);
                    var tenanciesOwnerResponse = JsonConvert.DeserializeObject<List<TenanciesOwnerResponse>>(responseContentTenanciesOwner);
                    tenanciesOwnerList = MapDataWithAutoProfile<TenanciesOwnerResponse, TenanciesOwner>(
                        tenanciesOwnerResponse, dataSource.Name);
                    _logger.LogInformation($"Successfully mapped {tenanciesOwnerList.Count} tenancies-Owner");

                    // Add Ownership data object into tenanciesOwner
                    if (tenanciesOwnerResponse != null && managementResponse != null)
                    {
                        foreach (var owner in tenanciesOwnerResponse)
                        {
                            var _dataOwnership = managementResponse.Any() ? managementResponse.FirstOrDefault(x => x.PropertyId == owner.PropertyId && x.ManagementId == owner.ManagementId) : null;
                            owner.ownershipResponse = _dataOwnership;
                        }
                    }
                }
                catch (Exception ex)
                {
                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = InviteCode,
                        UserId = userPropertiesList[0].UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message
                    });
                    _logger.LogError($"APIStep {apiStep} Error parsing response for : {ex.Message}");
                }

                try
                {
                    apiStep = (int)Constants.APIDetail.GetTenanciesTenant;
                    string responseContentTenanciesTenant = GetResponseContent((ObjectResult)responseBundle.TenanciesTenantResponse);
                    var tenanciesTenantResponse = JsonConvert.DeserializeObject<List<TenanciesTenantResponse>>(responseContentTenanciesTenant);
                    tenanciesTenantList = MapDataWithAutoProfile<TenanciesTenantResponse, TenanciesTenant>(
                       tenanciesTenantResponse, dataSource.Name);
                    _logger.LogInformation($"Successfully mapped {tenanciesTenantList.Count} tenancies-tenant");
                }
                catch (Exception ex)
                {
                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = InviteCode,
                        UserId = userPropertiesList[0].UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message
                    });
                    _logger.LogError($"APIStep {apiStep} Error parsing response for : {ex.Message}");
                }

                try
                {
                    apiStep = (int)Constants.APIDetail.GetMaintenance;
                    string responseContentMaintenance = GetResponseContent((ObjectResult)responseBundle.MaintenanceResponse);
                    var maintenanceResponse = JsonConvert.DeserializeObject<List<MaintenanceDetailResponse>>(responseContentMaintenance);
                    maintenanceList = MapDataWithAutoProfile<MaintenanceDetailResponse, MaintenanceDetail>(
                        maintenanceResponse, dataSource.Name);
                    _logger.LogInformation($"Successfully mapped {maintenanceList.Count} maintenance");
                }
                catch (Exception ex)
                {
                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = InviteCode,
                        UserId = userPropertiesList[0].UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message
                    });
                    _logger.LogError($"APIStep {apiStep} Error parsing response for : {ex.Message}");
                }


                try
                {
                    apiStep = (int)Constants.APIDetail.GetInspections;
                    string responseContentInspection = GetResponseContent((ObjectResult)responseBundle.InspectionResponse);
                    var inspectionResponse = JsonConvert.DeserializeObject<List<InspectionDetailResponse>>(responseContentInspection);
                    inspectionList = MapDataWithAutoProfile<InspectionDetailResponse, InspectionDetail>(
                        inspectionResponse, dataSource.Name);
                    _logger.LogInformation($"Successfully mapped {inspectionList.Count} inspection");
                }
                catch (Exception ex)
                {
                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = InviteCode,
                        UserId = userPropertiesList[0].UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message
                    });
                    _logger.LogError($"APIStep {apiStep} Error parsing response for : {ex.Message}");
                }

                try
                {
                    apiStep = (int)Constants.APIDetail.GetCompliance;
                    string responseContentCompliance = GetResponseContent((ObjectResult)responseBundle.ComplianceResponse);
                    var complianceResponse = JsonConvert.DeserializeObject<List<ComplianceDetailResponse>>(responseContentCompliance);
                    complianceList = MapDataWithAutoProfile<ComplianceDetailResponse, ComplianceDetail>(
                        complianceResponse, dataSource.Name);
                    _logger.LogInformation($"Successfully mapped {complianceList.Count} compliance");
                }
                catch (Exception ex)
                {

                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = InviteCode,
                        UserId = userPropertiesList[0].UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message
                    });
                    _logger.LogError($"APIStep {apiStep} Error parsing response for : {ex.Message}");
                }
                try
                {
                    apiStep = (int)Constants.APIDetail.GetFinancials;
                    string responseContentFinancial = GetResponseContent((ObjectResult)responseBundle.FinancialResponse);
                    var financialResponse = JsonConvert.DeserializeObject<List<FinancialDetailResponse>>(responseContentFinancial);
                    financialList = MapDataWithAutoProfile<FinancialDetailResponse, FinancialDetail>(
                        financialResponse, dataSource.Name);
                    _logger.LogInformation($"Successfully mapped {financialList.Count} financial");
                }
                catch (Exception ex)
                {

                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = InviteCode,
                        UserId = userPropertiesList[0].UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message
                    });
                    _logger.LogError($"APIStep {apiStep} Error parsing response for : {ex.Message}");
                }
                try
                {
                    apiStep = (int)Constants.APIDetail.GetDocuments;
                    string responseContentDocument = GetResponseContent((ObjectResult)responseBundle.DocumentResponse);
                    var documentResponse = JsonConvert.DeserializeObject<List<DocumentDetailResponse>>(responseContentDocument);
                    documentList = MapDocumentResponse(documentResponse);
                    _logger.LogInformation($"Successfully mapped {documentList.Count} Owner Document");
                }
                catch (Exception ex)
                {

                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = InviteCode,
                        UserId = userPropertiesList[0].UserId,
                        IsCompleted = false,
                        ErrorInfo = ex.Message
                    });
                    _logger.LogError($"APIStep {apiStep} Error parsing response for : {ex.Message}");
                }

                
                foreach (var property in userPropertiesList)
                {
                    if (property.PropertyRelationshipId is (int)PropertyRelationShipType.Owner or (int)PropertyRelationShipType.OwnerManaged
                            or (int)PropertyRelationShipType.OwnerOccupier or (int)PropertyRelationShipType.ProspectiveOwner)
                    {
                        property.TenanciesOwner = tenanciesOwnerList.Any() ? tenanciesOwnerList.FirstOrDefault(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCManagementId == property.SRCManagementId) : null;
                        var finData = financialList.Any() ? financialList.FirstOrDefault(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCManagementId == property.SRCManagementId) : null;
                        if (finData != null && property.TenanciesOwner != null)
                        {
                            property.TenanciesOwner.SRCAgencyId = finData.SRCAgencyId;
                            property.TenanciesOwner.SRCManagementId = finData.SRCManagementId;
                            property.TenanciesOwner.SRCPropertyId = finData.SRCPropertyId;
                            property.TenanciesOwner.OwnershipTotalAvailableBalance = finData.OwnershipTotalAvailableBalance;
                            property.TenanciesOwner.PropertyOutstandingFees = finData.PropertyOutstandingFees;
                            property.TenanciesOwner.PropertyOutstandingInvoices = finData.PropertyOutstandingInvoices;
                            property.TenanciesOwner.PropertyOverdueInvoices = finData.PropertyOverdueInvoices;
                            property.TenanciesOwner.LastPaymentAmount = finData.LastPaymentAmount;
                            property.TenanciesOwner.LastStatementDate = finData.LastStatementDate;
                        }
                        property.MaintenanceDetailList = maintenanceList.Any() ? maintenanceList.Where(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCManagementId == property.SRCManagementId).ToList() : null;
                        property.InspectionDetailList = inspectionList.Any() ? inspectionList.Where(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCManagementId == property.SRCManagementId).ToList() : null;
                        property.DocumentDetailList = documentList.Any() ? documentList.Where(x => x.SRCManagementId == property.SRCManagementId).ToList() : null;
                    }
                    else
                    {
                        property.TenanciesTenant = tenanciesTenantList.Any() ? tenanciesTenantList.FirstOrDefault(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCTenancyId == property.SRCTenancyId) : null;
                        property.MaintenanceDetailList = maintenanceList.Any() ? maintenanceList.Where(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCTenancyId == property.SRCTenancyId).ToList() : null;
                        property.InspectionDetailList = inspectionList.Any() ? inspectionList.Where(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCTenancyId == property.SRCTenancyId).ToList() : null;
                        property.DocumentDetailList = documentList.Any() ? documentList.Where(x => x.SRCTenancyId == property.SRCTenancyId).ToList() : null;
                    }

                    property.ComplianceDetailList = complianceList.Any() ? complianceList.Where(x => x.SRCPropertyId == property.SRCEntitytId && x.SRCManagementId == property.SRCManagementId).ToList() : null;
                    property.DataSourceId = dataSource.DataSourceId;
                    await _propertyRepository.AddPropertyOtherDetails(property);
                }

                return Constants.Success;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error parsing response: {ex.Message}");
                return Constants.Error;
            }
        }

        public async Task<bool> RemoveUserAccount(int userId, string providerId)
        {
            var dataSource = await _userRepository.GetDataSourcesByUserId(userId);
            foreach (var ds in dataSource.Where(ds => (Constants.DataSourceNames.Contains(ds.Name))))
            {
                var manifest = DataSourceManifest.FromJson(ds.ManifestJson);
                if (!Constants.integrationEndpoints.ContainsKey("DisconnectUser"))
                {
                    _logger.LogWarning("DisconnectUser endpoint not configured");
                    continue;
                }

                var proxyRequest = CreateDisConnectUserProxyRequest(manifest, providerId);
                var httpResponse = await _proxyService.ForwardRequestAsync(
                    proxyRequest,
                    $"AccessKey {manifest.ApiKey}", "AccessKey");

                if (httpResponse is not ObjectResult result)
                {
                    _logger.LogError("Failed to disconnect user from data source {DataSourceName}. Invalid response type.", ds.Name);
                    return false;
                }

                if (result.StatusCode == 200)
                {
                    _logger.LogInformation("Successfully received a success response.");
                }
                else
                {
                    _logger.LogError("Failed to disconnect user from data source {DataSourceName}. HTTP Status: {StatusCode}, Response: {ResponseValue}", ds.Name, result.StatusCode, result.Value);
                    return false;
                }
            }
            return await Task.FromResult(true);
        }

        #endregion

        #region Sync Methods

        public Task<bool> SyncPropertyData() => ExecuteBatchOperation(ProcessPropertyDataSync, "Property Data Sync");
        public Task<bool> SyncAgencyData() => ExecuteBatchOperation(ProcessAgencyDataSync, "Agency Data Sync");
        public Task<bool> SyncAgencyPartnerData() => ExecuteBatchOperation(ProcessAgencyPartnerDataSync, "Agency Partner Data Sync");
        public Task<bool> SyncDocumentData() => ExecuteBatchOperation(ProcessDocumentDataSync, "Document Data Sync");
        public Task<bool> SyncManagementData() => ExecuteBatchOperation(ProcessManagementDataSync, "Management Data Sync");
        public Task<bool> SyncComplianceData() => ExecuteBatchOperation(ProcessComplianceDataSync, "Compliance Data Sync");
        public Task<bool> SyncTenanciesTenantData() => ExecuteBatchOperation(ProcessTenanciesTenantDataSync, "Tenancies Tenant Data Sync");
        public Task<bool> SyncInspectionsData() => ExecuteBatchOperation(ProcessInspectionsDataSync, "Inspections Data Sync");
        public Task<bool> SyncMaintenanceData() => ExecuteBatchOperation(ProcessMaintenanceDataSync, "Maintenance Data Sync");
        public Task<bool> SyncFinancialsData() => ExecuteBatchOperation(ProcessFinancialsDataSync, "Financials Data Sync");
        public Task<bool> SyncTenanciesOwnerData() => ExecuteBatchOperation(ProcessTenanciesOwnerDataSync, "Tenancies Owner Data Sync");

        #endregion

        #region Document Processing

        private List<DocumentDetail> MapDocumentResponse(List<DocumentDetailResponse> documentResponse)
        {
            var documentList = new List<DocumentDetail>();
            if (documentResponse == null || !documentResponse.Any())
            {
                return documentList;
            }

            foreach (var doc in documentResponse)
            {
                if (doc.Added != null && doc.Added.Any())
                {
                    foreach (var newDoc in doc.Added)
                    {
                        if (newDoc.ManagementIds != null && newDoc.ManagementIds.Any())
                        {
                            foreach (var id in newDoc.ManagementIds)
                            {
                                DocumentDetail data = new DocumentDetail
                                {
                                    SRCManagementId = id,
                                    SRCTenancyId = null!,
                                    SRCDocumentId = doc.DocumentId,
                                    DocumentName = doc.DocumentName,
                                    DocumentLink = doc.DocumentLink,
                                    DocumentType = doc.DocumentType,
                                    MetaType = doc.Metadata.ReceiptType ?? null!,
                                    MetaNumber = doc.Metadata.InvoiceNumber ?? doc.Metadata.ReceiptNumber ?? null!,
                                    MetaDate = doc.Metadata.ReceiptDate ?? doc.Metadata.DueDate ?? doc.Metadata.IEGenerationDate ?? null,
                                    MetaPeriod = doc.Metadata.IEDateRange ?? doc.Metadata.StatementPeriod ?? null,
                                    MetaStatus = doc.Metadata.InvoiceStatus ?? null!,
                                    MetaAmount = doc.Metadata.InvoiceAmount ?? doc.Metadata.ReceiptAmount ?? null,
                                    MetaOwing = doc.Metadata.InvoiceOwing ?? null,
                                    MetaCurrency = doc.Metadata.Currency ?? doc.Metadata.ReceiptCurrency ?? null,
                                    SharedDate = doc.DateShared,
                                    LastUpdatedDate = doc.LastUpdated,
                                    isRemove = false,
                                };

                                documentList.Add(data);
                            }
                        }
                        if (newDoc.TenancyIds != null && newDoc.TenancyIds.Any())
                        {
                            foreach (var id in newDoc.TenancyIds)
                            {
                                DocumentDetail data = new DocumentDetail
                                {
                                    SRCManagementId = null!,
                                    SRCTenancyId = id,
                                    SRCDocumentId = doc.DocumentId,
                                    DocumentName = doc.DocumentName,
                                    DocumentLink = doc.DocumentLink,
                                    DocumentType = doc.DocumentType,
                                    MetaType = doc.Metadata.ReceiptType ?? null!,
                                    MetaNumber = doc.Metadata.InvoiceNumber ?? doc.Metadata.ReceiptNumber ?? null!,
                                    MetaDate = doc.Metadata.ReceiptDate ?? doc.Metadata.DueDate ?? doc.Metadata.IEGenerationDate ?? null,
                                    MetaPeriod = doc.Metadata.IEDateRange ?? doc.Metadata.StatementPeriod ?? null,
                                    MetaStatus = doc.Metadata.InvoiceStatus ?? null!,
                                    MetaAmount = doc.Metadata.InvoiceAmount ?? doc.Metadata.ReceiptAmount ?? null,
                                    MetaOwing = doc.Metadata.InvoiceOwing ?? null,
                                    MetaCurrency = doc.Metadata.Currency ?? doc.Metadata.ReceiptCurrency ?? null,
                                    SharedDate = doc.DateShared,
                                    LastUpdatedDate = doc.LastUpdated,
                                    isRemove = false,
                                };
                                documentList.Add(data);
                            }
                        }
                    }
                }
                if (doc.Removed != null && doc.Removed.Any())
                {
                    foreach (var removeDoc in doc.Removed)
                    {
                        if (removeDoc.ManagementIds != null && removeDoc.ManagementIds.Any())
                        {
                            foreach (var id in removeDoc.ManagementIds)
                            {
                                DocumentDetail data = new DocumentDetail
                                {
                                    SRCManagementId = id,
                                    SRCDocumentId = doc.DocumentId,
                                    MetaDate = doc.Metadata.ReceiptDate ?? doc.Metadata.DueDate ?? doc.Metadata.IEGenerationDate ?? null,
                                    SharedDate = doc.DateShared,
                                    LastUpdatedDate = doc.LastUpdated,
                                    isRemove = true,
                                };
                                documentList.Add(data);
                            }
                        }

                        if (removeDoc.TenancyIds != null && removeDoc.TenancyIds.Any())
                        {
                            foreach (var id in removeDoc.TenancyIds)
                            {
                                DocumentDetail data = new DocumentDetail
                                {
                                    SRCTenancyId = id,
                                    SRCDocumentId = doc.DocumentId,
                                    MetaDate = doc.Metadata.ReceiptDate ?? doc.Metadata.DueDate ?? doc.Metadata.IEGenerationDate ?? null,
                                    SharedDate = doc.DateShared,
                                    LastUpdatedDate = doc.LastUpdated,
                                    isRemove = true,
                                };
                                documentList.Add(data);
                            }
                        }
                    }
                }
            }
            return documentList;
        }

        #endregion

        #region Private Sync Implementation Methods

        private async Task<bool> ProcessPropertyDataSync(BatchProcessingConfig config)
        {
            // Create batch operation context for detailed logging
            var batchContext = _batchLogger.CreateBatchOperationContext(
                "Sync Property Data", 
                config.DataSourceName);

            // Declare variables outside try block to make them accessible in catch block
            int batchIndex = 0;
            int totalItemsProcessed = 0;

            try
            {
                if (!IsEndpointSupported(IntegrationEndPointsType.OwnerPropertyList) && !IsEndpointSupported(IntegrationEndPointsType.TenantPropertyList))
                {
                    _logger.LogWarning($"Property endpoint not supported for data source: {config.DataSourceName}");
                    return false;
                }

                var totalUserCount = await _userRepository.GetUserCount();
                if (totalUserCount == 0)
                {
                    _logger.LogWarning("No users found to sync property data.");
                    _batchLogger.LogBatchOperationComplete(batchContext, true, 0, 0);
                    return true;
                }

                var totalBatches = (int)Math.Ceiling((double)totalUserCount / config.BatchSize);
                _batchLogger.LogBatchProcessingStart(batchContext, config.DataSourceName, totalBatches);

                int skip = 0;
                bool hasMoreUsers = true;

                while (hasMoreUsers)
                {
                    batchIndex++;
                    var usersBatch = await _userRepository.GetUsersBatch(skip, config.BatchSize);

                    if (usersBatch == null || !usersBatch.Any())
                    {
                        hasMoreUsers = false;
                        break;
                    }

                    var userProviderIds = usersBatch.Where(u => !string.IsNullOrEmpty(u.ProviderId))
                                                   .Select(u => u.ProviderId)
                                                   .ToArray();

                    if (userProviderIds.Length == 0)
                    {
                        _logger.LogInformation($"Batch {batchIndex}: No valid provider IDs found, skipping batch");
                        skip += config.BatchSize;
                        continue;
                    }

                    // Log batch execution with specific provider IDs
                    _batchLogger.LogBatchExecutionWithIds(batchContext, batchIndex, userProviderIds, "ProviderId");
                    _logger.LogInformation($"Processing property data batch {batchIndex} with {userProviderIds.Length} users for data source: {config.DataSourceName}");

                    try
                    {
                        var proxyRequest = CreateProxyRequest(config.Manifest, IntegrationEndPointsType.OwnerPropertyList, userProviderIds, DateTime.UtcNow.ToString("o"));
                        var httpResponse = await _proxyService.ForwardRequestAsync(
                            proxyRequest,
                            $"AccessKey {config.Manifest.ApiKey}", "AccessKey");

                        proxyRequest = CreateProxyRequest(config.Manifest, IntegrationEndPointsType.TenantPropertyList, userProviderIds, DateTime.UtcNow.ToString("o"));
                        var httpResponseTenant = await _proxyService.ForwardRequestAsync(
                            proxyRequest,
                            $"AccessKey {config.Manifest.ApiKey}", "AccessKey");

                        var (isValid, responseContent) = ValidateAndExtractResponse(httpResponse, "ProcessPropertyDataSync");
                        var (isValidTenant, responseContentTenant) = ValidateAndExtractResponse(httpResponseTenant, "ProcessPropertyDataSync");
                        if (isValid || isValidTenant)
                        {
                            var propertResponse = JsonConvert.DeserializeObject<List<PropertyTreeInviteResponse>>(responseContent);
                            var addedProperties = MapDataWithAutoProfile<PropertyTreeInviteResponse, UserProperties>(
                                propertResponse, config.DataSourceName);

                            propertResponse = JsonConvert.DeserializeObject<List<PropertyTreeInviteResponse>>(responseContentTenant);
                            addedProperties.AddRange(MapDataWithAutoProfile<PropertyTreeInviteResponse, UserProperties>(propertResponse, config.DataSourceName));

                            var (validProperties, invalidProperties) = ValidateData(addedProperties);

                            foreach (var newProperty in validProperties)
                            {
                                var user = usersBatch.FirstOrDefault(u => u.ProviderId == newProperty.ProviderId);
                                if (user != null)
                                {
                                    newProperty.UserId = user.UserId;
                                    newProperty.DataSourceId = config.DataSourceId;
                                }
                            }

                            await LogInvalidData(invalidProperties, APIDetail.GetProperties, batchIndex);
                            validProperties = validProperties.Where(p => p.UserId > 0 && p.DataSourceId > 0).ToList();

                            if (validProperties.Any())
                            {
                                var bulkUpsertResult = await _propertyRepository.BulkUpsertUserProperties(validProperties, config.DataSourceId);
                                var rowsAffected = bulkUpsertResult.Count;
                                
                                // Log batch success with provider IDs
                                _batchLogger.LogBatchSuccessWithIds(batchContext, batchIndex, rowsAffected, userProviderIds, "ProviderId");
                                totalItemsProcessed += rowsAffected;
                                _logger.LogInformation($"Batch {batchIndex}: Successfully processed {rowsAffected} properties using bulk upsert");
                            }
                            else
                            {
                                _batchLogger.LogBatchSuccessWithIds(batchContext, batchIndex, 0, userProviderIds, "ProviderId");
                                _logger.LogInformation($"Batch {batchIndex}: No valid properties to process");
                            }
                        }
                        else
                        {
                            throw new Exception("Failed to get valid response from both owner and tenant property endpoints");
                        }
                    }
                    catch (Exception batchException)
                    {
                        // Log batch error with provider IDs
                        _batchLogger.LogBatchErrorWithIds(batchContext, batchIndex, batchException, userProviderIds, "ProviderId", "Failed processing property batch");
                        
                        // Re-throw to be caught by outer exception handler
                        throw;
                    }

                    skip += config.BatchSize;
                    if (usersBatch.Count < config.BatchSize)
                    {
                        hasMoreUsers = false;
                    }

                    await Task.Delay(config.DelayBetweenBatches);
                }

                _batchLogger.LogBatchOperationComplete(batchContext, true, totalItemsProcessed, batchIndex);
                return true;
            }
            catch (Exception ex)
            {
                _batchLogger.LogBatchOperationComplete(batchContext, false, totalItemsProcessed, batchIndex);
                _logger.LogError(ex, "An error occurred while syncing property data");
                return false;
            }
        }
        private async Task SendPushNotificationToUsers(List<SQLQueryMergeResult> data, string dataTypeName = null!)
        {
            try
            {
                var insertedRows = data.Where(r => r.MergeAction == "INSERT").ToList();
                var updatedRows = data.Where(r => r.MergeAction == "UPDATE").ToList();
                string[] srcIds = insertedRows.Where(u => !string.IsNullOrWhiteSpace(u.SRCPropertyId))
                                  .Select(u => u.SRCPropertyId!)
                                  .Distinct()
                                  .ToArray();
                if(dataTypeName == nameof(DataTypeName.inspections) || dataTypeName == nameof(DataTypeName.documents))
                {
                    srcIds = insertedRows.Where(u => !string.IsNullOrWhiteSpace(u.SRCId))
                                  .Select(u => u.SRCId!)
                                  .Distinct()
                                  .ToArray();
                }
                var notificationList = await _notificationService.GetNotificationDetailByCategory([GetNotificatioCategory(dataTypeName)]);
                var usersForNotificationList = await _notificationService.GetUsersForNotificationBySRCId(srcIds, dataTypeName);
                if (notificationList != null && notificationList.Any())
                {
                    foreach (var notification in notificationList)
                    {
                        NotificationRequestModel? notificationData = null;
                        if (notification.Category == GetNotificatioCategory(dataTypeName) && usersForNotificationList.Any())
                        {
                            foreach (var userDevice in usersForNotificationList)
                            {
                                notificationData = new NotificationRequestModel()
                                {
                                    Title = notification.Title,
                                    Body = notification.Body.Replace("[NICKNAME]", userDevice?.PropertyNickName).Replace("[DATE]", userDevice?.InspectionDate?.ToString("dd-MMMM-yyyy")).Replace("[DOCUMENTNAME]", userDevice.DocumentName),
                                    DeviceToken = userDevice.DeviceToken,
                                    UserId = userDevice.UserId,
                                    Path = notification.Path,
                                    NotificationId = notification.NotificationId
                                };

                                await _notificationService.SendPushNotificationAsync(notificationData);
                            }
                        }
                    }
                }
                
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while sending notification in sync propcess");
            }
           
        }
        private string GetNotificatioCategory(string dataTypeName)
        {
            return dataTypeName switch
            {
                nameof(DataTypeName.maintenance) => Constants.NewMaintenanceAdded,
                nameof(DataTypeName.inspections) => Constants.NewInspectionAdded,
                nameof(DataTypeName.documents) => Constants.DOCUMENTSHARED,
                _ => null
            };
        }

        private async Task<bool> ProcessAgencyDataSync(BatchProcessingConfig config)
        {
            // Create batch operation context for detailed logging
            var batchContext = _batchLogger.CreateBatchOperationContext(
                "Sync Agency Data", 
                config.DataSourceName);

            // Declare variables outside try block to make them accessible in catch block
            int batchIndex = 0;
            int totalItemsProcessed = 0;

            try
            {
                if (!IsEndpointSupported(IntegrationEndPointsType.AgencyList))
                {
                    _logger.LogWarning($"Agency endpoint not supported for data source: {config.DataSourceName}");
                    return false;
                }

                var totalAgencyCount = await _integrationRepository.GetUniqueAgenciesCount(config.DataSourceId);
                if (totalAgencyCount == 0)
                {
                    _logger.LogWarning($"No agencies found to sync for data source: {config.DataSourceName}");
                    _batchLogger.LogBatchOperationComplete(batchContext, true, 0, 0);
                    return true;
                }

                var totalBatches = (int)Math.Ceiling((double)totalAgencyCount / config.BatchSize);
                _batchLogger.LogBatchProcessingStart(batchContext, config.DataSourceName, totalBatches);
                _logger.LogInformation($"Processing {totalAgencyCount} agencies in batches of {config.BatchSize} for data source: {config.DataSourceName}");

                int skip = 0;
                bool hasMoreAgencies = true;

                while (hasMoreAgencies)
                {
                    batchIndex++;
                    var agenciesBatch = await _integrationRepository.GetUniqueAgencies(config.DataSourceId, config.BatchSize, skip);

                    if (agenciesBatch == null || !agenciesBatch.Any())
                    {
                        hasMoreAgencies = false;
                        break;
                    }

                    var agencyIds = agenciesBatch.Select(a => a.AgencyId).ToArray();
                    
                    // Log batch execution with specific agency IDs
                    _batchLogger.LogBatchExecutionWithIds(batchContext, batchIndex, agencyIds, "AgencyId");
                    _logger.LogInformation($"Processing agency data batch {batchIndex} with {agencyIds.Length} agencies for data source: {config.DataSourceName}");

                    try
                    {
                        var proxyRequest = CreateProxyRequest(config.Manifest, IntegrationEndPointsType.AgencyList, DateTime.UtcNow.ToString("o"), agencyIds: agencyIds);
                        var httpResponse = await _proxyService.ForwardRequestAsync(
                            proxyRequest,
                            $"AccessKey {config.Manifest.ApiKey}", "AccessKey");

                        var (isValid, responseContent) = ValidateAndExtractResponse(httpResponse, "ProcessAgencyDataSync");
                        if (isValid)
                        {
                            var agencyResponse = JsonConvert.DeserializeObject<List<AgencyDetails>>(responseContent);
                            if (agencyResponse != null && agencyResponse.Any())
                            {
                                foreach (var agency in agencyResponse)
                                {
                                    agency.DataSourceId = config.DataSourceId;
                                }

                                var (validData, invalidData) = ValidateData(agencyResponse);
                                await LogInvalidData(invalidData, APIDetail.GetAgencyDetails, batchIndex);

                                var bulkUpsertResult = await _integrationRepository.BulkUpsertAgencyDetails(validData, config.DataSourceId);
                                var rowsAffected = bulkUpsertResult.Count;
                                
                                // Log batch success with agency IDs
                                _batchLogger.LogBatchSuccessWithIds(batchContext, batchIndex, rowsAffected, agencyIds, "AgencyId");
                                totalItemsProcessed += agencyIds.Length;
                                _logger.LogInformation($"Batch {batchIndex}: Successfully processed {rowsAffected} agency details using bulk upsert");
                            }
                        }
                    }
                    catch (Exception batchException)
                    {
                        // Log batch error with agency IDs
                        _batchLogger.LogBatchErrorWithIds(batchContext, batchIndex, batchException, agencyIds, "AgencyId", "Failed processing agency batch");
                        
                        // Re-throw to be caught by outer exception handler
                        throw;
                    }

                    skip += config.BatchSize;
                    if (agenciesBatch.Count < config.BatchSize)
                    {
                        hasMoreAgencies = false;
                    }

                    await Task.Delay(config.DelayBetweenBatches);
                }

                _batchLogger.LogBatchOperationComplete(batchContext, true, totalItemsProcessed, batchIndex);
                return true;
            }
            catch (Exception ex)
            {
                _batchLogger.LogBatchOperationComplete(batchContext, false, totalItemsProcessed, batchIndex);
                _logger.LogError(ex, "An error occurred while syncing agency data");
                return false;
            }
        }

        private async Task<bool> ProcessManagementDataSync(BatchProcessingConfig config)
        {
            var syncConfig = new SyncConfig
            {
                SectionType = IntegrationEndPointsType.ManagementList,
                DataTypeName = "management",
                ColumnName = Constants.ManagementIdColumnName
            };
            return await SyncDataGeneric<ManagementResponse, PropertyManagerInformation>(syncConfig, config);
        }

        private async Task<bool> ProcessComplianceDataSync(BatchProcessingConfig config)
        {
            var syncConfig = new SyncConfig
            {
                SectionType = IntegrationEndPointsType.ComplianceList,
                DataTypeName = "compliance",
                ColumnName = Constants.ManagementIdColumnName
            };
            return await SyncDataGeneric<ComplianceDetailResponse, ComplianceDetail>(syncConfig, config);
        }

        private async Task<bool> ProcessTenanciesTenantDataSync(BatchProcessingConfig config)
        {
            var syncConfig = new SyncConfig
            {
                SectionType = IntegrationEndPointsType.TenanciesTenantList,
                DataTypeName = "tenancies tenant",
                ColumnName = Constants.TenancyIdColumnName,
                PropertyRelationshipFilter = Constants.PropertyRelationShipDic["Tenant"]
            };
            return await SyncDataGeneric<TenanciesTenantResponse, TenanciesTenant>(syncConfig, config);
        }

        private async Task<bool> ProcessInspectionsDataSync(BatchProcessingConfig config)
        {
            var syncConfig = new SyncConfig
            {
                SectionType = IntegrationEndPointsType.InspectionList,
                DataTypeName = "inspections",
                ColumnName = Constants.TenancyIdColumnName
            };
            return await SyncDataGeneric<InspectionDetailResponse, InspectionDetail>(syncConfig, config);
        }

        private async Task<bool> ProcessMaintenanceDataSync(BatchProcessingConfig config)
        {
            var syncConfig = new SyncConfig
            {
                SectionType = IntegrationEndPointsType.MaintenanceList,
                DataTypeName = "maintenance",
                ColumnName = Constants.ManagementIdColumnName
            };
            return await SyncDataGeneric<MaintenanceDetailResponse, MaintenanceDetail>(syncConfig, config);
        }

        private async Task<bool> ProcessFinancialsDataSync(BatchProcessingConfig config)
        {
            var syncConfig = new SyncConfig
            {
                SectionType = IntegrationEndPointsType.FinancialList,
                DataTypeName = "financials",
                ColumnName = Constants.ManagementIdColumnName
            };
            return await SyncDataGeneric<FinancialDetailResponse, FinancialDetail>(syncConfig, config);
        }

        private async Task<bool> ProcessTenanciesOwnerDataSync(BatchProcessingConfig config)
        {
            var syncConfig = new SyncConfig
            {
                SectionType = IntegrationEndPointsType.TenanciesOwnerList,
                DataTypeName = "tenancies owner",
                ColumnName = Constants.TenancyIdColumnName,
                PropertyRelationshipFilter = Constants.PropertyRelationShipDic["Owner"]
            };
            return await SyncDataGeneric<TenanciesOwnerResponse, TenanciesOwner>(syncConfig, config);
        }

        private async Task<bool> ProcessAgencyPartnerDataSync(BatchProcessingConfig config)
        {
            // Create batch operation context for detailed logging
            var batchContext = _batchLogger.CreateBatchOperationContext(
                "Sync Agency Partner Data", 
                config.DataSourceName);

            // Declare variables outside try block to make them accessible in catch block
            int batchIndex = 0;
            int totalItemsProcessed = 0;

            try
            {
                if (!IsEndpointSupported(IntegrationEndPointsType.AgencyPartnerList))
                {
                    _logger.LogWarning($"Agency partner endpoint not supported for data source: {config.DataSourceName}");
                    return false;
                }

                var totalAgencyCount = await _integrationRepository.GetUniqueAgenciesCount(config.DataSourceId);
                if (totalAgencyCount == 0)
                {
                    _logger.LogWarning($"No agencies found to sync agency partners for data source: {config.DataSourceName}");
                    _batchLogger.LogBatchOperationComplete(batchContext, true, 0, 0);
                    return true;
                }

                var totalBatches = (int)Math.Ceiling((double)totalAgencyCount / config.BatchSize);
                _batchLogger.LogBatchProcessingStart(batchContext, config.DataSourceName, totalBatches);
                _logger.LogInformation($"Processing {totalAgencyCount} agencies in batches of {config.BatchSize} for agency partner data from data source: {config.DataSourceName}");

                int skip = 0;
                bool hasMoreAgencies = true;
                var allAgencyPartners = new List<AgencyPartners>();

                while (hasMoreAgencies)
                {
                    batchIndex++;
                    var agenciesBatch = await _integrationRepository.GetUniqueAgencies(config.DataSourceId, config.BatchSize, skip);

                    if (agenciesBatch == null || !agenciesBatch.Any())
                    {
                        hasMoreAgencies = false;
                        break;
                    }

                    var agencyIds = agenciesBatch.Select(a => a.AgencyId).ToArray();
                    
                    // Log batch execution with specific agency IDs
                    _batchLogger.LogBatchExecutionWithIds(batchContext, batchIndex, agencyIds, "AgencyId");
                    _logger.LogInformation($"Processing agency partner data batch {batchIndex} with {agencyIds.Length} agencies for data source: {config.DataSourceName}");

                    try
                    {
                        // Process each agency ID individually for agency partners
                        foreach (var agency in agenciesBatch)
                        {
                            try
                            {
                                _logger.LogInformation($"Processing agency partner data for AgencyId: {agency.AgencyId}");

                                var proxyRequest = CreateAgencyPartnerProxyRequest(config.Manifest, agency.AgencyId);
                                var httpResponse = await _proxyService.ForwardRequestAsync(
                                    proxyRequest,
                                    $"AccessKey {config.Manifest.ApiKey}", "AccessKey");

                                var (isValid, responseContent) = ValidateAndExtractResponse(httpResponse, "ProcessAgencyPartnerDataSync");
                                if (isValid)
                                {
                                    _logger.LogInformation($"Successfully received agency partner data response for AgencyId: {agency.AgencyId} from {config.DataSourceName}");

                                    var agencyPartnerResponse = JsonConvert.DeserializeObject<List<AgencyPartners>>(responseContent);

                                    if (agencyPartnerResponse != null && agencyPartnerResponse.Any())
                                    {
                                        // Set the main AgencyId if not already set
                                        foreach (var agencyPartner in agencyPartnerResponse)
                                        {
                                            if (string.IsNullOrEmpty(agencyPartner.AgencyId))
                                            {
                                                agencyPartner.AgencyId = agency.AgencyId;
                                            }
                                        }

                                        // Add to collection for bulk processing
                                        allAgencyPartners.AddRange(agencyPartnerResponse);

                                        _logger.LogInformation($"Retrieved {agencyPartnerResponse.Count} agency partners for AgencyId: {agency.AgencyId}");
                                    }
                                    else
                                    {
                                        _logger.LogInformation($"No agency partners found for AgencyId: {agency.AgencyId}");
                                    }
                                }
                                else
                                {
                                    _logger.LogWarning("Failed to get agency partner data for AgencyId: {AgencyId} from data source {DataSourceName}", agency.AgencyId, config.DataSourceName);
                                }

                                // Add a small delay between individual requests to avoid overwhelming the API
                                await Task.Delay(config.DelayBetweenRequests);
                            }
                            catch (Exception ex)
                            {
                                _logger.LogError(ex, "Error processing agency partner data for AgencyId: {AgencyId} from data source {DataSourceName}", agency.AgencyId, config.DataSourceName);
                                // Continue processing other agencies
                            }
                        }

                        // Log batch success with agency IDs
                        _batchLogger.LogBatchSuccessWithIds(batchContext, batchIndex, allAgencyPartners.Count, agencyIds, "AgencyId");
                        totalItemsProcessed += agencyIds.Length;
                    }
                    catch (Exception batchException)
                    {
                        // Log batch error with agency IDs
                        _batchLogger.LogBatchErrorWithIds(batchContext, batchIndex, batchException, agencyIds, "AgencyId", "Failed processing agency partner batch");
                        
                        // Re-throw to be caught by outer exception handler
                        throw;
                    }

                    skip += config.BatchSize;
                    if (agenciesBatch.Count < config.BatchSize)
                    {
                        hasMoreAgencies = false;
                    }

                    await Task.Delay(config.DelayBetweenBatches);
                }

                // Bulk upsert all collected agency partners for this data source
                if (allAgencyPartners.Any())
                {
                    var (validData, invalidData) = ValidateData(allAgencyPartners);
                    await LogInvalidData(invalidData, APIDetail.GetAgencyPartnersDetails, batchIndex);

                    var rowsAffected = await _integrationRepository.BulkUpsertAgencyPartners(validData, config.DataSourceId);
                    totalItemsProcessed += rowsAffected.Count;
                    _logger.LogInformation($"Successfully bulk upserted {rowsAffected.Count} agency partner details for {allAgencyPartners.Count} partners from {config.DataSourceName}");
                }
                else
                {
                    _logger.LogInformation($"No agency partners collected for data source: {config.DataSourceName}");
                }

                _batchLogger.LogBatchOperationComplete(batchContext, true, totalItemsProcessed, batchIndex);
                return true;
            }
            catch (Exception ex)
            {
                _batchLogger.LogBatchOperationComplete(batchContext, false, totalItemsProcessed, batchIndex);
                _logger.LogError(ex, "An error occurred while syncing agency partner data");
                return false;
            }
        }

        private async Task<bool> ProcessDocumentDataSync(BatchProcessingConfig config)
        {
            try
            {
                var data = new List<SQLQueryMergeResult>();
                if (!IsEndpointSupported(IntegrationEndPointsType.DocumentList))
                {
                    _logger.LogWarning($"Document endpoint not supported for data source: {config.DataSourceName}");
                    return false;
                }

                _logger.LogInformation($"Starting Document data sync with batch size {config.BatchSize}");

                // Process documents by management IDs
                var managementData = await ProcessDocumentDataByColumn(config, Constants.ManagementIdColumnName);
                data.AddRange(managementData);
                // Process documents by tenancy IDs
                var tenancyData = await ProcessDocumentDataByColumn(config, Constants.TenancyIdColumnName);
                data.AddRange(tenancyData);

                if (managementData.Any() || tenancyData.Any())
                {
                    _ = Task.Run(async () =>
                    {
                        await SendPushNotificationToUsers(data, nameof(DataTypeName.documents));
                    });
                }

            _logger.LogInformation("Completed syncing document data for data source: {DataSourceName}", config.DataSourceName);
                return managementData.Any() || tenancyData.Any();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "An error occurred while syncing document data");
                return false;
            }
        }

        /// <summary>
        /// Process document data by specific column (Management ID or Tenancy ID)
        /// </summary>
        private async Task<List<SQLQueryMergeResult>> ProcessDocumentDataByColumn(BatchProcessingConfig config, string columnName)
        {
            try
            {
                var returnData = new List<SQLQueryMergeResult>();
                var totalCount = await _integrationRepository.GetUniqueManagementOrTenancyCount(config.DataSourceId, columnName);
                if (totalCount == 0)
                {
                    _logger.LogWarning($"No {columnName} found to sync for data source: {config.DataSourceName}");
                    return new List<SQLQueryMergeResult>();
                }

                _logger.LogInformation($"Processing {totalCount} {columnName} in batches of {config.BatchSize} for data source: {config.DataSourceName}");

                int skip = 0;
                int batchIndex = 0;
                bool hasMoreDocuments = true;

                while (hasMoreDocuments)
                {
                    batchIndex++;

                    var idBatch = await _integrationRepository.GetUniqueIdsFromUserProperty(columnName, config.DataSourceId, config.BatchSize, skip);

                    if (idBatch == null || !idBatch.Any())
                    {
                        hasMoreDocuments = false;
                        break;
                    }

                    var ids = idBatch.Select(a => a.Id).ToArray();
                    _logger.LogInformation($"Processing {columnName} data batch {batchIndex} with {ids.Length} documents for data source: {config.DataSourceName}");

                    // Create proxy request based on column type
                    var proxyRequest = columnName == Constants.ManagementIdColumnName 
                        ? CreateProxyRequest(config.Manifest, IntegrationEndPointsType.DocumentList, DateTime.UtcNow.ToString("o"), managementIds: ids)
                        : CreateProxyRequest(config.Manifest, IntegrationEndPointsType.DocumentList, DateTime.UtcNow.ToString("o"), tenancyIds: ids);

                    var httpResponse = await _proxyService.ForwardRequestAsync(
                        proxyRequest,
                        $"AccessKey {config.Manifest.ApiKey}", "AccessKey");

                    var (isValid, responseContent) = ValidateAndExtractResponse(httpResponse, "ProcessDocumentDataSync");
                    if (isValid)
                    {
                        _logger.LogInformation($"Successfully received document data for {columnName} response for batch {batchIndex} from {config.DataSourceName}");

                        try
                        {
                            var documentResponse = JsonConvert.DeserializeObject<List<DocumentDetailResponse>>(responseContent);

                            if (documentResponse != null && documentResponse.Any())
                            {
                                var documentList = MapDocumentResponse(documentResponse);
                                var (validData, invalidData) = ValidateData(documentList);

                                await LogInvalidData(invalidData, APIDetail.GetDocuments, batchIndex);

                                // Use bulk upsert to save/update/delete documents
                                var rowsAffected = await _integrationRepository.BulkUpsertDocumentDetails(validData, config.DataSourceId, columnName);
                                returnData.AddRange(rowsAffected);

                                _logger.LogInformation($"Batch {batchIndex}: Successfully processed {rowsAffected.Count()} document details of {columnName} using bulk upsert");
                                Console.WriteLine($"Document of {columnName} sync batch {batchIndex}: {rowsAffected.Count()} processed for {config.DataSourceName}");
                            }
                            else
                            {
                                _logger.LogWarning($"Batch {batchIndex}: No documents found in the response from {config.DataSourceName}");
                            }
                        }
                        catch (Exception ex)
                        {
                            _logger.LogError(ex, "Error processing document response for batch {BatchIndex} from data source {DataSourceName}", batchIndex, config.DataSourceName);
                        }
                    }
                    else
                    {
                        _logger.LogError("Failed to get document data for batch {BatchIndex} from data source {DataSourceName}", batchIndex, config.DataSourceName);
                    }

                    skip += config.BatchSize;
                    if (idBatch.Count < config.BatchSize)
                    {
                        hasMoreDocuments = false;
                    }

                    await Task.Delay(config.DelayBetweenBatches);
                }

                return returnData;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error processing document data for column {ColumnName}", columnName);
                return new List<SQLQueryMergeResult>();
            }
        }

        #endregion

        #region Generic Sync Methods

        /// <summary>
        /// Configuration for sync operations
        /// </summary>
        public class SyncConfig
        {
            public IntegrationEndPointsType SectionType { get; set; }
            public string DataTypeName { get; set; } = string.Empty;
            public string ColumnName { get; set; } = string.Empty;
            public int? PropertyRelationshipFilter { get; set; }
        }

        /// <summary>
        /// Generic method for standard sync operations that follow the common pattern
        /// </summary>
        private async Task<bool> SyncDataGeneric<TResponse, TEntity>(SyncConfig syncConfig, BatchProcessingConfig config)
            where TResponse : class
            where TEntity : class
        {
            // Create batch operation context for detailed logging
            var batchContext = _batchLogger.CreateBatchOperationContext(
                $"Sync {syncConfig.DataTypeName}", 
                config.DataSourceName);

            // Declare variables outside try block to make them accessible in catch block
            int batchIndex = 0;
            int totalItemsProcessed = 0;

            try
            {
                if (!IsEndpointSupported(syncConfig.SectionType))
                {
                    _logger.LogWarning($"No {syncConfig.DataTypeName} data endpoint configured for data source: {config.DataSourceName}");
                    return false;
                }

                var totalCount = syncConfig.PropertyRelationshipFilter.HasValue
                    ? await _integrationRepository.GetUniqueManagementOrTenancyCount(config.DataSourceId, syncConfig.ColumnName, syncConfig.PropertyRelationshipFilter.Value)
                    : await _integrationRepository.GetUniqueManagementOrTenancyCount(config.DataSourceId, syncConfig.ColumnName);

                if (totalCount == 0)
                {
                    _logger.LogWarning($"No {syncConfig.ColumnName} found to sync for data source: {config.DataSourceName}");
                    _batchLogger.LogBatchOperationComplete(batchContext, true, 0, 0);
                    return true;
                }

                var totalBatches = (int)Math.Ceiling((double)totalCount / config.BatchSize);
                _batchLogger.LogBatchProcessingStart(batchContext, config.DataSourceName, totalBatches);
                _logger.LogInformation($"Processing {totalCount} {syncConfig.ColumnName} in batches of {config.BatchSize} for data source: {config.DataSourceName}");

                int skip = 0;
                bool hasMoreData = true;

                while (hasMoreData)
                {
                    batchIndex++;

                    var idBatch = syncConfig.PropertyRelationshipFilter.HasValue
                        ? await _integrationRepository.GetUniqueIdsFromUserProperty(syncConfig.ColumnName, config.DataSourceId, config.BatchSize, skip, syncConfig.PropertyRelationshipFilter.Value)
                        : await _integrationRepository.GetUniqueIdsFromUserProperty(syncConfig.ColumnName, config.DataSourceId, config.BatchSize, skip);

                    if (idBatch == null || !idBatch.Any())
                    {
                        hasMoreData = false;
                        break;
                    }

                    var ids = idBatch.Select(a => a.Id).ToArray();
                    
                    // Log batch execution with specific entity IDs
                    _batchLogger.LogBatchExecutionWithIds(batchContext, batchIndex, ids, syncConfig.ColumnName);
                    _logger.LogInformation($"Processing {syncConfig.ColumnName} data batch {batchIndex} with {ids.Length} {syncConfig.DataTypeName} for data source: {config.DataSourceName}");

                    try
                    {
                        var proxyRequest = CreateProxyRequestForSync(config.Manifest, syncConfig, ids);
                        var httpResponse = await _proxyService.ForwardRequestAsync(
                            proxyRequest,
                            $"AccessKey {config.Manifest.ApiKey}", "AccessKey");

                        var rowsAffected = await ProcessSyncResponse<TResponse, TEntity>(httpResponse, config, syncConfig, batchIndex);
                        
                        // Log batch success with entity IDs
                        _batchLogger.LogBatchSuccessWithIds(batchContext, batchIndex, rowsAffected, ids, syncConfig.ColumnName);
                        totalItemsProcessed += ids.Length;
                    }
                    catch (Exception batchException)
                    {
                        // Log batch error with entity IDs
                        _batchLogger.LogBatchErrorWithIds(batchContext, batchIndex, batchException, ids, syncConfig.ColumnName, $"Failed processing {syncConfig.DataTypeName} batch");
                        
                        // Re-throw to be caught by outer exception handler
                        throw;
                    }

                    skip += config.BatchSize;
                    if (idBatch.Count < config.BatchSize)
                    {
                        hasMoreData = false;
                    }

                    await Task.Delay(config.DelayBetweenBatches);
                }

                _batchLogger.LogBatchOperationComplete(batchContext, true, totalItemsProcessed, batchIndex);
                return true;
            }
            catch (Exception ex)
            {
                _batchLogger.LogBatchOperationComplete(batchContext, false, totalItemsProcessed, batchIndex);
                _logger.LogError(ex, "An error occurred while syncing {DataTypeName} data", syncConfig.DataTypeName);
                return false;
            }
        }

        /// <summary>
        /// Create proxy request for sync operations
        /// </summary>
        private ProxyRequestModel CreateProxyRequestForSync(DataSourceManifest manifest, SyncConfig syncConfig, string[] ids)
        {
            return syncConfig.ColumnName switch
            {
                Constants.ManagementIdColumnName => CreateProxyRequest(manifest, syncConfig.SectionType, DateTime.UtcNow.ToString("o"), managementIds: ids),
                Constants.TenancyIdColumnName => CreateProxyRequest(manifest, syncConfig.SectionType, DateTime.UtcNow.ToString("o"), tenancyIds: ids),
                _ => throw new ArgumentException($"Unsupported column name: {syncConfig.ColumnName}")
            };
        }

        /// <summary>
        /// Process sync response and perform bulk upsert
        /// </summary>
        /// <returns>Number of rows affected by the bulk upsert operation</returns>
        private async Task<int> ProcessSyncResponse<TResponse, TEntity>(
            IActionResult httpResponse,
            BatchProcessingConfig config,
            SyncConfig syncConfig,
            int batchIndex)
            where TResponse : class
            where TEntity : class
        {
            var (isValid, responseContent) = ValidateAndExtractResponse(httpResponse, $"Process{syncConfig.DataTypeName}Sync");
            if (isValid)
            {
                _logger.LogInformation($"Successfully received {syncConfig.DataTypeName} data response for batch {batchIndex} from {config.DataSourceName}");

                try
                {
                    var response = JsonConvert.DeserializeObject<List<TResponse>>(responseContent);

                    if (response != null && response.Any())
                    {
                        var mappedList = MapDataWithAutoProfile<TResponse, TEntity>(response, config.DataSourceName);
                        var (validData, invalidData) = ValidateData(mappedList);

                        await LogInvalidData(invalidData, GetAPIDetailForDataType(syncConfig.DataTypeName), batchIndex);

                        var rowsAffected = validData.Any() ? await ExecuteBulkUpsert(validData, config.DataSourceId, syncConfig.DataTypeName) : new List<SQLQueryMergeResult>();
                        if (rowsAffected.Any())
                        {
                            _ = Task.Run(async () =>
                            {
                                await SendPushNotificationToUsers(rowsAffected, syncConfig.DataTypeName);
                            });
                        }
                        
                        var affectedCount = rowsAffected?.Count ?? 0;
                        _logger.LogInformation($"Batch {batchIndex}: Successfully processed {affectedCount} {syncConfig.DataTypeName} details using bulk upsert");
                        Console.WriteLine($"{syncConfig.DataTypeName} data sync batch {batchIndex}: {affectedCount} processed for {config.DataSourceName}");
                        return affectedCount;
                    }
                    else
                    {
                        _logger.LogWarning($"Batch {batchIndex}: No {syncConfig.DataTypeName} found in the response from {config.DataSourceName}");
                        return 0;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error processing {DataTypeName} response for batch {BatchIndex} from data source {DataSourceName}", syncConfig.DataTypeName, batchIndex, config.DataSourceName);
                    throw; // Re-throw to be caught by batch error handler
                }
            }
            else
            {
                _logger.LogError("Failed to get {DataTypeName} data for batch {BatchIndex} from data source {DataSourceName}", syncConfig.DataTypeName, batchIndex, config.DataSourceName);
                return 0;
            }
        }

        /// <summary>
        /// Get the appropriate bulk upsert method based on entity type
        /// </summary>
        private async Task<List<SQLQueryMergeResult>> ExecuteBulkUpsert<TEntity>(List<TEntity> list, int dataSourceId, string dataTypeName)
            where TEntity : class
        {
            return dataTypeName switch
            {
                "management" => await _integrationRepository.BulkUpsertPropertyManagerInformation((List<PropertyManagerInformation>)(object)list, dataSourceId),
                "compliance" => await _integrationRepository.BulkUpsertComplianceDetails((List<ComplianceDetail>)(object)list, dataSourceId),
                "tenancies tenant" => await _integrationRepository.BulkUpsertTenanciesTenants((List<TenanciesTenant>)(object)list, dataSourceId),
                "inspections" => await _integrationRepository.BulkUpsertInspectionsDetails((List<InspectionDetail>)(object)list, dataSourceId),
                "maintenance" => await _integrationRepository.BulkUpsertMaintenanceDetails((List<MaintenanceDetail>)(object)list, dataSourceId),
                "financials" => await _integrationRepository.BulkUpsertFinancialsDetails((List<FinancialDetail>)(object)list, dataSourceId),
                "tenancies owner" => await _integrationRepository.BulkUpsertTenanciesOwners((List<TenanciesOwner>)(object)list, dataSourceId),
                "agency" => await _integrationRepository.BulkUpsertAgencyDetails((List<AgencyDetails>)(object)list, dataSourceId),
                _ => throw new ArgumentException($"No bulk upsert method defined for {dataTypeName}")
            };
        }

        /// <summary>
        /// Get the appropriate API detail enum for data type
        /// </summary>
        private static APIDetail GetAPIDetailForDataType(string dataTypeName)
        {
            return dataTypeName switch
            {
                "management" => APIDetail.GetManagement,
                "compliance" => APIDetail.GetCompliance,
                "tenancies tenant" => APIDetail.GetTenanciesTenant,
                "inspections" => APIDetail.GetInspections,
                "maintenance" => APIDetail.GetMaintenance,
                "financials" => APIDetail.GetFinancials,
                "tenancies owner" => APIDetail.GetTenanciesOwner,
                "agency" => APIDetail.GetAgencyDetails,
                _ => APIDetail.GetProperties
            };
        }

        #endregion
    }
}