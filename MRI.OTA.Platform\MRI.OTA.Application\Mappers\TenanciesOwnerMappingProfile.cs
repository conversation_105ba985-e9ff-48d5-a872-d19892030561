﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class TenanciesOwnerMappingProfile : Profile
    {
        public TenanciesOwnerMappingProfile() : base("TenanciesOwnerMappingProfile")
        {
            CreateMap<TenanciesOwnerResponse, TenanciesOwner>()
            .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
            .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
            .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1))
            .ForMember(dest => dest.SRCTenancyId, opt => opt.MapFrom(src => src.TenancyId))
            .ForMember(dest => dest.TenancyName, opt => opt.MapFrom(src => src.TenancyName))
            .ForMember(dest => dest.TenancyStatus, opt => opt.MapFrom(src => src.TenancyStatus))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src =>
                string.Equals(src.IsActive, "Active", StringComparison.OrdinalIgnoreCase)))
            .ForMember(dest => dest.LeaseStart, opt => opt.MapFrom(src => src.TenancyStartDate))
            .ForMember(dest => dest.LeaseEnd, opt => opt.MapFrom(src => src.TenancyEndDate))
            .ForMember(dest => dest.Rent, opt => opt.MapFrom(src => src.Rent))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency))
            .ForMember(dest => dest.RentPeriod, opt => opt.MapFrom(src => src.RentPeriod))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.SRCAgencyId, opt => opt.MapFrom(src => src.ownershipResponse.AgencyId))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.SRCOwnershipId, opt => opt.MapFrom(src => src.ownershipResponse.OwnershipId))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyId, opt => opt.MapFrom(src => -1))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyManagerName, opt => opt.MapFrom(src => src.ownershipResponse.ManagementContactName))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyManagerMobile, opt => opt.MapFrom(src => src.ownershipResponse.ManagementContactNumber))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyManagerEmail, opt => opt.MapFrom(src => src.ownershipResponse.ManagementContactEmail))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.ContactRole, opt => opt.MapFrom(src => src.ownershipResponse.ManagementContactRole))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.Ownership, opt => opt.MapFrom(src => src.ownershipResponse.OwnershipName))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.AuthorityStartDate, opt => opt.MapFrom(src => src.ownershipResponse.AuthorityStartDate))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.AuthorityEndDate, opt => opt.MapFrom(src => src.ownershipResponse.AuthorityEndDate))
            .ReverseMap();

        }
    }
}
