﻿using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.Application.Interfaces
{
    public interface INotificationService
    {
        Task<bool> SendPushNotificationAsync(NotificationRequestModel notificationRequest);

        Task<bool> BroadcastPushNotificationAsync(NotificationRequestModel notificationRequest);
        public Task<List<NotificationMaster>> GetNotificationDetailByCategory(string[] categories);
        public Task<List<NotificationUserList>> GetUsersForNotificationBySRCId(string[] srcPropertyIds, string dataTypeName);
    }
}
