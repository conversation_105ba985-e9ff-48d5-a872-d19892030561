﻿using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.Notification.v1
{
    [AllowAnonymous]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/notifications")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class NotificationController : ControllerBase
    {
        private readonly INotificationService _notificationService;

        /// <summary>
        /// Constructor for notification controller
        /// </summary>
        /// <param name="notificationService"></param>
        public NotificationController(INotificationService notificationService)
        {
            _notificationService = notificationService;
        }

        /// <summary>
        /// Send push notification
        /// </summary>
        /// <param name="notificationRequest"></param>
        /// <returns></returns>
        [HttpPost("send")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "Notification sent successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SendNotification(NotificationRequestModel notificationRequest)
        {
            var result = await _notificationService.SendPushNotificationAsync(notificationRequest);
            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Notification sent successfully", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "Notification not sent", null!, StatusCodes.Status400BadRequest, new List<string> { "The notification could not be sent." }));
            }
        }

        /// <summary>
        /// Send push notification
        /// </summary>
        /// <param name="notificationRequest"></param>
        /// <returns></returns>
        [HttpPost("send-all")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "Notification sent successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SendAllNotification(NotificationRequestModel notificationRequest)
        {
            var result = await _notificationService.BroadcastPushNotificationAsync(notificationRequest);
            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Notification sent successfully", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "Notification not sent", null!, StatusCodes.Status400BadRequest, new List<string> { "The notification could not be sent." }));
            }
        }
    }
}
