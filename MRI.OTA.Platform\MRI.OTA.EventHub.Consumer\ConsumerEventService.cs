﻿using Azure.Messaging;
using System.Net.Http.Json;
using Microsoft.Extensions.Options;
using MRI.Integration.Consumer.SDK;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Newtonsoft.Json;

namespace MRI.OTA.EventHub.Consumer
{
    public class ConsumerEventService : IConsumerEventService
    {
        private readonly ILogger<ConsumerService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// ConsumerEventService constructor
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="httpClientFactory"></param>
        /// <param name="configuration"></param>
        public ConsumerEventService(ILogger<ConsumerService> logger, IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }

        public async Task<bool> AddConsumerEvent(CloudEvent cloudEvent)
        {
            var otaBaseUrl = _configuration.GetSection("Application:OtaBaseUrl").Value;
            if (string.IsNullOrEmpty(otaBaseUrl))
            {
                throw new InvalidOperationException("OtaBaseUrl is not configured.");
            }

            var client = _httpClientFactory.CreateClient("EventConsumer");
            client.BaseAddress = new Uri(otaBaseUrl);

            if (cloudEvent.Data == null)
            {
                throw new InvalidOperationException("CloudEvent data is null.");
            }

            EventData? eventData = JsonConvert.DeserializeObject<EventData>(cloudEvent.Data.ToString() ?? string.Empty);
            if (eventData == null)
            {
                throw new InvalidOperationException("Failed to deserialize CloudEvent data.");
            }

            EventConsumerModel eventConsumer = new EventConsumerModel
            {
                EventConsumerId = cloudEvent.Id,
                SourceServiceId = eventData.SourceServiceId,
                EntityId = eventData.EntityId,
                InstanceId = eventData.InstanceId,
                UserId = eventData.UserId,
                CollationId = eventData.CollationId,
                TimeStamp = eventData.TimeStamp?.ToString() ?? string.Empty,
                DataSchema = cloudEvent.DataSchema,
                Subject = cloudEvent.Subject,
                Source = cloudEvent.Source,
                Type = cloudEvent.Type,
                EventType = eventData.EventType,
                Data = cloudEvent.Data?.ToString() ?? string.Empty
            };
            var response = await client.PostAsJsonAsync(APIConstants.EventConsumerURL, eventConsumer);
            if (!response.IsSuccessStatusCode)
            {
                _logger.LogError($"Failed to call API. Status Code: {response.StatusCode}, Reason: {response.ReasonPhrase}");
                return false;
            }
            return true;
        }
    }}
