﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class PropertyManagerInformation
    {
        [ExcludeColumn]
        public int PropertyManagerInformationId { get; set; }
        [ExcludeColumn]
        public int PropertyId { get; set; }
        public string? SRCAgencyId { get; set; }
        public string? SRCPropertyId { get; set; }
        public string? SRCManagementId { get; set; }
        public string? ManagementType { get; set; }
        public string? AgencyName { get; set; }
        public string? PropertyManagerName { get; set; }
        public string? PropertyManagerMobile { get; set; }
        public string? PropertyManagerPhone { get; set; }
        public string? PropertyManagerEmail { get; set; }
        public string? ContactRole { get; set; }
        public DateTime? AuthorityStartDate { get; set; }
        public DateTime? AuthorityEndDate { get; set; }
        public string? Ownership { get; set; }
        public decimal? ExpenditureLimit { get; set; }
        public string? ExpenditureNotes { get; set; }
    }
}
