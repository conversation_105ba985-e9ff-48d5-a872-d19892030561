﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.ExceptionHandler;

namespace MRI.OTA.UnitTestCases.ExceptionHandler
{
    public class UnitTestConnectionTimeoutExceptionHandler
    {
        [Fact]
        public async Task TryHandleAsync_ReturnsFalse_WhenExceptionIsNotConnectionTimeoutException()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<ConnectionTimeoutExceptionHandler>>();
            var handler = new ConnectionTimeoutExceptionHandler(loggerMock.Object);
            var httpContext = new DefaultHttpContext();
            var exception = new Exception("Some other exception");

            // Act
            var result = await handler.TryHandleAsync(httpContext, exception, CancellationToken.None);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task TryHandleAsync_HandlesConnectionTimeoutException_ReturnsTrueAndWritesResponse()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<ConnectionTimeoutExceptionHandler>>();
            var handler = new ConnectionTimeoutExceptionHandler(loggerMock.Object);
            var httpContext = new DefaultHttpContext();
            var exception = new ConnectionTimeoutException("Timeout occurred");

            // Act
            var result = await handler.TryHandleAsync(httpContext, exception, CancellationToken.None);

            // Assert
            Assert.True(result);
            Assert.Equal("application/json; charset=utf-8", httpContext.Response.ContentType);
            Assert.Equal(StatusCodes.Status408RequestTimeout, httpContext.Response.StatusCode);
        }
    }
}
