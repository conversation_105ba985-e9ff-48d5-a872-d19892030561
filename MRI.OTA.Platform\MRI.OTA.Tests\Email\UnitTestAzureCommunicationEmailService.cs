using Azure;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Email;
using System.Text;
using Xunit;
using AzureEmailMessage = Azure.Communication.Email.EmailMessage;

namespace MRI.OTA.UnitTestCases.Email
{
    public class UnitTestAzureCommunicationEmailService
    {
        private readonly Mock<ILogger<AzureCommunicationEmailService>> _mockLogger;
        private readonly IConfiguration _configuration;

        public UnitTestAzureCommunicationEmailService()
        {
            _mockLogger = new Mock<ILogger<AzureCommunicationEmailService>>();
            _configuration = CreateMockConfiguration();
        }

        private IConfiguration CreateMockConfiguration()
        {
            var configurationBuilder = new ConfigurationBuilder();
            configurationBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["AzureCommunicationSettings:ConnectionString"] = "endpoint=https://test.communication.azure.com/;accesskey=testkey",
                ["AzureCommunicationSettings:DefaultFromEmail"] = "<EMAIL>"
            });

            return configurationBuilder.Build();
        }

        private EmailMessage CreateTestEmailMessage()
        {
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test plain text content",
                HtmlContent = "<p>Test HTML content</p>",
                FromAddress = new EmailAddress("<EMAIL>", "Test Sender")
            };

            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Test Recipient"));
            return message;
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_InitializesCorrectly()
        {
            // Arrange & Act
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);

            // Assert
            Assert.NotNull(service);
            Assert.IsAssignableFrom<IEmailService>(service);
        }

        [Fact]
        public void Constructor_WithNullLogger_AcceptsNullLogger()
        {
            // Arrange & Act
            var service = new AzureCommunicationEmailService(null!, _configuration);

            // Assert
            Assert.NotNull(service);
            Assert.IsAssignableFrom<IEmailService>(service);
        }

        [Fact]
        public void Constructor_WithNullConfiguration_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            // The constructor will fail when trying to access configuration.GetValue
            Assert.Throws<ArgumentNullException>(() => new AzureCommunicationEmailService(_mockLogger.Object, null!));
        }

        [Fact]
        public void Constructor_WithMissingConnectionString_ThrowsArgumentNullException()
        {
            // Arrange
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["AzureCommunicationSettings:DefaultFromEmail"] = "<EMAIL>"
                // Missing ConnectionString - will be null
            });
            var config = configBuilder.Build();

            // Act & Assert
            // EmailClient constructor will throw ArgumentNullException for null connection string
            Assert.Throws<ArgumentNullException>(() => new AzureCommunicationEmailService(_mockLogger.Object, config));
        }

        #endregion

        #region Interface Implementation Tests

        [Fact]
        public void AzureCommunicationEmailService_ImplementsIEmailService()
        {
            // Arrange & Act
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);

            // Assert
            Assert.IsAssignableFrom<IEmailService>(service);
        }

        [Fact]
        public void IEmailService_HasCorrectMethods()
        {
            // Arrange & Act
            var interfaceType = typeof(IEmailService);

            // Assert
            Assert.NotNull(interfaceType.GetMethod("SendEmailAsync"));
            var method = interfaceType.GetMethod("SendEmailAsync");
            Assert.Equal(typeof(Task<bool>), method!.ReturnType);
            var parameters = method.GetParameters();
            Assert.Single(parameters);
            Assert.Equal(typeof(EmailMessage), parameters[0].ParameterType);
        }

        #endregion

        #region SendEmailAsync Tests - Basic Scenarios

        [Fact]
        public async Task SendEmailAsync_WithNullMessage_ReturnsFalse()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);

            // Act
            var result = await service.SendEmailAsync(null!);

            // Assert
            Assert.False(result);
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithValidMessage_LogsInformation()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            // Act
            // This will likely fail due to invalid connection string, but we're testing the logging
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            // Verify error logging occurred
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Email Message Construction Tests

        [Fact]
        public async Task SendEmailAsync_WithEmptyToAddresses_ReturnsFalse()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content"
                // No ToAddresses added
            };

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result);
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithNullFromAddress_UsesDefaultFromConfiguration()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content",
                FromAddress = null! // Should use default from configuration
            };
            message.ToAddresses.Add(new EmailAddress("<EMAIL>"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithCcAndBccAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.CcAddresses.Add(new EmailAddress("<EMAIL>", "CC Recipient"));
            message.BccAddresses.Add(new EmailAddress("<EMAIL>", "BCC Recipient"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithAttachments_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            var attachment = new EmailAttachment("test.txt", Encoding.UTF8.GetBytes("Test content"), "text/plain");
            message.Attachments.Add(attachment);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Edge Case Tests

        [Fact]
        public async Task SendEmailAsync_WithEmptySubject_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.Subject = "";

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithNullSubject_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.Subject = null!;

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithNullContent_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.PlainTextContent = null!;
            message.HtmlContent = null!;

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Configuration Tests

        [Fact]
        public async Task SendEmailAsync_WithMissingDefaultFromEmail_HandlesCorrectly()
        {
            // Arrange
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["AzureCommunicationSettings:ConnectionString"] = "endpoint=https://test.communication.azure.com/;accesskey=testkey"
                // Missing DefaultFromEmail
            });
            var config = configBuilder.Build();

            var service = new AzureCommunicationEmailService(_mockLogger.Object, config);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content",
                FromAddress = null! // Should use default from configuration, but it's missing
            };
            message.ToAddresses.Add(new EmailAddress("<EMAIL>"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Multiple Recipients Tests

        [Fact]
        public async Task SendEmailAsync_WithMultipleToAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Test Recipient 2"));
            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Test Recipient 3"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithMultipleCcAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.CcAddresses.Add(new EmailAddress("<EMAIL>", "CC Recipient 1"));
            message.CcAddresses.Add(new EmailAddress("<EMAIL>", "CC Recipient 2"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithMultipleBccAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.BccAddresses.Add(new EmailAddress("<EMAIL>", "BCC Recipient 1"));
            message.BccAddresses.Add(new EmailAddress("<EMAIL>", "BCC Recipient 2"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Attachment Tests

        [Fact]
        public async Task SendEmailAsync_WithMultipleAttachments_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            var attachment1 = new EmailAttachment("document.pdf", Encoding.UTF8.GetBytes("PDF content"), "application/pdf");
            var attachment2 = new EmailAttachment("image.jpg", Encoding.UTF8.GetBytes("Image content"), "image/jpeg");
            var attachment3 = new EmailAttachment("data.json", Encoding.UTF8.GetBytes("{\"test\": \"data\"}"), "application/json");

            message.Attachments.Add(attachment1);
            message.Attachments.Add(attachment2);
            message.Attachments.Add(attachment3);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithLargeAttachment_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            // Create a large attachment (1MB)
            var largeContent = new byte[1024 * 1024];
            for (int i = 0; i < largeContent.Length; i++)
            {
                largeContent[i] = (byte)(i % 256);
            }

            var attachment = new EmailAttachment("large_file.bin", largeContent, "application/octet-stream");
            message.Attachments.Add(attachment);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        [Fact]
        public async Task SendEmailAsync_WithEmptyAttachment_HandlesCorrectly()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            var attachment = new EmailAttachment("empty.txt", new byte[0], "text/plain");
            message.Attachments.Add(attachment);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Content Type Tests

        [Theory]
        [InlineData("Plain text only", null)]
        [InlineData(null, "<p>HTML only</p>")]
        [InlineData("Plain text content", "<p>HTML content</p>")]
        [InlineData("", "")]
        public async Task SendEmailAsync_WithVariousContentTypes_HandlesCorrectly(string? plainText, string? htmlContent)
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.PlainTextContent = plainText!;
            message.HtmlContent = htmlContent!;

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Email Address Format Tests

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        public async Task SendEmailAsync_WithVariousEmailFormats_HandlesCorrectly(string emailAddress)
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content",
                FromAddress = new EmailAddress("<EMAIL>")
            };
            message.ToAddresses.Add(new EmailAddress(emailAddress));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test connection string
            VerifyErrorLogged("Failed to send email using Azure Communication Services");
        }

        #endregion

        #region Error Handling Tests

        [Fact]
        public async Task SendEmailAsync_LogsExceptionDetails_WhenExceptionOccurs()
        {
            // Arrange
            var service = new AzureCommunicationEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result);

            // Verify that error logging includes exception details
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to send email using Azure Communication Services")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion

        #region Helper Methods

        private void VerifyErrorLogged(string expectedMessage)
        {
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(expectedMessage)),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        private void VerifyInformationLogged(string expectedMessage)
        {
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains(expectedMessage)),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion
    }
}
