using System.Data;
using System.Text;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Repositories;

namespace MRI.OTA.UnitTestCases.Master
{
    public class UnitTestMasterRepository
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<MasterRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly MasterRepository _repository;

        public UnitTestMasterRepository()
        {
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<MasterRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);

            _repository = new MasterRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object);
        }

        [Fact]
        public async Task GetModulesList_Should_Return_Modules_When_Found()
        {
            // Arrange
            int propertyRelationshipId = 1;
            var expectedModules = new List<ViewModuleRelationship>
            {
                new ViewModuleRelationship
                {
                    ModuleId = 1,
                    ModuleName = "Dashboard",
                    Description = "Dashboard module",
                    ModuleOrder = 1,
                    PropertyRelationshipId = propertyRelationshipId,
                    PropertyRelationshipName = "Owner"
                },
                new ViewModuleRelationship
                {
                    ModuleId = 2,
                    ModuleName = "Properties",
                    Description = "Properties module",
                    ModuleOrder = 2,
                    PropertyRelationshipId = propertyRelationshipId,
                    PropertyRelationshipName = "Owner"
                }
            };

            var expectedQueryContent = new StringBuilder();
            expectedQueryContent.Append($"SELECT m.ModuleId,m.ModuleName,m.Description,mpr.ModuleOrder,pr.PropertyRelationshipId,pr.PropertyRelationshipName ");
            expectedQueryContent.Append($"FROM {Constants.ModulesTableName} m INNER JOIN {Constants.ModulePropertyRelationshipTableName} mpr ON m.ModuleID = mpr.ModuleID ");
            expectedQueryContent.Append($"INNER JOIN {Constants.PropertyRelationshipTableName} pr ON mpr.PropertyRelationshipId = pr.PropertyRelationshipId");
            expectedQueryContent.Append($" WHERE pr.PropertyRelationshipId = @PropertyRelationshipId");

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewModuleRelationship>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("PropertyRelationshipId") != null &&
                        (int)p.GetType().GetProperty("PropertyRelationshipId")!.GetValue(p)! == propertyRelationshipId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedModules);

            // Act
            var result = await _repository.GetModulesList(propertyRelationshipId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedModules.Count, result.Count);
            Assert.Equal(expectedModules[0].ModuleId, result[0].ModuleId);
            Assert.Equal(expectedModules[0].ModuleName, result[0].ModuleName);
            Assert.Equal(expectedModules[0].Description, result[0].Description);
            Assert.Equal(expectedModules[0].ModuleOrder, result[0].ModuleOrder);
            Assert.Equal(expectedModules[0].PropertyRelationshipId, result[0].PropertyRelationshipId);
            Assert.Equal(expectedModules[0].PropertyRelationshipName, result[0].PropertyRelationshipName);
            
            Assert.Equal(expectedModules[1].ModuleId, result[1].ModuleId);
            Assert.Equal(expectedModules[1].ModuleName, result[1].ModuleName);
            Assert.Equal(expectedModules[1].Description, result[1].Description);
            Assert.Equal(expectedModules[1].ModuleOrder, result[1].ModuleOrder);
            Assert.Equal(expectedModules[1].PropertyRelationshipId, result[1].PropertyRelationshipId);
            Assert.Equal(expectedModules[1].PropertyRelationshipName, result[1].PropertyRelationshipName);
        }

        [Fact]
        public async Task GetModulesList_Should_Return_Empty_List_When_Not_Found()
        {
            // Arrange
            int propertyRelationshipId = 999; // Non-existent ID
            var emptyList = new List<ViewModuleRelationship>();

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<ViewModuleRelationship>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("PropertyRelationshipId") != null &&
                        (int)p.GetType().GetProperty("PropertyRelationshipId")!.GetValue(p)! == propertyRelationshipId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(emptyList);

            // Act
            var result = await _repository.GetModulesList(propertyRelationshipId);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetPropertyRelationship_Should_Return_Relationships_When_Found()
        {
            // Arrange
            var expectedRelationships = new List<PropertyRelationship>
            {
                new PropertyRelationship
                {
                    PropertyRelationshipId = 1,
                    PropertyRelationshipName = "Owner",
                    Description = "Owner relationship",
                    IsActive = true
                },
                new PropertyRelationship
                {
                    PropertyRelationshipId = 2,
                    PropertyRelationshipName = "Tenant",
                    Description = "Tenant relationship",
                    IsActive = true
                }
            };

            var expectedQueryContent = new StringBuilder();
            expectedQueryContent.Append($"SELECT PropertyRelationshipId,PropertyRelationshipName,Description,IsActive ");
            expectedQueryContent.Append($"FROM {Constants.PropertyRelationshipTableName} WHERE IsActive = 1");

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<PropertyRelationship>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRelationships);

            // Act
            var result = await _repository.GetPropertyRelationship();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedRelationships.Count, result.Count);
            
            Assert.Equal(expectedRelationships[0].PropertyRelationshipId, result[0].PropertyRelationshipId);
            Assert.Equal(expectedRelationships[0].PropertyRelationshipName, result[0].PropertyRelationshipName);
            Assert.Equal(expectedRelationships[0].Description, result[0].Description);
            Assert.Equal(expectedRelationships[0].IsActive, result[0].IsActive);
            
            Assert.Equal(expectedRelationships[1].PropertyRelationshipId, result[1].PropertyRelationshipId);
            Assert.Equal(expectedRelationships[1].PropertyRelationshipName, result[1].PropertyRelationshipName);
            Assert.Equal(expectedRelationships[1].Description, result[1].Description);
            Assert.Equal(expectedRelationships[1].IsActive, result[1].IsActive);
        }

        [Fact]
        public async Task GetPropertyRelationship_Should_Return_Empty_List_When_Not_Found()
        {
            // Arrange
            var emptyList = new List<PropertyRelationship>();

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<PropertyRelationship>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(emptyList);

            // Act
            var result = await _repository.GetPropertyRelationship();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetDataByTableName_Should_Return_Data_When_Found()
        {
            // Arrange
            string tableName = "TestTable";
            var expectedData = new List<TestData>
            {
                new TestData { Id = 1, Name = "Test 1" },
                new TestData { Id = 2, Name = "Test 2" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestData>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains(tableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedData);

            // Act
            var result = await _repository.GetDataByTableName<TestData>(tableName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedData.Count, result.Count);
            Assert.Equal(expectedData[0].Id, result[0].Id);
            Assert.Equal(expectedData[0].Name, result[0].Name);
            Assert.Equal(expectedData[1].Id, result[1].Id);
            Assert.Equal(expectedData[1].Name, result[1].Name);
        }

        [Fact]
        public async Task GetDataByTableName_Should_Return_Empty_List_When_Not_Found()
        {
            // Arrange
            string tableName = "EmptyTable";
            var emptyList = new List<TestData>();

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestData>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains(tableName)),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(emptyList);

            // Act
            var result = await _repository.GetDataByTableName<TestData>(tableName);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        // Test data class to use with GetDataByTableName
        public class TestData
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
        }
    }
}
