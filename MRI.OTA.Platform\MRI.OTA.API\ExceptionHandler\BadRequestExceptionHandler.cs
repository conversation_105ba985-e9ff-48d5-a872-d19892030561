﻿using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using MRI.OTA.Common.Models;

namespace MRI.OTA.API.ExceptionHandler
{
    public class BadRequestExceptionHandler : IExceptionHandler
    {
        private readonly ILogger<BadRequestExceptionHandler> _logger;

        public BadRequestExceptionHandler(ILogger<BadRequestExceptionHandler> logger)
        {
            _logger = logger;
        }

        public async ValueTask<bool> TryHandleAsync(
            HttpContext httpContext,
            Exception exception,
            CancellationToken cancellationToken)
        {
            if (exception is not BadRequestException badRequestException)
            {
                return false;
            }

            _logger.LogError(
                badRequestException,
                "Exception occurred: {Message}",
                badRequestException.Message);

            var response = new ApiResponse<object>(false, "Bad request.", data: null!, StatusCodes.Status400BadRequest, new List<string> { exception.Message });

            httpContext.Response.ContentType = "application/json";
            httpContext.Response.StatusCode = StatusCodes.Status400BadRequest;
            await httpContext.Response.WriteAsJsonAsync(JsonSerializer.Serialize(response), cancellationToken);

            return true;
        }
    }
}
