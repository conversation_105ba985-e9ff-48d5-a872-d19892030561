﻿using System.Globalization;
using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    /// <summary>
    /// Entity for UserProperties
    /// </summary>
    public class UserProperties
    {
        /// <summary>
        /// userid
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// PropertyId
        /// </summary>
        [ExcludeColumn]
        public int PropertyId { get; set; }

        /// <summary>
        /// SRCPropertyId
        /// </summary>
        [ExcludeColumn]
        public string? ProviderId { get; set; }

        /// <summary>
        /// DataSourceId
        /// </summary>
        [ExcludeColumn]
        public int DataSourceId { get; set; }

        /// <summary>
        /// SRCServiceId
        /// </summary>
        public string? SRCServiceId { get; set; }
        /// <summary>
        /// AgencyId
        /// </summary>
        [ExcludeColumn]
        public string? AgencyId { get; set; }

        public string? SRCManagementId { get; set; }

        public string? SRCTenancyId { get; set; }


        [ExcludeColumn]
        public string? OwnershipName { get; set; }

        /// <summary>
        /// DefaultImageId
        /// </summary>
        [ExcludeColumn]
        public int? DefaultImageId { get; set; }

        /// <summary>
        /// OccupancyTypesId
        /// </summary>
        public string? OccupancyType { get; set; }

        /// <summary>
        /// PropertyName
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// SRCManagementId
        /// </summary>
        public string? SRCAgencyId { get; set; }

        /// <summary>
        /// SRCEntitytId
        /// </summary>
        public string? SRCEntitytId { get; set; }


        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Bedrooms
        /// </summary>
        public int Bedrooms { get; set; }

        /// <summary>
        /// BathRooms
        /// </summary>
        public int BathRooms { get; set; }

        /// <summary>
        /// CarSpaces
        /// </summary>
        public int CarSpaces { get; set; }

        /// <summary>
        /// FloorArea
        /// </summary>
        public int FloorArea { get; set; }

        /// <summary>
        /// LandArea
        /// </summary>
        public int LandArea { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// StreetAddress
        /// </summary>
        public string? StreetAddress { get; set; }
        /// <summary>
        /// Suburb
        /// </summary>
        public string? Suburb { get; set; }
        /// <summary>
        /// City
        /// </summary>
        public string? City { get; set; }
        /// <summary>
        /// StateId
        /// </summary>
        public string? StateId { get; set; }
        /// <summary>
        /// Country
        /// </summary>
        public string? CountryCode { get; set; }

        /// <summary>
        /// Country
        /// </summary>
        public string? CountryName { get; set; }

        /// <summary>
        /// StateCode
        /// </summary>
        public string? StateCode { get; set; }

        /// <summary>
        /// StateName
        /// </summary>
        public string? StateName { get; set; }

        /// <summary>
        /// AdministrativeArea
        /// </summary>
        public string? AdministrativeArea { get; set; }

        /// <summary>
        /// Locale
        /// </summary>
        public string? Locale { get; set; }

        /// <summary>
        /// PostalCode
        /// </summary>
        public string? PostalCode { get; set; }
        /// <summary>
        /// RuralDelivery
        /// </summary>
        public string? RuralDelivery { get; set; }
        /// <summary>
        /// PostOfficeName
        /// </summary>
        public string? PostOfficeName { get; set; }

        /// <summary>
        /// PropertyRelationshipId
        /// </summary>
        public int PropertyRelationshipId { get; set; }

        /// <summary>
        /// PropertyType
        /// </summary>
        public string? PropertyType { get; set; }

        /// <summary>
        /// LotNumber
        /// </summary>
        public string? LotNumber { get; set; }

        /// <summary>
        /// OccupancyStatus
        /// </summary>
        public string? OccupancyStatus { get; set; }

        /// <summary>
        /// UserPropertiesNickNameId
        /// </summary>
        public int? UserPropertiesNickNameId { get; set; }

        public string? Currency { get; set; }
        public string? BuildingNumber { get; set; }
        [ExcludeColumn]
        public string? DefaultImageLink { get; set; }
        /// <summary>
        /// dataSource
        /// </summary>
        public UserDataSource UserDataSource { get; set; }

        /// <summary>
        /// PropertyManagerInformation
        /// </summary>
        public PropertyManagerInformation? PropertyManagerInformation { get; set; }

        /// <summary>
        /// PropertyFinancialInformation
        /// </summary>
        public PropertyFinancialInformation? PropertyFinancialInformation { get; set; }

        /// <summary>
        /// IsAddedToPortfolio
        /// </summary>
        public bool IsAddedToPortfolio { get; set; }
        /// <summary>
        /// PropertyFinancialInformation
        /// </summary>
        [ExcludeColumn]
        public TenanciesTenant? TenanciesTenant { get; set; }
        [ExcludeColumn]
        public TenanciesOwner? TenanciesOwner { get; set; }
        [ExcludeColumn]
        public List<MaintenanceDetail>? MaintenanceDetailList { get; set; }
        [ExcludeColumn]
        public List<InspectionDetail>? InspectionDetailList { get; set; }
        [ExcludeColumn]
        public List<ComplianceDetail>? ComplianceDetailList { get; set; }
        [ExcludeColumn]
        public List<DocumentDetail>? DocumentDetailList { get; set; }
        public string? Unit { get; set; }
        public string? StreetNumber { get; set; }
    }
}
