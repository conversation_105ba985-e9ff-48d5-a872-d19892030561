name: my-place-source-sync-service

on:
  workflow_dispatch:
    inputs:
      target_environment:
        description: 'Target environment (dev, qa)'
        required: true
        default: 'qa'
        type: choice
        options:
          - dev
          - qa
      imageTagOverride:
        description: 'Optional image tag override'
        required: false
        default: ''

env:
  IMAGE_NAME: 'apacsharedplatform/mriota/worker'

jobs:
  build-and-push:
    name: 🏗️ Build & Push Docker Image
    runs-on: ubuntu-latest

    outputs:
      image_tag: ${{ steps.set-tag.outputs.image_tag }}

    steps:
      - name: 📮️ Checkout Code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: 🔧 Set up Docker Buildx with container driver
        uses: docker/setup-buildx-action@v3
        with:
          driver: docker-container
          buildkitd-flags: --debug
          install: true
          use: true

      - name: 🏷️ Set Image Tag
        id: set-tag
        run: |
          echo "📌 Setting image tag..."
          if [ -n "${{ github.event.inputs.imageTagOverride }}" ]; then
            echo "✅ Using overridden image tag: ${{ github.event.inputs.imageTagOverride }}"
            echo "image_tag=${{ github.event.inputs.imageTagOverride }}" >> $GITHUB_OUTPUT
          else
            GENERATED_TAG="revision-${{ github.run_id }}-${{ github.event.inputs.target_environment }}"
            echo "🔁 Auto-generated image tag: $GENERATED_TAG"
            echo "image_tag=$GENERATED_TAG" >> $GITHUB_OUTPUT
          fi

      - name: 🔐 Docker Login to ACR
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_ENDPOINT }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: 📦 Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: ./MRI.OTA.Platform
          file: ./MRI.OTA.Platform/MRI.OTA.Worker/Dockerfile
          push: true
          tags: |
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:latest
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ steps.set-tag.outputs.image_tag }}
          build-args: |
            BUILD_CONFIGURATION=Release

      - name: 📝 Log Docker Image Info
        run: |
          echo "✅ Docker Image: ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ steps.set-tag.outputs.image_tag }}"
          echo "📜 Note: This image will be configured with environment-specific settings during deployment"

  deploy-to-dev:
    name: 🚀 Deploy to DEV Environment
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: dev
    if: github.event.inputs.target_environment == 'dev'

    steps:
      - name: 📂 Upgrade Azure CLI & Extensions
        run: |
          az upgrade --yes
          az extension update --name webapp || az extension add --name webapp
          az extension update --name functionapp || az extension add --name functionapp

      - name: 📮️ Checkout Code
        uses: actions/checkout@v4

      - name: 🔐 Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: 🨭 Set DEV Environment Context
        uses: ./.github/actions/set-environment-context-funcapp
        with:
          environment: dev
          app_name: ${{ vars.DEV_FUNC_APP_NAME }}
          app_rg: ${{ vars.DEV_FUNC_RESOURCE_GROUP }}
          client_secret: ${{ secrets.DEV_CLIENT_SECRET }}
          AzureWebJobsStorage: ${{ vars.DEV_FUNC_AZURE_WEBJOBS_STORAGE }}
          FUNCTIONS_WORKER_RUNTIME: ${{ vars.DEV_FUNC_WORKER_RUNTIME }}
          PROPERTY_DATA_TIMER: ${{ vars.DEV_FUNC_TIMERS_PROPERTY_DATA }}
          AGENCY_DATA_TIMER: ${{ vars.DEV_FUNC_TIMERS_AGENCY_DATA }}
          BaseUrl: ${{ vars.DEV_FUNC_BASE_URL }}
          WorkerTimeOut: ${{ vars.DEV_FUNC_WORKER_TIMEOUT }}
          TenantName: ${{ vars.DEV_FUNC_TENANT_NAME }}
          ClientId: ${{ vars.DEV_FUNC_CLIENT_ID }}
          SignUpSignInPolicyId: ${{ vars.DEV_FUNC_SIGNUP_SIGNIN_POLICY_ID }}
          ApplicationInsights__ConnectionString: ${{ vars.DEV_FUNC_APPINSIGHTS_CONNECTION_STRING }}

      - name: 📲 Print DEV Environment Context
        run: |
          echo "📜 DEV Environment Context Loaded:"
          echo "ENVIRONMENT: $ENVIRONMENT"
          echo "APP_NAME: $APP_NAME"
          echo "APP_RG: $APP_RG"
          echo "AzureWebJobsStorage: $AzureWebJobsStorage"
          echo "FUNCTIONS_WORKER_RUNTIME: $FUNCTIONS_WORKER_RUNTIME"
          echo "PROPERTY_DATA_TIMER: $PROPERTY_DATA_TIMER"
          echo "AGENCY_DATA_TIMER: $AGENCY_DATA_TIMER"
          echo "BaseUrl: $BaseUrl"
          echo "WorkerTimeOut: $WorkerTimeOut"
          echo "TenantName: $TenantName"
          echo "ClientId: $ClientId"
          echo "ClientSecret: [HIDDEN]"
          echo "SignUpSignInPolicyId: $SignUpSignInPolicyId"
          echo "ApplicationInsights__ConnectionString: [HIDDEN]"

      - name: 🚀 Deploy Container to DEV Function App
        uses: ./.github/actions/deploy-function-app-container
        with:
          image-tag: ${{ needs.build-and-push.outputs.image_tag }}
          acr-endpoint: ${{ secrets.ACR_ENDPOINT }}
          image-name: ${{ env.IMAGE_NAME }}

      - name: 🔍 Verify DEV Deployment
        run: |
          echo "🔍 Verifying DEV deployment..."
          az functionapp show \
            --name "$APP_NAME" \
            --resource-group "$APP_RG" \
            --query "{name:name, state:state, kind:kind, latestRevisionName:latestRevisionName}" \
            --output table

      - name: ✅ DEV Deployment Summary
        run: |
          echo "## ✅ DEV Environment Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** DEV" >> $GITHUB_STEP_SUMMARY
          echo "**Function App Name:** $APP_NAME" >> $GITHUB_STEP_SUMMARY
          echo "**Resource Group:** $APP_RG" >> $GITHUB_STEP_SUMMARY
          echo "**Deployed Image:** ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ needs.build-and-push.outputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
          echo "**Deployment Time:** $(date -u +'%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
          echo "**Configuration:** Environment-specific App Settings applied at runtime" >> $GITHUB_STEP_SUMMARY

  deploy-to-qa:
    name: 🚀 Deploy to QA Environment
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: qa
    if: github.event.inputs.target_environment == 'qa'

    steps:
      - name: 📂 Upgrade Azure CLI & Extensions
        run: |
          az upgrade --yes
          az extension update --name webapp || az extension add --name webapp
          az extension update --name functionapp || az extension add --name functionapp

      - name: 📮️ Checkout Code
        uses: actions/checkout@v4

      - name: 🔐 Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: 🨭 Set QA Environment Context
        uses: ./.github/actions/set-environment-context-funcapp
        with:
          environment: qa
          app_name: ${{ vars.QA_FUNC_APP_NAME }}
          app_rg: ${{ vars.QA_FUNC_RESOURCE_GROUP }}
          client_secret: ${{ secrets.QA_CLIENT_SECRET }}
          AzureWebJobsStorage: ${{ vars.QA_FUNC_AZURE_WEBJOBS_STORAGE }}
          FUNCTIONS_WORKER_RUNTIME: ${{ vars.QA_FUNC_WORKER_RUNTIME }}
          PROPERTY_DATA_TIMER: ${{ vars.QA_FUNC_TIMERS_PROPERTY_DATA }}
          AGENCY_DATA_TIMER: ${{ vars.QA_FUNC_TIMERS_AGENCY_DATA }}
          BaseUrl: ${{ vars.QA_FUNC_BASE_URL }}
          WorkerTimeOut: ${{ vars.QA_FUNC_WORKER_TIMEOUT }}
          TenantName: ${{ vars.QA_FUNC_TENANT_NAME }}
          ClientId: ${{ vars.QA_FUNC_CLIENT_ID }}
          SignUpSignInPolicyId: ${{ vars.QA_FUNC_SIGNUP_SIGNIN_POLICY_ID }}
          ApplicationInsights__ConnectionString: ${{ vars.QA_FUNC_APPINSIGHTS_CONNECTION_STRING }}

      - name: 📲 Print QA Environment Context
        run: |
          echo "📜 QA Environment Context Loaded:"
          echo "ENVIRONMENT: $ENVIRONMENT"
          echo "APP_NAME: $APP_NAME"
          echo "APP_RG: $APP_RG"
          echo "AzureWebJobsStorage: $AzureWebJobsStorage"
          echo "FUNCTIONS_WORKER_RUNTIME: $FUNCTIONS_WORKER_RUNTIME"
          echo "PROPERTY_DATA_TIMER: $PROPERTY_DATA_TIMER"
          echo "AGENCY_DATA_TIMER: $AGENCY_DATA_TIMER"
          echo "BaseUrl: $BaseUrl"
          echo "WorkerTimeOut: $WorkerTimeOut"
          echo "TenantName: $TenantName"
          echo "ClientId: $ClientId"
          echo "ClientSecret: [HIDDEN]"
          echo "SignUpSignInPolicyId: $SignUpSignInPolicyId"
          echo "ApplicationInsights__ConnectionString: [HIDDEN]"

      - name: 🚀 Deploy Container to QA Function App
        uses: ./.github/actions/deploy-function-app-container
        with:
          image-tag: ${{ needs.build-and-push.outputs.image_tag }}
          acr-endpoint: ${{ secrets.ACR_ENDPOINT }}
          image-name: ${{ env.IMAGE_NAME }}

      - name: 🔍 Verify QA Deployment
        run: |
          echo "🔍 Verifying QA deployment..."
          az functionapp show \
            --name "$APP_NAME" \
            --resource-group "$APP_RG" \
            --query "{name:name, state:state, kind:kind, latestRevisionName:latestRevisionName}" \
            --output table

      - name: ✅ QA Deployment Summary
        run: |
          echo "## ✅ QA Environment Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "**Environment:** QA" >> $GITHUB_STEP_SUMMARY
          echo "**Function App Name:** $APP_NAME" >> $GITHUB_STEP_SUMMARY
          echo "**Resource Group:** $APP_RG" >> $GITHUB_STEP_SUMMARY
          echo "**Deployed Image:** ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ needs.build-and-push.outputs.image_tag }}" >> $GITHUB_STEP_SUMMARY
          echo "**Deployment Time:** $(date -u +'%Y-%m-%d %H:%M:%S UTC')" >> $GITHUB_STEP_SUMMARY
          echo "**Configuration:** Environment-specific App Settings applied at runtime" >> $GITHUB_STEP_SUMMARY
