<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <meta name="ROBOTS" content="NONE, NOARCHIVE">
    <meta name="GOOGLEBOT" content="NOARCHIVE">
    
    <style>
        /* CSS Variables */
        :root {
            --primary-color: rgb(0, 122, 198);
            --primary-hover: rgb(0, 102, 178);
            --secondary-color: #6C7278;
            --text-color: #607184;
            --text-dark: #111827;
            --background-color: #f7f7f7;
            --border-color: #d1d5db;
            --error-color: #a61e0c;
            --input-text-font-size: 16px;
        }

        /* Import Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        /* Base Styles */
        * {
            box-sizing: border-box;
        }

        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            background-color: #f7f7f7;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            padding: 40px 30px;
        }

        /* Logo */
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: 900;
            color: #333;
            font-family: 'Inter', sans-serif;
        }

        .logo-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        /* Title */
        .title {
            font-size: 16px;
            font-weight: 400;
            color: var(--text-dark);
            margin-bottom: 30px;
            text-align: left;
        }

        /* Social Buttons */
        .social-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .social-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 45px;
            border: 1px solid var(--border-color);
            border-radius: 999px;
            background: white;
            color: var(--text-dark);
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            padding: 0 20px;
            text-decoration: none;
        }

        .social-btn:hover {
            border-color: var(--primary-color);
            box-shadow: 0 2px 8px rgba(0, 122, 198, 0.1);
        }

        .social-btn::before {
            content: "";
            position: absolute;
            left: 20px;
            width: 18px;
            height: 18px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .google::before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE3LjY0IDkuMjA0NTVDMTcuNjQgOC41NjY4MiAxNy41ODI3IDcuOTUyMjcgMTcuNDc2NCA3LjM2MzY0SDE5VjEwLjg0MDlIMTUuNjkwOUMxNS4zIDEyLjI5NTUgMTQuMzgxOCAxMy40Njk1IDEzLjA4NjQgMTQuMjM4NkwxNS44MzY0IDE2LjM0MDlDMTYuOTY4MiAxNS4yOTU1IDE3LjY0IDEyLjQ2ODIgMTcuNjQgOS4yMDQ1NVoiIGZpbGw9IiM0Mjg1RjQiLz4KPHBhdGggZD0iTTkgMThDMTEuNDMgMTggMTMuNDY3MyAxNy4xOTQxIDE1LjgzNjQgMTUuMzQwOUwxMy4wODY0IDEzLjIzODZDMTIuMjQgMTMuNzc3MyAxMS4yMTA5IDE0LjA4MTggMTAgMTQuMDgxOEM3LjM5MDkxIDE0LjA4MTggNS4xNTQ1NSAxMi40MjI3IDQuNDA5MDkgMTAuMDVMMi4wOTA5MSAxMi4yNzI3QzMuMDkwOTEgMTQuNzk1NSA1LjgxODE4IDE4IDkgMThaIiBmaWxsPSIjMzRBODUzIi8+CjxwYXRoIGQ9Ik00LjQwOTA5IDcuOTVDNC4xNTQ1NSA3LjI5NTQ1IDQgNi42MTM2NCA0IDUuOTA5MDlDNCA1LjIwNDU1IDQuMTU0NTUgNC41MjI3MyA0LjQwOTA5IDMuODY4MThMNC40MDkwOSAzLjg2ODE4TDIuMDkwOTEgMS42NDU0NUM0LjE1NDU1IDEuNjQ1NDUgNC4xNTQ1NSAxLjY0NTQ1IDIuMDkwOTEgMS42NDU0NUM0LjE1NDU1IDEuNjQ1NDUgNC4xNTQ1NSAxLjY0NTQ1IDIuMDkwOTEgMS42NDU0NUMxLjA5MDkxIDQuMTY4MTggMy44MTgxOCA3LjM3MjczIDkgNy4zNzI3M1oiIGZpbGw9IiNGQkJDMDQiLz4KPHBhdGggZD0iTTkgNy4zNjM2NEMxMC40NzI3IDcuMzYzNjQgMTEuNzg2NCA3Ljg5NTQ1IDEyLjgyNzMgOC44NDA5MUwxNS4yMjczIDYuNDQwOTFDMTMuNDY3MyA0LjgwNDU1IDExLjQzIDQgOSA0QzUuODE4MTggNCAzLjA5MDkxIDcuMjA0NTUgMi4wOTA5MSA5LjcyNzI3TDQuNDA5MDkgMTEuOTVDNS4xNTQ1NSA5LjU3NzI3IDcuMzkwOTEgNy4zNjM2NCA5IDcuMzYzNjRaIiBmaWxsPSIjRUE0MzM1Ii8+Cjwvc3ZnPgo=');
        }

        .facebook::before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE4IDlDMTggNC4wMzc1IDE0LjA2MjUgMCA5IDBDMy45Mzc1IDAgMCA0LjAzNzUgMCA5QzAgMTMuNSAzLjIyNSAxNy4yNSA3LjUgMTcuOTM3NVYxMS42MjVINS4yNVY5SDcuNVY3LjAzMTI1QzcuNSA0LjgxMjUgOC44MTI1IDMuNTYyNSAxMC44MTI1IDMuNTYyNUMxMS43NSAzLjU2MjUgMTIuNzUgMy43NSAxMi43NSAzLjc1VjUuOTM3NUgxMS42NTYyQzEwLjU3NSA1LjkzNzUgMTAuMTI1IDYuNjU2MjUgMTAuMTI1IDcuNDA2MjVWOUgxMi42NTYyTDEyLjE4NzUgMTEuNjI1SDEwLjEyNVYxNy45Mzc1QzE0LjQgMTcuMjUgMTggMTMuNSAxOCA5WiIgZmlsbD0iIzE4NzdGMiIvPgo8L3N2Zz4K');
        }

        .apple::before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjUgNi43NUMxMy41IDYuNzUgMTIuNzUgNi4zNzUgMTEuNjI1IDYuMzc1QzEwLjUgNi4zNzUgOS43NSA2Ljc1IDkuNzUgNi43NUMxMC4xMjUgNi4zNzUgMTAuNSA2IDEwLjUgNS4yNUMxMC41IDQuNSAxMC4xMjUgMy43NSA5LjM3NSAzLjc1QzguNjI1IDMuNzUgOC4yNSA0LjUgOC4yNSA1LjI1QzguMjUgNiA4LjYyNSA2LjM3NSA5IDYuNzVDOSA2Ljc1IDguMjUgNi4zNzUgNy4xMjUgNi4zNzVDNiA2LjM3NSA1LjI1IDYuNzUgNS4yNSA2Ljc1QzUuMjUgNi43NSA0LjUgNy4xMjUgNC41IDhDNC41IDguODc1IDUuMjUgOS4zNzUgNS4yNSA5LjM3NUM1LjI1IDkuMzc1IDYgOS43NSA3LjEyNSA5Ljc1QzguMjUgOS43NSA5IDkuMzc1IDkgOS4zNzVDOSA5LjM3NSA5Ljc1IDkuNzUgMTAuODc1IDkuNzVDMTIgOS43NSAxMi43NSA5LjM3NSAxMi43NSA5LjM3NUMxMi43NSA5LjM3NSAxMy41IDguODc1IDEzLjUgOEMxMy41IDcuMTI1IDEyLjc1IDYuNzUgMTIuNzUgNi43NVoiIGZpbGw9IiMwMDAiLz4KPC9zdmc+Cg==');
        }

        /* Divider */
        .divider {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: var(--secondary-color);
            font-size: 14px;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--border-color);
        }

        .divider::before {
            margin-right: 16px;
        }

        .divider::after {
            margin-left: 16px;
        }

        /* Form */
        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            color: var(--text-color);
            font-size: 12px;
            font-weight: 400;
        }

        .form-input {
            width: 100%;
            height: 45px;
            padding: 12px;
            border: 1px solid var(--border-color);
            border-radius: 12px;
            font-size: var(--input-text-font-size);
            font-family: inherit;
            transition: border-color 0.2s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .forgot-password {
            display: block;
            text-align: right;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 14px;
            margin: 8px 0 20px 0;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        /* Login Button */
        .login-btn {
            width: 100%;
            height: 45px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 999px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background: var(--primary-hover);
        }

        /* Sign Up Link */
        .signup-link {
            text-align: center;
            color: var(--secondary-color);
            font-size: 14px;
        }

        .signup-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .container {
                padding: 15px;
            }
            
            .login-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-card">
            <div class="logo">
                <div class="logo-icon">S</div>
                Super App
            </div>
             <div id="api"></div>
            <h1 class="title">Log in to your Account</h1>
            
            <div class="social-buttons">
                <a href="#" class="social-btn google">Continue with Google</a>
                <a href="#" class="social-btn facebook">Continue with Facebook</a>
                <a href="#" class="social-btn apple">Continue with Apple</a>
            </div>
            
            <div class="divider">Or</div>
            
            <form>
                <div class="form-group">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" id="email" class="form-input" placeholder="Enter your email" required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">Password</label>
                    <input type="password" id="password" class="form-input" placeholder="Enter your password" required>
                </div>
                
                <a href="#" class="forgot-password">Forgot Password?</a>
                
                <button type="submit" class="login-btn">Log In</button>
            </form>
            
            <div class="signup-link">
                Don't have an account? <a href="#">Sign Up</a>
            </div>
        </div>
    </div>
</body>
</html>