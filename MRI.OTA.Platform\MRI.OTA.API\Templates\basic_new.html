<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <meta name="ROBOTS" content="NONE, NOARCHIVE">
    <meta name="GOOGLEBOT" content="NOARCHIVE">
    
    <style>
        /* CSS Variables */
        :root {
            --primary-color: rgb(0, 122, 198);
            --primary-hover: rgb(0, 102, 178);
            --secondary-color: #6C7278;
            --text-color: #607184;
            --text-dark: #111827;
            --background-color: #f7f7f7;
            --border-color: #d1d5db;
            --error-color: #a61e0c;
            --input-text-font-size: 16px;
        }

        /* Import Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        /* Base Styles */
        * {
            box-sizing: border-box;
        }

        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            background-color: #f5f5f5;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 4px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.08);
            width: 100%;
            max-width: 450px;
            padding: 30px;
        }

        /* Logo */
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
        }

        .logo-icon {
            width: 24px;
            height: 24px;
            border-radius: 4px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: bold;
        }

        /* Sign In Title */
        .signin-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }

        /* Subtitle */
        .signin-subtitle {
            font-size: 14px;
            color: #666;
            margin-bottom: 24px;
        }

        /* Title */
        .title {
            font-size: 16px;
            font-weight: 400;
            color: var(--text-dark);
            margin-bottom: 30px;
            text-align: left;
        }

        /* Social Buttons */
        .social-buttons {
            display: flex;
            flex-direction: column;
            gap: 12px;
            margin-bottom: 20px;
        }

        .social-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100%;
            height: 44px;
            border: 1px solid #ddd;
            border-radius: 4px;
            background: white;
            color: #333;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            position: relative;
            padding: 0 16px;
            text-decoration: none;
        }

        .social-btn:hover {
            border-color: #ccc;
            background: #f9f9f9;
        }

        .social-btn::before {
            content: "";
            width: 20px;
            height: 20px;
            margin-right: 12px;
            background-size: contain;
            background-repeat: no-repeat;
            background-position: center;
        }

        .google::before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE3LjY0IDkuMjA0NTVDMTcuNjQgOC41NjY4MiAxNy41ODI3IDcuOTUyMjcgMTcuNDc2NCA3LjM2MzY0SDE5VjEwLjg0MDlIMTUuNjkwOUMxNS4zIDEyLjI5NTUgMTQuMzgxOCAxMy40Njk1IDEzLjA4NjQgMTQuMjM4NkwxNS44MzY0IDE2LjM0MDlDMTYuOTY4MiAxNS4yOTU1IDE3LjY0IDEyLjQ2ODIgMTcuNjQgOS4yMDQ1NVoiIGZpbGw9IiM0Mjg1RjQiLz4KPHBhdGggZD0iTTkgMThDMTEuNDMgMTggMTMuNDY3MyAxNy4xOTQxIDE1LjgzNjQgMTUuMzQwOUwxMy4wODY0IDEzLjIzODZDMTIuMjQgMTMuNzc3MyAxMS4yMTA5IDE0LjA4MTggMTAgMTQuMDgxOEM3LjM5MDkxIDE0LjA4MTggNS4xNTQ1NSAxMi40MjI3IDQuNDA5MDkgMTAuMDVMMi4wOTA5MSAxMi4yNzI3QzMuMDkwOTEgMTQuNzk1NSA1LjgxODE4IDE4IDkgMThaIiBmaWxsPSIjMzRBODUzIi8+CjxwYXRoIGQ9Ik00LjQwOTA5IDcuOTVDNC4xNTQ1NSA3LjI5NTQ1IDQgNi42MTM2NCA0IDUuOTA5MDlDNCA1LjIwNDU1IDQuMTU0NTUgNC41MjI3MyA0LjQwOTA5IDMuODY4MThMNC40MDkwOSAzLjg2ODE4TDIuMDkwOTEgMS42NDU0NUM0LjE1NDU1IDEuNjQ1NDUgNC4xNTQ1NSAxLjY0NTQ1IDIuMDkwOTEgMS42NDU0NUM0LjE1NDU1IDEuNjQ1NDUgNC4xNTQ1NSAxLjY0NTQ1IDIuMDkwOTEgMS42NDU0NUMxLjA5MDkxIDQuMTY4MTggMy44MTgxOCA3LjM3MjczIDkgNy4zNzI3M1oiIGZpbGw9IiNGQkJDMDQiLz4KPHBhdGggZD0iTTkgNy4zNjM2NEMxMC40NzI3IDcuMzYzNjQgMTEuNzg2NCA3Ljg5NTQ1IDEyLjgyNzMgOC44NDA5MUwxNS4yMjczIDYuNDQwOTFDMTMuNDY3MyA0LjgwNDU1IDExLjQzIDQgOSA0QzUuODE4MTggNCAzLjA5MDkxIDcuMjA0NTUgMi4wOTA5MSA5LjcyNzI3TDQuNDA5MDkgMTEuOTVDNS4xNTQ1NSA5LjU3NzI3IDcuMzkwOTEgNy4zNjM2NCA5IDcuMzYzNjRaIiBmaWxsPSIjRUE0MzM1Ii8+Cjwvc3ZnPgo=');
        }

        .facebook::before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTE4IDlDMTggNC4wMzc1IDE0LjA2MjUgMCA5IDBDMy45Mzc1IDAgMCA0LjAzNzUgMCA5QzAgMTMuNSAzLjIyNSAxNy4yNSA3LjUgMTcuOTM3NVYxMS42MjVINS4yNVY5SDcuNVY3LjAzMTI1QzcuNSA0LjgxMjUgOC44MTI1IDMuNTYyNSAxMC44MTI1IDMuNTYyNUMxMS43NSAzLjU2MjUgMTIuNzUgMy43NSAxMi43NSAzLjc1VjUuOTM3NUgxMS42NTYyQzEwLjU3NSA1LjkzNzUgMTAuMTI1IDYuNjU2MjUgMTAuMTI1IDcuNDA2MjVWOUgxMi42NTYyTDEyLjE4NzUgMTEuNjI1SDEwLjEyNVYxNy45Mzc1QzE0LjQgMTcuMjUgMTggMTMuNSAxOCA5WiIgZmlsbD0iIzE4NzdGMiIvPgo8L3N2Zz4K');
        }

        .apple::before {
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTgiIGhlaWdodD0iMTgiIHZpZXdCb3g9IjAgMCAxOCAxOCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjUgNi43NUMxMy41IDYuNzUgMTIuNzUgNi4zNzUgMTEuNjI1IDYuMzc1QzEwLjUgNi4zNzUgOS43NSA2Ljc1IDkuNzUgNi43NUMxMC4xMjUgNi4zNzUgMTAuNSA2IDEwLjUgNS4yNUMxMC41IDQuNSAxMC4xMjUgMy43NSA5LjM3NSAzLjc1QzguNjI1IDMuNzUgOC4yNSA0LjUgOC4yNSA1LjI1QzguMjUgNiA4LjYyNSA2LjM3NSA5IDYuNzVDOSA2Ljc1IDguMjUgNi4zNzUgNy4xMjUgNi4zNzVDNiA2LjM3NSA1LjI1IDYuNzUgNS4yNSA2Ljc1QzUuMjUgNi43NSA0LjUgNy4xMjUgNC41IDhDNC41IDguODc1IDUuMjUgOS4zNzUgNS4yNSA5LjM3NUM1LjI1IDkuMzc1IDYgOS43NSA3LjEyNSA5Ljc1QzguMjUgOS43NSA5IDkuMzc1IDkgOS4zNzVDOSA5LjM3NSA5Ljc1IDkuNzUgMTAuODc1IDkuNzVDMTIgOS43NSAxMi43NSA5LjM3NSAxMi43NSA5LjM3NUMxMi43NSA5LjM3NSAxMy41IDguODc1IDEzLjUgOEMxMy41IDcuMTI1IDEyLjc1IDYuNzUgMTIuNzUgNi43NVoiIGZpbGw9IiMwMDAiLz4KPC9zdmc+Cg==');
        }

        /* Divider */
        .divider {
            display: flex;
            align-items: center;
            margin: 20px 0;
            color: var(--secondary-color);
            font-size: 14px;
        }

        .divider::before,
        .divider::after {
            content: '';
            flex: 1;
            height: 1px;
            background: var(--border-color);
        }

        .divider::before {
            margin-right: 16px;
        }

        .divider::after {
            margin-left: 16px;
        }

        /* Form */
        .form-group {
            margin-bottom: 16px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            color: #333;
            font-size: 14px;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            height: 44px;
            padding: 12px 16px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            font-family: inherit;
            transition: border-color 0.2s ease;
            box-sizing: border-box;
        }

        .form-input:focus {
            outline: none;
            border-color: var(--primary-color);
        }

        .form-input::placeholder {
            color: #999;
        }

        .forgot-password {
            display: block;
            text-align: right;
            color: var(--primary-color);
            text-decoration: none;
            font-size: 14px;
            margin: 8px 0 20px 0;
        }

        .forgot-password:hover {
            text-decoration: underline;
        }

        /* Login Button */
        .login-btn {
            width: 100%;
            height: 44px;
            background: var(--primary-color);
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s ease;
            margin-bottom: 20px;
        }

        .login-btn:hover {
            background: var(--primary-hover);
        }

        /* Sign Up Link */
        .signup-link {
            text-align: center;
            color: #666;
            font-size: 14px;
        }

        .signup-link a {
            color: var(--primary-color);
            text-decoration: none;
            font-weight: 500;
        }

        /* B2C Content Overrides */
        #api {
            margin-top: 0;
        }

        /* Override B2C social buttons with higher specificity */
        div#api .accountButton,
        div#api button.accountButton,
        div#api .claims-provider-list-buttons .accountButton,
        div#api .claims-provider-list-buttons button.accountButton,
        body div#api .accountButton,
        body div#api button.accountButton {
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            width: 100% !important;
            height: 44px !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            background: white !important;
            color: #333 !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            margin: 0 0 12px 0 !important;
            padding: 0 16px !important;
            text-decoration: none !important;
            transition: all 0.2s ease !important;
            box-sizing: border-box !important;
            min-height: 44px !important;
        }

        div#api .accountButton:hover,
        div#api button.accountButton:hover,
        body div#api .accountButton:hover,
        body div#api button.accountButton:hover {
            border-color: #ccc !important;
            background: #f9f9f9 !important;
        }

        /* Add icons to social buttons */
        div#api button[id*="Google"]:before,
        div#api .accountButton[id*="Google"]:before {
            content: '' !important;
            width: 20px !important;
            height: 20px !important;
            margin-right: 12px !important;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTIyLjU2IDEyLjI1QzIyLjU2IDExLjQ3IDIyLjQ5IDEwLjcyIDIyLjM2IDEwSDEyVjE0LjI2SDE3LjkyQzE3LjY2IDE1LjYgMTYuOTIgMTYuNzQgMTUuODQgMTcuNVYyMC4yNkgxOS4yOEMyMS4zNiAxOC40MyAyMi41NiAxNS42IDIyLjU2IDEyLjI1WiIgZmlsbD0iIzQyODVGNCIvPgo8cGF0aCBkPSJNMTIgMjNDMTUuMjQgMjMgMTcuOTUgMjEuOTIgMTkuMjggMjAuMjZMMTUuODQgMTcuNUMxNC43OCAxOC4xMyAxMy40NyAxOC41IDEyIDE4LjVDOC44NyAxOC41IDYuMjIgMTYuNjQgNS4yNyAxMy45NEgyLjc2VjE2Ljc0QzQuMDUgMTkuMzEgNy43OCAyMyAxMiAyM1oiIGZpbGw9IiMzNEE4NTMiLz4KPHBhdGggZD0iTTUuMjcgMTMuOTRDNS4wNyAxMy4zMSA0Ljk2IDEyLjY2IDQuOTYgMTJTNS4wNyAxMC42OSA1LjI3IDEwLjA2VjcuMjZIMi43NkMxLjk5IDguNzYgMS41IDEwLjM0IDEuNSAxMlMxLjk5IDE1LjI0IDIuNzYgMTYuNzRMNS4yNyAxMy45NFoiIGZpbGw9IiNGQkJDMDQiLz4KPHBhdGggZD0iTTEyIDUuNUMxMy42MiA1LjUgMTUuMDYgNi4wNCAxNi4yIDcuMTJMMTkuMjggNC4wNEMxNy45NSAyLjE4IDE1LjI0IDEgMTIgMUM3Ljc4IDEgNC4wNSA0LjY5IDIuNzYgNy4yNkw1LjI3IDEwLjA2QzYuMjIgNy4zNiA4Ljg3IDUuNSAxMiA1LjVaIiBmaWxsPSIjRUE0MzM1Ii8+Cjwvc3ZnPgo=') !important;
            background-size: contain !important;
            background-repeat: no-repeat !important;
            background-position: center !important;
        }

        div#api button[id*="Facebook"]:before,
        div#api .accountButton[id*="Facebook"]:before {
            content: '' !important;
            width: 20px !important;
            height: 20px !important;
            margin-right: 12px !important;
            background-image: url('data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTI0IDEyQzI0IDUuMzczIDE4LjYyNyAwIDEyIDBTMCA1LjM3MyAwIDEyQzAgMTcuOTkgNC4zODggMjIuOTU0IDEwLjEyNSAyMy44NTRWMTUuNDY5SDcuMDc4VjEySDEwLjEyNVY5LjM1NkMxMC4xMjUgNi4zNDkgMTEuOTE2IDQuNjg4IDE0LjY1OCA0LjY4OEMxNS45NyA0LjY4OCAxNy4zNDQgNC45MjIgMTcuMzQ0IDQuOTIyVjcuODc1SDE1LjgzQzE0LjMxIDcuODc1IDEzLjg3NSA4Ljc5OSAxMy44NzUgOS43NVYxMkgxNy4yMDNMMTYuNjcxIDE1LjQ2OUgxMy44NzVWMjMuODU0QzE5LjYxMiAyMi45NTQgMjQgMTcuOTkgMjQgMTJaIiBmaWxsPSIjMTg3N0YyIi8+Cjwvc3ZnPgo=') !important;
            background-size: contain !important;
            background-repeat: no-repeat !important;
            background-position: center !important;
        }

        /* Override B2C form elements */
        #api .entry-item {
            margin-bottom: 16px !important;
        }

        #api .entry-item label {
            display: block !important;
            margin-bottom: 6px !important;
            color: #333 !important;
            font-size: 14px !important;
            font-weight: 500 !important;
        }

        #api input[type="email"],
        #api input[type="password"],
        #api input[type="text"] {
            width: 100% !important;
            height: 44px !important;
            padding: 12px 16px !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            box-sizing: border-box !important;
        }

        #api input:focus {
            outline: none !important;
            border-color: var(--primary-color) !important;
        }

        /* Override B2C button */
        #api #next,
        #api button[type="submit"] {
            width: 100% !important;
            height: 44px !important;
            background: var(--primary-color) !important;
            color: white !important;
            border: none !important;
            border-radius: 4px !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            margin-bottom: 20px !important;
        }

        #api #next:hover,
        #api button[type="submit"]:hover {
            background: var(--primary-hover) !important;
        }

        /* Override B2C divider */
        #api .divider {
            display: flex !important;
            align-items: center !important;
            margin: 24px 0 !important;
            color: #666 !important;
            font-size: 14px !important;
        }

        #api .divider h2 {
            display: flex !important;
            align-items: center !important;
            margin: 0 !important;
            font-size: 14px !important;
            font-weight: 500 !important;
            width: 100% !important;
            color: #666 !important;
        }

        #api .divider h2:before,
        #api .divider h2:after {
            content: '' !important;
            flex: 1 !important;
            height: 1px !important;
            background: #ddd !important;
        }

        #api .divider h2:before {
            margin-right: 16px !important;
        }

        #api .divider h2:after {
            margin-left: 16px !important;
        }

        /* Override B2C create account link */
        #api .create {
            text-align: center !important;
            color: #666 !important;
            font-size: 14px !important;
        }

        #api .create a {
            color: var(--primary-color) !important;
            text-decoration: none !important;
        }

        /* Hide unwanted B2C elements */
        #api .intro,
        #api .heading {
            display: none !important;
        }

        .signup-link a:hover {
            text-decoration: underline;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .container {
                padding: 15px;
            }
            
            .login-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-card">
            <!-- Logo -->
            <div class="logo">
                <div class="logo-icon">S</div>
                Super App
            </div>

            <!-- Sign In Title -->
            <h1 class="signin-title">Sign in</h1>
            <p class="signin-subtitle">Sign in with your social account</p>

            <!-- B2C will inject content here -->
            <div id="api"></div>
        </div>
    </div>

    <script>
        // Wait for B2C content to load and customize it
        document.addEventListener('DOMContentLoaded', function() {
            function customizeB2CContent() {
                // Hide default B2C elements we don't want
                const elementsToHide = ['.intro', '.heading'];
                elementsToHide.forEach(selector => {
                    const elements = document.querySelectorAll(selector);
                    elements.forEach(el => el.style.display = 'none');
                });

                // Customize social buttons with more specific selectors
                const socialButtons = document.querySelectorAll('#api .accountButton, #api button.accountButton');
                socialButtons.forEach(btn => {
                    const btnId = btn.id || '';
                    const btnText = btn.textContent || '';

                    if (btnId.includes('Google') || btnText.includes('Google')) {
                        btn.textContent = 'Continue with Google';
                        btn.style.cssText = `
                            display: flex !important;
                            align-items: center !important;
                            justify-content: flex-start !important;
                            width: 100% !important;
                            height: 44px !important;
                            border: 1px solid #ddd !important;
                            border-radius: 4px !important;
                            background: white !important;
                            color: #333 !important;
                            font-size: 14px !important;
                            font-weight: 500 !important;
                            margin: 0 0 12px 0 !important;
                            padding: 0 16px !important;
                            text-decoration: none !important;
                            box-sizing: border-box !important;
                        `;
                    } else if (btnId.includes('Facebook') || btnText.includes('Facebook')) {
                        btn.textContent = 'Continue with Facebook';
                        btn.style.cssText = `
                            display: flex !important;
                            align-items: center !important;
                            justify-content: flex-start !important;
                            width: 100% !important;
                            height: 44px !important;
                            border: 1px solid #ddd !important;
                            border-radius: 4px !important;
                            background: white !important;
                            color: #333 !important;
                            font-size: 14px !important;
                            font-weight: 500 !important;
                            margin: 0 0 12px 0 !important;
                            padding: 0 16px !important;
                            text-decoration: none !important;
                            box-sizing: border-box !important;
                        `;
                    } else if (btnId.includes('Apple') || btnText.includes('Apple')) {
                        btn.textContent = 'Continue with Apple';
                        btn.style.cssText = `
                            display: flex !important;
                            align-items: center !important;
                            justify-content: flex-start !important;
                            width: 100% !important;
                            height: 44px !important;
                            border: 1px solid #ddd !important;
                            border-radius: 4px !important;
                            background: white !important;
                            color: #333 !important;
                            font-size: 14px !important;
                            font-weight: 500 !important;
                            margin: 0 0 12px 0 !important;
                            padding: 0 16px !important;
                            text-decoration: none !important;
                            box-sizing: border-box !important;
                        `;
                    }
                });

                // Customize form labels
                const emailLabel = document.querySelector('label[for="email"]');
                if (emailLabel) emailLabel.textContent = 'Email';

                const passwordLabel = document.querySelector('label[for="password"]');
                if (passwordLabel) passwordLabel.textContent = 'Password';

                // Customize placeholders
                const emailInput = document.getElementById('email');
                if (emailInput) {
                    emailInput.placeholder = 'Enter your email';
                    emailInput.style.cssText = `
                        width: 100% !important;
                        height: 44px !important;
                        padding: 12px 16px !important;
                        border: 1px solid #ddd !important;
                        border-radius: 4px !important;
                        font-size: 14px !important;
                        box-sizing: border-box !important;
                    `;
                }

                const passwordInput = document.getElementById('password');
                if (passwordInput) {
                    passwordInput.placeholder = 'Enter your password';
                    passwordInput.style.cssText = `
                        width: 100% !important;
                        height: 44px !important;
                        padding: 12px 16px !important;
                        border: 1px solid #ddd !important;
                        border-radius: 4px !important;
                        font-size: 14px !important;
                        box-sizing: border-box !important;
                    `;
                }

                // Customize login button
                const loginBtn = document.getElementById('next');
                if (loginBtn) {
                    loginBtn.textContent = 'Log In';
                    loginBtn.style.cssText = `
                        width: 100% !important;
                        height: 44px !important;
                        background: rgb(0, 122, 198) !important;
                        color: white !important;
                        border: none !important;
                        border-radius: 4px !important;
                        font-size: 14px !important;
                        font-weight: 600 !important;
                        margin-bottom: 20px !important;
                        cursor: pointer !important;
                    `;
                }

                // Customize divider
                const divider = document.querySelector('.divider h2');
                if (divider) divider.textContent = 'OR';

                // Customize sign up link
                const createAccount = document.querySelector('.create');
                if (createAccount) {
                    const paragraph = createAccount.querySelector('p');
                    if (paragraph) {
                        paragraph.innerHTML = "Don't have an account? <a href='#' style='color: rgb(0, 122, 198); text-decoration: none;'>Sign up now</a>";
                        createAccount.style.cssText = `
                            text-align: center !important;
                            color: #666 !important;
                            font-size: 14px !important;
                        `;
                    }
                }
            }

            // Run customization immediately
            customizeB2CContent();

            // Run customization after a short delay to catch late-loading content
            setTimeout(customizeB2CContent, 100);
            setTimeout(customizeB2CContent, 500);
            setTimeout(customizeB2CContent, 1000);

            // Use MutationObserver to handle dynamic content changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        setTimeout(customizeB2CContent, 50);
                    }
                });
            });

            const apiElement = document.getElementById('api');
            if (apiElement) {
                observer.observe(apiElement, {
                    childList: true,
                    subtree: true
                });
            }
        });
    </script>
</body>
</html>