﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using System.Security.Claims;


namespace MRI.OTA.Application.Services
{
    /// <summary>
    /// User service
    /// </summary>
    public class UserService : BaseService<Users, UserModel, int>, IUserService
    {
        private readonly IUserRepository _userRepository;
        private readonly IMapper _mapper;
        private readonly ILogger _logger;
        private readonly IJwtTokenValidator _jwtTokenValidator;
        private readonly IAzureB2CTokenService _azureB2CTokenService;
        private readonly IImageStorageService _imageStorageService;
        private readonly IPropertTreeService _propertTreeService;
        private readonly TaskContext _taskContext;

        /// <summary>
        /// User service constructor
        /// </summary>
        /// <param name="logger">Logger instance</param>
        /// <param name="repository">User repository</param>
        /// <param name="mapper">Mapper instance</param>
        /// <param name="configuration">Configuration instance</param>
        public UserService(
            ILogger<UserService> logger,
            IUserRepository repository,
            IMapper mapper,
            IAzureB2CTokenService azureB2CTokenService,
            IJwtTokenValidator jwtTokenValidator,
            IImageStorageService imageStorageService,
            IPropertTreeService propertTreeService, TaskContext taskContext)
            : base(logger, repository, mapper)
        {
            _userRepository = repository ?? throw new ArgumentNullException(nameof(repository));
            _mapper = mapper ?? throw new ArgumentNullException(nameof(mapper));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _jwtTokenValidator = jwtTokenValidator ?? throw new ArgumentNullException(nameof(jwtTokenValidator));
            _azureB2CTokenService = azureB2CTokenService ?? throw new ArgumentNullException(nameof(azureB2CTokenService));
            _imageStorageService = imageStorageService ?? throw new ArgumentNullException(nameof(imageStorageService));
            _propertTreeService = propertTreeService ?? throw new ArgumentNullException(nameof(propertTreeService));
            _taskContext = taskContext;
        }

        /// <summary>
        /// Get all users
        /// </summary>
        /// <returns>List of user models</returns>
        public async Task<List<UserModel>> GetAllUsers()
        {
            var result = await _userRepository.GetAllUsers();
            return _mapper.Map<List<UserModel>>(result);
        }

        /// <summary>
        /// Create user
        /// </summary>
        /// <param name="user">User model to create</param>
        /// <returns>Success or error code</returns>
        public async Task<int> CreateUser(AddUserModel user)
        {
            if (user == null)
            {
                _logger.LogError("User model cannot be null");
                return Constants.Error;
            }

            var usr = _mapper.Map<Users>(user);
            var result = await _userRepository.AddAsync(usr);

            return result > 0 ? Constants.Success : Constants.Error;
        }

        /// <summary>
        /// Create user profile from ID token
        /// </summary>
        /// <param name="idTokenModel">ID token model</param>
        /// <returns>User profile model</returns>
        public async Task<ViewUserProfileModel> CreateUserProfile(IdTokenModel idTokenModel)
        {
            try
            {
                ValidateIdToken(idTokenModel);
                
                var claims = await _jwtTokenValidator.GetClaims(idTokenModel.IdToken);
                if (claims.Count == 0)
                {
                    return new ViewUserProfileModel();
                }
                
                var user = ExtractUserFromClaims(claims);
                if (string.IsNullOrEmpty(user.UserEmail))
                {
                    return user;
                }
                
                if (string.IsNullOrEmpty(user.ProviderId))
                {
                    throw new ArgumentNullException(nameof(user.ProviderId), "ProviderId cannot be null or empty.");
                }
                
                return await ProcessUserProfile(user);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error adding user profile: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// Validate ID token model
        /// </summary>
        /// <param name="idTokenModel"></param>
        /// <exception cref="ArgumentNullException"></exception>
        private void ValidateIdToken(IdTokenModel idTokenModel)
        {
            if (idTokenModel == null)
            {
                throw new ArgumentNullException(nameof(idTokenModel));
            }
            
            if (string.IsNullOrEmpty(idTokenModel.IdToken))
            {
                throw new ArgumentNullException(nameof(idTokenModel.IdToken), "IdToken cannot be null or empty.");
            }
        }

        /// <summary>
        /// Extract user information from claims
        /// </summary>
        /// <param name="claims"></param>
        /// <returns></returns>
        private ViewUserProfileModel ExtractUserFromClaims(IReadOnlyCollection<Claim> claims)
        {
            var user = new ViewUserProfileModel();
            
            foreach (var claim in claims)
            {
                switch (claim.Type)
                {
                    case "sub":
                        user.ProviderId = claim.Value;
                        break;
                    case "idp":
                        user.ProviderTypeId = GetProviderType(claim.Value);
                        break;
                    case "given_name":
                        user.DisplayName = claim.Value;
                        break;
                    case "emails":
                        user.UserEmail = claim.Value;
                        break;
                    case "idp_access_token":
                        user.IdpAccessToken = claim.Value;
                        break;
                }
            }
            
            // Set default provider type if not found
            if (user.ProviderTypeId == 0)
            {
                user.ProviderTypeId = Constants.ProviderType["LocalAccount"];
            }
            
            return user;
        }

        /// <summary>
        /// Process user profile
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task<ViewUserProfileModel> ProcessUserProfile(ViewUserProfileModel user)
        {
            var existingUser = await _userRepository.GetUserDetails(user);
            
            if (existingUser != null)
            {
                return await UpdateExistingUser(user, existingUser);
            }
            
            return await CreateNewUser(user);
        }

        /// <summary>
        /// Update existing user profile
        /// </summary>
        /// <param name="user"></param>
        /// <param name="existingUser"></param>
        /// <returns></returns>
        private async Task<ViewUserProfileModel> UpdateExistingUser(ViewUserProfileModel user, ViewUserProfileModel existingUser)
        {
            if (user.UserEmail != existingUser.UserEmail)
            {
                return await UpdateUserEmail(user, existingUser);
            }
            
            if (user.ProviderTypeId != existingUser.ProviderTypeId)
            {
                return await UpdateProviderType(user, existingUser);
            }

            existingUser.IdpAccessToken = user.IdpAccessToken;
            return existingUser;
        }

        /// <summary>
        /// Update user email
        /// </summary>
        /// <param name="user"></param>
        /// <param name="existingUser"></param>
        /// <returns></returns>
        private async Task<ViewUserProfileModel> UpdateUserEmail(ViewUserProfileModel user, ViewUserProfileModel existingUser)
        {
            existingUser.UserEmail = user.UserEmail;
            var updateUser = _mapper.Map<Users>(user);
            await _userRepository.UpdateUserEmail(updateUser);
            user.UserId = existingUser.UserId;
            return user;
        }

        /// <summary>
        /// Update provider type
        /// </summary>
        /// <param name="user"></param>
        /// <param name="existingUser"></param>
        /// <returns></returns>
        private async Task<ViewUserProfileModel> UpdateProviderType(ViewUserProfileModel user, ViewUserProfileModel existingUser)
        {
            existingUser.ProviderTypeId = user.ProviderTypeId;
            var updateUser = _mapper.Map<Users>(user);
            await _userRepository.UpdateUserProviderType(updateUser);
            user.UserId = existingUser.UserId;
            user.ProviderName = GetProviderTypeName(updateUser.ProviderTypeId);
            return user;
        }

        /// <summary>
        /// Create new user profile
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        private async Task<ViewUserProfileModel> CreateNewUser(ViewUserProfileModel user)
        {
            user.IsActive = true;
            user.PreferredContactEmail = user.UserEmail;
            var addUsers = _mapper.Map<Users>(user);
            var result = await _userRepository.AddAsync(addUsers);
            
            if (result > 0)
            {
                user.UserId = result;
                user.ProviderName = GetProviderTypeName(user.ProviderTypeId);
            }
            
            return user;
        }

        /// <summary>
        /// Get provider type ID from string
        /// </summary>
        /// <param name="providerType"></param>
        /// <returns></returns>
        private int GetProviderType(string providerType)
        {
            return Constants.ProviderType.TryGetValue(providerType, out int providerId) 
                ? providerId 
                : 0;
        }

        /// <summary>
        /// Get provider type name from ID
        /// </summary>
        /// <param name="providerId"></param>
        /// <returns></returns>
        private string GetProviderTypeName(int providerId)
        {
            return Constants.ProviderTypeName.TryGetValue(providerId, out string providerName) 
                ? providerName 
                : string.Empty;
        }

        public async Task<int> UpdateUserTermsAndCondition(TermsConditionModel updateTermsConditionModel)
        {
            try
            {
                var users = _mapper.Map<Users>(updateTermsConditionModel);
                var result = await _userRepository.UpdateUserTermsAndCondition(users);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating users terms and conditions : {ex.Message}");
            }
            return -1;
        }

        public async Task<int> UpdateUserProfileSettings(UserProfileSettingsModel userProfileSettings)
        {
            try
            {
                var users = _mapper.Map<Users>(userProfileSettings);
                var result = await _userRepository.UpdateUserProfileSettings(users);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating users profiles : {ex.Message}");
            }
            return -1;
        }

        public async Task<bool> DeleteAccount(int userId, string providerId)
        {
            try
            {
                // Remove user from Azure B2C
                _logger.LogInformation($"Removing user from Azure B2C with providerId: {providerId}");
                
                if (string.IsNullOrEmpty(providerId))
                {
                    _logger.LogError("Cannot delete user from Azure B2C: providerId is null or empty");
                }
                else
                {
                    try
                    {
                        // Delete user using direct REST API call instead of Graph client
                        await DeleteUserFromB2C(providerId);
                        
                        _logger.LogInformation($"Successfully removed user from Azure B2C with providerId: {providerId}");
                    }
                    catch (Exception b2cEx)
                    {
                        // Log the error but continue with local account deletion
                        _logger.LogError($"Error removing user from Azure B2C: {b2cEx.Message}");
                        _logger.LogError($"Proceeding with local account deletion anyway.");
                    }
                }

                // Remove from all source applications
                await _propertTreeService.RemoveUserAccount(userId, providerId);

                // Remove images from blob container
                await _imageStorageService.DeletePropertyImagesByUserId(userId);

                // Remove the user data from the database
                 var result = await _userRepository.DeleteAccount(userId, providerId);
                 return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error deleting user account: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Deletes a user from Azure B2C/Entra ID using direct REST API call
        /// </summary>
        /// <param name="userId">The user ID (from Azure B2C/Entra ID)</param>
        private async Task DeleteUserFromB2C(string userId)
        {


            using var client = new HttpClient();
            var accessToken = _azureB2CTokenService.GetAccessTokenAsync().Result;

            // Delete the user
            var deleteEndpoint = $"https://graph.microsoft.com/v1.0/users/{userId}";
            using var deleteRequest = new HttpRequestMessage(HttpMethod.Delete, deleteEndpoint);
            deleteRequest.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
            
            var deleteResponse = await client.SendAsync(deleteRequest);
            
            if (!deleteResponse.IsSuccessStatusCode)
            {
                var errorContent = await deleteResponse.Content.ReadAsStringAsync();
                throw new Exception($"Failed to delete user: {deleteResponse.StatusCode}, Error: {errorContent}");
            }
        }

        public async Task<int> AddUpdateUserDevice(UserDeviceDetail deviceModel)
        {
            try
            {
                return await _userRepository.AddUpdateUserDevice(deviceModel, _taskContext.UserId);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in Add Update Device Details : {ex.Message}");
                return -1;
            }
        }
        public async Task<bool> DeleteUserDeviceInfo(string deviceId)
        {
            try
            {
                return await _userRepository.DeleteUserDeviceInfo(_taskContext.UserId, deviceId);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in Deleting Device Details : {ex.Message}");
                return false;
            }
        }
    }
}
