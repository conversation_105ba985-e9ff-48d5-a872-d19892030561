﻿using System.Text.Json;
using System.Text.Json.Serialization;
using MRI.OTA.Worker.Interface;

namespace MRI.OTA.Worker.Service
{
    public class AzureB2CTokenService : IAzureB2CTokenService
    {
        private readonly string _tenantname;
        private readonly string _policy;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _scope;
        private readonly IHttpClientFactory _httpClientFactory;

        public AzureB2CTokenService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
            _tenantname = Environment.GetEnvironmentVariable("TenantName") ?? throw new ArgumentNullException(nameof(_tenantname), "TenantName is not configured.");
            _clientId = Environment.GetEnvironmentVariable("ClientId") ?? throw new ArgumentNullException(nameof(_clientId), "ClientId is not configured.");
            _clientSecret = Environment.GetEnvironmentVariable("ClientSecret") ?? throw new ArgumentNullException(nameof(_clientSecret), "ClientSecret is not configured.");
            _policy = Environment.GetEnvironmentVariable("SignUpSignInPolicyId") ?? throw new ArgumentNullException(nameof(_policy), "SignUpSignInPolicyId is not configured.");
            _scope = $"https://{_tenantname}.onmicrosoft.com/{_clientId}/.default"; // Add the required scope  
        }

        public async Task<string> GetAccessTokenAsync() 
        {
            var client = _httpClientFactory.CreateClient("WorkerClient");
            var tokenEndpoint = $"https://{_tenantname}.b2clogin.com/{_tenantname}.onmicrosoft.com/{_policy}/oauth2/v2.0/token";
            var requestBody = new FormUrlEncodedContent(new[]
            {
                   new KeyValuePair<string, string>("client_id", _clientId),
                   new KeyValuePair<string, string>("client_secret", _clientSecret),
                   new KeyValuePair<string, string>("grant_type", "client_credentials"),
                   new KeyValuePair<string, string>("scope", _scope) // Include the scope in the request  
               });

            var response = await client.PostAsync(tokenEndpoint, requestBody);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Failed to retrieve token: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent);

            return tokenResponse?.AccessToken ?? throw new Exception("Access token not found in response.");
        }

        private class TokenResponse
        {
            [JsonPropertyName("access_token")]
            public string AccessToken { get; set; } = null!;

            [JsonPropertyName("token_type")]
            public string TokenType { get; set; } = null!;

            [JsonPropertyName("expires_in")]
            public int ExpiresIn { get; set; }

            [JsonPropertyName("ext_expires_in")]
            public int ExtExpiresIn { get; set; }
        }
    }
}
