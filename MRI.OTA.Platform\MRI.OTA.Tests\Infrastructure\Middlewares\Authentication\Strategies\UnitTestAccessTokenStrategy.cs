using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies;
using System.IO;
using System.Security.Claims;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares.Authentication.Strategies
{
    public class UnitTestAccessTokenStrategy
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<IJwtTokenValidator> _jwtTokenValidatorMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly Mock<IResponseGenerator> _responseGeneratorMock;
        private readonly AccessTokenStrategy _strategy;

        public UnitTestAccessTokenStrategy()
        {
            _nextMock = new Mock<RequestDelegate>();
            _jwtTokenValidatorMock = new Mock<IJwtTokenValidator>();
            _loggerMock = new Mock<ILogger>();
            _responseGeneratorMock = new Mock<IResponseGenerator>();

            _strategy = new AccessTokenStrategy(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _responseGeneratorMock.Object);
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenNextIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AccessTokenStrategy(
                null!,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _responseGeneratorMock.Object));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenJwtTokenValidatorIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AccessTokenStrategy(
                _nextMock.Object,
                null!,
                _loggerMock.Object,
                _responseGeneratorMock.Object));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenLoggerIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AccessTokenStrategy(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                null!,
                _responseGeneratorMock.Object));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenResponseGeneratorIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AccessTokenStrategy(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                null!));
        }

        [Fact]
        public void CanHandle_ReturnsTrue_WhenAccessKeyAndTokenPresent()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessToken"] = "test-token";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessKeyMissing()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessToken"] = "test-token";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessTokenMissing()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessKeyEmpty()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = string.Empty;
            context.Request.Headers["AccessToken"] = "test-token";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessTokenEmpty()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessToken"] = string.Empty;

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenBothHeadersMissing()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledAndAuthenticated_WhenValidCredentials()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "valid-key";
            var accessToken = "valid-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(true);

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.True(authenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledButNotAuthenticated_WhenInvalidCredentials()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "invalid-key";
            var accessToken = "invalid-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(false);

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.False(authenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_CallsValidateAccessToken_WithCorrectParameters()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "test-key";
            var accessToken = "test-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(true);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _jwtTokenValidatorMock.Verify(v => v.ValidateAccessToken(accessKey, accessToken), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_SetsUserPrincipal_WhenValidCredentials()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "valid-key";
            var accessToken = "valid-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(true);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.NotNull(context.User);
            Assert.True(context.User.Identity!.IsAuthenticated);

            var nameClaim = context.User.FindFirst(ClaimTypes.Name);
            var actorClaim = context.User.FindFirst(ClaimTypes.Actor);

            Assert.NotNull(nameClaim);
            Assert.NotNull(actorClaim);
            Assert.Equal(accessKey, nameClaim.Value);
            Assert.Equal(accessToken, actorClaim.Value);
        }

        [Fact]
        public async Task AuthenticateAsync_CallsNext_WhenValidCredentials()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "valid-key";
            var accessToken = "valid-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(true);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_WritesUnauthorizedResponse_WhenInvalidCredentials()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "invalid-key";
            var accessToken = "invalid-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(false);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _responseGeneratorMock.Verify(r => r.WriteUnauthorizedResponse(
                context,
                It.IsAny<string>(),
                StatusCodes.Status401Unauthorized), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_DoesNotCallNext_WhenInvalidCredentials()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "invalid-key";
            var accessToken = "invalid-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(false);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _nextMock.Verify(n => n(It.IsAny<HttpContext>()), Times.Never);
        }

        [Fact]
        public async Task AuthenticateAsync_LogsInformation()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "test-key";
            var accessToken = "test-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(true);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Attempting access token authentication")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_HandlesValidationException()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "test-key";
            var accessToken = "test-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ThrowsAsync(new InvalidOperationException("Validation error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _strategy.AuthenticateAsync(context));
        }

        [Fact]
        public async Task AuthenticateAsync_HandlesResponseGeneratorException()
        {
            // Arrange
            var context = CreateHttpContext();
            var accessKey = "invalid-key";
            var accessToken = "invalid-token";

            context.Request.Headers["AccessKey"] = accessKey;
            context.Request.Headers["AccessToken"] = accessToken;

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken(accessKey, accessToken))
                .ReturnsAsync(false);

            _responseGeneratorMock.Setup(r => r.WriteUnauthorizedResponse(
                It.IsAny<HttpContext>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
                .ThrowsAsync(new InvalidOperationException("Response generator error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _strategy.AuthenticateAsync(context));
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.Response.Body = new MemoryStream();
            return context;
        }
    }
}
