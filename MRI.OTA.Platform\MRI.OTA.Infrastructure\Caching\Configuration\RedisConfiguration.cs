﻿﻿namespace MRI.OTA.Infrastructure.Caching.Configuration
{
    /// <summary>
    /// Configuration options for Redis Cache
    /// </summary>
    public class RedisConfiguration
    {
        /// <summary>
        /// The Redis connection string
        /// </summary>
        public string ConnectionString { get; set; } = string.Empty;

        /// <summary>
        /// Default cache expiration time in minutes
        /// </summary>
        public int DefaultExpirationMinutes { get; set; } = 60;
    }
}
