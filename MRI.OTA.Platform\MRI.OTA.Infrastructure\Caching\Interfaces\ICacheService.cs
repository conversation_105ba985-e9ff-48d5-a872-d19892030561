﻿﻿namespace MRI.OTA.Infrastructure.Caching.Interfaces
{
    /// <summary>
    /// Interface for cache service operations
    /// </summary>
    public interface ICacheService
    {
        /// <summary>
        /// Gets a cached item by key
        /// </summary>
        /// <typeparam name="T">Type of cached item</typeparam>
        /// <param name="key">Key of cached item</param>
        /// <returns>Cached value or default(T) if not found</returns>
        Task<T?> GetAsync<T>(string key);

        /// <summary>
        /// Sets a cached item
        /// </summary>
        /// <typeparam name="T">Type of item to cache</typeparam>
        /// <param name="key">Key to store item under</param>
        /// <param name="value">Item to cache</param>
        /// <param name="expirationMinutes">Minutes until item expires (null for default)</param>
        /// <returns>Task representing the async operation</returns>
        Task SetAsync<T>(string key, T value, int? expirationMinutes = null);

        /// <summary>
        /// Removes a cached item by key
        /// </summary>
        /// <param name="key">Key of cached item</param>
        /// <returns>Task representing the async operation</returns>
        Task RemoveAsync(string key);

        /// <summary>
        /// Checks if a key exists in the cache
        /// </summary>
        /// <param name="key">Key to check</param>
        /// <returns>True if key exists, false otherwise</returns>
        Task<bool> ExistsAsync(string key);

        /// <summary>
        /// Refreshes the expiration time of a cached item
        /// </summary>
        /// <param name="key">Key of cached item</param>
        /// <param name="expirationMinutes">Minutes until item expires (null for default)</param>
        /// <returns>True if key was found and refreshed, false otherwise</returns>
        Task<bool> RefreshExpirationAsync(string key, int? expirationMinutes = null);
    }
}
