﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using MRI.OTA.API.Controllers.Invitation.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.Invitations
{
    public class UnitTestInvitationController
    {
        private readonly Mock<IInvitationService> _mockInvitationService;
        private readonly InvitationController _controller;
        private readonly TelemetryClient _telemetryClient;

        public UnitTestInvitationController()
        {
            _mockInvitationService = new Mock<IInvitationService>();
            
            // Create a TelemetryClient for testing with NullChannel to avoid actual telemetry
            var telemetryConfiguration = new TelemetryConfiguration();
            telemetryConfiguration.TelemetryChannel = new NullTelemetryChannel();
            _telemetryClient = new TelemetryClient(telemetryConfiguration);
            
            _controller = new InvitationController(_mockInvitationService.Object, _telemetryClient);
        }

        [Fact]
        public async Task SendInvitation_ReturnsOkResult_WhenInvitationIsSent()
        {
            // Arrange
            var invitationRequest = new InvitationRequestModel();
            _mockInvitationService.Setup(service => service.SendInvitationAsync(invitationRequest))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.SendInvitation(invitationRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Invitation sent successfully", apiResponse.Message);
        }

        [Fact]
        public async Task SendInvitation_ReturnsBadRequest_WhenInvitationIsNotSent()
        {
            // Arrange
            var invitationRequest = new InvitationRequestModel();
            _mockInvitationService.Setup(service => service.SendInvitationAsync(invitationRequest))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.SendInvitation(invitationRequest);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("Invitation not sent", apiResponse.Message);
            Assert.Contains("The invitation could not be sent.", apiResponse.Errors);
        }

        [Fact]
        public async Task AcceptInvitation_ReturnsOkResult_WhenInvitationIsAccepted()
        {
            // Arrange
            var acceptInvitationRequest = new AcceptInvitationModel();
            _mockInvitationService.Setup(service => service.AcceptInvitationAsync(acceptInvitationRequest))
                .ReturnsAsync((1, null)); // Updated to match the return type of AcceptInvitationAsync

            // Act
            var result = await _controller.AcceptInvitation(acceptInvitationRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Invitation accepted successfully", apiResponse.Message);
        }

        [Fact]
        public async Task AcceptInvitation_ReturnsBadRequest_WhenInvitationIsNotAccepted()
        {
            // Arrange
            var acceptInvitationRequest = new AcceptInvitationModel();
            _mockInvitationService.Setup(service => service.AcceptInvitationAsync(acceptInvitationRequest))
                .ReturnsAsync((0, null)); // Updated to match the return type of AcceptInvitationAsync

            // Act
            var result = await _controller.AcceptInvitation(acceptInvitationRequest);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("Invitation not accepted", apiResponse.Message);
            Assert.Contains("The invitation could not be accepted.", apiResponse.Errors);
        }

        [Fact]
        public async Task SendInvitation_ReturnsUnauthorized_WhenUnauthorizedAccessExceptionThrown()
        {
            // Arrange
            var invitationRequest = new InvitationRequestModel();
            _mockInvitationService.Setup(service => service.SendInvitationAsync(invitationRequest))
                .ThrowsAsync(new UnauthorizedAccessException("Unauthorized test"));

            // Act
            var result = await _controller.SendInvitation(invitationRequest);

            // Assert
            var unauthorizedResult = Assert.IsType<UnauthorizedObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(unauthorizedResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status401Unauthorized, apiResponse.StatusCode);
            Assert.Equal("Unauthorized access", apiResponse.Message);
            Assert.Contains("Unauthorized test", apiResponse.Errors);
        }

        [Fact]
        public async Task SendInvitation_ReturnsInternalServerError_WhenExceptionThrown()
        {
            // Arrange
            var invitationRequest = new InvitationRequestModel();
            _mockInvitationService.Setup(service => service.SendInvitationAsync(invitationRequest))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.SendInvitation(invitationRequest);

            // Assert
            var statusResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(StatusCodes.Status500InternalServerError, statusResult.StatusCode);
            var apiResponse = Assert.IsType<ApiResponse<object>>(statusResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status500InternalServerError, apiResponse.StatusCode);
            Assert.Equal("Internal Server Error", apiResponse.Message);
            Assert.Contains("An error occurred while processing your request.", apiResponse.Errors);
        }

        [Fact]
        public async Task AcceptInvitation_ReturnsBadRequest_WhenInvitationLinkExpired()
        {
            // Arrange
            var acceptInvitationRequest = new AcceptInvitationModel();
            var userProperties = new List<UserProperties>();
            _mockInvitationService.Setup(service => service.AcceptInvitationAsync(acceptInvitationRequest))
                .ReturnsAsync((Constants.InvitationLinkExpires, userProperties));

            // Act
            var result = await _controller.AcceptInvitation(acceptInvitationRequest);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("Invitation link expired.", apiResponse.Message);
            Assert.Contains("Invitation link expired.", apiResponse.Errors);
            Assert.Equal(userProperties, apiResponse.Data);
        }

        [Fact]
        public async Task AcceptInvitation_ReturnsUnauthorized_WhenUnauthorizedAccessExceptionThrown()
        {
            // Arrange
            var acceptInvitationRequest = new AcceptInvitationModel();
            _mockInvitationService.Setup(service => service.AcceptInvitationAsync(acceptInvitationRequest))
                .ThrowsAsync(new UnauthorizedAccessException("Unauthorized test"));

            // Act
            var result = await _controller.AcceptInvitation(acceptInvitationRequest);

            // Assert
            var unauthorizedResult = Assert.IsType<UnauthorizedObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(unauthorizedResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status401Unauthorized, apiResponse.StatusCode);
            Assert.Equal("Unauthorized access", apiResponse.Message);
            Assert.Contains("Unauthorized test", apiResponse.Errors);
        }

        [Fact]
        public async Task AcceptInvitation_ReturnsInternalServerError_WhenExceptionThrown()
        {
            // Arrange
            var acceptInvitationRequest = new AcceptInvitationModel();
            _mockInvitationService.Setup(service => service.AcceptInvitationAsync(acceptInvitationRequest))
                .ThrowsAsync(new Exception("General error"));

            // Act
            var result = await _controller.AcceptInvitation(acceptInvitationRequest);

            // Assert
            var statusResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(StatusCodes.Status500InternalServerError, statusResult.StatusCode);
            var apiResponse = Assert.IsType<ApiResponse<object>>(statusResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status500InternalServerError, apiResponse.StatusCode);
            Assert.Equal("Internal Server Error", apiResponse.Message);
            Assert.Contains("An error occurred while processing your request.", apiResponse.Errors);
        }
    }

    // Helper class to prevent actual telemetry during testing
    public class NullTelemetryChannel : ITelemetryChannel
    {
        public bool? DeveloperMode { get; set; }
        public string EndpointAddress { get; set; }

        public void Dispose()
        {
        }

        public void Flush()
        {
        }

        public void Send(ITelemetry item)
        {
            // Do nothing - this prevents actual telemetry from being sent during tests
        }
    }
}
