﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;

namespace MRI.OTA.UnitTestCases.DataSource.Mapper
{
    public class DataSourceMappingProfileTests
    {
        private readonly IMapper _mapper;

        public DataSourceMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<DataSourceMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_DataSource_To_DataSourceModel()
        {
            // Arrange  
            var dataSource = new MRI.OTA.Core.Entities.DataSource
            {
                DataSourceId = 1,
                Name = "TestDataSource"
            };

            // Act  
            var model = _mapper.Map<DataSourceModel>(dataSource);

            // Assert  
            Assert.NotNull(model);
            Assert.Equal(dataSource.DataSourceId, model.DataSourceId);
            Assert.Equal(dataSource.Name, model.Name);
        }

        [Fact]
        public void Should_Map_DataSourceModel_To_DataSource()
        {
            // Arrange  
            var model = new DataSourceModel
            {
                DataSourceId = 1,
                Name = "TestDataSourceModel"
            };

            // Act  
            var dataSource = _mapper.Map<MRI.OTA.Core.Entities.DataSource>(model);

            // Assert  
            Assert.NotNull(dataSource);
            Assert.Equal(model.DataSourceId, dataSource.DataSourceId);
            Assert.Equal(model.Name, dataSource.Name);
        }
    }
}
