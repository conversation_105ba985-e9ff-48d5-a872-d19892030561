﻿using System.Text.Json;
using System.Text.Json.Serialization;
using Microsoft.Extensions.Configuration;

namespace MRI.OTA.Application.Services
{
    public class AzureB2CTokenService : IAzureB2CTokenService
    {
        private readonly string _tenantId;
        private readonly string _clientId;
        private readonly string _clientSecret;
        private readonly string _scope;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;

        public AzureB2CTokenService(IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
            _tenantId = _configuration["Authentication:TenantId"] ?? throw new ArgumentNullException(nameof(_tenantId), "TenantId is not configured.");
            _clientId = _configuration["Authentication:ClientId"] ?? throw new ArgumentNullException(nameof(_clientId), "ClientId is not configured.");
            _clientSecret = _configuration["Authentication:ClientSecret"] ?? throw new ArgumentNullException(nameof(_clientSecret), "ClientSecret is not configured.");
            _scope = $"https://graph.microsoft.com/.default"; // Add the required scope  
        }

        public async Task<string> GetAccessTokenAsync()
        {
            var client = _httpClientFactory.CreateClient("AzureB2C");
            var tokenEndpoint = $"https://login.microsoftonline.com/{_tenantId}/oauth2/v2.0/token";
            var requestBody = new FormUrlEncodedContent(new[]
            {
                   new KeyValuePair<string, string>("client_id", _clientId),
                   new KeyValuePair<string, string>("client_secret", _clientSecret),
                   new KeyValuePair<string, string>("grant_type", "client_credentials"),
                   new KeyValuePair<string, string>("scope", _scope) // Include the scope in the request  
               });

            var response = await client.PostAsync(tokenEndpoint, requestBody);

            if (!response.IsSuccessStatusCode)
            {
                throw new Exception($"Failed to retrieve token: {response.StatusCode} - {await response.Content.ReadAsStringAsync()}");
            }

            var responseContent = await response.Content.ReadAsStringAsync();
            var tokenResponse = JsonSerializer.Deserialize<TokenResponse>(responseContent);

            return tokenResponse?.AccessToken ?? throw new Exception("Access token not found in response.");
        }

        public Task<string> GetAccessTokenAsync(string resource)
        {
            throw new NotImplementedException();
        }

        private class TokenResponse
        {
            [JsonPropertyName("access_token")]
            public string AccessToken { get; set; } = null!;

            [JsonPropertyName("token_type")]
            public string TokenType { get; set; } = null!;

            [JsonPropertyName("expires_in")]
            public int ExpiresIn { get; set; }

            [JsonPropertyName("ext_expires_in")]
            public int ExtExpiresIn { get; set; }
        }
    }
}
