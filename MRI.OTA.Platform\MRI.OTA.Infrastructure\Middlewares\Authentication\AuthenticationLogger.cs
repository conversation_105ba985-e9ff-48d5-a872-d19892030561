using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;
using System.Security.Claims;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication
{
    /// <summary>
    /// Implementation of IAuthenticationLogger for logging authentication events
    /// </summary>
    public class AuthenticationLogger : IAuthenticationLogger
    {
        private readonly ILogger _logger;
        private readonly TelemetryClient _telemetryClient;

        /// <summary>
        /// Constructor for AuthenticationLogger
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="telemetryClient">The telemetry client</param>
        public AuthenticationLogger(ILogger logger, TelemetryClient telemetryClient)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _telemetryClient = telemetryClient ?? throw new ArgumentNullException(nameof(telemetryClient));
        }

        /// <summary>
        /// Logs a successful user authentication
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="principal">The claims principal</param>
        public void LogSuccessfulUserAuthentication(HttpContext context, ClaimsPrincipal principal)
        {
            var userId = principal.FindFirst("sub")?.Value ??
                         principal.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier")?.Value ??
                         "unknown";

            var email = principal.FindFirst("emails")?.Value ??
                        principal.FindFirst(ClaimTypes.Email)?.Value ??
                        "unknown";

            var properties = new Dictionary<string, string>
            {
                ["UserId"] = userId,
                ["UserEmail"] = email,
                ["AuthType"] = "PKCE",
                ["Path"] = context.Request.Path,
                ["CorrelationId"] = GetOrGenerateCorrelationId(context)
            };

            // Track authentication event
            _telemetryClient.TrackEvent("UserAuthentication", properties);

            using (_logger.BeginScope(properties))
            {
                _logger.LogInformation(
                    "Successful user authentication for user {UserId} ({Email}) accessing {Path}",
                    userId,
                    email,
                    context.Request.Path);
            }
        }

        /// <summary>
        /// Logs a successful client credentials authentication
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="principal">The claims principal</param>
        public void LogSuccessfulClientCredentialsAuthentication(HttpContext context, ClaimsPrincipal principal)
        {
            var clientId = principal.FindFirst("azp")?.Value ??
                          principal.FindFirst("appid")?.Value ??
                          "unknown";

            var scope = principal.FindFirst("scope")?.Value ?? "unknown";

            using (_logger.BeginScope(new Dictionary<string, object>
            {
                ["ClientId"] = clientId,
                ["Scope"] = scope,
                ["AuthType"] = "ClientCredentials",
                ["Path"] = context.Request.Path
            }))
            {
                _logger.LogInformation(
                    "Successful client credentials authentication for client {ClientId} with scope {Scope} accessing {Path}",
                    clientId,
                    scope,
                    context.Request.Path);
            }
        }

        /// <summary>
        /// Gets or generates a correlation ID for the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>The correlation ID</returns>
        public string GetOrGenerateCorrelationId(HttpContext context)
        {
            const string CorrelationIdHeader = "X-Correlation-ID";

            if (!context.Request.Headers.TryGetValue(CorrelationIdHeader, out var correlationId) ||
                string.IsNullOrEmpty(correlationId))
            {
                correlationId = Guid.NewGuid().ToString();
                context.Request.Headers[CorrelationIdHeader] = correlationId;
            }

            context.Response.Headers[CorrelationIdHeader] = correlationId;
            return correlationId;
        }
    }
}
