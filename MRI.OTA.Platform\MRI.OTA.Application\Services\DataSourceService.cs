﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.Application.Services
{
    /// <summary>
    /// DataSource service
    /// </summary>
    public class DataSourceService : BaseService<DataSource, DataSourceModel, int>, IDataSourceService
    {
        private IDataSourceRepository _dataSourceRepository { get; set; }

        private IMapper _mapper { get; set; }

        private readonly IConfiguration _configuration;

        private ILogger _logger { get; set; }
        /// <summary>
        /// DataSource service constructor
        /// </summary>
        /// <param name="repository"></param>
        /// <param name="mapper"></param>
        public DataSourceService(ILogger<DataSourceService> logger, IDataSourceRepository repository, IMapper mapper, IConfiguration configuration)
        : base(logger, repository, mapper)
        {
            _dataSourceRepository = repository;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
        }

        public async Task<DataSourceModel> GetUserDataSource(string accessKey, string accessSecret)
        {
            var result = await _dataSourceRepository.GetUserDataSource(accessKey, accessSecret);
            return _mapper.Map<DataSourceModel>(result);
        }

        public async Task<int> UpdateDataSource(string accessKey, string accessSecret, DataSourceUpdateModel dataSourceModel)
        {
            try
            {
                return await _dataSourceRepository.UpdateDataSource(accessKey, accessSecret, dataSourceModel.ManifestJson);
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating data source : {ex.Message}");
                throw;
            }
        }
    }
}
