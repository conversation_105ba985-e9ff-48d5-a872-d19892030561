using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.PT.Mapper
{
    public class ComplianceDetailMappingProfileTests
    {
        private readonly IMapper _mapper;

        public ComplianceDetailMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<ComplianceDetailMappingProfile>();
                cfg.AddProfile<ComplianceDetailModelMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        #region ComplianceListResponse to ComplianceDetail Tests

        [Fact]
        public void Should_Map_ComplianceDetailModel_To_ComplianceDetail()
        {
            // Arrange
            var source = new ComplianceDetailModel
            {
                ManagementId = "MGM1",
                PropertyId = "456", // Must be a parsable int for the existing mapper
                ComplianceId = "123", // Must be a parsable int for the existing mapper
                ComplianceName = "Fire Safety Certificate",
                ExpiryDate = new DateTime(2025, 12, 31),
                ServicedBy = "Safety Inspector"
            };

            // Act
            var result = _mapper.Map<ComplianceDetail>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(source.ManagementId, result.SRCManagementId);
            Assert.Equal(source.PropertyId, result.SRCPropertyId);
            Assert.Equal(source.ComplianceName, result.ComplianceName);
            Assert.Equal(source.ExpiryDate, result.ExpiryDate);
            Assert.Equal(source.ServicedBy, result.ServicedBy);
        }

        #endregion

        #region ComplianceDetailResponse to List<ComplianceDetail> Tests

        [Fact]
        public void Should_Map_ComplianceDetailResponse_To_List_ComplianceDetail()
        {
            // Arrange
            var response = new ComplianceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                ComplianceEntries = new List<ComplianceListResponse>
                {
                    new ComplianceListResponse
                    {
                        ComplianceId = "COMP001",
                        ComplianceName = "Fire Safety Certificate",
                        ExpiryDate = new DateTime(2025, 12, 31),
                        ServicedBy = "Safety Inspector"
                    },
                    new ComplianceListResponse
                    {
                        ComplianceId = "COMP002",
                        ComplianceName = "Building Insurance",
                        ExpiryDate = new DateTime(2026, 6, 30),
                        ServicedBy = "Insurance Provider"
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<ComplianceDetail>>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Check first compliance entry
            var first = result[0];
            Assert.Equal("AGENCY123", first.SRCManagementId);
            Assert.Equal("PROP456", first.SRCPropertyId);
            Assert.Equal("COMP001", first.SRCComplianceId);
            Assert.Equal("Fire Safety Certificate", first.ComplianceName);
            Assert.Equal(new DateTime(2025, 12, 31), first.ExpiryDate);
            Assert.Equal("Safety Inspector", first.ServicedBy);
            Assert.Equal(-1, first.PropertyId);

            // Check second compliance entry
            var second = result[1];
            Assert.Equal("AGENCY123", second.SRCManagementId);
            Assert.Equal("PROP456", second.SRCPropertyId);
            Assert.Equal("COMP002", second.SRCComplianceId);
            Assert.Equal("Building Insurance", second.ComplianceName);
            Assert.Equal(new DateTime(2026, 6, 30), second.ExpiryDate);
            Assert.Equal("Insurance Provider", second.ServicedBy);
            Assert.Equal(-1, second.PropertyId);
        }

        [Fact]
        public void Should_Return_Empty_List_When_No_ComplianceEntries()
        {
            // Arrange
            var response = new ComplianceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                ComplianceEntries = new List<ComplianceListResponse>()
            };

            // Act
            var result = _mapper.Map<List<ComplianceDetail>>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void Should_Map_List_ComplianceDetailResponse_To_List_ComplianceDetail()
        {
            // Arrange
            var responses = new List<ComplianceDetailResponse>
            {
                new ComplianceDetailResponse
                {
                    ManagementId = "AGENCY1",
                    PropertyId = "PROP1",
                    ComplianceEntries = new List<ComplianceListResponse>
                    {
                        new ComplianceListResponse
                        {
                            ComplianceId = "COMP1",
                            ComplianceName = "Fire Safety",
                            ExpiryDate = new DateTime(2025, 12, 31),
                            ServicedBy = "Inspector 1"
                        }
                    }
                },
                new ComplianceDetailResponse
                {
                    ManagementId = "AGENCY2",
                    PropertyId = "PROP2",
                    ComplianceEntries = new List<ComplianceListResponse>
                    {
                        new ComplianceListResponse
                        {
                            ComplianceId = "COMP2",
                            ComplianceName = "Insurance",
                            ExpiryDate = new DateTime(2026, 6, 30),
                            ServicedBy = "Inspector 2"
                        }
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<ComplianceDetail>>(responses);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Check first compliance entry
            var first = result[0];
            Assert.Equal("AGENCY1", first.SRCManagementId);
            Assert.Equal("PROP1", first.SRCPropertyId);
            Assert.Equal("COMP1", first.SRCComplianceId);
            Assert.Equal("Fire Safety", first.ComplianceName);
            Assert.Equal(new DateTime(2025, 12, 31), first.ExpiryDate);
            Assert.Equal("Inspector 1", first.ServicedBy);

            // Check second compliance entry
            var second = result[1];
            Assert.Equal("AGENCY2", second.SRCManagementId);
            Assert.Equal("PROP2", second.SRCPropertyId);
            Assert.Equal("COMP2", second.SRCComplianceId);
            Assert.Equal("Insurance", second.ComplianceName);
            Assert.Equal(new DateTime(2026, 6, 30), second.ExpiryDate);
            Assert.Equal("Inspector 2", second.ServicedBy);
        }

        #endregion

        #region Legacy Mapping Tests

        [Fact]
        public void Should_Map_Single_ComplianceDetailResponse_Using_Legacy_Mapping()
        {
            // Arrange
            var response = new ComplianceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                ComplianceEntries = new List<ComplianceListResponse>
                {
                    new ComplianceListResponse
                    {
                        ComplianceId = "COMP001",
                        ComplianceName = "Fire Safety Certificate",
                        ExpiryDate = new DateTime(2025, 12, 31),
                        ServicedBy = "Safety Inspector"
                    }
                }
            };

            // Act
            var result = _mapper.Map<ComplianceDetail>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(response.ManagementId, result.SRCManagementId);
            Assert.Equal(response.PropertyId, result.SRCPropertyId);
            Assert.Equal(response.ComplianceEntries[0].ComplianceId, result.SRCComplianceId);
            Assert.Equal(response.ComplianceEntries[0].ComplianceName, result.ComplianceName);
            Assert.Equal(response.ComplianceEntries[0].ExpiryDate, result.ExpiryDate);
            Assert.Equal(response.ComplianceEntries[0].ServicedBy, result.ServicedBy);
            Assert.Equal(-1, result.PropertyId);
        }

        [Fact]
        public void Should_Handle_Empty_ComplianceEntries_In_Legacy_Mapping()
        {
            // Arrange
            var response = new ComplianceDetailResponse
            {
                ManagementId = "AGENCY123",
                PropertyId = "PROP456",
                ComplianceEntries = new List<ComplianceListResponse>()
            };

            // Act
            var result = _mapper.Map<ComplianceDetail>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(response.ManagementId, result.SRCManagementId);
            Assert.Equal(response.PropertyId, result.SRCPropertyId);
            Assert.Null(result.SRCComplianceId);
            Assert.Null(result.ComplianceName);
            Assert.Equal(default, result.ExpiryDate);
            Assert.Null(result.ServicedBy);
            Assert.Equal(-1, result.PropertyId);
        }

        #endregion
    }
}
