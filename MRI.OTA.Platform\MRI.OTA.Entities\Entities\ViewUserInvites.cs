﻿namespace MRI.OTA.DBCore.Entities
{
    public class ViewUserInvites
    {
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// InviteCode
        /// </summary>
        public string? InviteCode { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// DataSourceId
        /// </summary>
        public int? DataSourceId { get; set; }

        /// <summary>
        /// PortfolioId
        /// </summary>
        public string? PortfolioId { get; set; }

        /// <summary>
        /// AgencyId
        /// </summary>
        public string? AgencyId { get; set; }

        /// <summary>
        /// AgencyName
        /// </summary>
        public string? AgencyName { get; set; }

        /// <summary>
        /// AgencyLogo
        /// </summary>
        public string? AgencyLogo { get; set; }

        /// <summary>
        /// AgencyColour
        /// </summary>
        public string? AgencyColour { get; set; }

        /// <summary>
        /// ProviderId
        /// </summary>
        public string? ProviderId { get; set; }

        /// <summary>
        /// LoginEmail
        /// </summary>
        public string? LoginEmail { get; set; }
    }
}
