﻿using MRI.OTA.Worker.Interface;

namespace MRI.OTA.Worker.Service
{
    public class SyncPropertyTreeDataService : ISyncPropertyTreeDataService
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public SyncPropertyTreeDataService(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public async Task<bool> SyncPropertyAgencyData(string accessToken, string apiEndPoint)
        {

            try
            {
                var url = System.Environment.GetEnvironmentVariable("BaseUrl") + Constants.IntegrationApi + apiEndPoint;

                var client = _httpClientFactory.CreateClient("WorkerClient");

                var request = new HttpRequestMessage(HttpMethod.Get, url);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                HttpResponseMessage response = await client.SendAsync(request);

                response.EnsureSuccessStatusCode();
                if (response.IsSuccessStatusCode)
                {
                    string content = await response.Content.ReadAsStringAsync();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }

        public async Task<bool> SyncPropertyData(string accessToken, string apiEndPoint)
        {
            try
            {
                var url = System.Environment.GetEnvironmentVariable("BaseUrl") + Constants.IntegrationApi + apiEndPoint;

                var client = _httpClientFactory.CreateClient("WorkerClient");

                var request = new HttpRequestMessage(HttpMethod.Get, url);
                request.Headers.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", accessToken);
                HttpResponseMessage response = await client.SendAsync(request);

                response.EnsureSuccessStatusCode();
                if (response.IsSuccessStatusCode)
                {
                    string content = await response.Content.ReadAsStringAsync();
                    return true;
                }
                else
                {
                    return false;
                }
            }
            catch (Exception)
            {
                return false;
            }
        }
    }
}
