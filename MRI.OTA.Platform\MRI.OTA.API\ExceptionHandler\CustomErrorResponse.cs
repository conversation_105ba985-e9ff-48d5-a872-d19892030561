﻿namespace MRI.OTA.API.ExceptionHandler
{
    /// <summary>
    /// CustomErrorResponse returned in case of exceptions
    /// </summary>
    public class CustomErrorResponse
    {
        /// <summary>
        /// 
        /// </summary>
        /// <param name="statusCode"></param>
        /// <param name="message"></param>
        /// <param name="details"></param>
        public CustomErrorResponse(int statusCode, string message, string? details = null)
        {
            StatusCode = statusCode;
            Message = message;
            Details = details;
        }

        /// <summary>
        /// 
        /// </summary>
        public int StatusCode { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? Message { get; set; }
        /// <summary>
        /// 
        /// </summary>
        public string? Details { get; set; }
    }
}
