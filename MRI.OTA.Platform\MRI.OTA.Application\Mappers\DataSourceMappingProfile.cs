﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.Application.Mappers
{
    /// <summary>
    /// _mapper for  user entity and user model
    /// </summary>
    public class DataSourceMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for user mapper
        /// </summary>
        public DataSourceMappingProfile() : base("DataSourceMappingProfile")
        {
            CreateMap<DataSource, DataSourceModel>()
            .ReverseMap();
        }
    }
}
