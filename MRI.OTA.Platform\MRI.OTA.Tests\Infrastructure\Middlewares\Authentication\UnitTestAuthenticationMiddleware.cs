using System;
using System.Collections.Generic;
using System.IO;
using System.Threading.Tasks;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares.Authentication
{
    public class UnitTestAuthenticationMiddleware
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<IJwtTokenValidator> _jwtTokenValidatorMock;
        private readonly Mock<ILogger<AuthenticationMiddleware>> _loggerMock;
        private readonly TelemetryClient _telemetryClient;
        private readonly AuthenticationMiddleware _middleware;

        public UnitTestAuthenticationMiddleware()
        {
            _nextMock = new Mock<RequestDelegate>();
            _jwtTokenValidatorMock = new Mock<IJwtTokenValidator>();
            _loggerMock = new Mock<ILogger<AuthenticationMiddleware>>();

            // Create a real TelemetryClient instance for testing
            var telemetryConfiguration = new Microsoft.ApplicationInsights.Extensibility.TelemetryConfiguration();
            _telemetryClient = new TelemetryClient(telemetryConfiguration);

            _middleware = new AuthenticationMiddleware(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _telemetryClient);
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenNextIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AuthenticationMiddleware(
                null!,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _telemetryClient));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenJwtTokenValidatorIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AuthenticationMiddleware(
                _nextMock.Object,
                null!,
                _loggerMock.Object,
                _telemetryClient));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenLoggerIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AuthenticationMiddleware(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                null!,
                _telemetryClient));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenTelemetryClientIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AuthenticationMiddleware(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                null!));
        }

        [Fact]
        public void Constructor_CreatesMiddleware_WhenAllParametersAreValid()
        {
            // Arrange & Act
            var middleware = new AuthenticationMiddleware(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _telemetryClient);

            // Assert
            Assert.NotNull(middleware);
        }

        [Fact]
        public async Task InvokeAsync_ThrowsArgumentNullException_WhenContextIsNull()
        {
            // Arrange & Act & Assert
            await Assert.ThrowsAsync<ArgumentNullException>(() => _middleware.InvokeAsync(null!));
        }

        [Fact]
        public async Task InvokeAsync_ProcessesAnonymousEndpoint_Successfully()
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_GeneratesCorrelationId_WhenNotProvided()
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("X-Correlation-ID"));
        }

        [Fact]
        public async Task InvokeAsync_UsesProvidedCorrelationId_WhenPresent()
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();
            var correlationId = "test-correlation-id";
            context.Request.Headers["X-Correlation-ID"] = correlationId;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(correlationId, context.Response.Headers["X-Correlation-ID"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_LogsProcessingCompletion_WithElapsedTime()
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Authentication processing completed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_CreatesLoggerScope_WithRequestDetails()
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();
            context.Request.Path = "/api/test";
            context.Request.Method = "POST";

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _loggerMock.Verify(
                x => x.BeginScope(It.Is<Dictionary<string, object>>(d =>
                    d.ContainsKey("CorrelationId") &&
                    d.ContainsKey("Path") &&
                    d.ContainsKey("Method") &&
                    d["Path"].ToString() == "/api/test" &&
                    d["Method"].ToString() == "POST")),
                Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_HandlesException_AndCallsExceptionHandler()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer invalid-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken(It.IsAny<string>(), It.IsAny<bool>()))
                .ThrowsAsync(new UnauthorizedAccessException("Invalid token"));

            // Act
            await _middleware.InvokeAsync(context);

            // Assert - Exception handler returns 500 for general exceptions
            Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_ReturnsUnauthorized_WhenNoStrategyHandlesRequest()
        {
            // Arrange
            var context = CreateHttpContext();
            // No authentication headers or anonymous endpoint

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/api/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.Response.Body = new MemoryStream();
            return context;
        }

        private static HttpContext CreateHttpContextWithAnonymousEndpoint()
        {
            var context = CreateHttpContext();
            var endpoint = new Endpoint(
                c => Task.CompletedTask,
                new EndpointMetadataCollection(
                    new Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute()),
                "anonymous-endpoint");
            context.SetEndpoint(endpoint);
            return context;
        }

        [Fact]
        public async Task InvokeAsync_ProcessesBearerToken_Successfully()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ReturnsAsync(new System.Security.Claims.ClaimsPrincipal());

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_ProcessesAccessToken_Successfully()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessToken"] = "test-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken("test-key", "test-token"))
                .ReturnsAsync(true);

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_ProcessesApiKey_ReturnsUnauthorized()
        {
            // Arrange - API key strategy returns unauthorized for any key without proper validation
            var context = CreateHttpContext();
            context.Request.Headers["X-API-Key"] = "valid-api-key";

            // Act
            await _middleware.InvokeAsync(context);

            // Assert - API key strategy should return 401 for invalid keys
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Theory]
        [InlineData("/api/v1/invitations/invite-user")]
        [InlineData("/api/v1/integrations")]
        [InlineData("/api/v1/openid-configuration")]
        public async Task InvokeAsync_ProcessesClientCredentialsEndpoints_WithBearerToken(string endpoint)
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = endpoint;
            context.Request.Headers["Authorization"] = "Bearer client-credentials-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("client-credentials-token", true))
                .ReturnsAsync(new System.Security.Claims.ClaimsPrincipal());

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_HandlesInvalidBearerToken_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer invalid-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("invalid-token", false))
                .ThrowsAsync(new Microsoft.IdentityModel.Tokens.SecurityTokenException("Invalid token"));

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesInvalidAccessToken_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "invalid-key";
            context.Request.Headers["AccessToken"] = "invalid-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateAccessToken("invalid-key", "invalid-token"))
                .ReturnsAsync(false);

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Theory]
        [InlineData("GET")]
        [InlineData("POST")]
        [InlineData("PUT")]
        [InlineData("DELETE")]
        [InlineData("PATCH")]
        [InlineData("HEAD")]
        [InlineData("OPTIONS")]
        public async Task InvokeAsync_HandlesAllHttpMethods_WithAnonymousEndpoint(string method)
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();
            context.Request.Method = method;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_MaintainsOriginalRequestPath()
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();
            var originalPath = "/api/v1/test/endpoint";
            context.Request.Path = originalPath;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(originalPath, context.Request.Path.Value);
        }

        [Fact]
        public async Task InvokeAsync_MaintainsOriginalRequestMethod()
        {
            // Arrange
            var context = CreateHttpContextWithAnonymousEndpoint();
            var originalMethod = "PATCH";
            context.Request.Method = originalMethod;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(originalMethod, context.Request.Method);
        }

        [Fact]
        public async Task InvokeAsync_HandlesMultipleAuthenticationHeaders_PrioritizesCorrectly()
        {
            // Arrange - Bearer token should take precedence over API key
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer valid-token";
            context.Request.Headers["X-API-Key"] = "valid-api-key";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ReturnsAsync(new System.Security.Claims.ClaimsPrincipal());

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
            _jwtTokenValidatorMock.Verify(v => v.ValidateToken("valid-token", false), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_HandlesTokenExpiredException_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer expired-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("expired-token", false))
                .ThrowsAsync(new Microsoft.IdentityModel.Tokens.SecurityTokenExpiredException("Token expired"));

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesTokenInvalidSignatureException_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer invalid-signature-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("invalid-signature-token", false))
                .ThrowsAsync(new Microsoft.IdentityModel.Tokens.SecurityTokenInvalidSignatureException("Invalid signature"));

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesGenericException_ReturnsInternalServerError()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ThrowsAsync(new InvalidOperationException("Unexpected error"));

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesEmptyAuthorizationHeader_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "";

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesInvalidAuthorizationFormat_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "InvalidFormat token";

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesEmptyAccessKey_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "";
            context.Request.Headers["AccessToken"] = "test-token";

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesEmptyAccessToken_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessToken"] = "";

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_HandlesEmptyApiKey_ReturnsUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["X-API-Key"] = "";

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(StatusCodes.Status401Unauthorized, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_SetsResponseContentType_ToApplicationJson()
        {
            // Arrange
            var context = CreateHttpContext();
            // No authentication headers

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.StartsWith("application/json", context.Response.ContentType);
        }

        [Fact]
        public async Task InvokeAsync_WritesJsonResponse_WhenUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            var responseBody = new MemoryStream();
            context.Response.Body = responseBody;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            responseBody.Seek(0, SeekOrigin.Begin);
            var responseText = new StreamReader(responseBody).ReadToEnd();
            Assert.Contains("Authentication required", responseText);
            Assert.Contains("\"success\":false", responseText);
        }
    }
}
