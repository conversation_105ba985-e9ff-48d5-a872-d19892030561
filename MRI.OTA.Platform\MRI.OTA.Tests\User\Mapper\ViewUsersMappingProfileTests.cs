﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.UnitTestCases.User.Mapper
{
    public class ViewUsersMappingProfileTests
    {
        private readonly IMapper _mapper;

        public ViewUsersMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<ViewUsersMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_Users_To_UserModel()
        {
            // Arrange  
            var user = new Users
            {
                UserId = 1,
                DisplayName = "John Doe",
                UserEmail = "<EMAIL>"
            };

            // Act  
            var userModel = _mapper.Map<UserModel>(user);

            // Assert  
            Assert.NotNull(userModel);
            Assert.Equal(user.UserId, userModel.UserId);
            Assert.Equal(user.DisplayName, userModel.DisplayName);
            Assert.Equal(user.UserEmail, userModel.UserEmail);
        }

        [Fact]
        public void Should_Map_UserModel_To_Users()
        {
            // Arrange  
            var userModel = new UserModel
            {
                UserId = 1,
                DisplayName = "Jane Doe",
                UserEmail = "<EMAIL>"
            };

            // Act  
            var user = _mapper.Map<Users>(userModel);

            // Assert  
            Assert.NotNull(user);
            Assert.Equal(userModel.UserId, user.UserId);
            Assert.Equal(userModel.DisplayName, user.DisplayName);
            Assert.Equal(userModel.UserEmail, user.UserEmail);
        }
    }
}
