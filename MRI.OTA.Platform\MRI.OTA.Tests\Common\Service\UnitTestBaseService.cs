using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Interfaces;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Xunit;

namespace MRI.OTA.UnitTestCases.Common.Service
{
    public class UnitTestBaseService
    {
        private readonly Mock<ILogger<BaseService<TestEntity, TestModel, int>>> _mockLogger;
        private readonly Mock<IBaseRepository<TestEntity, int>> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;

        public UnitTestBaseService()
        {
            _mockLogger = new Mock<ILogger<BaseService<TestEntity, TestModel, int>>>();
            _mockRepository = new Mock<IBaseRepository<TestEntity, int>>();
            _mockMapper = new Mock<IMapper>();
        }

        private TestBaseService CreateService()
        {
            return new TestBaseService(
                _mockLogger.Object,
                _mockRepository.Object,
                _mockMapper.Object);
        }

        private TestEntity CreateTestEntity(int id = 1, string name = "Test Entity")
        {
            return new TestEntity { Id = id, Name = name };
        }

        private TestModel CreateTestModel(int id = 1, string name = "Test Model")
        {
            return new TestModel { Id = id, Name = name };
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var service = CreateService();

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullLogger()
        {
            // Arrange & Act
            var service = new TestBaseService(null!, _mockRepository.Object, _mockMapper.Object);

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullRepository()
        {
            // Arrange & Act
            var service = new TestBaseService(_mockLogger.Object, null!, _mockMapper.Object);

            // Assert
            Assert.NotNull(service);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullMapper()
        {
            // Arrange & Act
            var service = new TestBaseService(_mockLogger.Object, _mockRepository.Object, null!);

            // Assert
            Assert.NotNull(service);
        }

        #endregion

        #region Interface Implementation Tests

        [Fact]
        public void BaseService_ShouldImplementIBaseService()
        {
            // Arrange & Act
            var service = CreateService();

            // Assert
            Assert.IsAssignableFrom<IBaseService<TestEntity, TestModel, int>>(service);
        }

        [Fact]
        public void IBaseService_HasCorrectMethods()
        {
            // Arrange & Act
            var interfaceType = typeof(IBaseService<TestEntity, TestModel, int>);

            // Assert
            Assert.NotNull(interfaceType.GetMethod("GetAllAsync"));
            Assert.NotNull(interfaceType.GetMethod("GetDataByTableName"));
            Assert.NotNull(interfaceType.GetMethod("GetByIdAsync"));
            Assert.NotNull(interfaceType.GetMethod("AddAsync"));
            Assert.NotNull(interfaceType.GetMethod("UpdateAsync"));
            Assert.NotNull(interfaceType.GetMethod("DeleteAsync"));
        }

        #endregion

        #region GetAllAsync Tests

        [Fact]
        public async Task GetAllAsync_ShouldReturnMappedModels_WhenEntitiesExist()
        {
            // Arrange
            var entities = new List<TestEntity>
            {
                CreateTestEntity(1, "Entity 1"),
                CreateTestEntity(2, "Entity 2")
            };
            var models = new List<TestModel>
            {
                CreateTestModel(1, "Model 1"),
                CreateTestModel(2, "Model 2")
            };

            _mockRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(entities);
            _mockMapper.Setup(m => m.Map<List<TestModel>>(entities)).Returns(models);

            var service = CreateService();

            // Act
            var result = await service.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Equal(models, result);
            _mockRepository.Verify(r => r.GetAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<TestModel>>(entities), Times.Once);
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnEmptyList_WhenNoEntitiesExist()
        {
            // Arrange
            var entities = new List<TestEntity>();
            var models = new List<TestModel>();

            _mockRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(entities);
            _mockMapper.Setup(m => m.Map<List<TestModel>>(entities)).Returns(models);

            var service = CreateService();

            // Act
            var result = await service.GetAllAsync();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
            _mockRepository.Verify(r => r.GetAllAsync(), Times.Once);
            _mockMapper.Verify(m => m.Map<List<TestModel>>(entities), Times.Once);
        }

        [Fact]
        public async Task GetAllAsync_ShouldLogInformation_WhenCalled()
        {
            // Arrange
            var entities = new List<TestEntity>();
            var models = new List<TestModel>();

            _mockRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(entities);
            _mockMapper.Setup(m => m.Map<List<TestModel>>(entities)).Returns(models);

            var service = CreateService();

            // Act
            await service.GetAllAsync();

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: GetAllAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetAllAsync_ShouldLogErrorAndRethrow_WhenRepositoryThrows()
        {
            // Arrange
            var expectedException = new InvalidOperationException("Repository error");
            _mockRepository.Setup(r => r.GetAllAsync()).ThrowsAsync(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => service.GetAllAsync());
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: GetAllAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetAllAsync_ShouldLogErrorAndRethrow_WhenMapperThrows()
        {
            // Arrange
            var entities = new List<TestEntity> { CreateTestEntity() };
            var expectedException = new AutoMapperMappingException("Mapping error");

            _mockRepository.Setup(r => r.GetAllAsync()).ReturnsAsync(entities);
            _mockMapper.Setup(m => m.Map<List<TestModel>>(entities)).Throws(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AutoMapperMappingException>(() => service.GetAllAsync());
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: GetAllAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion

        #region GetDataByTableName Tests

        [Fact]
        public async Task GetDataByTableName_ShouldReturnEntities_WhenTableExists()
        {
            // Arrange
            var tableName = "TestTable";
            var entities = new List<TestEntity>
            {
                CreateTestEntity(1, "Entity 1"),
                CreateTestEntity(2, "Entity 2")
            };

            _mockRepository.Setup(r => r.GetDataByTableName<TestEntity>(tableName)).ReturnsAsync(entities);

            var service = CreateService();

            // Act
            var result = await service.GetDataByTableName(tableName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count());
            Assert.Equal(entities, result);
            _mockRepository.Verify(r => r.GetDataByTableName<TestEntity>(tableName), Times.Once);
        }

        [Fact]
        public async Task GetDataByTableName_ShouldReturnEmptyList_WhenTableIsEmpty()
        {
            // Arrange
            var tableName = "EmptyTable";
            var entities = new List<TestEntity>();

            _mockRepository.Setup(r => r.GetDataByTableName<TestEntity>(tableName)).ReturnsAsync(entities);

            var service = CreateService();

            // Act
            var result = await service.GetDataByTableName(tableName);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
            _mockRepository.Verify(r => r.GetDataByTableName<TestEntity>(tableName), Times.Once);
        }

        [Fact]
        public async Task GetDataByTableName_ShouldLogInformation_WhenCalled()
        {
            // Arrange
            var tableName = "TestTable";
            var entities = new List<TestEntity>();

            _mockRepository.Setup(r => r.GetDataByTableName<TestEntity>(tableName)).ReturnsAsync(entities);

            var service = CreateService();

            // Act
            await service.GetDataByTableName(tableName);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: GetDataByTableName")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetDataByTableName_ShouldLogErrorAndRethrow_WhenRepositoryThrows()
        {
            // Arrange
            var tableName = "TestTable";
            var expectedException = new InvalidOperationException("Repository error");
            _mockRepository.Setup(r => r.GetDataByTableName<TestEntity>(tableName)).ThrowsAsync(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => service.GetDataByTableName(tableName));
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: GetDataByTableName")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Theory]
        [InlineData("")]
        [InlineData("   ")]
        [InlineData("ValidTableName")]
        [InlineData("Table_With_Underscores")]
        [InlineData("TableWithNumbers123")]
        public async Task GetDataByTableName_ShouldHandleVariousTableNames(string tableName)
        {
            // Arrange
            var entities = new List<TestEntity>();
            _mockRepository.Setup(r => r.GetDataByTableName<TestEntity>(tableName)).ReturnsAsync(entities);

            var service = CreateService();

            // Act
            var result = await service.GetDataByTableName(tableName);

            // Assert
            Assert.NotNull(result);
            _mockRepository.Verify(r => r.GetDataByTableName<TestEntity>(tableName), Times.Once);
        }

        #endregion

        #region GetByIdAsync Tests

        [Fact]
        public async Task GetByIdAsync_ShouldReturnMappedModel_WhenEntityExists()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var entity = CreateTestEntity(id, "Test Entity");
            var model = CreateTestModel(id, "Test Model");

            _mockRepository.Setup(r => r.GetByIdAsync(id, idColumnName)).ReturnsAsync(entity);
            _mockMapper.Setup(m => m.Map<TestModel>(entity)).Returns(model);

            var service = CreateService();

            // Act
            var result = await service.GetByIdAsync(id, idColumnName);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(model.Id, result.Id);
            Assert.Equal(model.Name, result.Name);
            _mockRepository.Verify(r => r.GetByIdAsync(id, idColumnName), Times.Once);
            _mockMapper.Verify(m => m.Map<TestModel>(entity), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnNull_WhenEntityNotFound()
        {
            // Arrange
            var id = 999;
            var idColumnName = "Id";

            _mockRepository.Setup(r => r.GetByIdAsync(id, idColumnName)).ReturnsAsync((TestEntity?)null);
            _mockMapper.Setup(m => m.Map<TestModel>(It.IsAny<TestEntity>())).Returns((TestModel?)null!);

            var service = CreateService();

            // Act
            var result = await service.GetByIdAsync(id, idColumnName);

            // Assert
            Assert.Null(result);
            _mockRepository.Verify(r => r.GetByIdAsync(id, idColumnName), Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldLogInformationAndDebug_WhenCalled()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var entity = CreateTestEntity(id);
            var model = CreateTestModel(id);

            _mockRepository.Setup(r => r.GetByIdAsync(id, idColumnName)).ReturnsAsync(entity);
            _mockMapper.Setup(m => m.Map<TestModel>(entity)).Returns(model);

            var service = CreateService();

            // Act
            await service.GetByIdAsync(id, idColumnName);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: GetByIdAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Service Parameters: {id}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_ShouldLogErrorAndRethrow_WhenRepositoryThrows()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var expectedException = new InvalidOperationException("Repository error");
            _mockRepository.Setup(r => r.GetByIdAsync(id, idColumnName)).ThrowsAsync(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => service.GetByIdAsync(id, idColumnName));
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: GetByIdAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Service Parameters: {id}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(int.MaxValue)]
        [InlineData(int.MinValue)]
        public async Task GetByIdAsync_ShouldHandleVariousIdValues(int id)
        {
            // Arrange
            var idColumnName = "Id";
            _mockRepository.Setup(r => r.GetByIdAsync(id, idColumnName)).ReturnsAsync((TestEntity?)null);

            var service = CreateService();

            // Act
            var result = await service.GetByIdAsync(id, idColumnName);

            // Assert
            Assert.Null(result);
            _mockRepository.Verify(r => r.GetByIdAsync(id, idColumnName), Times.Once);
        }

        [Theory]
        [InlineData("Id")]
        [InlineData("EntityId")]
        [InlineData("TestId")]
        [InlineData("")]
        [InlineData("   ")]
        public async Task GetByIdAsync_ShouldHandleVariousColumnNames(string idColumnName)
        {
            // Arrange
            var id = 1;
            _mockRepository.Setup(r => r.GetByIdAsync(id, idColumnName)).ReturnsAsync((TestEntity?)null);

            var service = CreateService();

            // Act
            var result = await service.GetByIdAsync(id, idColumnName);

            // Assert
            Assert.Null(result);
            _mockRepository.Verify(r => r.GetByIdAsync(id, idColumnName), Times.Once);
        }

        #endregion

        #region AddAsync Tests

        [Fact]
        public async Task AddAsync_ShouldReturnId_WhenEntityAddedSuccessfully()
        {
            // Arrange
            var model = CreateTestModel(1, "Test Model");
            var entity = CreateTestEntity(1, "Test Entity");
            var expectedId = 1;

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.AddAsync(entity)).ReturnsAsync(expectedId);

            var service = CreateService();

            // Act
            var result = await service.AddAsync(model);

            // Assert
            Assert.Equal(expectedId, result);
            _mockMapper.Verify(m => m.Map<TestEntity>(model), Times.Once);
            _mockRepository.Verify(r => r.AddAsync(entity), Times.Once);
        }

        [Fact]
        public async Task AddAsync_ShouldReturnZero_WhenAddFails()
        {
            // Arrange
            var model = CreateTestModel(1, "Test Model");
            var entity = CreateTestEntity(1, "Test Entity");

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.AddAsync(entity)).ReturnsAsync(0);

            var service = CreateService();

            // Act
            var result = await service.AddAsync(model);

            // Assert
            Assert.Equal(0, result);
            _mockMapper.Verify(m => m.Map<TestEntity>(model), Times.Once);
            _mockRepository.Verify(r => r.AddAsync(entity), Times.Once);
        }

        [Fact]
        public async Task AddAsync_ShouldLogInformationAndDebug_WhenCalled()
        {
            // Arrange
            var model = CreateTestModel(1, "Test Model");
            var entity = CreateTestEntity(1, "Test Entity");

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.AddAsync(entity)).ReturnsAsync(1);

            var service = CreateService();

            // Act
            await service.AddAsync(model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: AddAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Parameters:")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AddAsync_ShouldLogErrorAndRethrow_WhenRepositoryThrows()
        {
            // Arrange
            var model = CreateTestModel(1, "Test Model");
            var entity = CreateTestEntity(1, "Test Entity");
            var expectedException = new InvalidOperationException("Repository error");

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.AddAsync(entity)).ThrowsAsync(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => service.AddAsync(model));
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: AddAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AddAsync_ShouldHandleNullModel()
        {
            // Arrange
            TestModel? model = null;
            TestEntity? entity = null;

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.AddAsync(entity!)).ReturnsAsync(0);

            var service = CreateService();

            // Act
            var result = await service.AddAsync(model!);

            // Assert
            Assert.Equal(0, result);
            _mockMapper.Verify(m => m.Map<TestEntity>(model), Times.Once);
        }

        #endregion

        #region UpdateAsync Tests

        [Fact]
        public async Task UpdateAsync_ShouldReturnRowsAffected_WhenUpdateSuccessful()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var model = CreateTestModel(id, "Updated Model");
            var entity = CreateTestEntity(id, "Updated Entity");
            var expectedRowsAffected = 1;

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.UpdateAsync(id, idColumnName, entity)).ReturnsAsync(expectedRowsAffected);

            var service = CreateService();

            // Act
            var result = await service.UpdateAsync(id, idColumnName, model);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockMapper.Verify(m => m.Map<TestEntity>(model), Times.Once);
            _mockRepository.Verify(r => r.UpdateAsync(id, idColumnName, entity), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldReturnZero_WhenUpdateFails()
        {
            // Arrange
            var id = 999;
            var idColumnName = "Id";
            var model = CreateTestModel(id, "Updated Model");
            var entity = CreateTestEntity(id, "Updated Entity");

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.UpdateAsync(id, idColumnName, entity)).ReturnsAsync(0);

            var service = CreateService();

            // Act
            var result = await service.UpdateAsync(id, idColumnName, model);

            // Assert
            Assert.Equal(0, result);
            _mockMapper.Verify(m => m.Map<TestEntity>(model), Times.Once);
            _mockRepository.Verify(r => r.UpdateAsync(id, idColumnName, entity), Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldLogInformationAndDebug_WhenCalled()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var model = CreateTestModel(id, "Updated Model");
            var entity = CreateTestEntity(id, "Updated Entity");

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.UpdateAsync(id, idColumnName, entity)).ReturnsAsync(1);

            var service = CreateService();

            // Act
            await service.UpdateAsync(id, idColumnName, model);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: UpdateAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Service Parameters: {id}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldLogErrorAndRethrow_WhenRepositoryThrows()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var model = CreateTestModel(id, "Updated Model");
            var entity = CreateTestEntity(id, "Updated Entity");
            var expectedException = new InvalidOperationException("Repository error");

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.UpdateAsync(id, idColumnName, entity)).ThrowsAsync(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => service.UpdateAsync(id, idColumnName, model));
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: UpdateAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Service Parameters: {id}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldLogErrorAndRethrow_WhenMapperThrows()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var model = CreateTestModel(id, "Updated Model");
            var expectedException = new AutoMapperMappingException("Mapping error");

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Throws(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<AutoMapperMappingException>(() => service.UpdateAsync(id, idColumnName, model));
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: UpdateAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldHandleNullModel()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            TestModel? model = null;
            TestEntity? entity = null;

            _mockMapper.Setup(m => m.Map<TestEntity>(model)).Returns(entity);
            _mockRepository.Setup(r => r.UpdateAsync(id, idColumnName, entity!)).ReturnsAsync(0);

            var service = CreateService();

            // Act
            var result = await service.UpdateAsync(id, idColumnName, model!);

            // Assert
            Assert.Equal(0, result);
            _mockMapper.Verify(m => m.Map<TestEntity>(model), Times.Once);
        }

        #endregion

        #region DeleteAsync Tests

        [Fact]
        public async Task DeleteAsync_ShouldReturnRowsAffected_WhenDeleteSuccessful()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var expectedRowsAffected = 1;

            _mockRepository.Setup(r => r.DeleteAsync(id, idColumnName)).ReturnsAsync(expectedRowsAffected);

            var service = CreateService();

            // Act
            var result = await service.DeleteAsync(id, idColumnName);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockRepository.Verify(r => r.DeleteAsync(id, idColumnName), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldReturnZero_WhenDeleteFails()
        {
            // Arrange
            var id = 999;
            var idColumnName = "Id";

            _mockRepository.Setup(r => r.DeleteAsync(id, idColumnName)).ReturnsAsync(0);

            var service = CreateService();

            // Act
            var result = await service.DeleteAsync(id, idColumnName);

            // Assert
            Assert.Equal(0, result);
            _mockRepository.Verify(r => r.DeleteAsync(id, idColumnName), Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldLogInformationAndDebug_WhenCalled()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";

            _mockRepository.Setup(r => r.DeleteAsync(id, idColumnName)).ReturnsAsync(1);

            var service = CreateService();

            // Act
            await service.DeleteAsync(id, idColumnName);

            // Assert
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: DeleteAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Service Parameters: {id}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task DeleteAsync_ShouldLogErrorAndRethrow_WhenRepositoryThrows()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var expectedException = new InvalidOperationException("Repository error");

            _mockRepository.Setup(r => r.DeleteAsync(id, idColumnName)).ThrowsAsync(expectedException);

            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => service.DeleteAsync(id, idColumnName));
            Assert.Same(expectedException, exception);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Service Exception: DeleteAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Service Parameters: {id}")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(int.MaxValue)]
        [InlineData(int.MinValue)]
        public async Task DeleteAsync_ShouldHandleVariousIdValues(int id)
        {
            // Arrange
            var idColumnName = "Id";
            _mockRepository.Setup(r => r.DeleteAsync(id, idColumnName)).ReturnsAsync(0);

            var service = CreateService();

            // Act
            var result = await service.DeleteAsync(id, idColumnName);

            // Assert
            Assert.Equal(0, result);
            _mockRepository.Verify(r => r.DeleteAsync(id, idColumnName), Times.Once);
        }

        #endregion

        #region JSON Serialization Tests

        [Fact]
        public async Task AddAsync_ShouldThrowJsonSerializationException_WhenCircularReferenceExists()
        {
            // Arrange
            var model = new TestModelWithCircularReference();
            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Newtonsoft.Json.JsonSerializationException>(() => service.AddAsync(model));
            Assert.Contains("Self referencing loop detected", exception.Message);

            // Verify that information logging occurred before the exception
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: AddAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_ShouldThrowJsonSerializationException_WhenCircularReferenceExists()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var model = new TestModelWithCircularReference();
            var service = CreateService();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Newtonsoft.Json.JsonSerializationException>(() => service.UpdateAsync(id, idColumnName, model));
            Assert.Contains("Self referencing loop detected", exception.Message);

            // Verify that information logging occurred before the exception
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Executing Base Service Method: UpdateAsync")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion
    }

    #region Test Classes

    public class TestEntity
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }

    public class TestModel
    {
        public int Id { get; set; }
        public string Name { get; set; } = string.Empty;
    }

    public class TestBaseService : BaseService<TestEntity, TestModel, int>
    {
        public TestBaseService(
            ILogger<BaseService<TestEntity, TestModel, int>> logger,
            IBaseRepository<TestEntity, int> repository,
            IMapper mapper)
            : base(logger, repository, mapper)
        {
        }
    }

    public class TestModelWithCircularReference : TestModel
    {
        public TestModelWithCircularReference? Parent { get; set; }
        public List<TestModelWithCircularReference> Children { get; set; } = new List<TestModelWithCircularReference>();

        public TestModelWithCircularReference()
        {
            // Create a circular reference to test JSON serialization handling
            Parent = this;
            Children.Add(this);
        }
    }

    #endregion
}
