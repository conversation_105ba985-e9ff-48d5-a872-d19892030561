﻿using Dapper;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;
using System.Text;

namespace MRI.OTA.DBCore.Repositories
{
    /// <summary>
    /// User repository
    /// </summary>
    public class UserRepository : BaseRepository<Users, int>, IUserRepository
    {
        private readonly ILogger<UserRepository> _logger;
        protected readonly IDapperWrapper _dapperWrapper;

        /// <summary>
        /// Contructor for user repository
        /// </summary>
        /// <param name="dbConnection"></param>
        public UserRepository(IDbConnectionFactory dbConnection, ILogger<UserRepository> logger, IDapperWrapper dapperWrapper)
            : base(dbConnection, logger, dapperWrapper)
        {
            _logger = logger;
            _dapperWrapper = dapperWrapper;
        }
        public async Task<ViewUserProfileModel> GetUserDetails(ViewUserProfileModel userProfileModel)
        {
            string query = $"SELECT TOP 1 u.UserId, u.ProviderId,u.UserEmail,u.ProviderTypeId,U.DisplayName,U.IsActive," +
                $"U.TermsAndConditions,U.PreferredContactEmail,U.PushNotificationEnabled,U.EmailNotificationEnabled,PT.ProviderName,COUNT(p.PropertyId) AS PropertyCount " +
                $"FROM  Users u  INNER JOIN ProviderType PT ON u.ProviderTypeId = PT.ProviderTypeId LEFT JOIN  UserProperties p ON u.UserId = p.UserId " +
                $"WHERE ProviderId = @ProviderId OR UserEmail = @UserEmail " +
                $"GROUP BY  u.UserId, u.ProviderId,u.UserEmail,u.ProviderTypeId,U.DisplayName,u.IsActive,U.TermsAndConditions," +
                $"U.PreferredContactEmail,U.PushNotificationEnabled,U.EmailNotificationEnabled,PT.ProviderName";
            var parameters = new { ProviderId = userProfileModel.ProviderId, UserEmail = userProfileModel.UserEmail };
            return await this.GetByIdAsync<ViewUserProfileModel>(query, parameters);
        }

        public async Task<int> UpdateUserEmail(Users user)
        {
            string query = $"UPDATE Users SET UserEmail = @UserEmail WHERE ProviderId = @ProviderId";
            var parameters = new { UserEmail = user.UserEmail, ProviderId = user.ProviderId };
            return await UpdateAsync(query, parameters);
        }

        public async Task<int> UpdateUserProviderType(Users user)
        {
            string query = $"UPDATE Users SET ProviderTypeId = @ProviderTypeId, ProviderId = @ProviderId WHERE UserEmail = @UserEmail";
            var parameters = new { UserEmail = user.UserEmail, ProviderTypeId = user.ProviderTypeId, ProviderId = user.ProviderId };
            return await UpdateAsync(query, parameters);
        }

        public async Task<int> UpdateUserTermsAndCondition(Users user)
        {
            string query = $"UPDATE Users SET TermsAndConditions = @TermsAndConditions WHERE UserId = @UserId";
            var parameters = new { TermsAndConditions = user.TermsAndConditions, UserId = user.UserId };
            return await UpdateAsync(query, parameters);
        }

        public async Task<int> UpdateUserProfileSettings(Users user)
        {
            if (!string.IsNullOrEmpty(user.PreferredContactEmail))
            {
                string query = $"UPDATE Users SET PreferredContactEmail = @PreferredContactEmail, PushNotificationEnabled = @PushNotificationEnabled , EmailNotificationEnabled = @EmailNotificationEnabled WHERE UserId = @UserId";
                var parameters = new { PreferredContactEmail = user.PreferredContactEmail, PushNotificationEnabled = user.PushNotificationEnabled, EmailNotificationEnabled = user.EmailNotificationEnabled, UserId = user.UserId };
                return await UpdateAsync(query, parameters);
            }
            else
            {
                string query = $"UPDATE Users SET PushNotificationEnabled = @PushNotificationEnabled , EmailNotificationEnabled = @EmailNotificationEnabled WHERE UserId = @UserId";
                var parameters = new { PushNotificationEnabled = user.PushNotificationEnabled, EmailNotificationEnabled = user.EmailNotificationEnabled, UserId = user.UserId };
                return await UpdateAsync(query, parameters);
            } 
        }

        /// <summary>
        /// Demo data for testing
        /// </summary>
        /// <returns></returns>
        public async Task<List<Users>> GetAllUsers()
        {
            string query = "SELECT UserId,ProviderId,ProviderTypeId,UserEmail FROM USERS Where IsActive = 1";
            var result = await QueryAsync<Users>(query, null);
            return result.ToList();
        }

        public async Task<bool> DeleteAccount(int userId, string providerId)
        {

            return await ExecuteWithTransactionAsync(async (connection, transaction) =>
            {
                try
                {
                    StringBuilder query = new StringBuilder();
                    query.Append($"DELETE FROM {Constants.UserInvitesTableName} WHERE ProviderId = @ProviderId;");
                    query.Append($"DELETE FROM {Constants.InspectionDetailsTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
                   // query.Append($"DELETE FROM {Constants.DocumentDetailsTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
                    query.Append($"DELETE FROM {Constants.ComplianceDetailsTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
                    query.Append($"DELETE FROM {Constants.MaintenanceDetailsTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
                    query.Append($"DELETE FROM {Constants.PropertyManagerInformationTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
                    query.Append($"DELETE FROM {Constants.PropertyFinancialInformationTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
                    query.Append($"DELETE FROM {Constants.PropertyImagesTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
                    query.Append($"DELETE FROM {Constants.UserPropertyTableName} WHERE UserId = @UserId;");
                    query.Append($"DELETE FROM {Constants.UserPropertiesNickNameTableName} WHERE UserId = @UserId;");
                    query.Append($"DELETE FROM {Constants.UserTableName} WHERE UserId = @UserId AND ProviderId = @ProviderId;");
                    query.Append($"DELETE FROM {Constants.UserDataSourceTableName} WHERE UserDataSourceId IN (SELECT UserDataSourceId FROM Users WHERE UserId = @UserId);");
                    query.Append($"DELETE FROM {Constants.UserDeviceDetailsTableName} WHERE UserId = @UserId;");
                    var parameters = new { UserId = userId, ProviderId = providerId };
                    await QueryAsync<int>(query.ToString(), parameters, transaction, connection);
                    return true;
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error Deleting user account: {ex.Message}");
                    throw;
                }
            });
        }


        public async Task<List<DataSource>> GetDataSourcesByUserId(int userId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT DISTINCT DS.DataSourceId,DS.Name, DS.ManifestJson ");
                query.Append($"FROM {Constants.UserPropertyTableName} UP INNER JOIN {Constants.UserDataSourceTableName} UD ");
                query.Append($"ON UP.UserDataSourceId = UD.UserDataSourceId LEFT JOIN {Constants.DataSourceTableName} DS ");
                query.Append($"ON UD.DataSourceId = DS.DataSourceId WHERE UP.UserId = @UserId;");
                var parameters = new { UserId = userId };
                var result = await QueryAsync<DataSource>(query.ToString(), parameters);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting data sources for user ID {userId}: {ex.Message}");
                return new List<DataSource>();
            }
        }

        /// <summary>
        /// Get total count of active users
        /// </summary>
        /// <returns>Total number of active users</returns>
        public async Task<int> GetUserCount()
        {
            try
            {
                string query = "SELECT COUNT(*) FROM Users WHERE IsActive = 1";
                var result = await GetByIdAsync<int>(query, null);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting user count: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Get users in batches for pagination
        /// </summary>
        /// <param name="skip">Number of records to skip</param>
        /// <param name="take">Number of records to take</param>
        /// <returns>List of users for the specified batch</returns>
        public async Task<List<Users>> GetUsersBatch(int skip, int take)
        {
            try
            {
                string query = @"
                    SELECT UserId, ProviderId, ProviderTypeId, UserEmail 
                    FROM Users 
                    WHERE IsActive = 1 
                    ORDER BY UserId 
                    OFFSET @Skip ROWS 
                    FETCH NEXT @Take ROWS ONLY";
                
                var parameters = new { Skip = skip, Take = take };
                var result = await QueryAsync<Users>(query, parameters);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting users batch (skip: {skip}, take: {take}): {ex.Message}");
                return new List<Users>();
            }
        }
        public async Task<int> AddUpdateUserDevice(UserDeviceDetail deviceModel, int userId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT TOP(1) UserDeviceDetailId ");
                query.Append($"FROM {Constants.UserDeviceDetailsTableName} UD ");
                query.Append($"WHERE UD.DeviceId = @DeviceId AND UD.DeviceType = @DeviceType AND UD.UserId = @UserId;");
                var parameters = new { DeviceId = deviceModel.DeviceId, DeviceType = deviceModel.DeviceType, UserId = userId };
                var result = await GetByIdAsync<int>(query.ToString(), parameters);
                if (result > 0)
                {
                    string updQuery = $"UPDATE UserDeviceDetails SET [DeviceToken] = @DeviceToken, IsActive = 1, [ModifiedDateTime] = GETUTCDATE() WHERE UserDeviceDetailId = @UserDeviceDetailId";
                    var updParameters = new { DeviceToken = deviceModel.DeviceToken, UserDeviceDetailId = result };
                    return await UpdateAsync(updQuery, updParameters);
                }
                else
                {
                    deviceModel.UserId = userId;
                    var propUserDeviceDataDic = ConvertToDictionary(deviceModel)
                        .ToDictionary(kvp => kvp.Key, kvp => kvp.Value?.ToString() ?? string.Empty); // Ensure all values are strings
                    return await AddAsync(Constants.UserDeviceDetailsTableName, propUserDeviceDataDic);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in add update user device info: {ex.Message}");
                return -1;
            }
        }
        public async Task<bool> DeleteUserDeviceInfo(int userId, string deviceId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"DELETE FROM {Constants.UserDeviceDetailsTableName} WHERE UserId = @UserId AND DeviceId = @DeviceId;");
                var parameters = new { UserId = userId, DeviceId = deviceId };
                await QueryAsync<int>(query.ToString(), parameters);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error in deleting user device info: {ex.Message}");
                throw;
            }
        }
    }
}
