using Microsoft.AspNetCore.Http;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces
{
    /// <summary>
    /// Interface for authentication strategies
    /// </summary>
    public interface IAuthenticationStrategy
    {
        /// <summary>
        /// Determines if this strategy can handle the current request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>True if this strategy can handle the request, false otherwise</returns>
        bool CanHandle(HttpContext context);

        /// <summary>
        /// Authenticates the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>
        /// A tuple containing:
        /// - bool: True if authentication was handled (success or failure), false if not handled
        /// - bool: True if authentication was successful, false if it failed
        /// </returns>
        Task<(bool Handled, bool Authenticated)> AuthenticateAsync(HttpContext context);
    }
}
