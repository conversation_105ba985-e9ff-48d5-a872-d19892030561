using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Http;
using Moq;
using MRI.OTA.Infrastructure.Telemetry;
using System.Security.Claims;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Telemetry
{
    public class UnitTestCustomTelemetryInitializer
    {
        private readonly Mock<IHttpContextAccessor> _httpContextAccessorMock;
        private readonly CustomTelemetryInitializer _initializer;

        public UnitTestCustomTelemetryInitializer()
        {
            _httpContextAccessorMock = new Mock<IHttpContextAccessor>();
            _initializer = new CustomTelemetryInitializer(_httpContextAccessorMock.Object);
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenHttpContextAccessorIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new CustomTelemetryInitializer(null!));
        }

        [Fact]
        public void Initialize_DoesNothing_WhenTelemetryIsNull()
        {
            // Arrange & Act & Assert
            // Should not throw
            _initializer.Initialize(null!);
        }

        [Fact]
        public void Initialize_DoesNothing_WhenHttpContextIsNull()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns((HttpContext?)null);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            // Should complete without throwing
            Assert.NotNull(telemetry);
        }

        [Fact]
        public void Initialize_SetsCorrelationId_WhenPresent()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            var correlationId = "test-correlation-id";
            context.Request.Headers["X-Correlation-ID"] = correlationId;

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.Equal(correlationId, telemetry.Context.Operation.Id);
        }

        [Fact]
        public void Initialize_DoesNotSetCorrelationId_WhenNotPresent()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.Null(telemetry.Context.Operation.Id);
        }

        [Fact]
        public void Initialize_SetsUserId_WhenUserAuthenticated()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            var userId = "user123";

            var claims = new[]
            {
                new Claim("sub", userId)
            };
            context.User = new ClaimsPrincipal(new ClaimsIdentity(claims, "test"));

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.Equal(userId, telemetry.Context.User.Id);
        }

        [Fact]
        public void Initialize_SetsUserIdFromNameIdentifier_WhenSubNotPresent()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            var userId = "user456";

            var claims = new[]
            {
                new Claim("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier", userId)
            };
            context.User = new ClaimsPrincipal(new ClaimsIdentity(claims, "test"));

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.Equal(userId, telemetry.Context.User.Id);
        }

        [Fact]
        public void Initialize_SetsAuthenticatedUserId_FromEmail()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            var email = "<EMAIL>";

            var claims = new[]
            {
                new Claim("emails", email)
            };
            context.User = new ClaimsPrincipal(new ClaimsIdentity(claims, "test"));

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.Equal(email, telemetry.Context.User.AuthenticatedUserId);
        }

        [Fact]
        public void Initialize_AddsUserProperties_WhenTelemetrySupportsProperties()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            var email = "<EMAIL>";
            var name = "Test User";

            var claims = new[]
            {
                new Claim("emails", email),
                new Claim("name", name)
            };
            context.User = new ClaimsPrincipal(new ClaimsIdentity(claims, "test"));

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.True(telemetry.Properties.ContainsKey("UserEmail"));
            Assert.True(telemetry.Properties.ContainsKey("UserName"));
            Assert.Equal(email, telemetry.Properties["UserEmail"]);
            Assert.Equal(name, telemetry.Properties["UserName"]);
        }

        [Fact]
        public void Initialize_DoesNotSetUserDetails_WhenUserNotAuthenticated()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            context.User = new ClaimsPrincipal(new ClaimsIdentity()); // Not authenticated

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.Null(telemetry.Context.User.Id);
            Assert.Null(telemetry.Context.User.AuthenticatedUserId);
        }

        [Fact]
        public void Initialize_SetsDeviceOperatingSystem_FromUserAgent()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            var userAgent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
            context.Request.Headers["User-Agent"] = userAgent;

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.NotNull(telemetry.Context.Device.OperatingSystem);
        }

        [Fact]
        public void Initialize_AddsDeviceProperties_WhenTelemetrySupportsProperties()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            var userAgent = "Mozilla/5.0 (iPhone; CPU iPhone OS 14_0 like Mac OS X) AppleWebKit/605.1.15";
            context.Request.Headers["User-Agent"] = userAgent;

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.True(telemetry.Properties.ContainsKey("DeviceFamily"));
            Assert.True(telemetry.Properties.ContainsKey("DeviceBrand"));
            Assert.True(telemetry.Properties.ContainsKey("DeviceModel"));
            Assert.True(telemetry.Properties.ContainsKey("BrowserVersion"));
            Assert.True(telemetry.Properties.ContainsKey("OSVersion"));
        }

        [Fact]
        public void Initialize_HandlesInvalidUserAgent_Gracefully()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            context.Request.Headers["User-Agent"] = "invalid-user-agent";

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act & Assert
            // Should not throw
            _initializer.Initialize(telemetry);
        }

        [Fact]
        public void Initialize_AddsRequestDetails_WhenTelemetrySupportsProperties()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            context.Request.Method = "POST";
            context.Request.Path = "/api/test";
            context.Request.QueryString = new QueryString("?param=value");

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(telemetry);

            // Assert
            Assert.True(telemetry.Properties.ContainsKey("Method"));
            Assert.True(telemetry.Properties.ContainsKey("Path"));
            Assert.True(telemetry.Properties.ContainsKey("QueryString"));
            Assert.True(telemetry.Properties.ContainsKey("Host"));
            Assert.True(telemetry.Properties.ContainsKey("Protocol"));
            Assert.True(telemetry.Properties.ContainsKey("ClientIP"));
            Assert.Equal("POST", telemetry.Properties["Method"]);
            Assert.Equal("/api/test", telemetry.Properties["Path"]);
            Assert.Equal("?param=value", telemetry.Properties["QueryString"]);
        }

        [Fact]
        public void Initialize_HandlesEmptyUserAgent()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            context.Request.Headers["User-Agent"] = string.Empty;

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act & Assert
            // Should not throw
            _initializer.Initialize(telemetry);
        }

        [Fact]
        public void Initialize_HandlesMissingUserAgent()
        {
            // Arrange
            var telemetry = new RequestTelemetry();
            var context = CreateHttpContext();
            // No User-Agent header

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act & Assert
            // Should not throw
            _initializer.Initialize(telemetry);
        }

        [Fact]
        public void Initialize_WorksWithDifferentTelemetryTypes()
        {
            // Arrange
            var requestTelemetry = new RequestTelemetry();
            var eventTelemetry = new EventTelemetry();
            var exceptionTelemetry = new ExceptionTelemetry();
            var context = CreateHttpContext();
            var correlationId = "test-id";
            context.Request.Headers["X-Correlation-ID"] = correlationId;

            _httpContextAccessorMock.Setup(h => h.HttpContext).Returns(context);

            // Act
            _initializer.Initialize(requestTelemetry);
            _initializer.Initialize(eventTelemetry);
            _initializer.Initialize(exceptionTelemetry);

            // Assert
            Assert.Equal(correlationId, requestTelemetry.Context.Operation.Id);
            Assert.Equal(correlationId, eventTelemetry.Context.Operation.Id);
            Assert.Equal(correlationId, exceptionTelemetry.Context.Operation.Id);
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.User = new ClaimsPrincipal(new ClaimsIdentity());
            return context;
        }
    }
}
