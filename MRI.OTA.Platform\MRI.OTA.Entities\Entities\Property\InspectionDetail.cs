﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class InspectionDetail
    {
        [ExcludeColumn]
        public int InspectionsDetailId { get; set; }
        public string SRCManagementId { get; set; }
        public string SRCTenancyId { get; set; }
        public string SRCPropertyId { get; set; }
        public int PropertyId { get; set; }
        public string SRCInspectionId { get; set; }
        public string InspectionStatus { get; set; }
        public DateTime InspectionDate { get; set; }
        public DateTime InspectionStartTime { get; set; }
        public DateTime InspectionEndTime { get; set; }
        public string Summary { get; set; }
    }
}
