﻿using System.Data;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Common.Repository;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.DBCore.Repositories
{
    public class EventConsumerRepository : BaseRepository<EventConsumer, int>, IEventConsumerRepository
    {
        protected readonly IDapperWrapper _dapperWrapper;

        /// <summary>
        /// Contructor for event consumer repository
        /// </summary>
        /// <param name="dbConnection"></param>
        public EventConsumerRepository(IDbConnectionFactory dbConnection, ILogger<EventConsumerRepository> logger, IDapperWrapper dapperWrapper) : base(dbConnection, logger, dapperWrapper)
        {
        }
    }
}
