name: deploy-acr-apac-shared-platform-mri-ota-api

on:
  workflow_dispatch:
  push:
    branches:
      - develop
      - 'feature/**'
  pull_request:
    branches:
      - develop

jobs:
  test:
    name: 'Run Unit Tests'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      - name: Restore dependencies
        run: dotnet restore MRI.OTA.Platform/MRI.OTA.Tests/MRI.OTA.UnitTestCases.csproj

      - name: Build
        run: dotnet build MRI.OTA.Platform/MRI.OTA.Tests/MRI.OTA.UnitTestCases.csproj --no-restore

      - name: Run Unit Tests
        run: dotnet test MRI.OTA.Platform/MRI.OTA.Tests/MRI.OTA.UnitTestCases.csproj --configuration Release --logger "console;verbosity=detailed" --blame-hang-timeout 60000
  build:
    name: 'Build and Push'
    runs-on: ubuntu-latest
    needs: test  # only run build if tests pass

    defaults:
      run:
        shell: bash

    env:
      IMAGE_NAME: 'apacsharedplatform/mriota/api'

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Replace placeholders in appsettings.Production.json
        run: |
          FILE="MRI.OTA.Platform/MRI.OTA.API/appsettings.json"
          sed -i "s|#{ota_db_connection_string}|${{ secrets.OTA_DB_CONNECTION_STRING }}|g" $FILE
          sed -i "s|#{ota_tenant_id}|${{ secrets.OTA_TENANT_ID }}|g" $FILE
          sed -i "s|#{ota_tenant_name}|${{ secrets.OTA_TENANT_NAME }}|g" $FILE
          sed -i "s|#{ota_client_id}|${{ secrets.OTA_CLIENT_ID }}|g" $FILE
          escaped_secret=$(printf '%s\n' "${{ secrets.OTA_CLIENT_SECRET }}" | sed 's/[&/]/\\&/g')
          sed -i "s|#{ota_client_secret}|$escaped_secret|g" $FILE
          sed -i "s|#{ota_b2c_instance}|${{ secrets.OTA_B2C_INSTANCE }}|g" $FILE
          sed -i "s|#{ota_b2c_domain}|${{ secrets.OTA_B2C_DOMAIN }}|g" $FILE
          sed -i "s|#{ota_signup_signin_policyid}|${{ secrets.OTA_SIGNUP_SIGNIN_POLICYID }}|g" $FILE
          sed -i "s|#{ota_api_allowed_origins}|${{ secrets.OTA_API_ALLOWED_ORIGINS }}|g" $FILE
          sed -i "s|#{ota_app_insight_connection_string}|${{ secrets.OTA_APP_INSIGHT_CONNECTION_STRING }}|g" $FILE
          sed -i "s|#{ota_storage_connection_string}|${{ secrets.ota_storage_connection_string }}|g" $FILE
          sed -i "s|#{ota_storage_container_name}|${{ secrets.ota_storage_container_name }}|g" $FILE
          sed -i "s|#{ota_azure_communication_connection_string}|${{ secrets.ota_azure_communication_connection_string }}|g" $FILE
          sed -i "s|#{ota_azure_communication_from_email}|${{ secrets.ota_azure_communication_from_email }}|g" $FILE
          sed -i "s|#{ota_redis_connection_string}|${{ secrets.ota_redis_connection_string }}|g" $FILE
          sed -i "s|#{ota_redis_default_expiration_minutes}|${{ secrets.ota_redis_default_expiration_minutes }}|g" $FILE
          sed -i "s|#{ota_base_url}|${{ secrets.ota_base_url }}|g" $FILE
          sed -i "s|#{ota_default_image_url}|${{ secrets.ota_default_image_url }}|g" $FILE
          echo "✅ Secrets replaced in appsettings.json:"
          cat $FILE
          FILE="MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json"
          sed -i "s|#{ota_db_connection_string}|${{ secrets.OTA_DB_CONNECTION_STRING }}|g" $FILE
          sed -i "s|#{ota_tenant_id}|${{ secrets.OTA_TENANT_ID }}|g" $FILE
          sed -i "s|#{ota_tenant_name}|${{ secrets.OTA_TENANT_NAME }}|g" $FILE
          sed -i "s|#{ota_client_id}|${{ secrets.OTA_CLIENT_ID }}|g" $FILE
          escaped_secret=$(printf '%s\n' "${{ secrets.OTA_CLIENT_SECRET }}" | sed 's/[&/]/\\&/g')
          sed -i "s|#{ota_client_secret}|$escaped_secret|g" $FILE
          sed -i "s|#{ota_b2c_instance}|${{ secrets.OTA_B2C_INSTANCE }}|g" $FILE
          sed -i "s|#{ota_b2c_domain}|${{ secrets.OTA_B2C_DOMAIN }}|g" $FILE
          sed -i "s|#{ota_signup_signin_policyid}|${{ secrets.OTA_SIGNUP_SIGNIN_POLICYID }}|g" $FILE
          sed -i "s|#{ota_api_allowed_origins}|${{ secrets.OTA_API_ALLOWED_ORIGINS }}|g" $FILE
          sed -i "s|#{ota_app_insight_connection_string}|${{ secrets.OTA_APP_INSIGHT_CONNECTION_STRING }}|g" $FILE
          sed -i "s|#{ota_storage_connection_string}|${{ secrets.ota_storage_connection_string }}|g" $FILE
          sed -i "s|#{ota_storage_container_name}|${{ secrets.ota_storage_container_name }}|g" $FILE
           sed -i "s|#{ota_azure_communication_connection_string}|${{ secrets.ota_azure_communication_connection_string }}|g" $FILE
          sed -i "s|#{ota_azure_communication_from_email}|${{ secrets.ota_azure_communication_from_email }}|g" $FILE
          sed -i "s|#{ota_redis_connection_string}|${{ secrets.ota_redis_connection_string }}|g" $FILE
          sed -i "s|#{ota_redis_default_expiration_minutes}|${{ secrets.ota_redis_default_expiration_minutes }}|g" $FILE
          sed -i "s|#{ota_base_url}|${{ secrets.ota_base_url }}|g" $FILE
          sed -i "s|#{ota_default_image_url}|${{ secrets.ota_default_image_url }}|g" $FILE
          echo "✅ Secrets replaced in appsettings.Production.json:"
          cat $FILE
          
      - name: Docker Login
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_ENDPOINT }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push to ACR
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile
          push: true
          tags: |
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:latest
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:revision-${{ github.run_id }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}
      
      - name: Debug Image Tag
        run: |
          echo "Final image: ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:revision-${{ github.run_id }}"

      - name: Deploy to Azure Container Apps
        run: |
          IMAGE="${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:revision-${{ github.run_id }}"
          echo "Deploying image: $IMAGE"

          az containerapp update \
            --name "${{ secrets.CONTAINER_APP_NAME }}" \
            --resource-group "${{ secrets.RESOURCE_GROUP }}" \
            --image "$IMAGE"

