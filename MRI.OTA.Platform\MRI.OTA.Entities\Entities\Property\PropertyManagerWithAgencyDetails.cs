using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    /// <summary>
    /// Entity for Property Manager Information with Agency Details
    /// </summary>
    public class PropertyManagerWithAgencyDetails
    {
        [ExcludeColumn]
        public int PropertyManagerInformationId { get; set; }
        [ExcludeColumn]
        public int PropertyId { get; set; }
        public string? SRCAgencyId { get; set; }
        public string? SRCPropertyId { get; set; }
        public string? SRCManagementId { get; set; }
        public string? ManagementType { get; set; }
        public string? PropertyManagerName { get; set; }
        public string? PropertyManagerMobile { get; set; }
        public string? PropertyManagerPhone { get; set; }
        public string? PropertyManagerEmail { get; set; }
        public DateTime? AuthorityStartDate { get; set; }
        public DateTime? AuthorityEndDate { get; set; }
        public string? Ownership { get; set; }
        public decimal? ExpenditureLimit { get; set; }
        public string? ExpenditureNotes { get; set; }

        // AgencyDetails fields - AgencyName comes from BusinessRegisteredName
        public string? AgencyName { get; set; } // Mapped from AD.BusinessRegisteredName
        public string? BusinessRegisteredName { get; set; }
        public string? BusinessName { get; set; }
        [ExcludeColumn]
        public string? ContactRole { get; set; }
    }
} 