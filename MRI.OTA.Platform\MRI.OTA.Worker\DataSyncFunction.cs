using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MRI.OTA.Worker.Interface;
using MRI.OTA.Worker.Service;
using System.Net;

namespace MRI.OTA.Worker
{
    public class DataSyncFunction
    {
        private readonly ILogger _logger;
        private readonly ISyncPropertyTreeDataService _syncPropertyTreeDataService;
        private readonly IAzureB2CTokenService _azureB2CTokenService;
        private readonly IConfiguration _configuration;

        public DataSyncFunction(ILoggerFactory loggerFactory, ISyncPropertyTreeDataService syncPropertyTreeDataService, IAzureB2CTokenService azureB2CTokenService, IConfiguration configuration)
        {
            _logger = loggerFactory.CreateLogger<DataSyncFunction>();
            _syncPropertyTreeDataService = syncPropertyTreeDataService;
            _azureB2CTokenService = azureB2CTokenService;
            _configuration = configuration;
        }

        [Function("SyncPropertyDataFunction")]
        public async Task SyncPropertyDataFunction([TimerTrigger("%PROPERTY_DATA_TIMER%", RunOnStartup = true)] TimerInfo timer)
        {
            _logger.LogInformation($"SyncPropertyTreeDataFunction executed at: {DateTime.Now}");

            var token = await _azureB2CTokenService.GetAccessTokenAsync();
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncProperty);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncManagement);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncFinancials);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncTenanciesOwner);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncTenanciesTenant);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncMaintenance);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncInspections);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncCompliance);
            await _syncPropertyTreeDataService.SyncPropertyData(token, Constants.SyncDocument);

            _logger.LogInformation($"Next timer schedule at: {timer.ScheduleStatus?.Next}");
        }

        [Function("SyncAgencyDataFunction")]
        public async Task SyncAgencyDataFunction([TimerTrigger("%AGENCY_DATA_TIMER%", RunOnStartup = true)] TimerInfo timer)
        {
            _logger.LogInformation($"SyncPropertyTreeAgencyDataFunction executed at: {DateTime.Now}");

            var token = await _azureB2CTokenService.GetAccessTokenAsync();
            await _syncPropertyTreeDataService.SyncPropertyAgencyData(token, Constants.SyncAgency);
            await _syncPropertyTreeDataService.SyncPropertyAgencyData(token, Constants.SyncAgencyPartner);

            _logger.LogInformation($"Next timer schedule at: {timer.ScheduleStatus?.Next}");
        }

        [Function("HealthCheck")]
        public async Task<HttpResponseData> HealthCheck([HttpTrigger(AuthorizationLevel.Anonymous, "get", Route = "health")] HttpRequestData req)
        {
            _logger.LogInformation("Health check endpoint called at: {DateTime}", DateTime.Now);

            var healthStatus = new
            {
                Status = "Healthy",
                Timestamp = DateTime.UtcNow,
                Version = "1.0.0",
                Environment = Environment.GetEnvironmentVariable("AZURE_FUNCTIONS_ENVIRONMENT") ?? "Unknown",
                Services = new
                {
                    AzureB2CTokenService = await CheckServiceHealthAsync(() => _azureB2CTokenService != null),
                    SyncPropertyTreeDataService = await CheckServiceHealthAsync(() => _syncPropertyTreeDataService != null),
                    BaseUrl = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("BaseUrl")),
                    ApplicationInsights = !string.IsNullOrEmpty(Environment.GetEnvironmentVariable("ApplicationInsights__ConnectionString")),
                    TimerConfiguration = new
                    {
                        PropertyDataTimer = _configuration["PROPERTY_DATA_TIMER"] ?? "Not configured",
                        AgencyDataTimer = _configuration["AGENCY_DATA_TIMER"] ?? "Not configured"
                    }
                }
            };

            var response = req.CreateResponse(HttpStatusCode.OK);
            response.Headers.Add("Content-Type", "application/json");
            await response.WriteStringAsync(System.Text.Json.JsonSerializer.Serialize(healthStatus, new System.Text.Json.JsonSerializerOptions
            {
                WriteIndented = true
            }));

            return response;
        }

        private async Task<bool> CheckServiceHealthAsync(Func<bool> serviceCheck)
        {
            try
            {
                return await Task.FromResult(serviceCheck());
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Service health check failed");
                return false;
            }
        }
    }
}
