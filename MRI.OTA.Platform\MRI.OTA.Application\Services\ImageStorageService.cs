﻿using AutoMapper;
using Azure.Storage.Blobs;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.DBCore.Repositories;

namespace MRI.OTA.Application.Services
{
    public class ImageStorageService : IImageStorageService
    {
        private readonly string _storageConnectionString;
        private readonly string _storageContainerName;
        private IImageRepository _imageRepository { get; set; }

        private IMapper _mapper { get; set; }

        private ILogger _logger { get; set; }

        /// <summary>
        /// Constructor for upload images
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="repository"></param>
        /// <param name="mapper"></param>
        /// <param name="configuration"></param>
        /// <exception cref="ArgumentNullException"></exception>
        public ImageStorageService(ILogger<MasterService> logger, IImageRepository repository, IMapper mapper,IConfiguration configuration)
        {
            _imageRepository = repository;
            _mapper = mapper;
            _logger = logger;
            _storageConnectionString = configuration["AzureStorage:ConnectionString"] ?? throw new ArgumentNullException(nameof(configuration), "AzureStorage:ConnectionString is not configured.");
            _storageContainerName = configuration["AzureStorage:ContainerName"] ?? throw new ArgumentNullException(nameof(configuration), "AzureStorage:ContainerName is not configured.");
        }

        /// <summary>
        /// Upload images
        /// </summary>
        /// <param name="images"></param>
        /// <returns></returns>
        public async Task<List<(int PropertyId, string ImageBlobUrl)>> UploadImages(ImageModel images)
        {
            var blobClient = new BlobContainerClient(_storageConnectionString, _storageContainerName);
            await blobClient.CreateIfNotExistsAsync();

            var uploadedImages = new List<(int PropertyId, string ImageBlobUrl)>();

            // set default image id to 0 if not provided
            if (images.DefaultImageIndex == null)
            {
                images.DefaultImageIndex = 0;
            }
            var defaultImageUri = string.Empty;
            foreach (var file in images.Files)
            {
                // Generate unique filename without folder  
                string fileName = file.FileName != null ? $"{Guid.NewGuid()}{Path.GetExtension(file.FileName)}" : $"{Guid.NewGuid()}.unknown";

                var blob = blobClient.GetBlobClient(fileName);

                // Convert base64 to byte array
                byte[] imageBytes;
                if (file.Base64Content.Contains(","))
                {
                    // Handle data URL format (e.g., "data:image/jpeg;base64,/9j/4AAQSkZJRg...")
                    var base64Data = file.Base64Content.Split(',')[1];
                    imageBytes = Convert.FromBase64String(base64Data);
                }
                else
                {
                    // Handle raw base64 string
                    imageBytes = Convert.FromBase64String(file.Base64Content);
                }

                // Upload the file  
                using (var stream = new MemoryStream(imageBytes))
                {
                    await blob.UploadAsync(stream, true);
                }

                // Get current index to check if this is the default image
                int currentIndex = images.Files.IndexOf(file);
                if(currentIndex == images.DefaultImageIndex)
                {
                    defaultImageUri = blob.Uri.ToString();
                }

                // Add PropertyId, URL, and default image flag to the list  
                uploadedImages.Add((images.PropertyId, blob.Uri.ToString()));
            }
            // Convert the list to PropertyImages entity using mapper
            var propertyImages = _mapper.Map<List<PropertyImages>>(uploadedImages);
            var result = await _imageRepository.AddPropertyImages(propertyImages);
            if(result > 0 && result == uploadedImages.Count)
            {
                if (images.DefaultImageIndex != Constants.NotAddDefaultImageIndex)
                {
                    await _imageRepository.UpdateDefaultImage(images.PropertyId, defaultImageUri);
                }
                return uploadedImages;
            }
            else
            {
                return new List<(int PropertyId, string ImageBlobUrl)>();
            }
        }

        public async Task<int> UpdateDefaultImage(UpdateDefaultImageModel defaultImageModel)
        {
            try
            {
                var result = await _imageRepository.UpdateDefaultImage(defaultImageModel.PropertyId, defaultImageModel.PropertyImagesId, defaultImageModel.UserId);

                return result ? Constants.Success : Constants.Error;   
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating default image : {ex.Message}");
            }
            return -1;
        }

        /// <summary>
        /// Delete property images
        /// </summary>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public async Task<int> DeletePropertyImage(int propertyImagesId)
        {
            try
            {
                var result = await _imageRepository.GetPropertyImage(propertyImagesId);
                if (result == null)
                {
                    _logger.LogError($"Image with ID {propertyImagesId} not found.");
                    return -1;
                }
                else
                {
                    var defaultImage = await _imageRepository.GetPropertyDefaultImage(result.PropertyId, propertyImagesId);
                    if (defaultImage != null && defaultImage.DefaultImageId > 0)
                    {
                        return Constants.DefaultImageExists;
                    }
                    var blobClient = new BlobContainerClient(_storageConnectionString, _storageContainerName);
                    var blobName = Path.GetFileName(result.ImageBlobUrl);
                    var blob = blobClient.GetBlobClient(blobName);
                    var bolbresult = await blob.DeleteIfExistsAsync();
                    // delete from database  
                    await _imageRepository.DeleteImage(propertyImagesId);
                    return Constants.Success;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error deleting property : {ex.Message}");
            }
            return -1;
        }

        public async Task<int> DeletePropertyImagesByUserId(int userId)
        {
            try
            {
                var result = await _imageRepository.GetPropertyImagesByUserId(userId);
                if (result == null || result.Count == 0)
                {
                    return Constants.Success;
                }
                else
                {
                    var blobClient = new BlobContainerClient(_storageConnectionString, _storageContainerName);
                    foreach (var image in result)
                    {
                        var blobName = Path.GetFileName(image.ImageBlobUrl);
                        var blob = blobClient.GetBlobClient(blobName);
                        var bolbresult = await blob.DeleteIfExistsAsync();
                    }
                    return Constants.Success;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error deleting images from blob storage : {ex.Message}");
            }
            return Constants.Error;
        }
    }
}
