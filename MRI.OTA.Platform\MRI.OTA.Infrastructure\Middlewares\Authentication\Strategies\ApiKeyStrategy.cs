using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies
{
    /// <summary>
    /// Strategy for handling API key authentication
    /// </summary>
    public class ApiKeyStrategy : IAuthenticationStrategy
    {
        private readonly RequestDelegate _next;
        private readonly ILogger _logger;
        private readonly IResponseGenerator _responseGenerator;

        /// <summary>
        /// Constructor for ApiKeyStrategy
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="logger">The logger</param>
        /// <param name="responseGenerator">The response generator</param>
        public ApiKeyStrategy(RequestDelegate next, ILogger logger, IResponseGenerator responseGenerator)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _responseGenerator = responseGenerator ?? throw new ArgumentNullException(nameof(responseGenerator));
        }

        /// <summary>
        /// Determines if this strategy can handle the current request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>True if this strategy can handle the request, false otherwise</returns>
        public bool CanHandle(HttpContext context)
        {
            var accessKey = context.Request.Headers["AccessKey"].ToString();
            var accessSecret = context.Request.Headers["AccessSecret"].ToString();
            return !string.IsNullOrEmpty(accessKey) && !string.IsNullOrEmpty(accessSecret);
        }

        /// <summary>
        /// Authenticates the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>A tuple indicating if the request was handled and authenticated</returns>
        public async Task<(bool Handled, bool Authenticated)> AuthenticateAsync(HttpContext context)
        {
            _logger.LogInformation("Attempting API Key authentication for request to {Path}", context.Request.Path);

            await _next(context);

            if (context.Response.StatusCode == StatusCodes.Status401Unauthorized)
            {
                await _responseGenerator.WriteUnauthorizedResponse(
                    context, 
                    MessagesConstants.InvalidApiKeyMessage, 
                    StatusCodes.Status401Unauthorized);
            }
            
            return (true, context.Response.StatusCode != StatusCodes.Status401Unauthorized);
        }
    }
}
