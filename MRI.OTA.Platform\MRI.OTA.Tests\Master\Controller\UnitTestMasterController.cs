﻿using Microsoft.AspNetCore.Mvc;
using Moq;
using MRI.OTA.API.Controllers.Master.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.Master;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.Tests.Master.Controller
{
    public class UnitTestMasterController
    {
        private readonly Mock<IMasterService> _mockMasterService;
        private readonly TaskContext _taskContext;
        private readonly MasterController _controller;

        public UnitTestMasterController()
        {
            _mockMasterService = new Mock<IMasterService>();
            _taskContext = new TaskContext { UserId = 1 };
            _controller = new MasterController(_mockMasterService.Object);
        }

        [Fact]
        public async Task GetAllCountries_ReturnsOkResult_WithListOfCountries()
        {
            // Arrange
            var countries = new List<CountryModel>
            {
                new CountryModel { CountryCode = "AU", CountryName = "Australia" },
                new CountryModel { CountryCode = "CA", CountryName = "Canada" }
            };
            _mockMasterService.Setup(service => service.GetAllCountriesAsync()).ReturnsAsync(countries);

            // Act
            var result = await _controller.GetAllCountries();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<List<CountryModel>>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(200, apiResponse.StatusCode);
            Assert.Equal(countries, apiResponse.Data);
        }

        [Fact]
        public async Task GetAllCountries_ReturnsNotFoundResult_WhenCountriesAreNull()
        {
            // Arrange
            _mockMasterService.Setup(service => service.GetAllCountriesAsync())
                .ReturnsAsync((List<CountryModel>)null);

            // Act
            var result = await _controller.GetAllCountries();

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(404, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetAllStates_ReturnsOkResult_WithListOfStates()
        {
            // Arrange
            var states = new List<StateModel>
            {
                new StateModel { StateCode = "NSW", StateName = "New South Wales", CountryCode = "AU" },
                new StateModel { StateCode = "TX", StateName = "Texas", CountryCode = "US" }
            };
            _mockMasterService.Setup(service => service.GetAllStatesAsync())
                .ReturnsAsync(states);

            // Act
            var result = await _controller.GetAllStates();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(200, apiResponse.StatusCode);
            Assert.Equal(states, apiResponse.Data);
        }

        [Fact]
        public async Task GetAllStates_ReturnsNotFoundResult_WhenStatesAreNull()
        {
            // Arrange
            _mockMasterService.Setup(service => service.GetAllStatesAsync())
                .ReturnsAsync((List<StateModel>)null);

            // Act
            var result = await _controller.GetAllStates();

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(404, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetAllAdminAreas_ReturnsOkResult_WithListOfAdminAreas()
        {
            // Arrange
            var adminAreas = new List<AdminAreaModel>
    {
        new AdminAreaModel { AreaCode = "AA1", CountryCode = "AU", LocalName = "Area 1", Type = "Type1" },
        new AdminAreaModel { AreaCode = "AA2", CountryCode = "US", LocalName = "Area 2", Type = "Type2" }
    };
            _mockMasterService.Setup(service => service.GetDataByTableName<AdminAreaModel>(Constants.AdministrativeAreasTableName))
                .ReturnsAsync(adminAreas);

            // Act
            var result = await _controller.GetAllAdminAreas();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(200, apiResponse.StatusCode);
            Assert.Equal(adminAreas, apiResponse.Data);
        }

        [Fact]
        public async Task GetAllAdminAreas_ReturnsNotFoundResult_WhenAdminAreasAreNull()
        {
            // Arrange
            _mockMasterService.Setup(service => service.GetDataByTableName<AdminAreaModel>(Constants.AdministrativeAreasTableName))
                .ReturnsAsync((List<AdminAreaModel>)null);

            // Act
            var result = await _controller.GetAllAdminAreas();

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(404, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetAllOccupancyTypes_ReturnsOkResult_WithListOfOccupancyTypes()
        {
            // Arrange
            var occupancyTypes = new List<OccupancyTypesModel>
    {
        new OccupancyTypesModel { OccupancyTypesId = 1, OccupancyTypesName = "Type1", Description = "Description1" },
        new OccupancyTypesModel { OccupancyTypesId = 2, OccupancyTypesName = "Type2", Description = "Description2" }
    };
            _mockMasterService.Setup(service => service.GetAllOccupancyTypes())
                .ReturnsAsync(occupancyTypes);

            // Act
            var result = await _controller.GetAllOccupancyTypes();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(200, apiResponse.StatusCode);
            Assert.Equal(occupancyTypes, apiResponse.Data);
        }

        [Fact]
        public async Task GetAllOccupancyTypes_ReturnsNotFoundResult_WhenOccupancyTypesAreNull()
        {
            // Arrange
            _mockMasterService.Setup(service => service.GetAllOccupancyTypes())
                .ReturnsAsync((List<OccupancyTypesModel>)null);

            // Act
            var result = await _controller.GetAllOccupancyTypes();

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(404, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyRelationships_ReturnsOkResult_WithListOfPropertyRelationships()
        {
            // Arrange
            var propertyRelationships = new List<PropertyRelationshipsModel>
    {
        new PropertyRelationshipsModel { PropertyRelationshipId = 1, PropertyRelationshipName = "Relationship1", Description = "Description1" },
        new PropertyRelationshipsModel { PropertyRelationshipId = 2, PropertyRelationshipName = "Relationship2", Description = "Description2" }
    };
            _mockMasterService.Setup(service => service.GetPropertyRelationships())
                .ReturnsAsync(propertyRelationships);

            // Act
            var result = await _controller.GetPropertyRelationships();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(200, apiResponse.StatusCode);
            Assert.Equal(propertyRelationships, apiResponse.Data);
        }

        [Fact]
        public async Task GetPropertyRelationships_ReturnsNotFoundResult_WhenPropertyRelationshipsAreNull()
        {
            // Arrange
            _mockMasterService.Setup(service => service.GetPropertyRelationships())
                .ReturnsAsync((List<PropertyRelationshipsModel>)null);

            // Act
            var result = await _controller.GetPropertyRelationships();

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(404, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetOccupancyStatusType_ReturnsOkResult_WithListOfOccupancyStatusTypes()
        {
            // Arrange
            var occupancyStatusTypes = new List<OccupancyStatusTypeModel>
    {
        new OccupancyStatusTypeModel { OccupancyStatusTypeId = 1, OccupancyStatus = "Status1" },
        new OccupancyStatusTypeModel { OccupancyStatusTypeId = 2, OccupancyStatus = "Status2" }
    };
            _mockMasterService.Setup(service => service.GetOccupancyStatusType())
                .ReturnsAsync(occupancyStatusTypes);

            // Act
            var result = await _controller.GetOccupancyStatusType();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(200, apiResponse.StatusCode);
            Assert.Equal(occupancyStatusTypes, apiResponse.Data);
        }

        [Fact]
        public async Task GetOccupancyStatusType_ReturnsNotFoundResult_WhenOccupancyStatusTypesAreNull()
        {
            // Arrange
            _mockMasterService.Setup(service => service.GetOccupancyStatusType())
                .ReturnsAsync((List<OccupancyStatusTypeModel>)null);

            // Act
            var result = await _controller.GetOccupancyStatusType();

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(404, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetModulesList_ReturnsOkResult_WithListOfModuleList()
        {
            // Arrange
            var viewModuleRelationshipList = new List<ViewModuleRelationship>
    {
        new ViewModuleRelationship { ModuleId = 1, ModuleName = "Property Details", Description = "Property Details", ModuleOrder = 1, PropertyRelationshipId = 1, PropertyRelationshipName = "Owner" },
        new ViewModuleRelationship { ModuleId = 2, ModuleName = "Management Details", Description = "Management Details", ModuleOrder = 2, PropertyRelationshipId = 1, PropertyRelationshipName = "Owner" }

    };
            _mockMasterService.Setup(service => service.GetModulesList(1))
                .ReturnsAsync(viewModuleRelationshipList);

            // Act
            var result = await _controller.GetModulesList(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(200, apiResponse.StatusCode);
            Assert.Equal(viewModuleRelationshipList, apiResponse.Data);
        }
        [Fact]
        public async Task GetModulesList_ReturnsNotFoundResult_WhenModulePropertyRelationshipsAreNull()
        {
            // Arrange
            _mockMasterService.Setup(service => service.GetModulesList(11))
                .ReturnsAsync((List<ViewModuleRelationship>)null);

            // Act
            var result = await _controller.GetModulesList(11);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(404, apiResponse.StatusCode);
        }
    }
}
