﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.Application.Mappers
{
    public class UpdateUsersMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for update user mapper
        /// </summary>
        public UpdateUsersMappingProfile() : base("UpdateUsersMappingProfile")
        {
            CreateMap<Users, ViewUserProfileModel>()
            .ReverseMap();
            CreateMap<Users, TermsConditionModel>()
           .ReverseMap();
            CreateMap<Users, UserProfileSettingsModel>()
          .ReverseMap();
        }
    }
}
