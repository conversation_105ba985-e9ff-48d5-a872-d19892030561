﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Application.Models;
using MRI.OTA.Application.Services;
using MRI.OTA.DBCore.Interfaces;
using Microsoft.Extensions.Configuration;

namespace MRI.OTA.UnitTestCases.Datasource.Service
{
    public class UnitTestDataSourceService
    {
        private readonly Mock<IDataSourceRepository> _dataSourceRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger<DataSourceService>> _loggerMock;
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly DataSourceService _service;

        public UnitTestDataSourceService()
        {
            _dataSourceRepositoryMock = new Mock<IDataSourceRepository>();
            _mapperMock = new Mock<IMapper>();
            _loggerMock = new Mock<ILogger<DataSourceService>>();
            _configurationMock = new Mock<IConfiguration>();
            _service = new DataSourceService(
                _loggerMock.Object,
                _dataSourceRepositoryMock.Object,
                _mapperMock.Object,
                _configurationMock.Object
            );
        }

        [Fact]
        public async Task GetUserDataSource_ReturnsMappedModel()
        {
            // Arrange
            var accessKey = "key";
            var accessSecret = "secret";
            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Test" };
            var dataSourceModel = new DataSourceModel { DataSourceId = 1, Name = "Test" };

            _dataSourceRepositoryMock
                .Setup(r => r.GetUserDataSource(accessKey, accessSecret))
                .ReturnsAsync(dataSource);
            _mapperMock
                .Setup(m => m.Map<DataSourceModel>(dataSource))
                .Returns(dataSourceModel);

            // Act
            var result = await _service.GetUserDataSource(accessKey, accessSecret);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(dataSourceModel.DataSourceId, result.DataSourceId);
            Assert.Equal(dataSourceModel.Name, result.Name);
        }

        [Fact]
        public async Task UpdateDataSource_ReturnsResult()
        {
            // Arrange
            var accessKey = "key";
            var accessSecret = "secret";
            var updateModel = new DataSourceUpdateModel { ManifestJson = "{}" };
            var expectedResult = 1;

            _dataSourceRepositoryMock
                .Setup(r => r.UpdateDataSource(accessKey, accessSecret, updateModel.ManifestJson))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _service.UpdateDataSource(accessKey, accessSecret, updateModel);

            // Assert
            Assert.Equal(expectedResult, result);
        }
    }
}