using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces.Integration;
using System.Diagnostics;

namespace MRI.OTA.Application.Services
{
    /// <summary>
    /// Helper class for consistent Application Insights logging in batch operations
    /// </summary>
    public class BatchOperationLogger : IBatchOperationLogger
    {
        private readonly TelemetryClient _telemetryClient;
        private readonly ILogger<BatchOperationLogger> _logger;

        public BatchOperationLogger(TelemetryClient telemetryClient, ILogger<BatchOperationLogger> logger)
        {
            _telemetryClient = telemetryClient;
            _logger = logger;
        }

        /// <summary>
        /// Create a batch operation context with correlation ID and telemetry tracking
        /// </summary>
        /// <param name="operationName">Name of the batch operation</param>
        /// <param name="dataSourceName">Name of the data source being processed</param>
        /// <param name="batchSize">Size of the batch</param>
        /// <returns>BatchOperationContext for tracking</returns>
        public BatchOperationContext CreateBatchOperationContext(string operationName, string dataSourceName = null, int? batchSize = null)
        {
            var operationId = Guid.NewGuid().ToString();
            var stopwatch = Stopwatch.StartNew();
            
            var requestTelemetry = new RequestTelemetry
            {
                Name = operationName,
                Id = operationId,
                Timestamp = DateTimeOffset.UtcNow
            };

            var customProperties = new Dictionary<string, string>
            {
                { "OperationId", operationId },
                { "OperationType", "BatchOperation" }
            };

            if (!string.IsNullOrEmpty(dataSourceName))
                customProperties.Add("DataSourceName", dataSourceName);

            if (batchSize.HasValue)
                customProperties.Add("BatchSize", batchSize.Value.ToString());

            // Track operation start
            _telemetryClient.TrackTrace($"Starting batch operation: {operationName}", SeverityLevel.Information, customProperties);
            
            _logger.LogInformation("Starting batch operation {OperationName} with OperationId {OperationId}", operationName, operationId);

            return new BatchOperationContext
            {
                OperationId = operationId,
                OperationName = operationName,
                DataSourceName = dataSourceName,
                BatchSize = batchSize,
                RequestTelemetry = requestTelemetry,
                CustomProperties = customProperties,
                Stopwatch = stopwatch
            };
        }

        /// <summary>
        /// Log batch processing start for a specific data source
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="dataSourceName">Name of the data source</param>
        /// <param name="totalBatches">Total number of batches to process</param>
        public void LogBatchProcessingStart(BatchOperationContext context, string dataSourceName, int totalBatches)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties);
            properties["DataSourceName"] = dataSourceName;
            properties["TotalBatches"] = totalBatches.ToString();
            properties["Phase"] = "BatchProcessingStart";

            _telemetryClient.TrackTrace($"Starting batch processing for data source: {dataSourceName}", SeverityLevel.Information, properties);
            
            _logger.LogInformation("Starting batch processing for data source {DataSourceName} with {TotalBatches} batches. OperationId: {OperationId}", 
                dataSourceName, totalBatches, context.OperationId);
        }

        /// <summary>
        /// Log individual batch execution
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="batchSize">Size of current batch</param>
        /// <param name="itemsProcessed">Number of items processed in this batch</param>
        public void LogBatchExecution(BatchOperationContext context, int batchIndex, int batchSize, int itemsProcessed)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties)
            {
                { "BatchIndex", batchIndex.ToString() },
                { "BatchSize", batchSize.ToString() },
                { "ItemsProcessed", itemsProcessed.ToString() },
                { "Phase", "BatchExecution" }
            };

            _telemetryClient.TrackTrace($"Processing batch {batchIndex} with {itemsProcessed} items", SeverityLevel.Information, properties);
            
            _logger.LogInformation("Processing batch {BatchIndex} with {ItemsProcessed} items for {DataSourceName}. OperationId: {OperationId}", 
                batchIndex, itemsProcessed, context.DataSourceName, context.OperationId);
        }

        /// <summary>
        /// Log individual batch execution with specific entity IDs
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="entityIds">Array of entity IDs being processed in this batch</param>
        /// <param name="entityType">Type of entity (e.g., AgencyId, ManagementId, TenancyId)</param>
        public void LogBatchExecutionWithIds(BatchOperationContext context, int batchIndex, string[] entityIds, string entityType)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties)
            {
                { "BatchIndex", batchIndex.ToString() },
                { "EntityType", entityType },
                { "EntityCount", entityIds.Length.ToString() },
                { "EntityIds", string.Join(",", entityIds) },
                { "Phase", "BatchExecution" }
            };

            // Limit EntityIds in properties to avoid too long values for Application Insights
            if (properties["EntityIds"].Length > 1000)
            {
                properties["EntityIds"] = string.Join(",", entityIds.Take(20)) + $"... ({entityIds.Length} total)";
            }

            _telemetryClient.TrackTrace($"Processing batch {batchIndex} with {entityIds.Length} {entityType}s: {string.Join(",", entityIds.Take(5))}{(entityIds.Length > 5 ? "..." : "")}", SeverityLevel.Information, properties);
            
            _logger.LogInformation("Processing batch {BatchIndex} with {EntityCount} {EntityType}s for {DataSourceName}. IDs: {EntityIds}. OperationId: {OperationId}", 
                batchIndex, entityIds.Length, entityType, context.DataSourceName, string.Join(",", entityIds.Take(10)) + (entityIds.Length > 10 ? "..." : ""), context.OperationId);
        }

        /// <summary>
        /// Log batch execution success
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="rowsAffected">Number of rows affected by the batch</param>
        public void LogBatchSuccess(BatchOperationContext context, int batchIndex, int rowsAffected)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties)
            {
                { "BatchIndex", batchIndex.ToString() },
                { "RowsAffected", rowsAffected.ToString() },
                { "Phase", "BatchSuccess" }
            };

            _telemetryClient.TrackTrace($"Batch {batchIndex} completed successfully with {rowsAffected} rows affected", SeverityLevel.Information, properties);
            
            _logger.LogInformation("Batch {BatchIndex} completed successfully with {RowsAffected} rows affected for {DataSourceName}. OperationId: {OperationId}", 
                batchIndex, rowsAffected, context.DataSourceName, context.OperationId);
        }

        /// <summary>
        /// Log batch execution success with specific entity IDs
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="rowsAffected">Number of rows affected by the batch</param>
        /// <param name="entityIds">Array of entity IDs that were successfully processed</param>
        /// <param name="entityType">Type of entity processed</param>
        public void LogBatchSuccessWithIds(BatchOperationContext context, int batchIndex, int rowsAffected, string[] entityIds, string entityType)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties)
            {
                { "BatchIndex", batchIndex.ToString() },
                { "RowsAffected", rowsAffected.ToString() },
                { "EntityType", entityType },
                { "EntityCount", entityIds.Length.ToString() },
                { "EntityIds", string.Join(",", entityIds) },
                { "Phase", "BatchSuccess" }
            };

            // Limit EntityIds in properties to avoid too long values for Application Insights
            if (properties["EntityIds"].Length > 1000)
            {
                properties["EntityIds"] = string.Join(",", entityIds.Take(20)) + $"... ({entityIds.Length} total)";
            }

            _telemetryClient.TrackTrace($"Batch {batchIndex} completed successfully with {rowsAffected} rows affected for {entityIds.Length} {entityType}s", SeverityLevel.Information, properties);
            
            _logger.LogInformation("Batch {BatchIndex} completed successfully with {RowsAffected} rows affected for {EntityCount} {EntityType}s in {DataSourceName}. IDs: {EntityIds}. OperationId: {OperationId}", 
                batchIndex, rowsAffected, entityIds.Length, entityType, context.DataSourceName, string.Join(",", entityIds.Take(10)) + (entityIds.Length > 10 ? "..." : ""), context.OperationId);
        }

        /// <summary>
        /// Log batch execution error
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="exception">Exception that occurred</param>
        /// <param name="additionalInfo">Additional error context</param>
        public void LogBatchError(BatchOperationContext context, int batchIndex, Exception exception, string additionalInfo = null)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties)
            {
                { "BatchIndex", batchIndex.ToString() },
                { "ExceptionType", exception.GetType().Name },
                { "Phase", "BatchError" }
            };

            if (!string.IsNullOrEmpty(additionalInfo))
                properties.Add("AdditionalInfo", additionalInfo);

            _telemetryClient.TrackException(exception, properties);
            
            _logger.LogError(exception, "Batch {BatchIndex} failed for {DataSourceName}. OperationId: {OperationId}. Additional Info: {AdditionalInfo}", 
                batchIndex, context.DataSourceName, context.OperationId, additionalInfo ?? "None");
        }

        /// <summary>
        /// Log batch execution error with specific entity IDs
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="exception">Exception that occurred</param>
        /// <param name="entityIds">Array of entity IDs that failed processing</param>
        /// <param name="entityType">Type of entity that failed</param>
        /// <param name="additionalInfo">Additional error context</param>
        public void LogBatchErrorWithIds(BatchOperationContext context, int batchIndex, Exception exception, string[] entityIds, string entityType, string additionalInfo = null)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties)
            {
                { "BatchIndex", batchIndex.ToString() },
                { "ExceptionType", exception.GetType().Name },
                { "EntityType", entityType },
                { "EntityCount", entityIds.Length.ToString() },
                { "EntityIds", string.Join(",", entityIds) },
                { "Phase", "BatchError" }
            };

            if (!string.IsNullOrEmpty(additionalInfo))
                properties.Add("AdditionalInfo", additionalInfo);

            // Limit EntityIds in properties to avoid too long values for Application Insights
            if (properties["EntityIds"].Length > 1000)
            {
                properties["EntityIds"] = string.Join(",", entityIds.Take(20)) + $"... ({entityIds.Length} total)";
            }

            _telemetryClient.TrackException(exception, properties);
            
            _logger.LogError(exception, "Batch {BatchIndex} failed for {EntityCount} {EntityType}s in {DataSourceName}. Failed IDs: {EntityIds}. OperationId: {OperationId}. Additional Info: {AdditionalInfo}", 
                batchIndex, entityIds.Length, entityType, context.DataSourceName, string.Join(",", entityIds.Take(10)) + (entityIds.Length > 10 ? "..." : ""), context.OperationId, additionalInfo ?? "None");
        }

        /// <summary>
        /// Log batch operation completion
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="success">Whether the operation was successful</param>
        /// <param name="totalItemsProcessed">Total number of items processed across all batches</param>
        /// <param name="totalBatches">Total number of batches processed</param>
        public void LogBatchOperationComplete(BatchOperationContext context, bool success, int totalItemsProcessed = 0, int totalBatches = 0)
        {
            context.Stopwatch.Stop();
            var duration = context.Stopwatch.Elapsed;

            var properties = new Dictionary<string, string>(context.CustomProperties)
            {
                { "Success", success.ToString() },
                { "TotalItemsProcessed", totalItemsProcessed.ToString() },
                { "TotalBatches", totalBatches.ToString() },
                { "DurationMs", duration.TotalMilliseconds.ToString("F2") },
                { "Phase", "OperationComplete" }
            };

            if (totalItemsProcessed > 0 && duration.TotalSeconds > 0)
            {
                var throughput = totalItemsProcessed / duration.TotalSeconds;
                properties.Add("ItemsPerSecond", throughput.ToString("F2"));
            }

            context.RequestTelemetry.Success = success;
            context.RequestTelemetry.ResponseCode = success ? "200" : "500";
            context.RequestTelemetry.Duration = duration;
            
            _telemetryClient.TrackRequest(context.RequestTelemetry);

            var severityLevel = success ? SeverityLevel.Information : SeverityLevel.Error;
            _telemetryClient.TrackTrace($"Batch operation {context.OperationName} completed. Success: {success}", severityLevel, properties);
            
            _logger.LogInformation("Batch operation {OperationName} completed. Success: {Success}, Duration: {Duration}ms, Items Processed: {TotalItemsProcessed}, Batches: {TotalBatches}. OperationId: {OperationId}", 
                context.OperationName, success, duration.TotalMilliseconds, totalItemsProcessed, totalBatches, context.OperationId);
        }

        /// <summary>
        /// Log data source processing metrics
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="dataSourceName">Name of the data source</param>
        /// <param name="totalRecords">Total records processed for this data source</param>
        /// <param name="successfulBatches">Number of successful batches</param>
        /// <param name="failedBatches">Number of failed batches</param>
        public void LogDataSourceMetrics(BatchOperationContext context, string dataSourceName, int totalRecords, int successfulBatches, int failedBatches)
        {
            var properties = new Dictionary<string, string>(context.CustomProperties);
            properties["DataSourceName"] = dataSourceName;
            properties["TotalRecords"] = totalRecords.ToString();
            properties["SuccessfulBatches"] = successfulBatches.ToString();
            properties["FailedBatches"] = failedBatches.ToString();
            properties["Phase"] = "DataSourceMetrics";

            var successRate = successfulBatches + failedBatches > 0 ? (double)successfulBatches / (successfulBatches + failedBatches) * 100 : 0;
            properties["SuccessRate"] = successRate.ToString("F2");

            _telemetryClient.TrackTrace($"Data source {dataSourceName} processing metrics", SeverityLevel.Information, properties);
            
            _logger.LogInformation("Data source {DataSourceName} metrics - Records: {TotalRecords}, Successful Batches: {SuccessfulBatches}, Failed Batches: {FailedBatches}, Success Rate: {SuccessRate}%. OperationId: {OperationId}", 
                dataSourceName, totalRecords, successfulBatches, failedBatches, successRate, context.OperationId);
        }
    }

    /// <summary>
    /// Context object for tracking batch operations
    /// </summary>
    public class BatchOperationContext
    {
        public string OperationId { get; set; } = string.Empty;
        public string OperationName { get; set; } = string.Empty;
        public string DataSourceName { get; set; } = string.Empty;
        public int? BatchSize { get; set; }
        public RequestTelemetry RequestTelemetry { get; set; } = null!;
        public Dictionary<string, string> CustomProperties { get; set; } = new();
        public Stopwatch Stopwatch { get; set; } = new();
    }
} 