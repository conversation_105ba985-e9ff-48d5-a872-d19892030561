﻿using System;
using System.Collections.Generic;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace MRI.OTA.Common.Models
{
    public class EndpointDefinition
    {
        // Static JsonSerializerOptions to avoid creating a new instance for each serialization
        private static readonly JsonSerializerOptions _jsonOptions = new()
        {
            WriteIndented = true
        };

        // Dictionary mapping parameter types to their default values
        private static readonly Dictionary<string, object> _defaultTypeValues = new(StringComparer.OrdinalIgnoreCase)
        {
            { "string", string.Empty },
            { "integer", 0 },
            { "int", 0 },
            { "boolean", false },
            { "bool", false },
            { "double", 0.0 },
            { "float", 0.0 },
            { "decimal", 0.0 },
            { "datetime", DateTime.MinValue },
            { "date", DateTime.MinValue },
            { "array", Array.Empty<object>() },
            { "list", Array.Empty<object>() }
        };

        [JsonPropertyName("endpoint")]
        public string Endpoint { get; set; }

        [JsonPropertyName("method")]
        public string Method { get; set; }

        [JsonPropertyName("params")]
        public Dictionary<string, string> Parameters { get; set; }

        [JsonPropertyName("webComponent")]
        public string WebComponent { get; set; }

        [JsonPropertyName("mobileComponent")]
        public string MobileComponent { get; set; }

        /// <summary>
        /// Creates a JSON string from the Parameters dictionary
        /// </summary>
        /// <param name="paramValues">Optional values to use for the parameters</param>
        /// <returns>JSON string representation of parameters</returns>
        public string ParametersToJson(Dictionary<string, object>? paramValues = null)
        {
            // Create a dictionary for the request body
            var requestBody = new Dictionary<string, object>();

            // If parameters exist, populate the request body
            if (Parameters?.Count > 0)
            {
                // Process each parameter
                foreach (var param in Parameters)
                {
                    var paramName = param.Key;
                    var paramType = param.Value;

                    // Get value from provided values or use default
                    requestBody[paramName] = paramValues != null && paramValues.TryGetValue(paramName, out var value)
                        ? value
                        : GetDefaultValueForType(paramType);
                }
            }

            // Serialize the request body to JSON
            return JsonSerializer.Serialize(requestBody, _jsonOptions);
        }

        /// <summary>
        /// Gets the default value for a parameter based on its type
        /// </summary>
        /// <param name="paramType">The parameter type as a string</param>
        /// <returns>The default value for the type</returns>
        private static object GetDefaultValueForType(string paramType)
        {
            return _defaultTypeValues.TryGetValue(paramType, out var value)
                ? value
                : string.Empty; // Default to empty string for unknown types
        }
    }
}
