﻿namespace MRI.OTA.DBCore.Entities.Property
{
    public class ViewUserPropertiesNickName
    {
        /// <summary>
        /// userid
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// PropertyId
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// UserPropertiesNickNameId
        /// </summary>
        public int? UserPropertiesNickNameId { get; set; }

        /// <summary>
        /// PropertyName
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// PropertyNickName
        /// </summary>
        public string? PropertyNickName { get; set; }

        /// <summary>
        /// PropertyRelationshipId
        /// </summary>
        public int PropertyRelationshipId { get; set; }

        /// <summary>
        /// PropertyRelationshipName
        /// </summary>
        public string PropertyRelationshipName { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }
    }
}
