﻿using System.Collections.Generic;
using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class ComplianceDetailMappingProfile : Profile
    {
        public ComplianceDetailMappingProfile() : base("ComplianceDetailMappingProfile")
        {
            // Map from MaintenanceJobs to MaintenanceDetail
            CreateMap<ComplianceListResponse, ComplianceDetail>()
                .ForMember(dest => dest.ComplianceDetailId, opt => opt.Ignore()) // Auto-generated
                .ForMember(dest => dest.SRCManagementId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1)) // Default value, will be updated
                .ForMember(dest => dest.SRCPropertyId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.SRCComplianceId, opt => opt.MapFrom(src => src.ComplianceId))
                .ForMember(dest => dest.ComplianceName, opt => opt.MapFrom(src => src.ComplianceName))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate))
                .ForMember(dest => dest.ServicedBy, opt => opt.MapFrom(src => src.ServicedBy));

            // Map from ComplianceDetailResponse to List <ComplianceDetail>
            // This handles the flattening of the response structure
            CreateMap<ComplianceDetailResponse, List<ComplianceDetail>>()
                .ConvertUsing((src, dest, context) =>
                {
                    if (src?.ComplianceEntries == null || src.ComplianceEntries.Count == 0)
                        return [];

                    var details = new List<ComplianceDetail>();

                    foreach (var data in src.ComplianceEntries)
                    {
                        var detail = context.Mapper.Map<ComplianceDetail>(data);

                        // Set the parent-level properties
                        detail.SRCManagementId = src.ManagementId;
                        detail.SRCPropertyId = src.PropertyId;

                        details.Add(detail);
                    }

                    return details;
                });

            // Map from List<InspectionDetailResponse> to List<InspectionDetail>
            // This handles the flattening of multiple responses, each containing multiple jobs
            CreateMap<List<ComplianceDetailResponse>, List<ComplianceDetail>>()
                .ConvertUsing((src, dest, context) =>
                {
                    if (src == null || src.Count == 0)
                        return [];

                    var allDetails = new List<ComplianceDetail>();

                    foreach (var response in src)
                    {
                        // Use the existing single response mapping to flatten each response
                        var responseDetails = context.Mapper.Map<List<ComplianceDetail>>(response);
                        allDetails.AddRange(responseDetails);
                    }

                    return allDetails;
                });

            // Legacy mapping for backward compatibility
            CreateMap<ComplianceDetailResponse, ComplianceDetail>()
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1))
                .ForMember(dest => dest.SRCComplianceId, opt => opt.MapFrom(src =>
                    src.ComplianceEntries != null && src.ComplianceEntries.Count > 0
                        ? src.ComplianceEntries.First().ComplianceId
                        : null))
                .ForMember(dest => dest.ComplianceName, opt => opt.MapFrom(src =>
                    src.ComplianceEntries != null && src.ComplianceEntries.Count > 0
                        ? src.ComplianceEntries.First().ComplianceName
                        : null))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src =>
                    src.ComplianceEntries != null && src.ComplianceEntries.Count > 0
                        ? src.ComplianceEntries.First().Status
                        : null))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src =>
                    src.ComplianceEntries != null && src.ComplianceEntries.Count > 0
                        ? src.ComplianceEntries.First().ExpiryDate
                        : default))
                .ForMember(dest => dest.ServicedBy, opt => opt.MapFrom(src =>
                    src.ComplianceEntries != null && src.ComplianceEntries.Count > 0
                        ? src.ComplianceEntries.First().ServicedBy
                        : null))
                .ReverseMap();
        }
    }
}
