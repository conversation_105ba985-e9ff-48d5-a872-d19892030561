﻿using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities.Property;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.Property.v1
{
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/properties")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class PropertyController : ControllerBase
    {
        private readonly IPropertyService _propertyService;

        /// <summary>
        /// Constructor for propertyRelations controller
        /// </summary>
        /// <param name="propertyService"></param>
        public PropertyController(IPropertyService propertyService)
        {
            _propertyService = propertyService;
        }

        /// <summary>
        /// Get all properties
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<ViewUserProperties>), description: "user data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<ViewUserProperties>>> GetAllProperties([FromQuery]SearchCriteriaModel searchCriteria)
        {
            var usersProperties = await _propertyService.GetAllProperties(searchCriteria);
            if (usersProperties == null)
            {
                return NotFound(new ApiResponse<object>(false, "Item not found", data: null!, StatusCodes.Status404NotFound, new List<string> { "The item with the specified ID does not exist." }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", usersProperties));
        }

        /// <summary>
        /// Get propertyRelations details based on propertyid
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ViewUserProperties), description: "property data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<ViewUserProperties>> GetPropertyById(int id)
        {
            var property = await _propertyService.GetPropertyById(id);
            if (property == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", property));
        }

        /// <summary>
        /// Get propertyRelations relations based on userPropertiesNickNameId
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("property-relations")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ViewUserPropertiesNickName), description: "property relations data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<ViewUserPropertiesNickName>> GetPropertyRelations(int userPropertiesNickNameId)
        {
            var propertyRelations = await _propertyService.GetPropertyRelations(userPropertiesNickNameId);
            if (propertyRelations == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", propertyRelations));
        }

        /// <summary>
        /// Get propertyRelations relations based on userid
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("property-nicknames")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ViewUserPropertiesNickName), description: "property nicknames retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<ViewUserPropertiesNickName>> GetPropertyNickNames(int userId)
        {
            var propertyRelations = await _propertyService.GetPropertyNickNames(userId);
            if (propertyRelations == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", propertyRelations));
        }

        /// <summary>
        /// Add new propertyRelations
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "property data added successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> AddProperty(UserPropertiesModel userProperties)
        {
            var result = await _propertyService.AddProperty(userProperties);
            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item added successfully", result));
            }
            else if (result == Constants.DuplicateError)
            {
                return Ok(new ApiResponse<object>(false, "Please enter a unique nickname, or select an existing property.", null!, StatusCodes.Status200OK, new List<string> { "Please enter a unique nickname, or select an existing property." }));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "Item not added", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be added." }));
            }
        }

        /// <summary>
        /// Update propertyRelations
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPut]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "property data update successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> UpdateProperty([FromHeader]int propertyId, UserPropertiesModel userProperties)
        {
            userProperties.PropertyId = propertyId;
            var result = await _propertyService.UpdateProperty(userProperties);
            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item updated successfully", result));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "Item not updated", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be updated." }));
            }
        }

        [HttpPut("property-status")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "property data update successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> UpdatePropertyStatus(PropertyStatusModel propertyStatus)
        {
            var result = await _propertyService.UpdatePropertyStatus(propertyStatus);
            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, MessagesConstants.ItemUpdatedSucess, result));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotUpdated, null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotUpdated }));
            }
        }

        /// <summary>
        /// Update property portfolio - creates or updates property-nickname relationships
        /// </summary>
        /// <param name="portfolioRequest">Portfolio request containing userid, propertyid, nicknameid, and nickname</param>
        /// <returns></returns>
        [HttpPut("property-portfolio")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "Property portfolio updated successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> UpdatePropertyPortfolio(PropertyPortfolioModel portfolioRequest)
        {
            var result = await _propertyService.UpdatePropertyPortfolio(portfolioRequest);

            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Property portfolio updated successfully", result));
            }
            else if (result == Constants.DuplicateError)
            {
                return Ok(new ApiResponse<object>(false, "Please enter a unique nickname, or select an existing property.", null!, StatusCodes.Status200OK, new List<string> { "Please enter a unique nickname, or select an existing property." }));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "Property portfolio could not be updated", null!, StatusCodes.Status400BadRequest, new List<string> { "The property portfolio could not be updated." }));
            }
        }

        /// <summary>
        /// Delete propertyRelations
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpDelete]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "Property data deleted successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> DeleteProperty([FromHeader] int propertyId)
        {
            var result = await _propertyService.DeleteProperty(propertyId);
            if (result == Constants.Success)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item deleted successfully", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "The item could not be deleted.", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be deleted." }));
            }
        }

        /// <summary>
        /// Get count of active and inactive properties grouped by data source
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Counts of active and inactive properties by data source</returns>
        [HttpGet("counts-by-source")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(PropertyCountModel), description: "Property counts retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        public async Task<ActionResult<PropertyCountModel>> GetPropertyCountsByDataSource(int userId)
        {
            var result = await _propertyService.GetPropertyCountsByDataSource(userId);
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Property counts by data source retrieved successfully", result));
        }

        /// <summary>
        /// Get count of active and inactive properties grouped by agency
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Counts of active and inactive properties by agency</returns>
        [HttpGet("counts-by-agency")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(ViewAgencyPropertyCount), description: "Property counts retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        public async Task<ActionResult<ViewAgencyPropertyCount>> GetPropertyCountsByAgency(int userId)
        {
            var result = await _propertyService.GetPropertyCountsByAgency(userId);
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Property counts by data source retrieved successfully", result));
        }

        /// <summary>
        /// Get maintenance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>Maintenance details data</returns>
        [HttpGet("maintenance-details")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(object), description: "Maintenance details retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<MaintenanceDetailModel>>> GetMaintenanceDetails([FromQuery] int propertyId, [FromQuery] string? managementId, [FromQuery] string? tenancyId)
        {
            var maintenanceDetails = await _propertyService.GetMaintenanceDetails(propertyId, managementId, tenancyId);
            if (maintenanceDetails == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Maintenance details retrieved successfully", maintenanceDetails));
        }

        /// <summary>
        /// Get compliance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>Maintenance details data</returns>
        [HttpGet("compliance-details")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(object), description: "Compliance details retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<ComplianceDetailModel>>> GetComplianceDetails([FromQuery] string managementId, [FromQuery] int propertyId)
        {
            var maintenanceDetails = await _propertyService.GetCompliance(managementId, propertyId);
            if (maintenanceDetails == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Compliance details retrieved successfully", maintenanceDetails));
        }

        /// <summary>
        /// Get inspections list based on tenancyId and propertyId
        /// </summary>
        /// <param name="tenancyId">The tenancy ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>Inspections list data</returns>
        [HttpGet("inspections")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<InspectionDetailModel>), description: "Inspections list retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<InspectionDetailModel>>> GetInspections([FromQuery] int propertyId, [FromQuery] string? managementId, [FromQuery] string? tenancyId)
        {
            var inspections = await _propertyService.GetInspections(propertyId, managementId, tenancyId);
            if (inspections == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Inspections list retrieved successfully", inspections));
        }

        /// <summary>
        /// Get Documents list based on tenancyId or managementId
        /// </summary>
        /// <param name="tenancyId">The tenancy ID</param>
        /// <param name="managementId">The Management ID</param>
        /// <returns>Documents list data</returns>
        [HttpGet("documents")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<DocumentDetail>), description: "document list retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<UserPropertyDocumentDetail>>> GetDocuments([FromQuery] GetDocumentRequestModel requestModel)
        {
            var documets = await _propertyService.GetDocuments(requestModel);
            if (documets == null || documets.Count == 0)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Document list retrieved successfully", documets));
        }

        /// <summary>
        /// Get property manager information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID to search for property manager information (optional)</param>
        /// <param name="propertyId">The property ID to search for property manager information (optional)</param>
        /// <param name="srcPropertyId">The source property ID to search for property manager information (optional)</param>
        /// <returns>Property manager information data</returns>
        [HttpGet("management")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(PropertyManagerViewModel), description: "Property manager information retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<PropertyManagerViewModel>> GetPropertyManagerInformation([FromQuery] string? managementId, [FromQuery] int? propertyId, [FromQuery] string? srcPropertyId)
        {
            // Validation: At least one parameter must be provided
            if (string.IsNullOrWhiteSpace(managementId) && (propertyId == null || propertyId <= 0) && string.IsNullOrWhiteSpace(srcPropertyId))
            {
                return BadRequest(new ApiResponse<object>(false, "At least one parameter is required", null!, StatusCodes.Status400BadRequest, new List<string> { "At least one of the parameters (managementId, propertyId, or srcPropertyId) must be provided." }));
            }

            // Validation: If managementId is present, srcPropertyId must also be present
            if (!string.IsNullOrWhiteSpace(managementId) && string.IsNullOrWhiteSpace(srcPropertyId))
            {
                return BadRequest(new ApiResponse<object>(false, "Source Property ID is required when Management ID is provided", null!, StatusCodes.Status400BadRequest, new List<string> { "When managementId is provided, srcPropertyId must also be provided." }));
            }

            var managerInfo = await _propertyService.GetPropertyManagerInformation(managementId, propertyId, srcPropertyId);
            if (managerInfo == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<PropertyManagerViewModel>(true, StatusCodes.Status200OK, "Property manager information retrieved successfully", managerInfo));
        }

        /// <summary>
        /// Get property financial information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID to search for property financial information (optional)</param>
        /// <param name="propertyId">The property ID to search for property financial information (optional)</param>
        /// <param name="srcPropertyId">The source property ID to search for property financial information (optional)</param>
        /// <returns>Property financial information data</returns>
        [HttpGet("financials")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(PropertyFinancialViewModel), description: "Property financial information retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<PropertyFinancialViewModel>> GetPropertyFinancialInformation([FromQuery] string? managementId, [FromQuery] int? propertyId, [FromQuery] string? srcPropertyId)
        {
            // Validation: At least one parameter must be provided
            if (string.IsNullOrWhiteSpace(managementId) && (propertyId == null || propertyId <= 0) && string.IsNullOrWhiteSpace(srcPropertyId))
            {
                return BadRequest(new ApiResponse<object>(false, "At least one parameter is required", null!, StatusCodes.Status400BadRequest, new List<string> { "At least one of the parameters (managementId, propertyId, or srcPropertyId) must be provided." }));
            }

            // Validation: If managementId is present, srcPropertyId must also be present
            if (!string.IsNullOrWhiteSpace(managementId) && string.IsNullOrWhiteSpace(srcPropertyId))
            {
                return BadRequest(new ApiResponse<object>(false, "Source Property ID is required when Management ID is provided", null!, StatusCodes.Status400BadRequest, new List<string> { "When managementId is provided, srcPropertyId must also be provided." }));
            }

            var financialInfo = await _propertyService.GetPropertyFinancialInformation(managementId, propertyId, srcPropertyId);
            if (financialInfo == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<PropertyFinancialViewModel>(true, StatusCodes.Status200OK, "Property financial information retrieved successfully", financialInfo));
        }
        /// <summary>
        /// Get Tenancy list based on tenancyId, srcpropertyId or propertyId
        /// </summary>
        /// <param name="propertyId">The PropertyId ID</param>
        /// <param name="tenancyId">The tenancy ID</param>
        /// <param name="srcPropertyId">The SRCPropertyId ID</param>
        /// <returns>Tenancy-Owner list data</returns>
        [HttpGet("tenant-owner")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<TenanciesTenantDetailResponse>), description: "Data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<TenanciesTenantDetailResponse>> GetTenantOwnerDetail([FromQuery] int? propertyId, [FromQuery] string? tenancyId, [FromQuery] string? srcPropertyId)
        {
            // Validation: At least one parameter must be provided
            if (string.IsNullOrWhiteSpace(tenancyId) && (propertyId == null || propertyId <= 0) && string.IsNullOrWhiteSpace(srcPropertyId))
            {
                return BadRequest(new ApiResponse<object>(false, "At least one parameter is required", null!, StatusCodes.Status400BadRequest, new List<string> { "At least one of the parameters (tenancyId, propertyId, or srcPropertyId) must be provided." }));
            }

            // Validation: If tenancyId is present, srcPropertyId must also be present
            if (!string.IsNullOrWhiteSpace(tenancyId) && string.IsNullOrWhiteSpace(srcPropertyId))
            {
                return BadRequest(new ApiResponse<object>(false, "Source Property ID is required when TenancyId ID is provided", null!, StatusCodes.Status400BadRequest, new List<string> { "When tenancyId is provided, srcPropertyId must also be provided." }));
            }
            var data = await _propertyService.GetTenantOwnerDetail(tenancyId, srcPropertyId, propertyId);
            if (data == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Data retrieved successfully", data));
        }
    }
}
