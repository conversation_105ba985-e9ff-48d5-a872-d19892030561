﻿using System.Text;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.DBCore.Repositories
{
    public class ImageRepository : BaseRepository<PropertyImages, int>, IImageRepository
    {
        private readonly ILogger<ImageRepository> _logger;
        protected readonly IDapperWrapper _dapperWrapper;

        /// <summary>  
        /// Constructor for image repository  
        /// </summary>  
        /// <param name="dbConnection"></param>  
        public ImageRepository(IDbConnectionFactory dbConnection, ILogger<ImageRepository> logger, IDapperWrapper dapperWrapper) : base(dbConnection, logger, dapperWrapper)
        {
            _logger = logger;
            _dapperWrapper = dapperWrapper;
        }

        public async Task<int> AddPropertyImages(List<PropertyImages> imagesList)
        {
            int imagesAddedCount = 0;
            foreach (var image in imagesList)
            {
                var result = await this.AddAsync(image);
                if (result > 0)
                {
                    imagesAddedCount++;
                }
                else
                {
                    _logger.LogError($"Failed to add image with ID: {image.PropertyId}");
                }
            }
            return imagesAddedCount;
        }

        public async Task<PropertyImages> GetPropertyImage(int propertyImagesId)
        {
            var result = await this.GetByIdAsync(propertyImagesId, "PropertyImagesId");
            if (result != null)
            {
                return result;
            }
            else
            {
                _logger.LogError($"Failed to get image with ID: {propertyImagesId}");
                return null;
            }
        }

        public async Task<List<PropertyImages>> GetPropertyImagesByUserId(int userId)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT PropertyImagesId,PropertyId,ImageBlobUrl ");
            query.Append($"FROM {Constants.PropertyImagesTableName} WHERE PropertyId IN (SELECT PropertyId FROM UserProperties WHERE UserId = @UserId);");
            var parameters = new { UserId = userId };
            return await GetAllAsync<PropertyImages>(query.ToString(), parameters);
        }

        public async Task<ViewPropertyImages> GetPropertyDefaultImage(int propertyImagesId, int defaultImageId)
        {
            var query = "SELECT DefaultImageId FROM UserProperties WHERE PropertyId = @PropertyId AND DefaultImageId = @DefaultImageId";
            var parameters = new { PropertyId = propertyImagesId, DefaultImageId = defaultImageId };
            return await GetByIdAsync<ViewPropertyImages>(query, parameters);
        }

        public async Task<bool> UpdateDefaultImage(int propertyId, string defaultImageUri)
        {
            var query = @"UPDATE UserProperties
                         SET DefaultImageId = (SELECT PropertyImagesId FROM PropertyImages WHERE ImageBlobUrl = @ImageBlobUrl)
                         WHERE PropertyId = @PropertyId";
            var parameters = new { ImageBlobUrl = defaultImageUri, PropertyId = propertyId };
            var result = await this.UpdateAsync(query, parameters);
            return result > 0;
        }

        public async Task<bool> UpdateDefaultImage(int propertyId, int propertyImagesId, int userId)
        {
            var query = "UPDATE UserProperties SET DefaultImageId = @PropertyImagesId WHERE PropertyId = @PropertyId AND UserId = @UserId";
            var parameters = new { PropertyImagesId = propertyImagesId, PropertyId = propertyId, UserId = userId };
            var result = await this.UpdateAsync(query, parameters);
            return result > 0;
        }

        public async Task<int> DeleteImage(int propertyImagesId)
        {
            var result = await this.DeleteAsync(propertyImagesId, "PropertyImagesId");
            if (result > 0)
            {
                return result;
            }
            else
            {
                _logger.LogError($"Failed to delete image with ID: {propertyImagesId}");
                return 0;
            }
        }
    }
}
