using Microsoft.AspNetCore.Http;
using Moq;
using MRI.OTA.Infrastructure.Middlewares;
using System.IO;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares
{
    public class UnitTestSecurityHeadersMiddleware
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly SecurityHeadersMiddleware _middleware;

        public UnitTestSecurityHeadersMiddleware()
        {
            _nextMock = new Mock<RequestDelegate>();
            _middleware = new SecurityHeadersMiddleware(_nextMock.Object);
        }

        [Fact]
        public void Constructor_CreatesInstance_WhenNextIsProvided()
        {
            // Arrange
            var nextMock = new Mock<RequestDelegate>();

            // Act
            var middleware = new SecurityHeadersMiddleware(nextMock.Object);

            // Assert
            Assert.NotNull(middleware);
        }

        [Fact]
        public async Task InvokeAsync_AddsXContentTypeOptionsHeader()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("X-Content-Type-Options"));
            Assert.Equal("nosniff", context.Response.Headers["X-Content-Type-Options"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_AddsXFrameOptionsHeader()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("X-Frame-Options"));
            Assert.Equal("DENY", context.Response.Headers["X-Frame-Options"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_AddsXXSSProtectionHeader()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("X-XSS-Protection"));
            Assert.Equal("1; mode=block", context.Response.Headers["X-XSS-Protection"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_AddsReferrerPolicyHeader()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("Referrer-Policy"));
            Assert.Equal("strict-origin-when-cross-origin", context.Response.Headers["Referrer-Policy"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_AddsContentSecurityPolicyHeader()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("Content-Security-Policy"));
            Assert.Equal("default-src 'self'", context.Response.Headers["Content-Security-Policy"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_AddsPermissionsPolicyHeader()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("Permissions-Policy"));
            Assert.Equal("camera=(), microphone=(), geolocation=()", context.Response.Headers["Permissions-Policy"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_CallsNext()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task InvokeAsync_AddsAllSecurityHeaders()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            var expectedHeaders = new[]
            {
                "X-Content-Type-Options",
                "X-Frame-Options",
                "X-XSS-Protection",
                "Referrer-Policy",
                "Content-Security-Policy",
                "Permissions-Policy"
            };

            foreach (var header in expectedHeaders)
            {
                Assert.True(context.Response.Headers.ContainsKey(header), $"Header {header} was not added");
            }
        }

        [Fact]
        public async Task InvokeAsync_DoesNotOverrideExistingHeaders()
        {
            // Arrange
            var context = CreateHttpContext();
            var existingValue = "existing-value";
            context.Response.Headers["X-Content-Type-Options"] = existingValue;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            // The middleware should append, not override
            Assert.Contains(existingValue, context.Response.Headers["X-Content-Type-Options"].ToString());
        }

        [Fact]
        public async Task InvokeAsync_HandlesNextMiddlewareException()
        {
            // Arrange
            var context = CreateHttpContext();
            _nextMock.Setup(n => n(It.IsAny<HttpContext>()))
                .ThrowsAsync(new InvalidOperationException("Next middleware error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _middleware.InvokeAsync(context));
        }

        [Fact]
        public async Task InvokeAsync_AddsHeadersBeforeCallingNext()
        {
            // Arrange
            var context = CreateHttpContext();
            var headersAddedBeforeNext = false;

            _nextMock.Setup(n => n(It.IsAny<HttpContext>()))
                .Callback<HttpContext>(ctx =>
                {
                    headersAddedBeforeNext = ctx.Response.Headers.ContainsKey("X-Content-Type-Options");
                });

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(headersAddedBeforeNext);
        }

        [Theory]
        [InlineData("GET")]
        [InlineData("POST")]
        [InlineData("PUT")]
        [InlineData("DELETE")]
        [InlineData("PATCH")]
        [InlineData("HEAD")]
        [InlineData("OPTIONS")]
        public async Task InvokeAsync_AddsHeadersForAllHttpMethods(string method)
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Method = method;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("X-Content-Type-Options"));
            Assert.True(context.Response.Headers.ContainsKey("X-Frame-Options"));
            Assert.True(context.Response.Headers.ContainsKey("X-XSS-Protection"));
        }

        [Fact]
        public async Task InvokeAsync_WorksWithDifferentResponseStatusCodes()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Response.StatusCode = StatusCodes.Status404NotFound;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("X-Content-Type-Options"));
            Assert.Equal(StatusCodes.Status404NotFound, context.Response.StatusCode);
        }

        [Fact]
        public async Task InvokeAsync_WorksWithEmptyResponseBody()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.True(context.Response.Headers.ContainsKey("X-Content-Type-Options"));
            Assert.Equal(0, context.Response.Body.Length);
        }

        [Fact]
        public async Task InvokeAsync_PreservesResponseContentType()
        {
            // Arrange
            var context = CreateHttpContext();
            var contentType = "application/json";
            context.Response.ContentType = contentType;

            // Act
            await _middleware.InvokeAsync(context);

            // Assert
            Assert.Equal(contentType, context.Response.ContentType);
        }

        [Fact]
        public async Task InvokeAsync_WorksWithMultipleRequests()
        {
            // Arrange
            var context1 = CreateHttpContext();
            var context2 = CreateHttpContext();

            // Act
            await _middleware.InvokeAsync(context1);
            await _middleware.InvokeAsync(context2);

            // Assert
            Assert.True(context1.Response.Headers.ContainsKey("X-Content-Type-Options"));
            Assert.True(context2.Response.Headers.ContainsKey("X-Content-Type-Options"));
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.Response.Body = new MemoryStream();
            return context;
        }
    }
}
