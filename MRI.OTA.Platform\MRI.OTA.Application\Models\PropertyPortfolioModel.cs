namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// Model for Property Portfolio operations
    /// </summary>
    public class PropertyPortfolioModel
    {
        /// <summary>
        /// User ID
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Property ID
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// Nickname ID - if 0, create new entry; if greater than 0, update existing entry
        /// </summary>
        public int NicknameId { get; set; }

        /// <summary>
        /// Nickname for the property
        /// </summary>
        public string? Nickname { get; set; }
    }
} 