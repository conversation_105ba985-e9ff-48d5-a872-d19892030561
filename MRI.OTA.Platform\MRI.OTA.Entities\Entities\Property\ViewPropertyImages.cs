﻿namespace MRI.OTA.DBCore.Entities.Property
{
    public class ViewPropertyImages
    {
        /// <summary>
        /// PropertyImageId
        /// </summary>
        public int PropertyImagesId { get; set; }
        /// <summary>
        /// PropertyId
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// DefaultImageId
        /// </summary>
        public int DefaultImageId { get; set; }
        /// <summary>
        /// ImageUrl
        /// </summary>
        public string? ImageBlobUrl { get; set; }
    }
}
