﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.12.35514.174
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MRI.OTA.API", "MRI.OTA.API\MRI.OTA.API.csproj", "{279F82E1-0678-4603-8474-A14C7EFD4844}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MRI.OTA.DBCore", "MRI.OTA.Entities\MRI.OTA.DBCore.csproj", "{C3FFA189-6CBF-4F67-8449-558A68C69ABD}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MRI.OTA.Infrastructure", "MRI.OTA.Infrastructure\MRI.OTA.Infrastructure.csproj", "{BAA1FA78-18EA-4FF1-B3DB-9D973F55E710}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MRI.OTA.Application", "MRI.OTA.Application\MRI.OTA.Application.csproj", "{E7207769-5EFC-46A9-B5E4-229F033509FA}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MRI.OTA.Common", "MRI.OTA.Common\MRI.OTA.Common.csproj", "{A3FC3066-6C87-4D17-8809-FA065C4F3525}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MRI.OTA.Integration", "MRI.OTA.Integration\MRI.OTA.Integration.csproj", "{831ED126-52D6-4CD1-A8A0-B0D56C027FC6}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "MRI.OTA.Identity", "MRI.OTA.Identity\MRI.OTA.Identity.csproj", "{F11CB348-4234-4A9C-AF2A-325C19DE0797}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{11CD333E-FC4C-4690-8EE1-33EF15225C61}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
		Directory.Build.props = Directory.Build.props
		Directory.Packages.props = Directory.Packages.props
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MRI.OTA.UnitTestCases", "MRI.OTA.Tests\MRI.OTA.UnitTestCases.csproj", "{3C8EF19B-8706-48E0-B61B-BA96FB722CBC}"
EndProject

Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MRI.OTA.Email", "MRI.OTA.Email\MRI.OTA.Email.csproj", "{AF051E38-D552-4983-BF67-C3993291756D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "MRI.OTA.Worker", "MRI.OTA.Worker\MRI.OTA.Worker.csproj", "{912E5CF5-8544-4F52-84C6-888D06FBE62E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{279F82E1-0678-4603-8474-A14C7EFD4844}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{279F82E1-0678-4603-8474-A14C7EFD4844}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{279F82E1-0678-4603-8474-A14C7EFD4844}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{279F82E1-0678-4603-8474-A14C7EFD4844}.Release|Any CPU.Build.0 = Release|Any CPU
		{C3FFA189-6CBF-4F67-8449-558A68C69ABD}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C3FFA189-6CBF-4F67-8449-558A68C69ABD}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C3FFA189-6CBF-4F67-8449-558A68C69ABD}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C3FFA189-6CBF-4F67-8449-558A68C69ABD}.Release|Any CPU.Build.0 = Release|Any CPU
		{BAA1FA78-18EA-4FF1-B3DB-9D973F55E710}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BAA1FA78-18EA-4FF1-B3DB-9D973F55E710}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BAA1FA78-18EA-4FF1-B3DB-9D973F55E710}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BAA1FA78-18EA-4FF1-B3DB-9D973F55E710}.Release|Any CPU.Build.0 = Release|Any CPU
		{E7207769-5EFC-46A9-B5E4-229F033509FA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E7207769-5EFC-46A9-B5E4-229F033509FA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E7207769-5EFC-46A9-B5E4-229F033509FA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E7207769-5EFC-46A9-B5E4-229F033509FA}.Release|Any CPU.Build.0 = Release|Any CPU
		{A3FC3066-6C87-4D17-8809-FA065C4F3525}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{A3FC3066-6C87-4D17-8809-FA065C4F3525}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{A3FC3066-6C87-4D17-8809-FA065C4F3525}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{A3FC3066-6C87-4D17-8809-FA065C4F3525}.Release|Any CPU.Build.0 = Release|Any CPU
		{831ED126-52D6-4CD1-A8A0-B0D56C027FC6}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{831ED126-52D6-4CD1-A8A0-B0D56C027FC6}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{831ED126-52D6-4CD1-A8A0-B0D56C027FC6}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{831ED126-52D6-4CD1-A8A0-B0D56C027FC6}.Release|Any CPU.Build.0 = Release|Any CPU
		{F11CB348-4234-4A9C-AF2A-325C19DE0797}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{F11CB348-4234-4A9C-AF2A-325C19DE0797}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{F11CB348-4234-4A9C-AF2A-325C19DE0797}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{F11CB348-4234-4A9C-AF2A-325C19DE0797}.Release|Any CPU.Build.0 = Release|Any CPU
		{3C8EF19B-8706-48E0-B61B-BA96FB722CBC}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{3C8EF19B-8706-48E0-B61B-BA96FB722CBC}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{3C8EF19B-8706-48E0-B61B-BA96FB722CBC}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{3C8EF19B-8706-48E0-B61B-BA96FB722CBC}.Release|Any CPU.Build.0 = Release|Any CPU
		{BB2026B0-F23D-44C8-AB94-F543D25494E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{BB2026B0-F23D-44C8-AB94-F543D25494E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{BB2026B0-F23D-44C8-AB94-F543D25494E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{BB2026B0-F23D-44C8-AB94-F543D25494E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{AF051E38-D552-4983-BF67-C3993291756D}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AF051E38-D552-4983-BF67-C3993291756D}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AF051E38-D552-4983-BF67-C3993291756D}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AF051E38-D552-4983-BF67-C3993291756D}.Release|Any CPU.Build.0 = Release|Any CPU
		{912E5CF5-8544-4F52-84C6-888D06FBE62E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{912E5CF5-8544-4F52-84C6-888D06FBE62E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{912E5CF5-8544-4F52-84C6-888D06FBE62E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{912E5CF5-8544-4F52-84C6-888D06FBE62E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {BA40FAA4-0360-47EA-BF2D-5EBF0DEF5D63}
	EndGlobalSection
EndGlobal
