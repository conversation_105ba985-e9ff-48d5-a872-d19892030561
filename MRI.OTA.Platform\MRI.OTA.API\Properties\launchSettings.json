{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"MRI_LAUNCH_PROFILE": "http", "ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7009"}, "https": {"commandName": "Project", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"MRI_LAUNCH_PROFILE": "https", "ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://localhost:7009;http://localhost:5215"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "launchUrl": "swagger", "environmentVariables": {"MRI_LAUNCH_PROFILE": "IIS Express", "ASPNETCORE_ENVIRONMENT": "Development"}}, "Container (Dockerfile)": {"commandName": "<PERSON>er", "launchBrowser": true, "launchUrl": "{Scheme}://{ServiceHost}:{ServicePort}/swagger", "environmentVariables": {"MRI_LAUNCH_PROFILE": "Container (Dockerfile)", "ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_HTTPS_PORTS": "8081", "ASPNETCORE_HTTP_PORTS": "8080"}, "publishAllPorts": true, "useSSL": true}, "Docker Compose": {"commandName": "<PERSON>er", "publishAllPorts": true, "dotnetRunMessages": true, "environmentVariables": {"MRI_LAUNCH_PROFILE": "<PERSON><PERSON>", "ASPNETCORE_ENVIRONMENT": "Development", "ASPNETCORE_HTTP_PORTS": "8080"}}}, "$schema": "http://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:14695", "sslPort": 44383}}}