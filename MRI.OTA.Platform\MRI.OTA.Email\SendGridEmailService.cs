﻿using System.Net;
using SendGrid;
using SendGrid.Helpers.Mail;

namespace MRI.OTA.Email
{
    public class SendGridEmailService : IEmailService
    {
        private readonly ILogger<SendGridEmailService> _logger;
        private readonly IConfiguration _configuration;
        private readonly SendGridClient _client;
        public SendGridEmailService(ILogger<SendGridEmailService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _client = new SendGridClient(_configuration.GetValue<string>("SendGridSettings:ApiKey"));
        }
        public async Task<bool> SendEmailAsync(EmailMessage message)
        {
            var sendGridMessage = new SendGridMessage();

            // Set subject
            sendGridMessage.SetSubject(message.Subject);

            // Set content
            sendGridMessage.AddContent(MimeType.Text, message.PlainTextContent ?? string.Empty);
            if (!string.IsNullOrEmpty(message.HtmlContent))
            {
                sendGridMessage.AddContent(MimeType.Html, message.HtmlContent);
            }

            // Set from address (use from address from message or default from settings)
            var from = message.FromAddress ?? new EmailAddress(_configuration.GetValue<string>("SendGridSettings:DefaultFromEmail"), _configuration.GetValue<string>("SendGridSettings:DefaultFromName"));
            sendGridMessage.SetFrom(new SendGrid.Helpers.Mail.EmailAddress(from.Email, from.Name));

            // Set reply-to
            if (message.ReplyToAddress != null)
            {
                sendGridMessage.SetReplyTo(new SendGrid.Helpers.Mail.EmailAddress(
                    message.ReplyToAddress.Email,
                    message.ReplyToAddress.Name));
            }

            // Add recipients
            if (message.ToAddresses.Any())
            {
                sendGridMessage.AddTos(message.ToAddresses
                    .Select(a => new SendGrid.Helpers.Mail.EmailAddress(a.Email, a.Name))
                    .ToList());
            }

            if (message.CcAddresses.Any())
            {
                sendGridMessage.AddCcs(message.CcAddresses
                    .Select(a => new SendGrid.Helpers.Mail.EmailAddress(a.Email, a.Name))
                    .ToList());
            }

            if (message.BccAddresses.Any())
            {
                sendGridMessage.AddBccs(message.BccAddresses
                    .Select(a => new SendGrid.Helpers.Mail.EmailAddress(a.Email, a.Name))
                    .ToList());
            }

            // Add attachments
            foreach (var attachment in message.Attachments)
            {
                sendGridMessage.AddAttachment(
                    attachment.FileName,
                    Convert.ToBase64String(attachment.Content),
                    attachment.ContentType);
            }

            // Send email
            var response = await _client.SendEmailAsync(sendGridMessage);
            return response.IsSuccessStatusCode;
        }
    }
}
