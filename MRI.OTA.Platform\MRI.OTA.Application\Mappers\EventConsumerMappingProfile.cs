﻿using AutoMapper;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.Application.Mappers
{
    public class EventConsumerMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for event consumer mapper
        /// </summary>
        public EventConsumerMappingProfile() : base("EventConsumerMappingProfile")
        {
            CreateMap<EventConsumer, EventConsumerModel>()
            .ReverseMap();
        }
    }
}

