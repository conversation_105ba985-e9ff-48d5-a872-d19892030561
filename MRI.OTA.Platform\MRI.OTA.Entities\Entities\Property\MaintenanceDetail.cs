﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class MaintenanceDetail
    {
        [ExcludeColumn]
        public int MaintenanceDetailId { get; set; }
        public string SRCManagementId { get; set; }
        public string SRCTenancyId { get; set; }
        public int PropertyId { get; set; }
        public string SRCPropertyId { get; set; }
        public string SRCJobId { get; set; }
        public string JobSummary { get; set; }
        public string JobStatus { get; set; }
        public string SRCRequestId { get; set; }
        public string RequestSummary { get; set; }
        public string RequestStatus { get; set; }
        public string RequestRaisedBy { get; set; }
        public DateTime RequestRaisedDate { get; set; }
        public string ImageLink { get; set; }
    }
}
