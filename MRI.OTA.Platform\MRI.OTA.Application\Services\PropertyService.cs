﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Models;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.Application.Services
{
    /// <summary>
    /// Property service class
    /// </summary>
    public class PropertyService : BaseService<UserProperties, UserPropertiesModel, int>, IPropertyService
    {
        private IPropertyRepository _propertyRepository { get; set; }

        private IMasterRepository _masterRepository { get; set; }

        private IMapper _mapper { get; set; }

        private readonly ILogger<PropertyService> _logger;

        private readonly TaskContext _taskContext;

        /// <summary>
        /// User service constructor
        /// </summary>
        /// <param name="repository"></param>
        /// <param name="mapper"></param>
        public PropertyService(ILogger<PropertyService> logger, IPropertyRepository repository, IMasterRepository masterRepository, IMapper mapper, TaskContext taskContext)
        : base(logger, repository, mapper)
        {
            _propertyRepository = repository;
            _mapper = mapper;
            _logger = logger;
            _taskContext = taskContext;
            _masterRepository = masterRepository;
        }

        public async Task<List<ViewUserProperties>> GetAllProperties(SearchCriteriaModel searchCriteria)
        {
            try
            {
                var result = await _propertyRepository.GetAllProperties(_taskContext.UserId, searchCriteria.OffSet, searchCriteria.Limit, searchCriteria.ShowAllRecords);
                if (result != null && result.Count > 0)
                {
                    var propertyIds = result.Select(property => property.PropertyId).ToList();
                    var propertyImages = await _propertyRepository.GetPropertyImages(propertyIds);

                    // Build a dictionary for fast lookup
                    var imagesByPropertyId = propertyImages
                        .GroupBy(img => img.PropertyId)
                        .ToDictionary(g => g.Key, g => g.ToList());

                    foreach (var property in result)
                    {
                        imagesByPropertyId.TryGetValue(property.PropertyId, out var images);
                        property.PropertyImages = images ?? null!;
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting property data : {ex.Message}");
            }
            return new List<ViewUserProperties>();
        }

        /// <summary>
        /// Add property
        /// </summary>
        /// <param name="userProperties"></param>
        /// <returns></returns>
        public async Task<int> AddProperty(UserPropertiesModel userProperties)
        {
            try
            {
                var property = _mapper.Map<UserProperties>(userProperties);
                property.UserId = _taskContext.UserId;
                var result = await _propertyRepository.AddProperty(property);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error adding property : {ex.Message}");
            }
            return -1;
        }

        /// <summary>
        /// Update property
        /// </summary>
        /// <param name="userProperties"></param>
        /// <returns></returns>
        public async Task<int> UpdateProperty(UserPropertiesModel userProperties)
        {
            try
            {
                var property = _mapper.Map<UserProperties>(userProperties);
                property.UserId = _taskContext.UserId;
                var result = await _propertyRepository.UpdateProperty(property);

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating property : {ex.Message}");
            }
            return -1;
        }

        /// <summary>
        /// Get property by Id
        /// </summary>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public async Task<ViewUserProperties?> GetPropertyById(int propertyId)
        {
            try
            {
                var result = await _propertyRepository.GetPropertyById(_taskContext.UserId,propertyId);
                if (result != null)
                {
                    var propertyModules = await _masterRepository.GetModulesList(result.PropertyRelationshipId);
                    result.ModuleList = propertyModules;
                    var propertyImages = await _propertyRepository.GetPropertyImages(new List<int>() { result.PropertyId });
                    result.PropertyImages = propertyImages;
                }

                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property by Id");
            }
            return null;
        }

        /// <summary>
        /// Get property by Id
        /// </summary>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public async Task<List<ViewUserPropertiesNickName>> GetPropertyRelations(int userPropertiesNickNameId)
        {
            try
            {
                var result = await _propertyRepository.GetPropertyRelations(userPropertiesNickNameId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property relations");
            }
            return null;
        }

        /// <summary>
        /// Get property by Id
        /// </summary>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public async Task<List<ViewUserPropertiesNickName>> GetPropertyNickNames(int userId)
        {
            try
            {
                var result = await _propertyRepository.GetPropertyNickNames(userId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property nicknames");
            }
            return null;
        }

        /// <summary>
        /// Delete property
        /// </summary>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public async Task<int> DeleteProperty(int propertyId)
        {
            try
            {
                var result = await _propertyRepository.DeleteProperty(propertyId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error deleting property : {ex.Message}");
            }
            return -1;
        }

        /// <summary>
        /// Update property status
        /// </summary>
        /// <param name="propertyStatus"></param>
        /// <returns></returns>
        public async Task<int> UpdatePropertyStatus(PropertyStatusModel propertyStatus)
        {
            try
            {
                var result = await _propertyRepository.UpdatePropertyStatus(propertyStatus.UserId,propertyStatus.PropertyId,propertyStatus.IsActive);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating property status : {ex.Message}");
            }
            return -1;
        }

        /// <summary>
        /// Update property portfolio - creates or updates property-nickname relationships
        /// </summary>
        /// <param name="portfolioRequest">Portfolio request containing userid, propertyid, nicknameid, and nickname</param>
        /// <returns>Returns affected rows count</returns>
        public async Task<int> UpdatePropertyPortfolio(PropertyPortfolioModel portfolioRequest)
        {
            try
            {
                var result = await _propertyRepository.UpdatePropertyPortfolio(portfolioRequest.UserId, portfolioRequest.PropertyId, portfolioRequest.NicknameId, portfolioRequest.Nickname);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error updating property portfolio : {ex.Message}");
            }
            return -1;
        }

        /// <summary>
        /// Get count of active and inactive properties grouped by data source
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>Model with counts of active and inactive properties by data source</returns>
        public async Task<PropertyCountModel> GetPropertyCountsByDataSource(int userId)
        {
            try
            {
                var countsBySource = await _propertyRepository.GetPropertyCountsByDataSource(userId);

                var model = new PropertyCountModel();

                // Calculate total counts across all data sources
                foreach (var (dataSourceId, dataSourceName, activeCount, inactiveCount) in countsBySource)
                {
                    model.ActiveCount += activeCount;
                    model.InactiveCount += inactiveCount;

                    // Add counts by data source
                    DataSourceCountModel dataSourceCountModel = new DataSourceCountModel();
                    dataSourceCountModel.DataSourceId = dataSourceId;
                    dataSourceCountModel.DataSourceName = dataSourceName;
                    dataSourceCountModel.ActiveCount = activeCount;
                    dataSourceCountModel.InactiveCount = inactiveCount;
                    if (model.CountsBySource == null)
                    {
                        model.CountsBySource = new DataSourceCountModel[0];
                    }

                    model.CountsBySource = model.CountsBySource.Append(dataSourceCountModel).ToArray();
                }

                return model;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property counts by data source for user {UserId}", userId);
                return new PropertyCountModel();
            }
        }

        /// <summary>
        /// Get count of active and inactive properties grouped by agency ID with comprehensive agency information
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of agency count models with comprehensive agency information</returns>
        public async Task<List<ViewAgencyPropertyCount>> GetPropertyCountsByAgency(int userId)
        {
            try
            {
                var countsByAgency = await _propertyRepository.GetPropertyCountsByAgency(userId);
                return countsByAgency;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property counts by agency for user {UserId}", userId);
                return new List<ViewAgencyPropertyCount>();
            }
        }

        /// <summary>
        /// Get maintenance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of maintenance details</returns>
        public async Task<List<MaintenanceDetailModel>> GetMaintenanceDetails(int propertyId, string? managementId, string? tenancyId)
        {
            try
            {
                var entities = await _propertyRepository.GetMaintenanceDetails(propertyId, managementId, tenancyId);
                var result = _mapper.Map<List<MaintenanceDetailModel>>(entities);
                return result ?? new List<MaintenanceDetailModel>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting maintenance details for managementId {ManagementId} or TenancyId {TenancyId} and propertyId {PropertyId}", managementId, tenancyId, propertyId);
                return new List<MaintenanceDetailModel>();
            }
        }

        /// <summary>
        /// Get compliance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of compliance details</returns>
        public async Task<List<ComplianceDetailModel>> GetCompliance(string managementId, int propertyId)
        {
            try
            {
                var entities = await _propertyRepository.GetCompliance(managementId, propertyId);
                var result = _mapper.Map<List<ComplianceDetailModel>>(entities);
                return result ?? new List<ComplianceDetailModel>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting compliance details for managementId {ManagementId} and propertyId {PropertyId}", managementId, propertyId);
                return new List<ComplianceDetailModel>();
            }
        }

        /// <summary>
        /// Get inspections list based on tenancyId and propertyId
        /// </summary>
        /// <param name="tenancyId">The tenancy ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of inspection details</returns>
        public async Task<List<InspectionDetailModel>> GetInspections(int propertyId, string? managementId, string? tenancyId)
        {
            try
            {
                var result = await _propertyRepository.GetInspections(propertyId, managementId, tenancyId);
                return _mapper.Map<List<InspectionDetailModel>>(result) ?? new List<InspectionDetailModel>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting inspections for TenancyId {TenancyId} or ManagementId {ManagementId}, PropertyId {PropertyId}", tenancyId, managementId, propertyId);
                return new List<InspectionDetailModel>();
            }
        }

        public async Task<List<UserPropertyDocumentDetail>> GetDocuments(GetDocumentRequestModel requestModel)
        {
            try
            {
                var result = await _propertyRepository.GetDocument(requestModel, _taskContext.UserId);
                return result ?? new List<UserPropertyDocumentDetail>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting documets for TenancyId {TenancyId}, ManagementId {ManagementId}", requestModel.TenancyId, requestModel.ManagementId);
                return new List<UserPropertyDocumentDetail>();
            }
        }

        /// <summary>
        /// Get property manager information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property manager information view model</returns>
        public async Task<PropertyManagerViewModel?> GetPropertyManagerInformation(string? managementId, int? propertyId, string? srcPropertyId)
        {
            try
            {
                var entity = await _propertyRepository.GetPropertyManagerInformation(managementId, propertyId, srcPropertyId);
                if (entity == null)
                {
                    _logger.LogWarning("No property manager information found for ManagementId: {ManagementId}, PropertyId: {PropertyId}, SRCPropertyId: {SRCPropertyId}", managementId, propertyId, srcPropertyId);
                    return null;
                }
                
                var result = _mapper.Map<PropertyManagerViewModel>(entity);
                _logger.LogInformation("Successfully retrieved property manager information for ManagementId: {ManagementId}, PropertyId: {PropertyId}, SRCPropertyId: {SRCPropertyId}", managementId, propertyId, srcPropertyId);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property manager information for ManagementId {ManagementId}, PropertyId {PropertyId}, SRCPropertyId {SRCPropertyId}", managementId, propertyId, srcPropertyId);
                return null;
            }
        }

        /// <summary>
        /// Get property financial information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property financial information view model</returns>
        public async Task<PropertyFinancialViewModel?> GetPropertyFinancialInformation(string? managementId, int? propertyId, string? srcPropertyId)
        {
            try
            {
                var entity = await _propertyRepository.GetPropertyFinancialInformation(managementId, propertyId, srcPropertyId);
                if (entity == null)
                {
                    return null;
                }

                var result = _mapper.Map<PropertyFinancialViewModel>(entity);
                return result;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting property financial information for ManagementId {ManagementId}, PropertyId {PropertyId}, SRCPropertyId {SRCPropertyId}", managementId, propertyId, srcPropertyId);
                return null;
            }
        }

        public async Task<TenanciesTenantDetailResponse> GetTenantOwnerDetail(string? tenancyId, string? srcPropertyId, int? propertyId)
        {
            try
            {
                return await _propertyRepository.GetTenantOwnerDetail(tenancyId, srcPropertyId, propertyId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Tenant Owner data for TenancyId {TenancyId} SRCPropertyId {SRCPropertyId} PropertyId {PropertyId}", tenancyId, srcPropertyId, propertyId);
                return null;
            }
        }
    }
}
