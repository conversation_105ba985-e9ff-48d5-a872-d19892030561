﻿﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Moq;
using MRI.OTA.Infrastructure.Caching;
using MRI.OTA.Infrastructure.Caching.Configuration;
using System.Text;
using System.Text.Json;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Caching
{
    public class UnitTestRedisCacheService
    {
        private readonly Mock<IDistributedCache> _cacheMock;
        private readonly Mock<ILogger<RedisCacheService>> _loggerMock;
        private readonly IOptions<RedisConfiguration> _options;
        private readonly RedisCacheService _service;

        public UnitTestRedisCacheService()
        {
            _cacheMock = new Mock<IDistributedCache>();
            _loggerMock = new Mock<ILogger<RedisCacheService>>();
            _options = Options.Create(new RedisConfiguration
            {
                ConnectionString = "localhost",
                DefaultExpirationMinutes = 10
            });
            _service = new RedisCacheService(_cacheMock.Object, _options, _loggerMock.Object);
        }

        [Fact]
        public async Task GetAsync_ReturnsDeserializedObject_WhenKeyExists()
        {
            // Arrange
            var key = "test-key";
            var value = new TestClass { Id = 1, Name = "Test" };
            var json = JsonSerializer.Serialize(value);
            var bytes = Encoding.UTF8.GetBytes(json);

            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ReturnsAsync(bytes);

            // Act
            var result = await _service.GetAsync<TestClass>(key);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(value.Id, result!.Id);
            Assert.Equal(value.Name, result.Name);
        }

        [Fact]
        public async Task GetAsync_ReturnsDefault_WhenKeyDoesNotExist()
        {
            // Arrange
            var key = "missing-key";
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ReturnsAsync((byte[]?)null);

            // Act
            var result = await _service.GetAsync<TestClass>(key);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task SetAsync_SetsValueWithDefaultExpiration()
        {
            // Arrange
            var key = "set-key";
            var value = new TestClass { Id = 2, Name = "Set" };

            // Act
            await _service.SetAsync(key, value);

            // Assert
            _cacheMock.Verify(c => c.SetAsync(
                key,
                It.IsAny<byte[]>(),
                It.Is<DistributedCacheEntryOptions>(o => o.AbsoluteExpirationRelativeToNow == TimeSpan.FromMinutes(10)),
                It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task SetAsync_SetsValueWithCustomExpiration()
        {
            // Arrange
            var key = "set-key-custom";
            var value = new TestClass { Id = 3, Name = "Custom" };

            // Act
            await _service.SetAsync(key, value, 5);

            // Assert
            _cacheMock.Verify(c => c.SetAsync(
                key,
                It.IsAny<byte[]>(),
                It.Is<DistributedCacheEntryOptions>(o => o.AbsoluteExpirationRelativeToNow == TimeSpan.FromMinutes(5)),
                It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task RemoveAsync_RemovesKey()
        {
            // Arrange
            var key = "remove-key";

            // Act
            await _service.RemoveAsync(key);

            // Assert
            _cacheMock.Verify(c => c.RemoveAsync(key, It.IsAny<CancellationToken>()), Times.Once);
        }

        [Fact]
        public async Task ExistsAsync_ReturnsTrue_WhenKeyExists()
        {
            // Arrange
            var key = "exists-key";
            var data = Encoding.UTF8.GetBytes("data");
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ReturnsAsync(data);

            // Act
            var exists = await _service.ExistsAsync(key);

            // Assert
            Assert.True(exists);
        }

        [Fact]
        public async Task ExistsAsync_ReturnsFalse_WhenKeyDoesNotExist()
        {
            // Arrange
            var key = "not-exists-key";
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ReturnsAsync((byte[]?)null);

            // Act
            var exists = await _service.ExistsAsync(key);

            // Assert
            Assert.False(exists);
        }

        [Fact]
        public async Task RefreshExpirationAsync_Refreshes_WhenKeyExists()
        {
            // Arrange
            var key = "refresh-key";
            var value = "data";
            var bytes = Encoding.UTF8.GetBytes(value);
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ReturnsAsync(bytes);

            // Act
            var refreshed = await _service.RefreshExpirationAsync(key, 7);

            // Assert
            Assert.True(refreshed);
            _cacheMock.Verify(c => c.SetAsync(
                key,
                It.Is<byte[]>(b => Encoding.UTF8.GetString(b) == value),
                It.Is<DistributedCacheEntryOptions>(o => o.AbsoluteExpirationRelativeToNow == TimeSpan.FromMinutes(7)),
                It.IsAny<CancellationToken>()),
                Times.Once);
        }

        [Fact]
        public async Task RefreshExpirationAsync_ReturnsFalse_WhenKeyDoesNotExist()
        {
            // Arrange
            var key = "refresh-missing";
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ReturnsAsync((byte[]?)null);

            // Act
            var refreshed = await _service.RefreshExpirationAsync(key);

            // Assert
            Assert.False(refreshed);
        }

        [Fact]
        public async Task GetAsync_ReturnsDefault_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Cache error"));

            // Act
            var result = await _service.GetAsync<TestClass>(key);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetAsync_LogsError_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            var exception = new InvalidOperationException("Cache error");
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            await _service.GetAsync<TestClass>(key);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error retrieving data from Redis cache for key {key}")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task SetAsync_DoesNothing_WhenValueIsNull()
        {
            // Arrange
            var key = "null-key";
            TestClass? value = null;

            // Act
            await _service.SetAsync(key, value);

            // Assert
            _cacheMock.Verify(c => c.SetAsync(
                It.IsAny<string>(),
                It.IsAny<byte[]>(),
                It.IsAny<DistributedCacheEntryOptions>(),
                It.IsAny<CancellationToken>()),
                Times.Never);
        }

        [Fact]
        public async Task SetAsync_LogsError_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            var value = new TestClass { Id = 1, Name = "Test" };
            var exception = new InvalidOperationException("Cache error");

            _cacheMock.Setup(c => c.SetAsync(
                It.IsAny<string>(),
                It.IsAny<byte[]>(),
                It.IsAny<DistributedCacheEntryOptions>(),
                It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            await _service.SetAsync(key, value);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error setting data in Redis cache for key {key}")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task RemoveAsync_LogsError_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            var exception = new InvalidOperationException("Cache error");

            _cacheMock.Setup(c => c.RemoveAsync(key, It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            await _service.RemoveAsync(key);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error removing data from Redis cache for key {key}")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task ExistsAsync_ReturnsFalse_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Cache error"));

            // Act
            var exists = await _service.ExistsAsync(key);

            // Assert
            Assert.False(exists);
        }

        [Fact]
        public async Task ExistsAsync_LogsError_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            var exception = new InvalidOperationException("Cache error");
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            await _service.ExistsAsync(key);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error checking existence in Redis cache for key {key}")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task RefreshExpirationAsync_ReturnsFalse_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ThrowsAsync(new InvalidOperationException("Cache error"));

            // Act
            var refreshed = await _service.RefreshExpirationAsync(key);

            // Assert
            Assert.False(refreshed);
        }

        [Fact]
        public async Task RefreshExpirationAsync_LogsError_WhenExceptionThrown()
        {
            // Arrange
            var key = "exception-key";
            var exception = new InvalidOperationException("Cache error");
            _cacheMock.Setup(c => c.GetAsync(key, It.IsAny<CancellationToken>()))
                .ThrowsAsync(exception);

            // Act
            await _service.RefreshExpirationAsync(key);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains($"Error refreshing expiration in Redis cache for key {key}")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        private class TestClass
        {
            public int Id { get; set; }
            public string Name { get; set; } = string.Empty;
        }
    }
}
