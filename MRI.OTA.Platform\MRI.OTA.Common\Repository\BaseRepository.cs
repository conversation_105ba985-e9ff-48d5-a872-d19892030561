﻿using System.Data;
using System.Reflection;
using System.Text;
using Dapper;
using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Common.Models;
using MRI.OTA.Common.Repository;

/// <summary>
/// Base repository
/// </summary>
/// <typeparam name="TEntity"></typeparam>
/// <typeparam name="TKey"></typeparam>
public abstract class BaseRepository<TEntity, TKey> : IBaseRepository<TEntity, TKey> where TEntity : class
{
    protected readonly IDbConnectionFactory _connectionFactory;
    protected readonly ILogger _logger;
    protected readonly SqlRetryHandler _retryHandler;
    protected readonly IDapperWrapper _dapperWrapper;

    protected BaseRepository(IDbConnectionFactory connectionFactory, ILogger logger, IDapperWrapper dapperWrapper)
    {
        _connectionFactory = connectionFactory;
        _logger = logger;
        _retryHandler = new SqlRetryHandler(logger);
        _dapperWrapper = dapperWrapper;
    }

    /// <summary>
    /// Gets a database connection and ensures it's opened
    /// </summary>
    protected virtual IDbConnection GetConnection()
    {
        try
        {
            var connection = _connectionFactory.CreateConnection();
            if (connection is SqlConnection sqlConnection)
            {
                sqlConnection.StatisticsEnabled = true;
            }

            if (connection.State != ConnectionState.Open)
            {
                connection.Open();
            }
            return connection;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Failed to create database connection");
            throw;
        }
    }

    /// <summary>
    /// Executes an action with a managed database connection that returns a value.
    /// </summary>
    /// <typeparam name="TResult">The type of the result</typeparam>
    /// <param name="func">The function to execute</param>
    /// <returns>The result of the function</returns>
    protected virtual async Task<TResult> ExecuteWithConnectionAsync<TResult>(Func<IDbConnection, Task<TResult>> func)
    {
        return await _retryHandler.ExecuteWithRetryAsync(async () =>
        {
            using var connection = GetConnection();
            try
            {
                return await func(connection);
            }
            catch (Exception ex) when (ex is TimeoutException || ex is SqlException)
            {
                _logger.LogError(ex, "Database operation timeout");
                throw;// new ConnectionTimeoutException("Database operation timed out", ex);
            }
        });
    }

    /// <summary>
    /// Executes an action with a managed database connection.
    /// </summary>
    /// <param name="action">The action to execute</param>
    protected virtual async Task ExecuteWithConnectionAsync(Func<IDbConnection, Task> action)
    {
        await _retryHandler.ExecuteWithRetryAsync(async () =>
        {
            using var connection = GetConnection();
            try
            {
                await action(connection);
                return true;
            }
            catch (Exception ex) when (ex is TimeoutException || ex is SqlException)
            {
                _logger.LogError(ex, "Database operation timeout");
                throw;// new ConnectionTimeoutException("Database operation timed out", ex);
            }
        });
    }

    /// <summary>
    /// Executes an action with a managed database connection and transaction
    /// </summary>
    /// <typeparam name="TResult">The type of the result</typeparam>
    /// <param name="func">The function to execute</param>
    /// <returns>The result of the function</returns>
    protected virtual async Task<TResult> ExecuteWithTransactionAsync<TResult>(Func<IDbConnection, IDbTransaction, Task<TResult>> func)
    {
        return await _retryHandler.ExecuteWithRetryAsync(async () =>
        {
            using var connection = GetConnection();
            using var transaction = connection.BeginTransaction();
            try
            {
                var result = await func(connection, transaction);
                transaction.Commit();
                return result;
            }
            catch (Exception ex)
            {
                transaction.Rollback();
                _logger.LogError(ex, "Error executing database transaction");
                throw;
            }
        });
    }


    /// <summary>
    /// Get all data from table
    /// </summary>
    /// <returns></returns>
    public virtual async Task<IEnumerable<TEntity>> GetAllAsync()
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            var query = $"SELECT * FROM {typeof(TEntity).Name}";
            return await _dapperWrapper.QueryAsync<TEntity>(connection, query);
        });
    }

    /// <summary>
    /// Get all
    /// </summary>
    /// <param name="query"></param>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public virtual async Task<List<T>> GetAllAsync<T>(string query, object? parameters = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            var result = await _dapperWrapper.QueryAsync<T>(connection, query, parameters);
            return result.ToList();
        });
    }

    /// <summary>
    /// Get by Id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public virtual async Task<TEntity> GetByIdAsync(TKey id, string idColumnName)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            var query = $"SELECT * FROM {typeof(TEntity).Name} WHERE {idColumnName} = @Id";
            return await _dapperWrapper.QuerySingleOrDefaultAsync<TEntity>(connection, query, new { Id = id });
        });
    }

    /// <summary>
    /// Get by Id
    /// </summary>
    /// <param name="query"></param>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public virtual async Task<T> GetByIdAsync<T>(string query, object? parameters = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            return await _dapperWrapper.QuerySingleOrDefaultAsync<T>(connection, query, parameters);
        });
    }

    public virtual async Task<T> GetByIdAsync<T>(string query, object? parameters = null, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            return await _dapperWrapper.QuerySingleOrDefaultAsync<T>(dbConnectionTran, query, parameters, transaction);
        });
    }

    /// <summary>
    /// Get by Id
    /// </summary>
    /// <param name="query"></param>
    /// <param name="parameters"></param>
    /// <returns></returns>
    public virtual async Task<List<T>> GetByIdListAsync<T>(string query, object? parameters = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            try
            {
                var result = await _dapperWrapper.QueryAsync<T>(connection, query, parameters);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching data from table {query}", query);
                throw;
            }
        });
    }

    /// <summary>
    /// Get table data by table name
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="tableName">The name of the table to fetch data from.</param>
    /// <returns>A list of data of type <typeparamref name="T"/>.</returns>
    public virtual async Task<List<T>> GetDataByTableName<T>(string tableName)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            try
            {
                string query = $"SELECT * FROM {tableName}";
                var result = await _dapperWrapper.QueryAsync<T>(connection, query);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error occurred while fetching data from table {TableName}", tableName);
                throw;
            }
        });
    }

    /// <summary>
    /// Add Entity
    /// </summary>
    /// <param name="entity"></param>
    /// <returns></returns>
    public virtual async Task<int> AddAsync(TEntity entity)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            var entityType = typeof(TEntity);
            var tableName = entityType.Name;

            var properties = entityType.GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                       .Where(p => p.CanRead && !p.GetCustomAttributes(typeof(ExcludeColumnAttribute), true).Any() && !p.GetGetMethod().IsVirtual);

            var columnNames = new StringBuilder();
            var columnValues = new StringBuilder();
            foreach (var property in properties)
            {
                if (columnNames.Length > 0)
                {
                    columnNames.Append(", ");
                    columnValues.Append(", ");
                }
                columnNames.Append(property.Name);
                columnValues.Append($"@{property.Name}");
            }

            var query = $"INSERT INTO {tableName} ({columnNames}) VALUES ({columnValues}); SELECT CAST(SCOPE_IDENTITY() AS INT);";
            return await _dapperWrapper.ExecuteScalarAsync<int>(connection, query, entity);
        });
    }

    /// <summary>
    /// Add Entity
    /// </summary>
    /// <param name="tableName">The name of the table to insert into.</param>
    /// <param name="idColumnname">The name of the ID column to return after insertion.</param>
    /// <param name="keyValuePairs">The key-value pairs representing the columns and their values.</param>
    /// <param name="transaction">The optional transaction to use.</param>
    /// <returns>The ID of the inserted entity.</returns>
    public virtual async Task<int> AddAsync(string tableName, string idColumnname, Dictionary<string, object> keyValuePairs, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            // Construct the column names and values
            var columnNames = new StringBuilder();
            var columnValues = new StringBuilder();
            var parameters = new DynamicParameters();

            foreach (var pair in keyValuePairs)
            {
                if (columnNames.Length > 0)
                {
                    columnNames.Append(", ");
                    columnValues.Append(", ");
                }
                columnNames.Append(pair.Key);
                columnValues.Append($"@{pair.Key}");

                // Add the parameter to the DynamicParameters object
                parameters.Add($"@{pair.Key}", pair.Value);
            }

            // Construct the SQL query
            var query = $"INSERT INTO {tableName} ({columnNames}) OUTPUT INSERTED.{idColumnname} VALUES ({columnValues})";

            // Execute the query using Dapper
            if (transaction != null && dbConnectionTran != null)
            {
                return await _dapperWrapper.ExecuteScalarAsync<int>(dbConnectionTran, query, parameters, transaction);
            }
            else
            {
                return await _dapperWrapper.ExecuteScalarAsync<int>(connection, query, parameters);
            }

        });
    }

    /// <summary>
    /// Add Entity
    /// </summary>
    /// <param name="tableName">The name of the table to insert into.</param>
    /// <param name="keyValuePairs">The key-value pairs representing the columns and their values.</param>
    /// <returns>The number of rows affected.</returns>
    public virtual async Task<int> AddAsync(string tableName, Dictionary<string, string> keyValuePairs)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            // Construct the column names and values
            var columnNames = new StringBuilder();
            var columnValues = new StringBuilder();
            var parameters = new DynamicParameters();

            foreach (var pair in keyValuePairs)
            {
                if (columnNames.Length > 0)
                {
                    columnNames.Append(", ");
                    columnValues.Append(", ");
                }
                columnNames.Append(pair.Key);
                columnValues.Append($"@{pair.Key}");

                // Add the parameter to the DynamicParameters object
                parameters.Add($"@{pair.Key}", pair.Value);
            }

            // Construct the SQL query
            var query = $"INSERT INTO {tableName} ({columnNames}) VALUES ({columnValues})";

            // Execute the query using Dapper
            return await _dapperWrapper.ExecuteAsync(connection, query, parameters);
        });
    }

    /// <summary>
    /// Update Entity
    /// </summary>
    /// <param name="id"></param>
    /// <param name="idColumnName"></param>
    /// <param name="entity"></param>
    /// <returns></returns>
    public virtual async Task<int> UpdateAsync(TKey id, string idColumnName, TEntity entity)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            var tableName = typeof(TEntity).Name;

            var properties = typeof(TEntity).GetProperties(BindingFlags.Instance | BindingFlags.Public)
                .Where(p => !string.Equals(p.Name, "Id", StringComparison.OrdinalIgnoreCase) && !p.GetCustomAttributes(typeof(ExcludeColumnAttribute), true).Any())
                .ToList();

            if (!properties.Any())
            {
                throw new InvalidOperationException("No updatable properties were found.");
            }

            var setClause = string.Join(", ", properties.Select(p => $"{p.Name} = @{p.Name}"));
            var sql = $"UPDATE {tableName} SET {setClause} WHERE {idColumnName} = @Id";

            var parameters = new DynamicParameters(entity);
            parameters.Add("Id", id);

            return await _dapperWrapper.ExecuteAsync(connection, sql, parameters);
        });
    }

    /// <summary>
    /// Update Entity
    /// </summary>
    /// <param name="tableName">The name of the table to update.</param>
    /// <param name="idColumnName">The name of the ID column.</param>
    /// <param name="idValue">The value of the ID column.</param>
    /// <param name="keyValuePairs">The key-value pairs representing the columns and their new values.</param>
    /// <param name="transaction">The optional transaction to use.</param>
    /// <returns>The number of rows affected.</returns>
    public virtual async Task<int> UpdateAsync(string tableName, string idColumnName, object idValue, Dictionary<string, object> keyValuePairs, IDbTransaction? transaction = null)
    {
        try
        {
            return await ExecuteWithConnectionAsync(async connection =>
            {
                var parameters = new DynamicParameters();
                var updateColumns = new StringBuilder();

                // Build SET clause excluding the ID column
                foreach (var pair in keyValuePairs.Where(x => x.Key != idColumnName))
                {
                    if (updateColumns.Length > 0)
                        updateColumns.Append(", ");

                    updateColumns.Append($"{pair.Key} = @{pair.Key}");
                    parameters.Add($"@{pair.Key}", pair.Value);
                }

                // Add ID parameter for WHERE clause
                parameters.Add($"@{idColumnName}", idValue);

                // Build and execute query
                var query = $"UPDATE {tableName} SET {updateColumns} WHERE {idColumnName} = @{idColumnName}";
                if (transaction != null)
                {
                    return await _dapperWrapper.ExecuteAsync(connection, query, parameters, transaction);
                }
                else
                {
                    return await _dapperWrapper.ExecuteAsync(connection, query, parameters);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error executing query: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Update Entity
    /// </summary>
    /// <param name="query">The SQL query to execute.</param>
    /// <returns>The number of rows affected.</returns>
    public virtual async Task<int> UpdateAsync(string query)
    {
        try
        {
            return await ExecuteWithConnectionAsync(async connection =>
            {
                return await _dapperWrapper.ExecuteAsync(connection, query);
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error executing query: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Update Entity
    /// </summary>
    /// <param name="query">The SQL query to execute.</param>
    /// <param name="parameters">The parameters for the query.</param>
    /// <param name="transaction">The optional transaction to use.</param>
    /// <returns>The number of rows affected.</returns>
    public virtual async Task<int> UpdateAsync(string query, object? parameters = null, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null)
    {
        try
        {
            return await ExecuteWithConnectionAsync(async connection =>
            {
                // Execute the query using Dapper
                if (transaction != null && dbConnectionTran != null)
                {
                    return await _dapperWrapper.ExecuteAsync(dbConnectionTran, query, parameters, transaction);
                }
                else if (parameters != null)
                {
                    return await _dapperWrapper.ExecuteAsync(connection, query, parameters);
                }
                else
                {
                    return await _dapperWrapper.ExecuteAsync(connection, query);
                }
            });
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error executing query: {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Delete Entity
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public virtual async Task<int> DeleteAsync(string tableName, string id, string idColumnName, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            var query = $"DELETE FROM {tableName} WHERE {idColumnName} = @Id";
            if (transaction != null && dbConnectionTran != null)
            {
                return await _dapperWrapper.ExecuteAsync(dbConnectionTran, query, new { Id = id }, transaction);
            } 
            else
            {
                return await _dapperWrapper.ExecuteAsync(connection, query, new { Id = id });
            }  
        });
    }
    public virtual async Task<int> DeleteAsync(TKey id, string idColumnName)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            var query = $"DELETE FROM {typeof(TEntity).Name} WHERE {idColumnName} = @Id";
            return await _dapperWrapper.ExecuteAsync(connection, query, new { Id = id });
        });
    }

    /// <summary>
    /// Execute a query and return the first result, or default if no results
    /// </summary>
    /// <typeparam name="T">The type to map the result to</typeparam>
    /// <param name="query">The SQL query to execute</param>
    /// <param name="parameters">The parameters for the query</param>
    /// <returns>The first result or default value</returns>
    public virtual async Task<T> QueryFirstOrDefaultAsync<T>(string query, object? parameters = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            try
            {
                return await _dapperWrapper.QueryFirstOrDefaultAsync<T>(connection, query, parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing QueryFirstOrDefaultAsync with query: {Query}", query);
                throw;
            }
        });
    }

    /// <summary>
    /// Execute a query and return multiple results
    /// </summary>
    /// <typeparam name="T">The type to map the results to</typeparam>
    /// <param name="query">The SQL query to execute</param>
    /// <param name="parameters">The parameters for the query</param>
    /// <returns>An enumerable of results</returns>
    public virtual async Task<IEnumerable<T>> QueryAsync<T>(string query, object? parameters = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            try
            {
                return await _dapperWrapper.QueryAsync<T>(connection, query, parameters);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing QueryAsync with query: {Query}", query);
                throw;
            }
        });
    }

    /// <summary>
    /// Execute a query and return multiple results
    /// </summary>
    /// <typeparam name="T">The type to map the results to</typeparam>
    /// <param name="query">The SQL query to execute</param>
    /// <param name="parameters">The parameters for the query</param>
    /// <returns>An enumerable of results</returns>
    public virtual async Task<IEnumerable<T>> QueryAsync<T>(string query, object? parameters = null, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            try
            {

                try
                {
                    return await ExecuteWithConnectionAsync(async connection =>
                    {
                        return await _dapperWrapper.QueryAsync<T>(dbConnectionTran, query, parameters, transaction);
                    });
                }
                catch (Exception ex)
                {
                    _logger.LogError($"Error executing query: {ex.Message}");
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing QueryAsync with query: {Query}", query);
                throw;
            }
        });
    }


    /// <summary>
    /// Executes a SQL command asynchronously using the provided query, parameters, transaction, and connection.
    /// Returns the number of rows affected by the command.
    /// </summary>
    /// <param name="query">The SQL query to execute.</param>
    /// <param name="parameters">The parameters for the query (optional).</param>
    /// <param name="transaction">The transaction to use for the command (optional).</param>
    /// <param name="dbConnectionTran">The database connection to use for the transaction (optional).</param>
    /// <returns>The number of rows affected.</returns>
    public virtual async Task<int> ExecuteAsync(string query, object? parameters = null, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null)
    {
        return await ExecuteWithConnectionAsync(async connection =>
        {
            try
            {
                var conn = dbConnectionTran ?? connection;
                return await _dapperWrapper.ExecuteAsync(conn, query, parameters, transaction);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error executing ExecuteAsync with query: {Query}", query);
                throw;
            }
        });
    }

    /// <summary>
    /// Convert to dictionary
    /// </summary>
    /// <typeparam name="T"></typeparam>
    /// <param name="obj"></param>
    /// <returns></returns>
    public virtual Dictionary<string, object> ConvertToDictionary<T>(T obj)
    {
        try
        {
            var dictionary = new Dictionary<string, object>();
            foreach (PropertyInfo property in typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance)
                                       .Where(p => p.CanRead && !p.GetCustomAttributes(typeof(ExcludeColumnAttribute), true).Any() && !p.GetGetMethod().IsVirtual
                                       && !IsComplexType(p.PropertyType)))
            {
                if (property != null)
                {
                    var value = property.GetValue(obj);
                    if (value != null)
                    {
                        if (value is DateTime || value is DateTime?)
                        {
                            // Format DateTime for SQL Server (yyyy-MM-dd HH:mm:ss.fff)
                            dictionary.Add(property.Name, ((DateTime)value).ToUniversalTime().ToString(Constants.SqlDateTimeFormat));
                        }
                        else if (value is DateTimeOffset || value is DateTimeOffset?)
                        {
                            // Format DateTimeOffset for SQL Server (yyyy-MM-dd HH:mm:ss.fff zzz)
                            dictionary.Add(property.Name, ((DateTimeOffset)value).ToUniversalTime().ToString(Constants.SqlDateTimeOffsetFormat));
                        }
                        else
                        {
                            dictionary.Add(property.Name, value);
                        }
                    }
                    else
                    {
                        dictionary.Add(property.Name, value);
                    }
                }
            }
            return dictionary;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Error converting to dictionary : {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Create update statement
    /// </summary>
    /// <param name="tableName"></param>
    /// <param name="values"></param>
    /// <param name="whereClause"></param>
    /// <returns></returns>
    /// <exception cref="ArgumentException"></exception>
    public virtual Task<string> GenerateUpdateStatement(string tableName, Dictionary<string, object> values, string whereClause)
    {
        if (values == null || values.Count == 0)
            throw new ArgumentException("Values dictionary cannot be empty.");

        if (string.IsNullOrWhiteSpace(whereClause))
            throw new ArgumentException("Where clause cannot be null or empty.");

        // Use parameterized query format to avoid SQL injection and handle special characters
        var setClause = string.Join(", ", values.Select(kvp => kvp.Value == null ? $"{kvp.Key} = NULL" : $"{kvp.Key} = @{kvp.Key}"));
        var sql = $"UPDATE {tableName} SET {setClause} WHERE {whereClause}";
        return Task.FromResult(sql);
    }

    /// <summary>
    /// Convert to dictionary
    /// </summary>
    /// <param name="type"></param>
    /// <returns></returns>
    private static bool IsComplexType(Type type)
    {
        // Unwrap nullable types, if applicable.
        Type nonNullableType = Nullable.GetUnderlyingType(type) ?? type;

        // Check for simple types, including string and dates.
        return !(nonNullableType.IsPrimitive ||
                 nonNullableType.IsEnum ||
                 nonNullableType == typeof(string) ||  // Handles string
                 nonNullableType == typeof(decimal) ||
                 nonNullableType == typeof(DateTime) ||
                 nonNullableType == typeof(DateTimeOffset));
    }
}






