﻿namespace MRI.OTA.Common.Helper
{
    public static class Utilities
    {
        /// <summary>
        /// Method used to generate random alphanumeric string of given length
        /// </summary>
        /// <param name="length"></param>
        /// <returns></returns>
        public static string GenerateRandomString(int length)
        {
            string allowedCharacters = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
            Random r = new Random();
            return new string(Enumerable.Range(0, length)
                       .Select(n => allowedCharacters[r.Next(0, allowedCharacters.Length)])
                       .ToArray());
        }

        /// <summary>
        /// Method used to generate random number string of given length
        /// </summary>
        /// <param name="length"></param>
        /// <returns></returns>
        public static string GenerateRandomNumber(int length)
        {
            string allowedCharacters = "0123456789";
            Random r = new Random();
            return new string(Enumerable.Range(0, length)
                       .Select(n => allowedCharacters[r.Next(0, allowedCharacters.Length)])
                       .ToArray());
        }
    }
}
