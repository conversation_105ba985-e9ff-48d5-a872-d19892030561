﻿namespace MRI.OTA.Application.Models
{
    public class TenanciesTenantResponse
    {
        public string ManagementId { get; set; }
        public string PropertyId { get; set; }
        public string TenancyId { get; set; }
        public string? TenancyName { get; set; }
        public string? TenancyStatus { get; set; }
        public DateTime? TenancyStartDate { get; set; }
        public DateTime? TenancyEndDate { get; set; }
        public DateTime? VacateDate { get; set; }
        public decimal? AmountToVacate { get; set; }
        public DateTime? PayToDate { get; set; }
        public decimal? Rent { get; set; }
        public decimal? Arrears { get; set; }
        public decimal? OutstandingInvoicesAmount { get; set; }
        public decimal? IncreaseRent { get; set; }
        public DateTime? IncreaseDate { get; set; }
        public string? TenancyContactName { get; set; }
        public string? TenancyContactNumber { get; set; }
        public string? TenancyContactEmail { get; set; }
        public string? TenancyContactRole { get; set; }

        public int? RentPeriod { get; set; }

        public string? Currency { get; set; }
    }
}
