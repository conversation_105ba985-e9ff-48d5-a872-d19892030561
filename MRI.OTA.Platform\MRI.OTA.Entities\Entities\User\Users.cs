﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.Core.Entities
{
    /// <summary>
    /// User entity
    /// </summary>
    public class Users
    {
        /// <summary>
        /// UserId
        /// </summary>
        [ExcludeColumn]
        public int UserId { get; set; }
        /// <summary>
        /// ProviderId
        /// </summary>
        public string? ProviderId { get; set; }
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// ProviderTypeId
        /// </summary>
        public int ProviderTypeId { get; set; }
        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// DisplayName
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// TermsAndConditions
        /// </summary>
        public bool? TermsAndConditions { get; set; }

        /// <summary>
        /// PreferredContactEmail
        /// </summary>
        public string? PreferredContactEmail { get; set; }

        /// <summary>
        /// PushNotificationEnabled
        /// </summary>
        public bool? PushNotificationEnabled { get; set; }

        /// <summary>
        /// EmailNotificationEnabled
        /// </summary>
        public bool? EmailNotificationEnabled { get; set; }

    }
}
