﻿using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Net.Http;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.Integration.Http
{
    public class HttpClientFactoryManager : IHttpClientFactoryManager
    {
        private readonly IHttpClientFactory _httpClientFactory;

        public HttpClientFactoryManager(IHttpClientFactory httpClientFactory)
        {
            _httpClientFactory = httpClientFactory;
        }

        public HttpClient GetHttpClient()
        {
            var client = _httpClientFactory.CreateClient("PooledHttpClient");
            return client;
        }
    }
}
