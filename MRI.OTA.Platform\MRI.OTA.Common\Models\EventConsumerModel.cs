﻿namespace MRI.OTA.Common.Models
{
    public class EventConsumerModel
    {
        public string? EventConsumerId { get; set; }

        public string? SourceServiceId { get; set; }

        public string? EntityId { get; set; }

        public string? InstanceId { get; set; }

        public string? UserId { get; set; }

        public string? TimeStamp { get; set; }

        public string? CollationId { get; set; }

        public string? EventType { get; set; }

        public string? DataSchema { get; set; }

        public string? Subject { get; set; }

        public string? Source { get; set; }

        public string? Type { get; set; }

        public string? Data { get; set; }
    }
}
