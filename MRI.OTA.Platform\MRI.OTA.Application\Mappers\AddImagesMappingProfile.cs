﻿using AutoMapper;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class AddImagesMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for user mapper
        /// </summary>
        public AddImagesMappingProfile() : base("AddImagesMappingProfile")
        {
            // Map ImageModel to PropertyImages  
            CreateMap<(int PropertyId, string ImageBlobUrl), PropertyImages>()
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.ImageBlobUrl, opt => opt.MapFrom(src => src.ImageBlobUrl));
        }
    }
}
