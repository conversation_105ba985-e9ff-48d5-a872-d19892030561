﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.Infrastructure.Middlewares
{
    public class TaskContextMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly ILogger<TaskContextMiddleware> _logger;

        public TaskContextMiddleware(RequestDelegate next, ILogger<TaskContextMiddleware> logger)
        {
            _next = next;
            _logger = logger;
        }

        // Constants for claim types to improve readability
        private const string ClaimTypeName = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/name";
        private const string ClaimTypeActor = "http://schemas.xmlsoap.org/ws/2009/09/identity/claims/actor";
        private const string ClaimTypeSub = "sub";
        private const string ClaimTypeNameIdentifier = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier";
        private const string ClaimTypeEmails = "emails";

        public async Task InvokeAsync(HttpContext context, TaskContext taskContext, IUserRepository userRepository)
        {
            // Initialize TaskContext
            taskContext.RequestStartTime = DateTime.UtcNow;

            // Extract user claims
            var userClaims = context.User.Claims.ToDictionary(c => c.Type, c => c.Value);

            // Try API key authentication first
            if (!TryAuthenticateWithApiKey(userClaims, taskContext))
            {
                // Fall back to user authentication
                await AuthenticateWithUserCredentials(userClaims, taskContext, userRepository);
            }

            await _next(context);
        }

        /// <summary>
        /// Attempts to authenticate using API key and token
        /// </summary>
        /// <returns>True if authentication was successful</returns>
        private bool TryAuthenticateWithApiKey(IDictionary<string, string> userClaims, TaskContext taskContext)
        {
            var accessKey = GetClaimValue(userClaims, ClaimTypeName) ?? string.Empty;
            _logger.LogInformation("Setting TaskContext.AccessKey: {AccessKey}", accessKey);
            taskContext.AccessKey = accessKey;
            var accessToken = GetClaimValue(userClaims, ClaimTypeActor) ?? string.Empty;
            if (string.IsNullOrEmpty(accessToken) || string.IsNullOrEmpty(accessKey))
            {
                return false;
            }
            taskContext.AccessToken = accessToken;
            
            return true;
        }

        /// <summary>
        /// Authenticates using user credentials and database lookup
        /// </summary>
        private async Task AuthenticateWithUserCredentials(
            IDictionary<string, string> userClaims,
            TaskContext taskContext,
            IUserRepository userRepository)
        {
            var providerId = GetProviderIdFromClaims(userClaims);
            var userEmail = GetClaimValue(userClaims, ClaimTypeEmails) ?? string.Empty;

            taskContext.Email = userEmail;

            if (string.IsNullOrEmpty(providerId)) return;

            var user = await GetUserByProviderId(providerId, userRepository);
            if (user != null) taskContext.UserId = user.UserId;
        }

        /// <summary>
        /// Gets the provider ID from claims, checking multiple possible claim types
        /// </summary>
        private static string GetProviderIdFromClaims(IDictionary<string, string> userClaims)
        {
            return GetClaimValue(userClaims, ClaimTypeSub) ??
                   GetClaimValue(userClaims, ClaimTypeNameIdentifier) ??
                   string.Empty;
        }

        /// <summary>
        /// Retrieves user details from the database by provider ID
        /// </summary>
        private async Task<Users?> GetUserByProviderId(string providerId, IUserRepository userRepository)
        {
            const string query = "SELECT userid FROM Users WHERE providerid = @ProviderId";
            var parameters = new { ProviderId = providerId };

            try
            {
                return await userRepository.GetByIdAsync<Users>(query, parameters);
            }
            catch (Exception ex)
            {
                return null;
            }
        }

        /// <summary>
        /// Helper method to get claim value safely
        /// </summary>
        private static string? GetClaimValue(IDictionary<string, string> claims, string claimType)
        {
            return claims.TryGetValue(claimType, out var value) ? value : null;
        }
    }
}
