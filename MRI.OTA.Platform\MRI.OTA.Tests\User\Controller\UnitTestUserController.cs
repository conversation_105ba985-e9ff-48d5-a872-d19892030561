﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using MRI.OTA.API.Controllers.User.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.Tests.Users.Http
{
    public class UnitTestUserController
    {
        private readonly Mock<IUserService> _mockUserService;
        private readonly UserController _controller;

        public UnitTestUserController()
        {
            _mockUserService = new Mock<IUserService>();
            _controller = new UserController(_mockUserService.Object);
        }

        #region GetUserById Tests

        [Fact]
        public async Task GetUserById_ReturnsOkResult_WhenUserExists()
        {
            // Arrange
            var user = new UserModel { UserId = 1, UserEmail = "<EMAIL>", DisplayName = "Test User", ProviderTypeId = 1, ProviderId = "1", IsActive = true };
            _mockUserService.Setup(service => service.GetByIdAsync(1, "UserId")).ReturnsAsync(user);

            // Act
            var result = await _controller.GetUserById(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(user, apiResponse.Data);
        }

        [Fact]
        public async Task GetUserById_ReturnsNotFoundResult_WhenUserDoesNotExist()
        {
            // Arrange
            _mockUserService.Setup(service => service.GetByIdAsync(1, "UserId")).ReturnsAsync((UserModel)null);

            // Act
            var result = await _controller.GetUserById(1);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotFound, apiResponse.Message);
        }

        #endregion

        #region AddUser Tests

        [Fact]
        public async Task AddUser_ReturnsOkResult_WhenUserIsAddedSuccessfully()
        {
            // Arrange
            var user = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Test User", ProviderTypeId = 1, ProviderId = "1", IsActive = true };
            _mockUserService.Setup(service => service.CreateUser(user)).ReturnsAsync(1);

            // Act
            var result = await _controller.AddUser(user);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemCreated, apiResponse.Message);
        }

        [Fact]
        public async Task AddUser_ReturnsBadRequestResult_WhenUserIsNotAdded()
        {
            // Arrange
            var user = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Test User", ProviderTypeId = 1, ProviderId = "1", IsActive = true };
            _mockUserService.Setup(service => service.CreateUser(user)).ReturnsAsync(0);

            // Act
            var result = await _controller.AddUser(user);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotCreated, apiResponse.Message);
        }

        #endregion

        #region CreateUserProfile Tests

        [Fact]
        public async Task CreateUserProfile_ReturnsOkResult_WhenUserProfileIsCreatedSuccessfully()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "valid-token" };
            var userProfile = new ViewUserProfileModel
            {
                UserId = 1,
                UserEmail = "<EMAIL>",
                DisplayName = "Test User",
                ProviderTypeId = 1,
                ProviderId = "1",
                IsActive = true,
                ProviderName = "Google"
            };
            _mockUserService.Setup(service => service.CreateUserProfile(idTokenModel)).ReturnsAsync(userProfile);

            // Act
            var result = await _controller.CreateUserProfile(idTokenModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Item created successfully", apiResponse.Message);
            Assert.Equal(userProfile, apiResponse.Data);
        }

        [Fact]
        public async Task CreateUserProfile_ReturnsBadRequestResult_WhenUserProfileIsNull()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "invalid-token" };
            _mockUserService.Setup(service => service.CreateUserProfile(idTokenModel)).ReturnsAsync((ViewUserProfileModel)null);

            // Act
            var result = await _controller.CreateUserProfile(idTokenModel);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotCreated, apiResponse.Message);
        }

        [Fact]
        public async Task CreateUserProfile_ReturnsOkResult_WithErrorMessage_WhenEmailNotFound()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "token-without-email" };
            var userProfile = new ViewUserProfileModel
            {
                UserId = 0,
                UserEmail = "",
                DisplayName = "Test User",
                ProviderTypeId = 1,
                ProviderId = "1",
                IsActive = true,
                ProviderName = "Google"
            };
            _mockUserService.Setup(service => service.CreateUserProfile(idTokenModel)).ReturnsAsync(userProfile);

            // Act
            var result = await _controller.CreateUserProfile(idTokenModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.EmailNotFound, apiResponse.Message);
        }

        [Fact]
        public async Task CreateUserProfile_ReturnsBadRequest_WhenOtherError()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "token" };
            var userProfile = new ViewUserProfileModel
            {
                UserId = 0,
                UserEmail = "<EMAIL>",
                DisplayName = "Test User",
                ProviderTypeId = 1,
                ProviderId = "1",
                IsActive = true,
                ProviderName = "Google"
            };
            _mockUserService.Setup(service => service.CreateUserProfile(idTokenModel)).ReturnsAsync(userProfile);

            // Act
            var result = await _controller.CreateUserProfile(idTokenModel);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotCreated, apiResponse.Message);
            Assert.Contains(MessagesConstants.ItemNotCreatedError, apiResponse.Errors);
        }

        [Fact]
        public async Task UpdateUserTermsCondition_ReturnsOkResult_WhenUpdated()
        {
            // Arrange
            var model = new TermsConditionModel { UserId = 1, TermsAndConditions = true };
            _mockUserService.Setup(s => s.UpdateUserTermsAndCondition(model)).ReturnsAsync(1);

            // Act
            var result = await _controller.UpdateUserTermsCondition(model);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemUpdatedSucess, apiResponse.Message);
        }

        [Fact]
        public async Task UpdateUserTermsCondition_ReturnsBadRequest_WhenNotUpdated()
        {
            // Arrange
            var model = new TermsConditionModel { UserId = 1, TermsAndConditions = true };
            _mockUserService.Setup(s => s.UpdateUserTermsAndCondition(model)).ReturnsAsync(0);

            // Act
            var result = await _controller.UpdateUserTermsCondition(model);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotUpdated, apiResponse.Message);
        }

        [Fact]
        public async Task UpdateUserProfileSettings_ReturnsOkResult_WhenUpdated()
        {
            // Arrange
            var model = new UserProfileSettingsModel { UserId = 1, PreferredContactEmail = "<EMAIL>" };
            _mockUserService.Setup(s => s.UpdateUserProfileSettings(model)).ReturnsAsync(1);

            // Act
            var result = await _controller.UpdateUserProfileSettings(model);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemUpdatedSucess, apiResponse.Message);
        }

        [Fact]
        public async Task UpdateUserProfileSettings_ReturnsBadRequest_WhenNotUpdated()
        {
            // Arrange
            var model = new UserProfileSettingsModel { UserId = 1, PreferredContactEmail = "<EMAIL>" };
            _mockUserService.Setup(s => s.UpdateUserProfileSettings(model)).ReturnsAsync(0);

            // Act
            var result = await _controller.UpdateUserProfileSettings(model);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotUpdated, apiResponse.Message);
        }

        [Fact]
        public async Task DeleteAccount_ReturnsOkResult_WhenDeleted()
        {
            // Arrange
            _mockUserService.Setup(s => s.DeleteAccount(1, "provider")).ReturnsAsync(true);

            // Act
            var result = await _controller.DeleteAccount(1, "provider");

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemDeleted, apiResponse.Message);
        }

        [Fact]
        public async Task DeleteAccount_ReturnsBadRequest_WhenNotDeleted()
        {
            // Arrange
            _mockUserService.Setup(s => s.DeleteAccount(0, "provider")).ReturnsAsync(false);

            // Act
            var result = await _controller.DeleteAccount(1, "provider");

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotDeleted, apiResponse.Message);
        }
        #endregion
    }
}
