/* CSS Variables for consistent theming */
:root {
    /* Primary Colors */
    --primary-color: rgb(0, 122, 198);
    --primary-hover: rgb(0, 102, 178);
    --primary-light: rgb(0, 142, 218);

    /* Secondary Colors */
    --secondary-color: #6C7278;
    --text-color: #607184;
    --text-dark: #111827;

    /* Background Colors */
    --background-color: #f7f7f7;
    --panel-background: #fff;

    /* Border Colors */
    --border-color: #d1d5db;
    --border-focus: #0D75B0;

    /* Error Colors */
    --error-color: #a61e0c;
    --error-light: #d63301;

    /* Text Colors */
    --link-color: rgb(0, 122, 198);
    --placeholder-color: #6d6d6d;

    /* Button Colors */
    --button-disabled: #767676;
    --button-focus: #8a8886;

    --input-text-font-size: 16px;
}
/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

/* Base Styles */
body,
html {
    margin: 0;
    padding: 0;
    height: 100%;
    background-color: transparent !important;
    background: transparent !important;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
}

.container {
    display: flex;
    flex-direction: column;
    margin: 0 auto;
    padding: 20px;
}

/* Logo styles */
.logo {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 24px;
    font-size: 18px;
    font-weight: bold;
    color: #333;
    font-family: Inter;
    font-style: normal;
    font-weight: 900;
    line-height: 130%;
    letter-spacing: -0.468px;
}

.logo-icon {
    width: 20px;
    height: 20px;
    border-radius: 4px;
    background-image: url('https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/app_logo.svg');
}

/* Typography */
h4,
.title {
    font-weight: 600;
    margin-bottom: 5px;
    color: #111827;
}

.title-tag-line {
    font-size: small;
    display: inline-block;
    color: #6C7278;
    font-size: 14px;
    margin-bottom: 20px;
}

/* Form Controls */
#attributeList label {
    display: block;
    text-align: left;
    margin-bottom: 4px;
    color: #607184;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; 
    font-size: 12px;
    font-style: normal;
    font-weight: 400;
    line-height: 160%;
    letter-spacing: -0.24px;
}

#attributeList ul {
    list-style: none !important;
    padding: 0 !important;
    margin: 0 !important;
}


.attrEntry {
    padding-top: 8px;
}

 input[type=email],
 input[type=number],
 input[type=password],
 input[type=text] { 
    font-size: var(--input-text-font-size) !important;
 }

.attrEntry input[type=email],
.attrEntry input[type=password],
.attrEntry input[type=text],
.attrEntry input[type=number],
.textInput {
    width: 100%;
    padding: 6px 12px;
    border-radius: 12px;
    border: 1px solid #d1d5db;
    font-size: var(--input-text-font-size) !important;
    height: 45px;
    box-sizing: border-box;
    font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; 
}

.attrEntry input:focus {
    outline: 1px solid #0D75B0;
    border-color: #0D75B0;
}

.attrEntry .validate,
.Password .validate {
    display: flex;
    flex-direction: column;
}

.attrEntry .error.itemLevel {
    /* display: none; */
    color: var(--error-color);
    font-size: small;
}

.attrEntry.validate .error.itemLevel.show,
.attrEntry.validate .helpText.show,
.helpText.show {
    display: block;
}

.attrEntry .error.itemLevel,
.attrEntry .error.itemLevel.show,
.Password .error.itemLevel.show {
    order: 2;
    margin-top: 4px;
    font-size: small;
}

#attributeList ul li label.required::after {
    content: " *";
    color: var(--error-color);
}

/* Button Styles */
.buttons {
    margin-top: 10px;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

button:disabled {
    background-color: #767676;
    cursor: not-allowed;
}

button#continue,
button#next,
.changeClaims,
.verifyCode,
.sendCode {
    width: 100%;
    padding: 12px 0;
    border-radius: 100px;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    border: none;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 45px;
    text-align: center;
}

button.sendNewCode {
    background: none;
    border: none;
    padding: 0;
    color: var(--primary-color);
    /* or your brand's primary link color */
    text-decoration: underline;
    font: inherit;
    cursor: pointer;
    display: block;
    margin-top: -1rem;
    width: 100%;
}

button.sendNewCode:focus {
    outline: none;
}

 #emailVerificationControl_but_change_claims {
     all: unset;
     display: inline-block;
     text-decoration: underline;
     position: relative;
     color: transparent
 }

 #emailVerificationControl_but_change_claims::after {
     content: "Change Email";
     position: relative;
     width: auto;
     height: auto;
     overflow: visible;
     display: inline-block;
     color: var(--primary-color);
     float: inline-end;
     margin-right: 10px;
     margin-top: -10px;
 }

 #cancel {
     background-color: transparent !important;
     color: var(--primary-color) !important;
     border: 2px solid var(--primary-color) !important;
    width: 100%;
        padding: 12px 0;
        border-radius: 100px;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 45px;
        text-align: center;
 }

button#continue::after {
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: absolute;
}

button#next::after {
    content: "Log in";
    color: #fff;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: absolute;
}

button:hover {
    opacity: 0.9;
}

button:disabled {
    background-color: #767676;
    cursor: not-allowed;
}

/* Social Button Styles */
button#SignUpWithLogonEmailExchange,
button#GoogleExchange,
button#FacebookExchange,
button#AppleManagedExchange {
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    font-weight: 500;
    background-color: #ffffff;
    border: 1px solid #d1d5db;
    border-radius: 999px;
    cursor: pointer;
    position: relative;
    margin-top: 10px;
    background-image: none;
    width: 100%;
    height: 45px;
    color: transparent;
}

button#GoogleExchange::before,
button#FacebookExchange::before,
button#AppleManagedExchange::before {
    content: "";
    position: absolute;
    left: 35px;
    top: 50%;
    transform: translateY(-50%);
    width: 18px;
    height: 18px;
    background-size: contain;
    background-repeat: no-repeat;
    background-position: center;
}

button#GoogleExchange::before {
    background-image: url('https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/google.svg');
}

button#FacebookExchange::before {
    background-image: url('https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/facebook.svg');
}

button#AppleManagedExchange::before {
    background-image: url('https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/apple_pay.svg');
}

button#GoogleExchange::after,
button#FacebookExchange::after,
button#AppleManagedExchange::after {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: #111827;
    font-size: 16px;
    font-weight: 500;
    white-space: nowrap;
}

button#GoogleExchange::after {
    content: "Continue with Google";
    font-weight: 600;
}

button#FacebookExchange::after {
    content: "Continue with Facebook";
    font-weight: 600;
}

button#AppleManagedExchange::after {
    content: "Continue with Apple";
    font-weight: 600;
}

/* Common Elements and Utilities */
.heading {
    display: none !important;
}

.intro {
    display: none !important;
}

.back-button {
    margin-bottom: 10px;
}

.divider {
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 20px 0;
    position: relative;
}

.divider::before,
.divider::after {
    content: "";
    flex: 1;
    height: 1px;
    background: #EDF1F3;
}

.divider h2 {
    margin: 0 10px;
    font-size: x-small;
    font-weight: 400;
    color: #6C7278;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.verificationSuccessText,
#emailVerificationControl_success_message {
    font-size: small;
    font-weight: 600;
}

.tos-notice {
    font-size: 13px;
    color: #6C7278;
    font-family: Segoe UI, Arial, sans-serif;
    margin-top: 50px;
}

.tos-link {
    color: var(--primary-color);
    font-weight: 500;
}

/* Media Queries */
@media only screen and (max-width: 501px) {
    .container {
        padding: 15px;
    }

    .attrEntry input[type=text],
    input[type=email],
    input[type=password] {
        width: calc(100% - 5px) !important;
    }
}