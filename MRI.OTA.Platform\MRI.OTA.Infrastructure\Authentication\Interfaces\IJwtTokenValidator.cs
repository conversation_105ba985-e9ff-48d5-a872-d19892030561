﻿using System.Security.Claims;
using MRI.OTA.Common.Models;

namespace MRI.OTA.Infrastructure.Authentication.Interfaces
{
    /// <summary>
    /// Interface to manage token service
    /// </summary>
    public interface IJwtTokenValidator
    {
        /// <summary>
        /// Function used to validate token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public Task<ClaimsPrincipal> ValidateToken(string token, bool isClientCredentials = false);

        /// <summary>
        /// Read JWT token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public Task<List<Claim>> GetClaims(string token);

        /// <summary>
        /// Get access token
        /// </summary>
        /// <param name="accessToken"></param>
        /// <returns></returns>
        public Task<bool> ValidateAccessToken(string accessKey, string accessToken);

        /// <summary>
        /// Extract claims from the validated principal to create response
        /// </summary>
        /// <param name="principal">The validated claims principal</param>
        /// <param name="token">The original token</param>
        /// <returns>Token validation response with extracted claims</returns>
        TokenValidationResponse ExtractClaimsFromPrincipal(ClaimsPrincipal principal, string token);
    }
}
