<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <meta name="ROBOTS" content="NONE, NOARCHIVE">
    <meta name="GOOGLEBOT" content="NOARCHIVE">
    
    <style>
        /* CSS Variables for consistent theming */
        :root {
            --primary-color: rgb(0, 122, 198);
            --primary-hover: rgb(0, 102, 178);
            --primary-light: rgb(0, 142, 218);
            --secondary-color: #6C7278;
            --text-color: #607184;
            --text-dark: #111827;
            --background-color: #f7f7f7;
            --panel-background: #fff;
            --border-color: #d1d5db;
            --border-focus: #0D75B0;
            --error-color: #a61e0c;
            --error-light: #d63301;
            --link-color: rgb(0, 122, 198);
            --placeholder-color: #6d6d6d;
            --button-disabled: #767676;
            --button-focus: #8a8886;
            --input-text-font-size: 16px;
        }

        /* Import Google Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        /* Base Styles */
        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            background-color: transparent !important;
            background: transparent !important;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
        }

        /* Loading Animation */
        body.loading .container { display: none !important; }
        body.loading #shimmer-loader { display: block; }
        body:not(.loading) #shimmer-loader { display: none; }
        
        #shimmer-loader {
            width: 100%;
            max-width: 400px;
            margin: 60px auto 0 auto;
            padding: 40px 0;
            display: none;
        }
        
        .shimmer {
            width: 100%;
            height: 180px;
            background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
            background-size: 200% 100%;
            animation: shimmer 1.5s infinite;
            border-radius: 12px;
        }
        
        @keyframes shimmer {
            0% { background-position: -200% 0; }
            100% { background-position: 200% 0; }
        }

        .container {
            display: flex;
            flex-direction: column;
            margin: 0 auto;
            padding: 20px;
        }

        /* Logo styles */
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 24px;
            font-size: 18px;
            font-weight: bold;
            color: #333;
            font-family: Inter;
            font-style: normal;
            font-weight: 900;
            line-height: 130%;
            letter-spacing: -0.468px;
        }

        .logo-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            background-image: url('https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/app_logo.svg');
        }

        /* Typography */
        h4, .title {
            font-weight: 600;
            margin-bottom: 5px;
            color: #111827;
        }

        .title-tag-line {
            font-size: small;
            display: inline-block;
            color: #6C7278;
            font-size: 14px;
            margin-bottom: 20px;
        }

        /* Panel Layout */
        img#background_background_image {
            height: 100%;
            width: 100%
        }

        div#background_page_overlay {
            background: left top no-repeat fixed #f7f7f7;
            height: 100%;
            opacity: 1
        }

        .panel {
            background: #fff;
            float: right;
            height: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            position: fixed;
            right: 0;
            width: 500px;
            z-index: 1
        }

        .inner_container {
            max-height: 90%;
            min-height: 90%;
            width: 100%
        }

        #panel_center, #panel_left, #panel_right {
            display: inline-block;
            border: 0;
            height: 100%;
            margin: 0
        }

        #panel_left {
            padding: 0;
            width: 50px
        }

        #panel_center {
            min-height: 100%;
            padding: 0;
            width: 378px
        }

        .hide {
            opacity: 0
        }

        /* Form Controls */
        #attributeList label {
            display: block;
            text-align: left;
            margin-bottom: 4px;
            color: var(--text-color);
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; 
            font-size: 12px;
            font-style: normal;
            font-weight: 400;
            line-height: 160%;
            letter-spacing: -0.24px;
        }

        #attributeList ul {
            list-style: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        .attrEntry {
            padding-top: 8px;
        }

        input[type=email],
        input[type=number],
        input[type=password],
        input[type=text] { 
            font-size: var(--input-text-font-size) !important;
        }

        .attrEntry input[type=email],
        .attrEntry input[type=password],
        .attrEntry input[type=text],
        .attrEntry input[type=number],
        .textInput {
            width: 100%;
            padding: 6px 12px;
            border-radius: 12px;
            border: 1px solid #d1d5db;
            font-size: var(--input-text-font-size) !important;
            height: 45px;
            box-sizing: border-box;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; 
        }

        .attrEntry input:focus {
            outline: 1px solid #0D75B0;
            border-color: #0D75B0;
        }

        .attrEntry .validate,
        .Password .validate {
            display: flex;
            flex-direction: column;
        }

        .attrEntry .error.itemLevel {
            color: var(--error-color);
            font-size: small;
        }

        .attrEntry.validate .error.itemLevel.show,
        .attrEntry.validate .helpText.show,
        .helpText.show {
            display: block;
        }

        .attrEntry .error.itemLevel,
        .attrEntry .error.itemLevel.show,
        .Password .error.itemLevel.show {
            order: 2;
            margin-top: 4px;
            font-size: small;
        }

        #attributeList ul li label.required::after {
            content: " *";
            color: var(--error-color);
        }

        /* Remember Me */
        .rememberMe input[type=checkbox] {
            vertical-align: middle;
            position: relative;
            bottom: -2.5px;
            width: 13px;
        }

        .rememberMe label {
            display: inline-block
        }

        /* Intro and Entry */
        .intro {
            display: inline;
            margin-bottom: 5px
        }

        .intro p {
            padding-bottom: 7px
        }

        .entry {
            padding-top: 8px;
            padding-bottom: 0 !important
        }

        /* Button Styles */
        .buttons {
            margin-top: 10px;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        button:disabled {
            background-color: #767676;
            cursor: not-allowed;
        }

        button#continue,
        button#next,
        .changeClaims,
        .verifyCode,
        .sendCode {
            width: 100%;
            padding: 12px 0;
            border-radius: 100px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 45px;
            text-align: center;
        }

        /* Login button */
        #next {
            width: 100%;
            padding: 12px 0;
            border-radius: 100px;
            background: var(--primary-color);
            color: white;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            border: none;
            display: flex;
            align-items: center;
            justify-content: center;
            height: 45px;
            color: transparent;
        }

        button.sendNewCode {
            background: none;
            border: none;
            padding: 0;
            color: var(--primary-color);
            text-decoration: underline;
            font: inherit;
            cursor: pointer;
            display: block;
            margin-top: -1rem;
            width: 100%;
        }

        button.sendNewCode:focus {
            outline: none;
        }

        #emailVerificationControl_but_change_claims {
            all: unset;
            display: inline-block;
            text-decoration: underline;
            position: relative;
            color: transparent
        }

        #emailVerificationControl_but_change_claims::after {
            content: "Change Email";
            position: relative;
            width: auto;
            height: auto;
            overflow: visible;
            display: inline-block;
            color: var(--primary-color);
        }

        /* Social Buttons */
        .claims-provider-list-buttons {
            margin: 20px 0;
        }

        .claims-provider-list-buttons .intro h2 {
            font-size: 16px;
            margin-bottom: 15px;
            color: var(--text-dark);
        }

        .accountButton {
            border: 1px solid #fff;
            color: #fff;
            margin: 2px 0;
            border-radius: 8px;
            text-align: center;
            word-wrap: break-word;
            height: 45px;
            width: 100%;
            padding-left: 30px;
            background-color: #505050;
            cursor: pointer;
            font-size: 14px;
            display: block;
            margin-bottom: 10px;
        }

        .accountButton:hover {
            background-color: #404040;
        }

        .accountButton.firstButton {
            margin-top: 0;
        }

        /* Specific social button colors */
        #GoogleExchange {
            background-color: #db4437;
        }

        #FacebookExchange {
            background-color: #3b5998;
        }

        #AppleManagedExchange {
            background-color: #000000;
        }

        /* Divider */
        .divider {
            margin: 20px 0 10px 0;
        }

        .divider h2 {
            color: var(--secondary-color);
            font-size: 14px;
            font-weight: normal;
            text-align: center;
            position: relative;
            margin: 20px 0;
        }

        .divider h2:after,
        .divider h2:before {
            border-top: 1px solid #b8b8b8;
            content: '';
            display: table-cell;
            position: relative;
            top: .7em;
            width: 50%;
        }

        .divider h2:before { 
            right: 1.8%; 
        }

        .divider h2:after { 
            left: 1.8%; 
        }

        /* Create Account */
        .create {
            position: relative;
            width: 100%;
            margin: 30px auto 20px;
            text-align: center;
            padding: 0 15px;
            box-sizing: border-box;
            clear: both;
            margin-top: 40px;
        }

        .create a {
            color: var(--primary-color);
            text-decoration: none;
            font-family: 'Inter', 'Open Sans', sans-serif;
            font-size: 14px;
            font-weight: 500;
        }

        .create p {
            font-size: smaller;
        }

        .create a:hover {
            text-decoration: underline;
        }

        /* Verification styles */
        .verificationErrorText {
            color: var(--error-color);
        }

        .verificationSuccessText {
            color: #28a745;
        }

        .verificationInfoText {
            color: var(--text-color);
        }

        /* Working styles */
        .working {
            display: none;
        }

        /* API Container */
        .api_container {
            display: inline-block;
            padding: 120px 0 0 0;
            position: relative;
            width: 100%;
            height: 100%;
        }

        /* Responsive Design */
        @media (max-width: 768px) {
            .panel {
                width: 100% !important;
                position: relative !important;
                float: none !important;
                height: auto !important;
            }
            
            .api_container {
                padding: 40px 20px !important;
            }
            
            #panel_center {
                width: 100% !important;
            }
            
            #panel_left {
                display: none;
            }
            
            div#background_page_overlay {
                position: relative;
                height: auto;
            }
        }

        @media (max-width: 480px) {
            .attrEntry input[type=email],
            .attrEntry input[type=password],
            .attrEntry input[type=text],
            .attrEntry input[type=number] {
                font-size: 16px; /* Prevents zoom on iOS */
            }
            
            .logo {
                font-size: 16px;
            }
            
            .container {
                padding: 15px;
            }
        }

        /* Additional B2C specific styles */
        .heading {
            display: none !important;
        }

        .intro {
            display: none !important;
        }

        /* Custom heading for login */
        .api_container::before {
            content: "Log in using social accounts";
            display: block;
            font-size: 18px;
            font-weight: 600;
            color: var(--text-dark);
            text-align: center;
            margin-bottom: 20px;
        }

        /* Hide default B2C elements */
        .claims-provider-list-buttons .intro {
            display: block !important;
        }

        .claims-provider-list-buttons .intro h2 {
            display: block !important;
        }
    </style>
</head>
<body class="loading">
    <div id="shimmer-loader">
        <div class="shimmer"></div>
    </div>

    <div class="container">
        <div class="logo">
            <div class="logo-icon"></div>
            Super App
        </div>
    </div>

    <div id="background_page_overlay">
        <div class="panel">
            <div class="api_container">
                <div id="panel_center">
                    <div id="api" data-name="Unified"></div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // Remove loading state when content loads
        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(function() {
                document.body.classList.remove('loading');
            }, 500);
        });

        // Handle form submission
        document.addEventListener('DOMContentLoaded', function() {
            // Add any custom JavaScript for form handling
            var form = document.querySelector('form');
            if (form) {
                form.addEventListener('submit', function(e) {
                    // Add loading state to button
                    var submitButton = document.querySelector('#next');
                    if (submitButton) {
                        submitButton.disabled = true;
                        submitButton.textContent = 'Signing in...';
                    }
                });
            }
        });
    </script>
</body>
</html>