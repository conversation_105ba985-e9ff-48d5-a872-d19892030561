﻿using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Repositories;
using System.Data;

namespace MRI.OTA.UnitTestCases.Notification.Repository
{
    public class NotificationRepositoryTest
    {
        private readonly Mock<IDbConnectionFactory> _connectionFactoryMock = new();
        private readonly Mock<IDapperWrapper> _dapperWrapperMock = new();
        private readonly Mock<ILogger<IntegrationRepository>> _loggerMock = new();
        private readonly Mock<IDbConnection> _dbConnectionMock = new();
        private readonly NotificationRepository _repository;

        public NotificationRepositoryTest()
        {
            _connectionFactoryMock.Setup(f => f.CreateConnection()).Returns(_dbConnectionMock.Object);
            _repository = new NotificationRepository(_connectionFactoryMock.Object, _loggerMock.Object, _dapperWrapperMock.Object);
        }

        [Fact]
        public async Task GetNotificationDetailByCategory_ReturnsList_WhenSuccess()
        {
            // Arrange
            var categories = new[] { "cat1", "cat2" };
            var notifications = new List<NotificationMaster>
            {
                new NotificationMaster { NotificationId = 1, Category = "cat1" },
                new NotificationMaster { NotificationId = 2, Category = "cat2" }
            };
            _dapperWrapperMock
                .Setup(d => d.QueryAsync<NotificationMaster>(It.IsAny<IDbConnection>(), It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(notifications);

            // Act
            var result = await _repository.GetNotificationDetailByCategory(categories);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Contains(result, n => n.Category == "cat1");
            Assert.Contains(result, n => n.Category == "cat2");
        }

        [Fact]
        public async Task GetNotificationDetailByCategory_ReturnsNull_OnException()
        {
            // Arrange
            var categories = new[] { "cat1" };
            _dapperWrapperMock
                .Setup(d => d.QueryAsync<NotificationMaster>(It.IsAny<IDbConnection>(), It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetNotificationDetailByCategory(categories);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetUsersForNotificationBySRCPropertyId_ReturnsList_WhenSuccess()
        {
            // Arrange
            var srcPropertyIds = new[] { "p1", "p2" };
            var users = new List<(int, int, string, string)>
            {
                (1, 1, "dev1", "token1"),
                (2, 2, "dev2", "token2")
            };
            _dapperWrapperMock
                .Setup(d => d.QueryAsync<(int, int, string, string)>(It.IsAny<IDbConnection>(), It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(users);

            // Act
            var result = await _repository.GetUsersForNotificationBySRCId(srcPropertyIds, "inspection");

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetUsersForNotificationBySRCPropertyId_ReturnsNull_OnException()
        {
            // Arrange
            var srcPropertyIds = new[] { "p1" };
            _dapperWrapperMock
                .Setup(d => d.QueryAsync<(int, int, string, string)>(It.IsAny<IDbConnection>(), It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await _repository.GetUsersForNotificationBySRCId(srcPropertyIds, "inspection");

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task AddUserNotificationDetails_ReturnsExistingId_IfExists()
        {
            // Arrange
            var userNotificationDetails = new UserNotificationDetails { UserNotificationDetailId = 5 };
            var expectedId = 10;
            var notificationId = 1;
            var userId = 2;
            typeof(UserNotificationDetails).GetProperty("NotificationId")?.SetValue(userNotificationDetails, notificationId);
            typeof(UserNotificationDetails).GetProperty("UserId")?.SetValue(userNotificationDetails, userId);

            var repoMock = new Mock<NotificationRepository>(_connectionFactoryMock.Object, _loggerMock.Object, _dapperWrapperMock.Object) { CallBase = true };
            repoMock.Setup(r => r.GetByIdAsync<int>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(expectedId);

            // Act
            var result = await repoMock.Object.AddUserNotificationDetails(userNotificationDetails);

            // Assert
            Assert.Equal(expectedId, result);
        }

        [Fact]
        public async Task AddUserNotificationDetails_InsertsAndReturnsId_IfNotExists()
        {
            // Arrange
            var userNotificationDetails = new UserNotificationDetails { UserNotificationDetailId = 0 };
            var notificationId = 1;
            var userId = 2;
            typeof(UserNotificationDetails).GetProperty("NotificationId")?.SetValue(userNotificationDetails, notificationId);
            typeof(UserNotificationDetails).GetProperty("UserId")?.SetValue(userNotificationDetails, userId);

            var repoMock = new Mock<NotificationRepository>(_connectionFactoryMock.Object, _loggerMock.Object, _dapperWrapperMock.Object) { CallBase = true };
            repoMock.Setup(r => r.GetByIdAsync<int>(It.IsAny<string>(), It.IsAny<object>())).ReturnsAsync(0);
            repoMock.Setup(r => r.ConvertToDictionary(userNotificationDetails)).Returns(new Dictionary<string, object> { { "UserId", userId }, { "NotificationId", notificationId } });
            repoMock.Setup(r => r.AddAsync("UserNotificationDetails", "UserNotificationDetailId", It.IsAny<Dictionary<string, object>>(), null, null)).ReturnsAsync(123);

            // Act
            var result = await repoMock.Object.AddUserNotificationDetails(userNotificationDetails);

            // Assert
            Assert.Equal(123, result);
        }

        [Fact]
        public async Task AddUserNotificationDetails_ReturnsMinusOne_OnException()
        {
            // Arrange
            var userNotificationDetails = new UserNotificationDetails();
            var repoMock = new Mock<NotificationRepository>(_connectionFactoryMock.Object, _loggerMock.Object, _dapperWrapperMock.Object) { CallBase = true };
            repoMock.Setup(r => r.GetByIdAsync<int>(It.IsAny<string>(), It.IsAny<object>())).ThrowsAsync(new Exception("DB error"));

            // Act
            var result = await repoMock.Object.AddUserNotificationDetails(userNotificationDetails);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task UpdateUserNotification_ThrowsNotImplementedException()
        {
            // Act & Assert
            await Assert.ThrowsAsync<NotImplementedException>(() => _repository.UpdateUserNotification(1));
        }
    }
}
