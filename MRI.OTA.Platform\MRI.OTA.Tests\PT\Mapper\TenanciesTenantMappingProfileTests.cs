using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.PT.Mapper
{
    public class TenanciesTenantMappingProfileTests
    {
        private readonly IMapper _mapper;

        public TenanciesTenantMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<TenanciesTenantMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_TenanciesTenantResponse_To_TenanciesTenant()
        {
            // Arrange
            var source = new TenanciesTenantResponse
            {
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001",
                TenancyName = "Test Tenancy",
                TenancyStatus = "Active",
                TenancyStartDate = new DateTime(2025, 1, 1),
                TenancyEndDate = new DateTime(2026, 1, 1),
                VacateDate = new DateTime(2025, 12, 31),
                AmountToVacate = 1000.50m,
                PayToDate = new DateTime(2025, 6, 30),
                Rent = 2500.00m,
                Arrears = 0.00m,
                OutstandingInvoicesAmount = 150.00m,
                TenancyContactName = "John Smith",
                TenancyContactNumber = "0400123456",
                TenancyContactEmail = "<EMAIL>"
            };

            // Act
            var result = _mapper.Map<TenanciesTenant>(source);

            // Assert
            Assert.NotNull(result);
            
            // Check main tenancy details
            Assert.Equal(source.ManagementId, result.SRCManagementId);
            Assert.Equal(source.PropertyId, result.SRCPropertyId);
            Assert.Equal(-1, result.PropertyId);
            Assert.Equal(source.TenancyId, result.SRCTenancyId);
            Assert.Equal(source.TenancyName, result.TenancyName);
            Assert.True(result.IsActive); // Because TenancyStatus is "Active"
            Assert.Equal(source.TenancyStartDate, result.LeaseStart);
            Assert.Equal(source.TenancyEndDate, result.LeaseEnd);
            Assert.Equal(source.VacateDate, result.VacateDate);
            Assert.Equal(source.AmountToVacate, result.AmountToVacate);
            Assert.Equal(source.PayToDate, result.PayToDate);
            Assert.Equal(source.Rent, result.Rent);
            Assert.Equal(source.Arrears, result.Arrears);
            Assert.Equal(source.OutstandingInvoicesAmount, result.OutstandingInvoices);

            // Check property manager details
            Assert.NotNull(result.TenanciesPropertyManagerDetails);
            Assert.Equal(source.ManagementId, result.TenanciesPropertyManagerDetails.SRCManagementId);
            Assert.Equal(source.PropertyId, result.TenanciesPropertyManagerDetails.SRCPropertyId);
            Assert.Equal(-1, result.TenanciesPropertyManagerDetails.PropertyId);
            Assert.Equal(source.TenancyContactName, result.TenanciesPropertyManagerDetails.PropertyManagerName);
            Assert.Equal(source.TenancyContactNumber, result.TenanciesPropertyManagerDetails.PropertyManagerMobile);
            Assert.Equal(source.TenancyContactEmail, result.TenanciesPropertyManagerDetails.PropertyManagerEmail);
        }

        [Theory]
        [InlineData("Active", true)]
        [InlineData("active", true)]
        [InlineData("ACTIVE", true)]
        [InlineData("Inactive", false)]
        [InlineData("Pending", false)]
        [InlineData(null, false)]
        public void Should_Map_TenancyStatus_To_IsActive_Correctly(string tenancyStatus, bool expectedIsActive)
        {
            // Arrange
            var source = new TenanciesTenantResponse
            {
                TenancyStatus = tenancyStatus,
                // Set minimum required properties
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001"
            };

            // Act
            var result = _mapper.Map<TenanciesTenant>(source);

            // Assert
            Assert.Equal(expectedIsActive, result.IsActive);
        }

        [Fact]
        public void Should_Map_Null_Dates_To_Default_DateTime()
        {
            // Arrange
            var source = new TenanciesTenantResponse
            {
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001",
                TenancyStartDate = null,
                TenancyEndDate = null,
                VacateDate = null,
                PayToDate = null
            };

            // Act
            var result = _mapper.Map<TenanciesTenant>(source);

            // Assert
            Assert.Equal(default, result.LeaseStart);
            Assert.Equal(default, result.LeaseEnd);
            Assert.Equal(default, result.VacateDate);
            Assert.Equal(default, result.PayToDate);
        }

        [Fact]
        public void Should_Map_Null_Decimal_Values_To_Zero()
        {
            // Arrange
            var source = new TenanciesTenantResponse
            {
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001",
                AmountToVacate = null,
                Rent = null,
                Arrears = null,
                OutstandingInvoicesAmount = null
            };

            // Act
            var result = _mapper.Map<TenanciesTenant>(source);

            // Assert
            Assert.Equal(null!, result.AmountToVacate);
            Assert.Equal(null!, result.Rent);
            Assert.Equal(null!, result.Arrears);
            Assert.Equal(null!, result.OutstandingInvoices);
        }

        [Fact]
        public void Should_Handle_Reverse_Mapping()
        {
            // Arrange
            var source = new TenanciesTenant
            {
                SRCManagementId = "OWN456",
                SRCPropertyId = "PROP789",
                PropertyId = -1,
                SRCTenancyId = "TEN001",
                TenancyName = "Test Tenancy",
                IsActive = true,
                LeaseStart = new DateTime(2025, 1, 1),
                LeaseEnd = new DateTime(2026, 1, 1),
                VacateDate = new DateTime(2025, 12, 31),
                AmountToVacate = 1000.50m,
                PayToDate = new DateTime(2025, 6, 30),
                Rent = 2500.00m,
                Arrears = 0.00m,
                OutstandingInvoices = 150.00m,
                TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails
                {
                    PropertyManagerName = "John Smith",
                    PropertyManagerMobile = "0400123456",
                    PropertyManagerEmail = "<EMAIL>"
                }
            };

            // Act
            var result = _mapper.Map<TenanciesTenantResponse>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(null!, result.ManagementId);
            Assert.Equal(null!, result.PropertyId);
            Assert.Equal(source.SRCTenancyId, result.TenancyId);
            Assert.Equal(source.TenancyName, result.TenancyName);
            Assert.Equal(null!, result.TenancyStatus);
            Assert.Equal(source.LeaseStart, result.TenancyStartDate);
            Assert.Equal(source.LeaseEnd, result.TenancyEndDate);
            Assert.Equal(source.VacateDate, result.VacateDate);
            Assert.Equal(source.AmountToVacate, result.AmountToVacate);
            Assert.Equal(source.PayToDate, result.PayToDate);
            Assert.Equal(source.Rent, result.Rent);
            Assert.Equal(source.Arrears, result.Arrears);
            Assert.Equal(source.OutstandingInvoices, result.OutstandingInvoicesAmount);
            Assert.Equal(source.TenanciesPropertyManagerDetails.PropertyManagerName, result.TenancyContactName);
            Assert.Equal(source.TenanciesPropertyManagerDetails.PropertyManagerMobile, result.TenancyContactNumber);
            Assert.Equal(source.TenanciesPropertyManagerDetails.PropertyManagerEmail, result.TenancyContactEmail);
        }
    }
}
