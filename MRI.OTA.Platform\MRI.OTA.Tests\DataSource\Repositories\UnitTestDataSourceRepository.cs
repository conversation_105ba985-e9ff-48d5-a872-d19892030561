using System.Data;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Repositories;

namespace MRI.OTA.UnitTestCases.DataSource.Repositories
{
    public class UnitTestDataSourceRepository
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<DataSourceRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<IDbTransaction> _mockTransaction;
        private readonly DataSourceRepository _repository;

        public UnitTestDataSourceRepository()
        {
            // Setup mocks
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<DataSourceRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();
            _mockTransaction = new Mock<IDbTransaction>();

            // Configure mock connection factory
            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);
            _mockConnection.Setup(c => c.BeginTransaction()).Returns(_mockTransaction.Object);

            // Create repository instance to test
            _repository = new DataSourceRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object
            );
        }

        #region GetUserDataSource with accessKey and accessSecret tests

        [Fact]
        public async Task GetUserDataSource_WithAccessKeyAndSecret_ShouldReturnDataSource()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";
            var expectedDataSource = new MRI.OTA.Core.Entities.DataSource 
            { 
                DataSourceId = 1, 
                Name = "Test DataSource",
                Description = "Test Description",
                ManifestJson = "{\"test\":\"data\"}",
                AccessKey = accessKey,
                AccessSecret = accessSecret
            };

            string expectedQuery = "SELECT * FROM DataSource WHERE AccessKey = @AccessKey AND AccessSecret = @AccessSecret";
            var expectedParams = new { AccessKey = accessKey, AccessSecret = accessSecret };            
            
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedDataSource);

            // Act
            var result = await _repository.GetUserDataSource(accessKey, accessSecret);            
            
            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedDataSource.DataSourceId, result.DataSourceId);
            Assert.Equal(expectedDataSource.Name, result.Name);
            Assert.Equal(expectedDataSource.AccessKey, result.AccessKey);
            Assert.Equal(expectedDataSource.AccessSecret, result.AccessSecret);

            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }

        [Fact]
        public async Task GetUserDataSource_WithAccessKeyAndSecret_WhenNotFound_ShouldReturnNull()
        {
            // Arrange
            var accessKey = "invalid-key";
            var accessSecret = "invalid-secret";
            
            string expectedQuery = "SELECT * FROM DataSource WHERE AccessKey = @AccessKey AND AccessSecret = @AccessSecret";
            var expectedParams = new { AccessKey = accessKey, AccessSecret = accessSecret };            
            
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((MRI.OTA.Core.Entities.DataSource)null);

            // Act
            var result = await _repository.GetUserDataSource(accessKey, accessSecret);

            // Assert
            Assert.Null(result);

            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }

        #endregion

        #region GetUserDataSource with accessKey only tests

        [Fact]
        public async Task GetUserDataSource_WithAccessKeyOnly_ShouldReturnDataSource()
        {
            // Arrange
            var accessKey = "test-key";
            var expectedDataSource = new MRI.OTA.Core.Entities.DataSource 
            { 
                DataSourceId = 1, 
                Name = "Test DataSource",
                AccessKey = accessKey
            };

            string expectedQuery = "SELECT * FROM DataSource WHERE AccessKey = @AccessKey";
            var expectedParams = new { AccessKey = accessKey };            
            
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedDataSource);

            // Act
            var result = await _repository.GetUserDataSource(accessKey);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedDataSource.DataSourceId, result.DataSourceId);
            Assert.Equal(expectedDataSource.Name, result.Name);
            Assert.Equal(expectedDataSource.AccessKey, result.AccessKey);

            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }

        [Fact]
        public async Task GetUserDataSource_WithAccessKeyOnly_WhenNotFound_ShouldReturnNull()
        {
            // Arrange
            var accessKey = "invalid-key";
            
            string expectedQuery = "SELECT * FROM DataSource WHERE AccessKey = @AccessKey";
            var expectedParams = new { AccessKey = accessKey };            
            
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((MRI.OTA.Core.Entities.DataSource)null);

            // Act
            var result = await _repository.GetUserDataSource(accessKey);

            // Assert
            Assert.Null(result);

            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }

        #endregion

        #region UpdateDataSource tests

        [Fact]
        public async Task UpdateDataSource_ShouldExecuteUpdateQuery()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";
            var manifestJson = "{\"updated\": \"data\"}";
            var expectedRowsAffected = 1;

            string expectedQuery = "UPDATE DataSource SET ManifestJson = @ManifestJson WHERE AccessKey = @AccessKey AND AccessSecret = @AccessSecret";
            var expectedParams = new { ManifestJson = manifestJson, AccessKey = accessKey, AccessSecret = accessSecret };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.Is<object>(p => 
                        p.GetType().GetProperty("ManifestJson").GetValue(p).ToString() == manifestJson &&
                        p.GetType().GetProperty("AccessKey").GetValue(p).ToString() == accessKey &&
                        p.GetType().GetProperty("AccessSecret").GetValue(p).ToString() == accessSecret
                    ),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdateDataSource(accessKey, accessSecret, manifestJson);

            // Assert
            Assert.Equal(expectedRowsAffected, result);

            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdateDataSource_WhenNoRowsUpdated_ShouldReturnZero()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";
            var manifestJson = "{\"updated\": \"data\"}";
            var expectedRowsAffected = 0;

            string expectedQuery = "UPDATE DataSource SET ManifestJson = @ManifestJson WHERE AccessKey = @AccessKey AND AccessSecret = @AccessSecret";
            var expectedParams = new { ManifestJson = manifestJson, AccessKey = accessKey, AccessSecret = accessSecret };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.Is<object>(p => 
                        p.GetType().GetProperty("ManifestJson").GetValue(p).ToString() == manifestJson &&
                        p.GetType().GetProperty("AccessKey").GetValue(p).ToString() == accessKey &&
                        p.GetType().GetProperty("AccessSecret").GetValue(p).ToString() == accessSecret
                    ),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdateDataSource(accessKey, accessSecret, manifestJson);

            // Assert
            Assert.Equal(expectedRowsAffected, result);

            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }

        [Fact]
        public async Task UpdateDataSource_WhenExceptionOccurs_ShouldPropagateException()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";
            var manifestJson = "{\"updated\": \"data\"}";
            var expectedException = new Exception("Database error");

            string expectedQuery = "UPDATE DataSource SET ManifestJson = @ManifestJson WHERE AccessKey = @AccessKey AND AccessSecret = @AccessSecret";
            var expectedParams = new { ManifestJson = manifestJson, AccessKey = accessKey, AccessSecret = accessSecret };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(expectedException);

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() =>
                _repository.UpdateDataSource(accessKey, accessSecret, manifestJson));
            
            Assert.Equal(expectedException, exception);
        }

        #endregion

        #region GetAccessToken tests        
        [Fact]
        public async Task GetAccessToken_ShouldReturnDataSource()
        {
            // Arrange
            var accessToken = "test-token";
            var expectedDataSource = new MRI.OTA.Core.Entities.DataSource
            {
                DataSourceId = 1,
                Name = "Test DataSource",
                AccessSecret = accessToken
            };

            string expectedQuery = "SELECT * FROM DataSource WHERE AccessSecret = @AccessToken";
            var expectedParams = new { AccessToken = accessToken };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedDataSource);            // Act
            var result = await _repository.GetAccessToken(accessToken);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedDataSource.DataSourceId, result.DataSourceId);
            Assert.Equal(expectedDataSource.Name, result.Name);
            Assert.Equal(expectedDataSource.AccessSecret, result.AccessSecret);

            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }



        [Fact]
        public async Task GetAccessToken_WhenNotFound_ShouldReturnNull()
        {
            // Arrange
            var accessToken = "invalid-token";
            
            string expectedQuery = "SELECT * FROM DataSource WHERE AccessSecret = @AccessToken";
            var expectedParams = new { AccessToken = accessToken };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    expectedQuery,
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((MRI.OTA.Core.Entities.DataSource)null);

            // Act
            var result = await _repository.GetAccessToken(accessToken);

            // Assert
            Assert.Null(result);

            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<MRI.OTA.Core.Entities.DataSource>(
                It.IsAny<IDbConnection>(),
                expectedQuery,
                It.IsAny<object>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()),
                Times.Once);
        }

        #endregion
    }
}
