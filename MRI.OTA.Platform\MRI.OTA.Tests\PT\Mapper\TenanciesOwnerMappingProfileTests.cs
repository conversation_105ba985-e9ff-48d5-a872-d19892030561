using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.PT.Mapper
{
    public class TenanciesOwnerMappingProfileTests
    {
        private readonly IMapper _mapper;

        public TenanciesOwnerMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<TenanciesOwnerMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_TenanciesOwnerResponse_To_TenanciesOwner()
        {
            // Arrange
            var ownershipResponse = new ManagementResponse
            {
                AgencyId = "AGENCY123",
                ManagementContactName = "John Smith",
                ManagementContactNumber = "0400123456",
                ManagementContactEmail = "<EMAIL>",
                OwnershipName = "Primary Owner",
                AuthorityStartDate = new DateTime(2025, 1, 1),
                AuthorityEndDate = new DateTime(2026, 12, 31)
            };

            var source = new TenanciesOwnerResponse
            {
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001",
                TenancyName = "Test Tenancy",
                IsActive = "Active",
                TenancyStartDate = new DateTime(2025, 1, 1),
                TenancyEndDate = new DateTime(2026, 1, 1),
                Rent = 2500.00m,
                ownershipResponse = ownershipResponse
            };

            // Act
            var result = _mapper.Map<TenanciesOwner>(source);

            // Assert
            Assert.NotNull(result);
            
            // Check main tenancy details
            Assert.Equal(source.ManagementId, result.SRCManagementId);
            Assert.Equal(source.PropertyId, result.SRCPropertyId);
            Assert.Equal(-1, result.PropertyId);
            Assert.Equal(source.TenancyId, result.SRCTenancyId);
            Assert.Equal(source.TenancyName, result.TenancyName);
            Assert.True(result.IsActive); // Because IsActive is "Active"
            Assert.Equal(source.TenancyStartDate, result.LeaseStart);
            Assert.Equal(source.TenancyEndDate, result.LeaseEnd);
            Assert.Equal(source.Rent, result.Rent);

            // Check property manager details
            Assert.NotNull(result.TenanciesPropertyManagerDetails);
            Assert.Equal(source.ownershipResponse.AgencyId, result.TenanciesPropertyManagerDetails.SRCAgencyId);
            Assert.Equal(source.ManagementId, result.TenanciesPropertyManagerDetails.SRCManagementId);
            Assert.Equal(source.PropertyId, result.TenanciesPropertyManagerDetails.SRCPropertyId);
            Assert.Equal(-1, result.TenanciesPropertyManagerDetails.PropertyId);
            Assert.Equal(source.ownershipResponse.ManagementContactName, result.TenanciesPropertyManagerDetails.PropertyManagerName);
            Assert.Equal(source.ownershipResponse.ManagementContactNumber, result.TenanciesPropertyManagerDetails.PropertyManagerMobile);
            Assert.Equal(source.ownershipResponse.ManagementContactEmail, result.TenanciesPropertyManagerDetails.PropertyManagerEmail);
            Assert.Equal(source.ownershipResponse.OwnershipName, result.TenanciesPropertyManagerDetails.Ownership);
            Assert.Equal(source.ownershipResponse.AuthorityStartDate, result.TenanciesPropertyManagerDetails.AuthorityStartDate);
            Assert.Equal(source.ownershipResponse.AuthorityEndDate, result.TenanciesPropertyManagerDetails.AuthorityEndDate);
        }

        [Theory]
        [InlineData("Active", true)]
        [InlineData("active", true)]
        [InlineData("ACTIVE", true)]
        [InlineData("Inactive", false)]
        [InlineData("Pending", false)]
        [InlineData(null, false)]
        public void Should_Map_IsActive_Status_Correctly(string isActive, bool expectedIsActive)
        {
            // Arrange
            var source = new TenanciesOwnerResponse
            {
                IsActive = isActive,
                // Set minimum required properties
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001",
                ownershipResponse = new ManagementResponse()
            };

            // Act
            var result = _mapper.Map<TenanciesOwner>(source);

            // Assert
            Assert.Equal(expectedIsActive, result.IsActive);
        }

        [Fact]
        public void Should_Handle_Null_OwnershipResponse()
        {
            // Arrange
            var source = new TenanciesOwnerResponse
            {
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001",
                TenancyName = "Test Tenancy",
                IsActive = "Active",
                ownershipResponse = null
            };

            // Act
            var result = _mapper.Map<TenanciesOwner>(source);

            // Assert
            Assert.NotNull(result);
            Assert.NotNull(result.TenanciesPropertyManagerDetails);
            Assert.Null(result.TenanciesPropertyManagerDetails.PropertyManagerName);
            Assert.Null(result.TenanciesPropertyManagerDetails.PropertyManagerMobile);
            Assert.Null(result.TenanciesPropertyManagerDetails.PropertyManagerEmail);
            Assert.Null(result.TenanciesPropertyManagerDetails.Ownership);
            Assert.Equal(default, result.TenanciesPropertyManagerDetails.AuthorityStartDate);
            Assert.Equal(default, result.TenanciesPropertyManagerDetails.AuthorityEndDate);
        }

        [Fact]
        public void Should_Handle_Null_Dates()
        {
            // Arrange
            var source = new TenanciesOwnerResponse
            {
                ManagementId = "OWN456",
                PropertyId = "PROP789",
                TenancyId = "TEN001",
                TenancyStartDate = null,
                TenancyEndDate = null,
                ownershipResponse = new ManagementResponse
                {
                    AuthorityStartDate = null,
                    AuthorityEndDate = null
                }
            };

            // Act
            var result = _mapper.Map<TenanciesOwner>(source);

            // Assert
            Assert.Equal(default, result.LeaseStart);
            Assert.Equal(default, result.LeaseEnd);
            Assert.Equal(default, result.TenanciesPropertyManagerDetails.AuthorityStartDate);
            Assert.Equal(default, result.TenanciesPropertyManagerDetails.AuthorityEndDate);
        }

        [Fact]
        public void Should_Handle_Reverse_Mapping()
        {
            // Arrange
            var source = new TenanciesOwner
            {
                SRCAgencyId = "AGENCY123",
                SRCManagementId = "OWN456",
                SRCPropertyId = "PROP789",
                PropertyId = -1,
                SRCTenancyId = "TEN001",
                TenancyName = "Test Tenancy",
                IsActive = true,
                LeaseStart = new DateTime(2025, 1, 1),
                LeaseEnd = new DateTime(2026, 1, 1),
                Rent = 2500.00m,
                TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails
                {
                    PropertyManagerName = "John Smith",
                    PropertyManagerMobile = "0400123456",
                    PropertyManagerEmail = "<EMAIL>",
                    Ownership = "Primary Owner",
                    AuthorityStartDate = new DateTime(2025, 1, 1),
                    AuthorityEndDate = new DateTime(2026, 12, 31)
                }
            };

            // Act
            var result = _mapper.Map<TenanciesOwnerResponse>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(null!, result.ManagementId);
            Assert.Equal(null!, result.PropertyId);
            Assert.Equal(source.SRCTenancyId, result.TenancyId);
            Assert.Equal(source.TenancyName, result.TenancyName);
            Assert.Equal("True", result.IsActive);
            Assert.Equal(source.LeaseStart, result.TenancyStartDate);
            Assert.Equal(source.LeaseEnd, result.TenancyEndDate);
            Assert.Equal(source.Rent, result.Rent);

            // Check ownership response
            Assert.NotNull(result.ownershipResponse);
            Assert.Equal(source.TenanciesPropertyManagerDetails.PropertyManagerName, result.ownershipResponse.ManagementContactName);
            Assert.Equal(source.TenanciesPropertyManagerDetails.PropertyManagerMobile, result.ownershipResponse.ManagementContactNumber);
            Assert.Equal(source.TenanciesPropertyManagerDetails.PropertyManagerEmail, result.ownershipResponse.ManagementContactEmail);
            Assert.Equal(source.TenanciesPropertyManagerDetails.Ownership, result.ownershipResponse.OwnershipName);
            Assert.Equal(source.TenanciesPropertyManagerDetails.AuthorityStartDate, result.ownershipResponse.AuthorityStartDate);
            Assert.Equal(source.TenanciesPropertyManagerDetails.AuthorityEndDate, result.ownershipResponse.AuthorityEndDate);
        }
    }
}
