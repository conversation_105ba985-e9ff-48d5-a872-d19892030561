﻿using MRI.Integration.SDK.Shared.Enums;

namespace MRI.OTA.EventHub.Consumer
{
    public interface IConsumerService
    {
        Task ConnectAndHandle(CancellationToken cancellationToken);

        Task GetDeadLetteredEvents(string startDate, string endDate, string eventType, string tenant,
            string tenantEnvironment, DeadLetterSortOptions sortBy, int pageNumber, int itemsPerPage);

        Task AcknowledgeDeadLetteredEvent(Guid eventId, string eventSource);

        Task ResubmitDeadLetteredEvent(Guid eventId, string eventSource);
    }
}
