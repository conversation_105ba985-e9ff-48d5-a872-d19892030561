﻿using MRI.Integration.Consumer.SDK.Models;

namespace MRI.OTA.EventHub.Consumer
{
    public class ConsumerConfig
    {
        public int ProtocolVersion { get; set; }
        public bool CancellationTokenEnabled { get; set; }
        public int CancellationTokenTimeSpanInSeconds { get; set; }
        public string? ApiBaseUrl { get; set; }
        public string? ConsumerId { get; set; }
        public string? TokenEndpointUrl { get; set; }
        public string? ClientId { get; set; }
        public string? ClientSecret { get; set; }
        public string? CommandEventSource { get; set; }
        public int WaitTimeForAbandoningEvents { get; set; }
        public Severity MinimumSeverityToTriggerFaultDelegate { get; set; }
    }
}
