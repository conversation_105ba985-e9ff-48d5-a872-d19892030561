﻿using Azure;
using Azure.Communication.Email;

namespace MRI.OTA.Email
{
    public class AzureCommunicationEmailService : IEmailService
    {
        private readonly ILogger<AzureCommunicationEmailService> _logger;
        private readonly IConfiguration _configuration;
        private readonly EmailClient _client;

        public AzureCommunicationEmailService(ILogger<AzureCommunicationEmailService> logger, IConfiguration configuration)
        {
            _logger = logger;
            _configuration = configuration;
            _client = new EmailClient(_configuration.GetValue<string>("AzureCommunicationSettings:ConnectionString"));
        }

        public async Task<bool> SendEmailAsync(EmailMessage message)
        {
            try
            {
                // Get sender address from message or configuration    
                string senderAddress = message.FromAddress?.Email ??
                    _configuration.GetValue<string>("AzureCommunicationSettings:DefaultFromEmail");

                // Convert to addresses to Azure EmailAddress objects    
                var toAddresses = message.ToAddresses?.Select(to =>
                    new Azure.Communication.Email.EmailAddress(to.Email)).ToList()
                    ?? new List<Azure.Communication.Email.EmailAddress>();

                var ccAddresses = message.CcAddresses?.Select(cc =>
                    new Azure.Communication.Email.EmailAddress(cc.Email)).ToList()
                    ?? new List<Azure.Communication.Email.EmailAddress>();

                var bccAddresses = message.BccAddresses?.Select(bcc =>
                    new Azure.Communication.Email.EmailAddress(bcc.Email)).ToList()
                    ?? new List<Azure.Communication.Email.EmailAddress>();

                // Create email content    
                var emailContent = new Azure.Communication.Email.EmailContent(message.Subject)
                {
                    PlainText = message.PlainTextContent,
                    Html = message.HtmlContent
                };

                // Create email recipients    
                var emailRecipients = new Azure.Communication.Email.EmailRecipients(toAddresses);

                // Add CC and BCC if they exist    
                if (ccAddresses.Any() || bccAddresses.Any())
                {
                    emailRecipients = new Azure.Communication.Email.EmailRecipients(
                        toAddresses,
                        ccAddresses,
                        bccAddresses
                    );
                }

                // Create email message with all required properties    
                var azureEmailMessage = new Azure.Communication.Email.EmailMessage(
                    senderAddress,
                    emailRecipients,
                    emailContent
                );

                // Add attachments if any    
                if (message.Attachments?.Any() == true)
                {
                    foreach (var attachment in message.Attachments)
                    {
                        azureEmailMessage.Attachments.Add(
                            new Azure.Communication.Email.EmailAttachment(
                                attachment.FileName,
                                attachment.ContentType,
                                new BinaryData(attachment.Content)
                            ));
                    }
                }

                // Send the email    
                var response = await _client.SendAsync(WaitUntil.Completed, azureEmailMessage);

                _logger.LogInformation($"Email sent successfully. Message ID: {response.Value}");
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to send email using Azure Communication Services: {ErrorMessage}", ex.Message);
                return false;
            }
        }
    }
}
