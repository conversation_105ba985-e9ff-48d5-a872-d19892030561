﻿using MRI.OTA.Integration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using MRI.OTA.Integration.Http;
using System;
using System.Collections.Generic;
using System.Net.Http;
using Xunit;
using Microsoft.AspNetCore.Hosting;
using Microsoft.AspNetCore.TestHost;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Xunit.Abstractions;
using Microsoft.VisualStudio.TestPlatform.Utilities;

namespace MRI.OTA.Tests
{
    public class HttpClientFactoryManagerTests
    {
        private readonly Mock<IHttpClientFactory> _httpClientFactoryMock;
        private readonly HttpClientFactoryManager _httpClientFactoryManager;
        private readonly HttpClient _httpClient;

        public HttpClientFactoryManagerTests()
        {
            _httpClientFactoryMock = new Mock<IHttpClientFactory>();

            // Create a single HttpClient instance to simulate pooling
            _httpClient = new HttpClient { BaseAddress = new Uri("https://example.com") };

            _httpClientFactoryMock
                .Setup(factory => factory.CreateClient("PooledHttpClient"))
                .Returns(_httpClient);

            _httpClientFactoryManager = new HttpClientFactoryManager(_httpClientFactoryMock.Object);
        }

        [Fact]
        public void GetHttpClient_ShouldReturnHttpClientInstance()
        {
            // Act
            var client = _httpClientFactoryManager.GetHttpClient();

            // Assert
            Assert.NotNull(client);
            Assert.IsType<HttpClient>(client);
            Assert.Equal("https://example.com/", client.BaseAddress?.ToString());
        }

        [Fact]
        public void GetHttpClient_ShouldReturnSameInstanceForPooling()
        {
            // Act
            var client1 = _httpClientFactoryManager.GetHttpClient();
            var client2 = _httpClientFactoryManager.GetHttpClient();

            // Assert
            Assert.Same(client1, client2); // Ensuring the same instance is returned
        }

        [Fact]
        public void GetHttpClient_ShouldCallHttpClientFactory()
        {
            // Act
            var client = _httpClientFactoryManager.GetHttpClient();

            // Assert
            _httpClientFactoryMock.Verify(factory => factory.CreateClient("PooledHttpClient"), Times.Once);
        }
    }
}
