﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFramework>net8.0</TargetFramework>
	</PropertyGroup>

	<ItemGroup>
		<!-- Core ASP.NET and Dependency Injection -->
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" />
		<PackageReference Include="Serilog"  />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" />
		<PackageReference Include="Serilog.Extensions.Hosting" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" />
		<PackageReference Include="Microsoft.Extensions.Http" />
	</ItemGroup>

</Project>