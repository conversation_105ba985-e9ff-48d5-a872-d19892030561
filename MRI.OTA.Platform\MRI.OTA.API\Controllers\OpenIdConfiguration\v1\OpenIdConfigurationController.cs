using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;
using System.Text.Json;

namespace MRI.OTA.API.Controllers.OpenIdConfiguration.v1
{
    /// <summary>
    /// Controller to handle OpenID configuration retrieval
    /// </summary>
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/openid-configuration")]
    [ApiController]
    [AllowAnonymous]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class OpenIdConfigurationController : ControllerBase
    {
        private readonly HttpClient _httpClient;
        private readonly ILogger<OpenIdConfigurationController> _logger;
        private readonly IConfiguration _configuration;

        /// <summary>
        /// Constructor for OpenIdConfigurationController
        /// </summary>
        /// <param name="httpClient">HTTP client for making external requests</param>
        /// <param name="logger">Logger</param>
        /// <param name="configuration">Configuration</param>
        public OpenIdConfigurationController(
            HttpClient httpClient,
            ILogger<OpenIdConfigurationController> logger,
            IConfiguration configuration)
        {
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        /// <summary>
        /// Get OpenID configuration from Azure B2C
        /// </summary>
        /// <returns>OpenID configuration response</returns>
        [HttpGet]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(object), description: "OpenID configuration retrieved successfully")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized - Valid token required")]
        [SwaggerResponse(statusCode: StatusCodes.Status500InternalServerError, type: typeof(ProblemDetailsModel), description: "Internal Server Error")]
        public async Task<ActionResult<ApiResponse<object>>> GetOpenIdConfiguration()
        {
            try
            {
                var tenantName = _configuration["Authentication:TenantName"];
                var tenantId = _configuration["Authentication:TenantId"];
                var policyId = _configuration["Authentication:SignUpSignInPolicyId"];

                var openIdConfigurationUrl = $"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/v2.0/.well-known/openid-configuration?p={policyId}";

                _logger.LogInformation("Fetching OpenID configuration from: {Url}", openIdConfigurationUrl);

                var response = await _httpClient.GetAsync(openIdConfigurationUrl);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    var configurationData = JsonSerializer.Deserialize<object>(content);

                    _logger.LogInformation("OpenID configuration retrieved successfully");

                    return Ok(configurationData);
                }
                else
                {
                    _logger.LogError("Failed to retrieve OpenID configuration. Status code: {StatusCode}", response.StatusCode);
                    
                    return StatusCode(StatusCodes.Status500InternalServerError, "Failed to retrieve OpenID configuration" );
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError(ex, "HTTP request exception while fetching OpenID configuration");
                
                return StatusCode(StatusCodes.Status500InternalServerError,"Network error while retrieving OpenID configuration");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Unexpected error while fetching OpenID configuration");
                
                return StatusCode(StatusCodes.Status500InternalServerError,"Unexpected error while retrieving OpenID configuration");
            }
        }
    }
} 