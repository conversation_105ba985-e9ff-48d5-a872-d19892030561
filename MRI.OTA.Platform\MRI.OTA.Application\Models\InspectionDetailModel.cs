namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// Application model for Inspection Detail
    /// </summary>
    public class InspectionDetailModel
    {
        /// <summary>
        /// Inspection Detail ID
        /// </summary>
        public int InspectionsDetailId { get; set; }
        public string? SRCManagementId { get; set; }

        /// <summary>
        /// Source Tenancy ID
        /// </summary>
        public string? SRCTenancyId { get; set; }

        /// <summary>
        /// Source Property ID
        /// </summary>
        public string? SRCPropertyId { get; set; }

        /// <summary>
        /// Property ID
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// Source Inspection ID
        /// </summary>
        public string? SRCInspectionId { get; set; }

        /// <summary>
        /// Inspection Status
        /// </summary>
        public string? InspectionStatus { get; set; }

        /// <summary>
        /// Inspection Date and Time
        /// </summary>
        public DateTime InspectionDate { get; set; }
        public DateTime InspectionStartTime { get; set; }
        public DateTime InspectionEndTime { get; set; }

        /// <summary>
        /// Inspection Summary
        /// </summary>
        public string? Summary { get; set; }
    }
} 