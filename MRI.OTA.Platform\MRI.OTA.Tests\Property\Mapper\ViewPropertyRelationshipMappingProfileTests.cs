﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models.Master;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.UnitTestCases.Property.Mapper
{
    public class ViewPropertyRelationshipMappingProfileTests
    {
        private readonly IMapper _mapper;

        public ViewPropertyRelationshipMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<ViewPropertyRelationshipMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_PropertyRelationship_To_PropertyRelationshipsModel()
        {
            // Arrange  
            var propertyRelationship = new PropertyRelationship
            {
                PropertyRelationshipId = 1,
                PropertyRelationshipName = "Test Relationship"
            };

            // Act  
            var model = _mapper.Map<PropertyRelationshipsModel>(propertyRelationship);

            // Assert  
            Assert.NotNull(model);
            Assert.Equal(propertyRelationship.PropertyRelationshipId, model.PropertyRelationshipId);
            Assert.Equal(propertyRelationship.PropertyRelationshipName, model.PropertyRelationshipName);
        }

        [Fact]
        public void Should_Map_PropertyRelationshipsModel_To_PropertyRelationship()
        {
            // Arrange  
            var model = new PropertyRelationshipsModel
            {
                PropertyRelationshipId = 1,
                PropertyRelationshipName = "Test Relationship"
            };

            // Act  
            var propertyRelationship = _mapper.Map<PropertyRelationship>(model);

            // Assert  
            Assert.NotNull(propertyRelationship);
            Assert.Equal(model.PropertyRelationshipId, propertyRelationship.PropertyRelationshipId);
            Assert.Equal(model.PropertyRelationshipName, propertyRelationship.PropertyRelationshipName);
        }
    }
}
