{
    "Serilog": {
        "Using": [ "Serilog.Sinks.File", "Serilog.Sinks.Async", "Serilog.Sinks.Console" ],
        "MinimumLevel": {
            "Default": "Debug",
            "Override": {
                "Microsoft": "Error",
                "System": "Error"
            }
        },
        "WriteTo": [
            {
                "Name": "Async",
                "Args": {
                    "configure": [
                        {
                            "Name": "File",
                            "Args": {
                                "path": "Logs/log.txt",
                                "rollingInterval": "Day"
                            }
                        },
                        {
                            "Name": "Console",
                            "Args": {
                                "theme": "Serilog.Sinks.SystemConsole.Themes.AnsiConsoleTheme::Code, Serilog.Sinks.Console",
                                "outputTemplate": "[{Timestamp:HH:mm:ss} {Level:u3}] {Message:lj} <s:{SourceContext}>{NewLine}{Exception}"
                            }
                        }
                    ]
                }
            }
        ],
        "Enrich": [ "FromLogContext", "WithMachineName", "WithThreadId" ],
        "Properties": {
            "Application": "MRI.OTA.EventHub.Consumer"
        }
    },
    "Application": {
        "OtaBaseUrl": "https://localhost:7009"
    },
    "ConsumerConfig": {
        // a45bc38f-f27e-40ea-b187-311cd8707e09 
        "ConsumerID": "58749e35-5aae-4629-9746-4c3e4224103a",
        "ClientID": "0oa1as6mc46IyMduB0h8",
        "ClientSecret": "k1qthFpAz4yD9nTw-zzvdOTtkgLc93ZFugbI14i4",
        "ApiBaseUrl": "wss://sandbox-integrationhub.integrationservices.mrisoftware.com/consumer/api/consumers",
        "TokenEndpointUrl": "https://mrisaas.oktapreview.com/oauth2/aus11uel8uuRWh9ZR0h8/v1/token",
        "CommandEventSource": "/JavaSDKsampleproduct/OnLocDev1/sandbox", // this is only needed if using the command functionaility
        "CancellationTokenEnabled": false, // set whether a cancelation token should be passed when connecting to the websocket through the SDK (used by this sample app)
        "CancellationTokenTimeSpanInSeconds": 30, // set a cancelation period for the cancelation token (used by this sample app)
        // the following config settings are optional, if not set default values are used in the SDK / Consumer API. In normal circumstances default values would be used.
        "ProtocolVersion": 1, // consumer API protocol to use
        "WaitTimeForAbandoningEventsInSeconds": 30, // sets the amount of time that the consumer API will wait for an acknowledgement / defer / deadletter action before automatically abandoning the event and resending to the consumer
        "MinimumSeverityToTriggerFaultDelegate": "Information" // sets the minimum serverity level before the fault delegate fires. Useful when only interested in error faults. Possible values: Information, Warning, Error, Fatal
    }
}