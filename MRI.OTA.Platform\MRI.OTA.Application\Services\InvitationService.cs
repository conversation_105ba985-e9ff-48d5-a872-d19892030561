﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Application.Models.Integration;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Helper;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Email;
using MRI.OTA.Integration.Services;
using System.Security;
using static MRI.OTA.Common.Constants.Constants;

namespace MRI.OTA.Application.Services
{
    public class InvitationService : BaseService<UserInvites, InvitationRequestModel, int>, IInvitationService
    {
        private readonly ILogger<InvitationService> _logger;
        private readonly IConfiguration _configuration;
        private IInvitationRepository _invitationRepository { get; set; }
        private IDataSourceRepository _dataSourceRepository { get; set; }
        private IMapper _mapper { get; set; }
        private readonly IEmailServiceFactory _emailServiceFactory;
        private readonly TaskContext _taskContext;
        private readonly IProxyService _proxyService;
        private readonly IPropertTreeService _ptService;
        private readonly IAPITrackingService _apiTrackingService;

        public InvitationService(ILogger<InvitationService> logger,
            IInvitationRepository repository,
            IMapper mapper,
            IConfiguration configuration,
            IEmailServiceFactory emailServiceFactory,
            IDataSourceRepository dataSourceRepository,
            TaskContext taskContext,
            IProxyService proxyService,
            IPropertTreeService ptService,
            IAPITrackingService apiTrackingService)
            : base(logger, repository, mapper)
        {
            _logger = logger;
            _configuration = configuration;
            _mapper = mapper;
            _invitationRepository = repository;
            _emailServiceFactory = emailServiceFactory;
            _dataSourceRepository = dataSourceRepository;
            _taskContext = taskContext;
            _proxyService = proxyService;
            _ptService = ptService;
            _apiTrackingService = apiTrackingService;
        }

        /// <summary>
        /// Invite user
        /// </summary>
        /// <param name="invitationRequest"></param>
        /// <returns></returns>
        public async Task<bool> SendInvitationAsync(InvitationRequestModel invitationRequest)
        {
            try
            {
                // Check if invitation already exists for this PortfolioId and UserEmail
                var existingInvitation = await _invitationRepository.GetInvitationByPortfolioIdAndEmail(
                    invitationRequest.PortfolioId,
                    invitationRequest.UserEmail);

                UserInvites invitation;
                if (existingInvitation != null)
                {
                    // Use existing invitation
                    _logger.LogInformation($"Found existing invitation for PortfolioId: {invitationRequest.PortfolioId} and UserEmail: {invitationRequest.UserEmail}");
                    invitation = existingInvitation;
                }
                else
                {
                    // Create new invitation
                    _logger.LogInformation($"Creating new invitation for PortfolioId: {invitationRequest.PortfolioId} and UserEmail: {invitationRequest.UserEmail}");
                    invitation = await CreateInvitation(invitationRequest);
                    // Save new invitation to database
                    await _invitationRepository.AddAsync(invitation);
                }

                // Fire-and-forget: send email in the background
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await SendInvitationEmail(invitationRequest, invitation.InviteLink, invitation.InviteCode);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error sending invitation email in background: {ex.Message}");
                    }
                });

                // Return immediately after DB operations
                return true;
            }
            catch (UnauthorizedAccessException ex)
            {
                _logger.LogError($"Unauthorized access: {ex.Message}");
                throw; // Let the controller handle the 401 response
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error sending invitation: {ex.Message}");
                return false;
            }
        }

        private async Task<UserInvites> CreateInvitation(InvitationRequestModel invitationRequest)
        {
            var inviteCode = Utilities.GenerateRandomString(8);
            var baseUrl = _configuration["ApplicationOption:BaseUrl"];
            var invitationLink = $"{baseUrl}invite?code={inviteCode}";

            var invitation = _mapper.Map<UserInvites>(invitationRequest);
            invitation.InviteCode = inviteCode;
            invitation.InviteLink = invitationLink;
            invitation.IsActive = true;
            var result = await _dataSourceRepository.GetUserDataSource(_taskContext.AccessKey);
            if(result != null)
            {
                invitation.DataSourceId = result.DataSourceId;
            }
            else
            {
                _logger.LogError($"Failed to retrieve user data source for access key: {_taskContext.AccessKey}");
                throw new UnauthorizedAccessException("Invalid access key. Authorization failed.");
            }

            return invitation;
        }

        private async Task<bool> SendInvitationEmail(InvitationRequestModel invitationRequest, string invitationLink, string inviteCode)
        {
            // Get email service
            IEmailService emailService;
            try {
                emailService = _emailServiceFactory.GetEmailService(1, 1);
            } catch (Exception ex) {
                _logger.LogError($"Failed to initialize email service: {ex.Message}");
                throw new ApplicationException("Email service configuration error", ex);
            }

            // Create email message
            var message = CreateEmailMessage(invitationRequest, invitationLink, inviteCode);

            // Send email
            return await emailService.SendEmailAsync(message);
        }

        private EmailMessage CreateEmailMessage(InvitationRequestModel invitationRequest, string invitationLink, string inviteCode)
        {
            var message = new EmailMessage
            {
                Subject = "You're Invited To MyPlace!",
                HtmlContent = GetHtmlContentFromTemplateAsync(invitationRequest, invitationLink, inviteCode).Result,
            };

            message.ToAddresses.Add(new EmailAddress(invitationRequest.UserEmail));

            return message;
        }

        public async Task<(int, List<UserProperties>)> AcceptInvitationAsync(AcceptInvitationModel acceptInvitation)
        {
            // Status codes:
            //  1  = Success
            // -1  = ProcessInvitationAcceptanceAsync failed
            // -2  = Invalid invitation
            // -3  = Invalid data source
            //  0  = Exception
            try
            {
                // Step 1: Validate invitation
                var invitationDetails = await GetAndValidateInvitationAsync(acceptInvitation.InviteCode);
                if (invitationDetails == null)
                {
                    _logger.LogWarning($"Invalid invitation code: {acceptInvitation.InviteCode}");
                    return (-2, null!);
                }

                // Step 2: Get and validate data source
                var dataSource = await GetAndValidateDataSourceAsync(invitationDetails.DataSourceId.GetValueOrDefault());
                if (dataSource == null)
                {
                    _logger.LogWarning($"Invalid data source for invitation code: {acceptInvitation.InviteCode}");
                    return (-3, null!);
                }

                // Step 3: Process invitation acceptance
                var (isSuccess, userProperties) = await ProcessInvitationAcceptanceAsync(invitationDetails, dataSource, acceptInvitation);
                if (!isSuccess)
                {
                    _logger.LogWarning($"Failed to process invitation acceptance for code: {acceptInvitation.InviteCode}");
                    return (-1, userProperties);
                }

                // Step 4: Fire-and-forget: send completion mail to user
                _ = Task.Run(async () =>
                {
                    try
                    {
                        await SendAcceptanceEmailNotification(invitationDetails, userProperties, acceptInvitation);
                    }
                    catch (Exception ex)
                    {
                        _logger.LogError($"Error sending accept email (fire-and-forget): {ex.Message}");
                    }
                });

                // Step 5: Update user login details in userInvites table
                await _invitationRepository.UpdateUserInvites(acceptInvitation.InviteCode, acceptInvitation.ProviderId, acceptInvitation.UserEmail, false);

                return (1, userProperties);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, $"Error accepting invitation for code: {acceptInvitation.InviteCode}");
                return (0, null!);
            }
        }

        private async Task<ViewUserInvites> GetAndValidateInvitationAsync(string? inviteCode)
        {
            var invitationDetails = await _invitationRepository.GetInvitationDetailsById(inviteCode);

            if (invitationDetails == null)
            {
                _logger.LogError($"Invitation not found for code: {inviteCode}");
                return null;
            }
            return invitationDetails;
        }

        private async Task<DataSource> GetAndValidateDataSourceAsync(int dataSourceId)
        {
            var dataSource = await _dataSourceRepository.GetByIdAsync(dataSourceId, "DataSourceId");

            if (dataSource == null)
            {
                _logger.LogError($"DataSource not found for ID: {dataSourceId}");
            }

            return dataSource;
        }

        private async Task<(bool, List<UserProperties>)> ProcessInvitationAcceptanceAsync(ViewUserInvites invitation, DataSource dataSource, AcceptInvitationModel acceptInvitation)
        {
            var addedProperties = new List<UserProperties>();
            int apiStep = 0;
            try
            {
                var manifest = DataSourceManifest.FromJson(dataSource.ManifestJson);
                string[] userId = [acceptInvitation.ProviderId!];

                // Step 1: Associate Portfolio
                apiStep = (int)Constants.APIDetail.GetAssociatePortfolio;
                var proxyRequest = _ptService.CreateAssociatePortfolioProxyRequest(manifest, invitation.PortfolioId, acceptInvitation.ProviderId, acceptInvitation.ProviderType);
                var httpResponse = await _proxyService.ForwardRequestAsync(
                    proxyRequest,
                    $"AccessKey {manifest.ApiKey}", "AccessKey");

                if (httpResponse is ObjectResult result && result.StatusCode == 200)
                {
                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = acceptInvitation.InviteCode!,
                        UserId = (int)acceptInvitation.UserId!,
                        IsCompleted = true,
                        ErrorInfo = ""
                    });

                    _logger.LogInformation("Successfully received a success response.");
                    // Step 2: Fetch owner and tenant properties in parallel
                    apiStep = (int)Constants.APIDetail.GetProperties;
                    var getPropsOwnerRequest = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.OwnerPropertyList, userId);
                    var getPropsTenantRequest = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.TenantPropertyList, userId);

                    var ownerTask = _proxyService.ForwardRequestAsync(getPropsOwnerRequest, $"AccessKey {manifest.ApiKey}", "AccessKey");
                    var tenantTask = _proxyService.ForwardRequestAsync(getPropsTenantRequest, $"AccessKey {manifest.ApiKey}", "AccessKey");

                    await Task.WhenAll(ownerTask, tenantTask);
                    var httpResponseGetPropertiesOwner = ownerTask.Result;
                    var httpResponseGetPropertiesTenant = tenantTask.Result;

                    bool ownerOk = httpResponseGetPropertiesOwner is ObjectResult resultGetPropsOwner && resultGetPropsOwner.StatusCode == 200;
                    bool tenantOk = httpResponseGetPropertiesTenant is ObjectResult resultGetPropsTenant && resultGetPropsTenant.StatusCode == 200;

                    if (ownerOk || tenantOk)
                    {
                        _logger.LogInformation("Successfully received a success response for properties.");
                        // Process both property responses in parallel
                        var processOwnerTask = ownerOk ? _ptService.ProcessGetPropertiesProxyResponse(httpResponseGetPropertiesOwner, dataSource, acceptInvitation) : Task.FromResult<(bool, List<UserProperties>)>((false, null!));
                        var processTenantTask = tenantOk ? _ptService.ProcessGetPropertiesProxyResponse(httpResponseGetPropertiesTenant, dataSource, acceptInvitation) : Task.FromResult<(bool, List<UserProperties>)>((false, null!));
                        await Task.WhenAll(processOwnerTask, processTenantTask);
                        var (success, propertiesOwner) = processOwnerTask.Result;
                        var (successTenant, propertiesTenant) = processTenantTask.Result;
                        var properties = new List<UserProperties>();
                        if (propertiesOwner != null && propertiesOwner.Any())
                            properties.AddRange(propertiesOwner);
                        if (propertiesTenant != null && propertiesTenant.Any())
                            properties.AddRange(propertiesTenant);
                        if (properties.Any())
                        {
                            await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                            {
                                APIDetailId = apiStep,
                                InviteCode = acceptInvitation.InviteCode!,
                                UserId = (int)acceptInvitation.UserId!,
                                IsCompleted = true,
                                ErrorInfo = ""
                            });
                            addedProperties.AddRange(properties);

                            // Fire-and-forget: fetch additional property data in parallel
                            _ = Task.Run(async () =>
                            {
                                var tenancyIds = addedProperties.Select(x => x.SRCTenancyId).ToArray();
                                var managementIds = addedProperties.Select(x => x.SRCManagementId).ToArray();

                                // Prepare all requests
                                var tenanciesTenantReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.TenanciesTenantList, null, null, tenancyIds);
                                var managementReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.ManagementList, null, managementIds);
                                var tenanciesOwnerReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.TenanciesOwnerList, null, null, tenancyIds);
                                var maintenanceReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.MaintenanceList, null, managementIds, tenancyIds);
                                var inspectionReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.InspectionList, null, managementIds, tenancyIds);
                                var complianceReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.ComplianceList, null, managementIds);
                                var financialReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.FinancialList, null, managementIds);
                                var documentReq = _ptService.CreateProxyRequest(manifest, IntegrationEndPointsType.DocumentList, null, managementIds, tenancyIds);

                                // Fire all requests in parallel
                                var tasks = new[]
                                {
                                    _proxyService.ForwardRequestAsync(managementReq, $"AccessKey {manifest.ApiKey}", "AccessKey"),
                                    _proxyService.ForwardRequestAsync(tenanciesTenantReq, $"AccessKey {manifest.ApiKey}", "AccessKey"),
                                    _proxyService.ForwardRequestAsync(tenanciesOwnerReq, $"AccessKey {manifest.ApiKey}", "AccessKey"),
                                    _proxyService.ForwardRequestAsync(maintenanceReq, $"AccessKey {manifest.ApiKey}", "AccessKey"),
                                    _proxyService.ForwardRequestAsync(inspectionReq, $"AccessKey {manifest.ApiKey}", "AccessKey"),
                                    _proxyService.ForwardRequestAsync(complianceReq, $"AccessKey {manifest.ApiKey}", "AccessKey"),
                                    _proxyService.ForwardRequestAsync(financialReq, $"AccessKey {manifest.ApiKey}", "AccessKey"),
                                    _proxyService.ForwardRequestAsync(documentReq, $"AccessKey {manifest.ApiKey}", "AccessKey")
                                };
                                await Task.WhenAll(tasks);

                                var responseBundle = new PropertyTreeResponseBundle(
                                    tasks[0].Result, // management
                                    tasks[1].Result, // tenanciesTenant
                                    tasks[2].Result, // tenanciesOwner
                                    tasks[3].Result, // maintenance
                                    tasks[4].Result, // inspection
                                    tasks[5].Result, // compliance
                                    tasks[6].Result, // financial
                                    tasks[7].Result  // document
                                );
                                await _ptService.ProcessGetPropertiesOtherDataResponse(responseBundle, dataSource, properties, acceptInvitation.InviteCode);
                            });

                            return (success, addedProperties);
                        }
                        else
                        {
                            await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                            {
                                APIDetailId = apiStep,
                                InviteCode = acceptInvitation.InviteCode!,
                                UserId = (int)acceptInvitation.UserId!,
                                IsCompleted = false,
                                ErrorInfo = $"Failed to receive a success response for owner-tenant properties. HTTP Status : {((ObjectResult)httpResponseGetPropertiesOwner)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesOwner)?.Value} OR {((ObjectResult)httpResponseGetPropertiesTenant)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesTenant)?.Value}"
                            });
                            _logger.LogError($"Failed to receive a success response for owner properties. HTTP Status: {((ObjectResult)httpResponseGetPropertiesOwner)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesOwner)?.Value}");
                            _logger.LogError($"Failed to receive a success response for tenant properties. HTTP Status: {((ObjectResult)httpResponseGetPropertiesTenant)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesTenant)?.Value}");
                            return (false, addedProperties);
                        }
                    }
                    else
                    {
                        await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                        {
                            APIDetailId = apiStep,
                            InviteCode = acceptInvitation.InviteCode!,
                            UserId = (int)acceptInvitation.UserId!,
                            IsCompleted = false,
                            ErrorInfo = $"Failed to receive a success response for owner-tenant properties. HTTP Status : {((ObjectResult)httpResponseGetPropertiesOwner)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesOwner)?.Value} OR {((ObjectResult)httpResponseGetPropertiesTenant)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesTenant)?.Value}"
                        });
                        _logger.LogError($"Failed to receive a success response for owner properties. HTTP Status: {((ObjectResult)httpResponseGetPropertiesOwner)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesOwner)?.Value}");
                        _logger.LogError($"Failed to receive a success response for tenant properties. HTTP Status: {((ObjectResult)httpResponseGetPropertiesTenant)?.StatusCode}, Response: {((ObjectResult)httpResponseGetPropertiesTenant)?.Value}");
                        return (false, addedProperties);
                    }
                }
                else
                {
                    await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                    {
                        APIDetailId = apiStep,
                        InviteCode = acceptInvitation.InviteCode!,
                        UserId = (int)acceptInvitation.UserId!,
                        IsCompleted = false,
                        ErrorInfo = $"Failed to receive a success response. HTTP Status: {((ObjectResult)httpResponse)?.StatusCode}, Response: {((ObjectResult)httpResponse)?.Value}"
                    });
                    _logger.LogError($"Failed to receive a success response. HTTP Status: {((ObjectResult)httpResponse)?.StatusCode}, Response: {((ObjectResult)httpResponse)?.Value}");
                    return (false, addedProperties);
                }
            }
            catch (Exception ex)
            {
                await _apiTrackingService.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = apiStep,
                    InviteCode = acceptInvitation.InviteCode!,
                    UserId = (int)acceptInvitation.UserId!,
                    IsCompleted = false,
                    ErrorInfo = ex.Message
                });
                _logger.LogError($"Error processing invitation acceptance: {ex.Message}");
                return (false, addedProperties);
            }
        }

        private async Task SendAcceptanceEmailNotification(ViewUserInvites acceptInvitation, List<UserProperties> addedProperties, AcceptInvitationModel acceptInvitationModel)
        {
            var message = new EmailMessage
            {
                Subject = "Invitation Accepted - MyPlace",
                HtmlContent = await GetAcceptanceHtmlContentFromTemplateAsync(acceptInvitation, acceptInvitationModel.UserEmail),
            };

            message.ToAddresses.Add(new EmailAddress(acceptInvitation.UserEmail));
            message.ToAddresses.Add(new EmailAddress(acceptInvitationModel.UserEmail));

            // Send the email using the email service
            var emailService = _emailServiceFactory.GetEmailService(1, 1);
            await emailService.SendEmailAsync(message);
        }

        private async Task<string> GetHtmlContentFromTemplateAsync(InvitationRequestModel invitationRequest, string invitationLink, string inviteCode)
        {
            try
            {
                var baseUrl = _configuration["ApplicationOption:BaseUrl"];
                // Define the relative path to the template file  
                var templatePath = string.Empty;
                if (invitationRequest.MigrateUser)
                {
                    templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates", "Migration_MyPlace.html");
                }
                else
                {
                    templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates", "Invitation_MyPlace.html");
                }

                // Read the HTML template file  
                var htmlTemplate = await File.ReadAllTextAsync(templatePath);

                var defaultAppLogo = _configuration["ApplicationOption:DefaultImageUrl"];

                // Replace placeholders with actual values  
                htmlTemplate = htmlTemplate.Replace("{AgencyName}", invitationRequest.AgencyName);
                htmlTemplate = htmlTemplate.Replace("{AgencyLogo}", invitationRequest.AgencyLogo);
                htmlTemplate = htmlTemplate.Replace("{DefaultAppLogo}", defaultAppLogo);
                htmlTemplate = htmlTemplate.Replace("{Name}", CapitalizeWords(invitationRequest.Name));
                htmlTemplate = htmlTemplate.Replace("{inviteCode}", inviteCode);
                htmlTemplate = htmlTemplate.Replace("{invitationLink}", invitationLink);
                htmlTemplate = htmlTemplate.Replace("{ota_web_url}", baseUrl);
                htmlTemplate = htmlTemplate.Replace("{AgencyColour}", invitationRequest.AgencyColour);

                return htmlTemplate;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading HTML template: {ex.Message}");
                throw;
            }
        }

        private async Task<string> GetAcceptanceHtmlContentFromTemplateAsync(ViewUserInvites acceptInvitation, string userEmail)
        {
            try
            {
                // Define the relative path to the template file  
                var templatePath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Templates", "InvitationAccepted_MyPlace.html");

                // Read the HTML template file  
                var htmlTemplate = await File.ReadAllTextAsync(templatePath);

                // Get agency logo with fallback
                var agencyLogo = acceptInvitation.AgencyLogo;

                var defaultAppLogo = _configuration["ApplicationOption:DefaultImageUrl"];


                // Replace placeholders with actual values  
                htmlTemplate = htmlTemplate.Replace("{AgencyName}", acceptInvitation.AgencyName ?? "Agency");
                htmlTemplate = htmlTemplate.Replace("{AgencyLogo}", agencyLogo);
                htmlTemplate = htmlTemplate.Replace("{DefaultAppLogo}", defaultAppLogo);
                htmlTemplate = htmlTemplate.Replace("{UserName}", CapitalizeWords(acceptInvitation.Name ?? "User"));
                htmlTemplate = htmlTemplate.Replace("{UserEmail}", userEmail);

                return htmlTemplate;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error reading HTML acceptance template: {ex.Message}");
                throw;
            }
        }

        private string CapitalizeWords(string input)
        {
            if (string.IsNullOrEmpty(input)) return input;
            return string.Join(" ", input.Split(' ')
                .Select(word => char.ToUpper(word[0]) + word.Substring(1)));
        }
    }
}

