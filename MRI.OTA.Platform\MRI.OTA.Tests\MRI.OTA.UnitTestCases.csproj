﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>

    <IsPackable>false</IsPackable>
    <IsTestProject>true</IsTestProject>
  </PropertyGroup>
  <ItemGroup>
    <ProjectReference Include="..\MRI.OTA.API\MRI.OTA.API.csproj" />
    <ProjectReference Include="..\MRI.OTA.Application\MRI.OTA.Application.csproj" />
    <ProjectReference Include="..\MRI.OTA.Common\MRI.OTA.Common.csproj" />
    <ProjectReference Include="..\MRI.OTA.Entities\MRI.OTA.DBCore.csproj" />
    <ProjectReference Include="..\MRI.OTA.Infrastructure\MRI.OTA.Infrastructure.csproj" />
    <ProjectReference Include="..\MRI.OTA.Integration\MRI.OTA.Integration.csproj" />
    <ProjectReference Include="..\MRI.OTA.Email\MRI.OTA.Email.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Using Include="Xunit" />
  </ItemGroup>

	<ItemGroup>
		<!-- Testing Dependencies -->
		<PackageReference Include="AutoMapper" />
		<PackageReference Include="Moq" />
		<PackageReference Include="xunit" />
		<PackageReference Include="Microsoft.NET.Test.Sdk" />
		<PackageReference Include="xunit.runner.visualstudio" />
		<PackageReference Include="coverlet.collector" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" />
	</ItemGroup>

	<ItemGroup>
	  <Folder Include="Authentication\" />
	  <Folder Include="Common\Repository\" />
	</ItemGroup>

</Project>
