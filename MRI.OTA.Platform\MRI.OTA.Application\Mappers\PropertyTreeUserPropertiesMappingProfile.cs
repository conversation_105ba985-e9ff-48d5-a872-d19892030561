﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class PropertyTreeUserPropertiesMappingProfile : Profile
    {

        /// <summary>  
        /// Constructor for PT property mapping  
        /// </summary>  
        public PropertyTreeUserPropertiesMappingProfile() : base("PropertyTreeUserPropertiesMappingProfile")
        {
            CreateMap<UserProperties, PropertyTreeInviteResponse>()
              .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => src.SRCEntitytId))
              .ForMember(dest => dest.UserId, opt => opt.MapFrom(src => src.ProviderId))
              .ForMember(dest => dest.AgencyId, opt => opt.MapFrom(src => src.SRCAgencyId))
              .ForMember(dest => dest.PropertyName, opt => opt.MapFrom(src => src.PropertyName))
              .ForMember(dest => dest.PTSuburb, opt => opt.MapFrom(src => src.Suburb))
              .ForMember(dest => dest.UserRelationshipToProperty, opt => opt.MapFrom(src =>
                  Constants.PropertyRelationShipDic.Where(x => x.Value == src.PropertyRelationshipId)
                                                 .Select(x => x.Key)
                                                 .FirstOrDefault() ?? "Other"))
                .ReverseMap()
              .ForMember(dest => dest.PropertyRelationshipId, opt => opt.MapFrom(src =>
                  Constants.PropertyRelationShipDic.Keys
                      .FirstOrDefault(k => string.Equals(k, src.UserRelationshipToProperty, StringComparison.OrdinalIgnoreCase)) != null
                      ? Constants.PropertyRelationShipDic[Constants.PropertyRelationShipDic.Keys
                          .FirstOrDefault(k => string.Equals(k, src.UserRelationshipToProperty, StringComparison.OrdinalIgnoreCase))]
                      : 0))
              .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1))
              .ForMember(dest => dest.UserId, opt => opt.Ignore())
              .ForMember(dest => dest.SRCTenancyId, opt => opt.MapFrom(src => src.TenancyId))
              .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
              .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src => src.UserRelationshipStatus == "Active" ? 1 : 0));
        }
    }
}
