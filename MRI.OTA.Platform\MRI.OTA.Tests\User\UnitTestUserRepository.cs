using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Repositories;
using System.Data;

namespace MRI.OTA.UnitTestCases.User
{
    public class UnitTestUserRepository
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<UserRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly UserRepository _repository;

        public UnitTestUserRepository()
        {
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<UserRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);

            _repository = new UserRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object);
        }

        [Fact]
        public async Task GetUserDetails_Should_Return_UserProfile_When_Found()
        {
            // Arrange
            var userProfileModel = new ViewUserProfileModel
            {
                ProviderId = "test-provider",
                UserEmail = "<EMAIL>"
            };

            var expectedUserProfile = new ViewUserProfileModel
            {
                UserId = 1,
                ProviderId = "test-provider",
                UserEmail = "<EMAIL>",
                ProviderTypeId = 1,
                ProviderName = "Test Provider",
                DisplayName = "Test User",
                IsActive = true,
                PropertyCount = 5,
                TermsAndConditions = true,
                PreferredContactEmail = "<EMAIL>",
                PushNotificationEnabled = true,
                EmailNotificationEnabled = true
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<ViewUserProfileModel>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("ProviderId") != null &&
                        p.GetType().GetProperty("UserEmail") != null &&
                        p.GetType().GetProperty("ProviderId")!.GetValue(p)!.ToString() == userProfileModel.ProviderId &&
                        p.GetType().GetProperty("UserEmail")!.GetValue(p)!.ToString() == userProfileModel.UserEmail),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedUserProfile);

            // Act
            var result = await _repository.GetUserDetails(userProfileModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedUserProfile.UserId, result.UserId);
            Assert.Equal(expectedUserProfile.ProviderId, result.ProviderId);
            Assert.Equal(expectedUserProfile.UserEmail, result.UserEmail);
            Assert.Equal(expectedUserProfile.ProviderTypeId, result.ProviderTypeId);
            Assert.Equal(expectedUserProfile.ProviderName, result.ProviderName);
            Assert.Equal(expectedUserProfile.DisplayName, result.DisplayName);
            Assert.Equal(expectedUserProfile.IsActive, result.IsActive);
            Assert.Equal(expectedUserProfile.PropertyCount, result.PropertyCount);
            Assert.Equal(expectedUserProfile.TermsAndConditions, result.TermsAndConditions);
            Assert.Equal(expectedUserProfile.PreferredContactEmail, result.PreferredContactEmail);
            Assert.Equal(expectedUserProfile.PushNotificationEnabled, result.PushNotificationEnabled);
            Assert.Equal(expectedUserProfile.EmailNotificationEnabled, result.EmailNotificationEnabled);
        }

        [Fact]
        public async Task GetUserDetails_Should_Return_Null_When_Not_Found()
        {
            // Arrange
            var userProfileModel = new ViewUserProfileModel
            {
                ProviderId = "non-existent-provider",
                UserEmail = "<EMAIL>"
            };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<ViewUserProfileModel>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((ViewUserProfileModel)null!);

            // Act
            var result = await _repository.GetUserDetails(userProfileModel);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task UpdateUserEmail_Should_Update_UserEmail()
        {
            // Arrange
            var user = new Users
            {
                ProviderId = "test-provider",
                UserEmail = "<EMAIL>"
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("ProviderId") != null &&
                        p.GetType().GetProperty("UserEmail") != null &&
                        p.GetType().GetProperty("ProviderId")!.GetValue(p)!.ToString() == user.ProviderId &&
                        p.GetType().GetProperty("UserEmail")!.GetValue(p)!.ToString() == user.UserEmail),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.UpdateUserEmail(user);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task UpdateUserProviderType_Should_Update_ProviderType()
        {
            // Arrange
            var user = new Users
            {
                UserEmail = "<EMAIL>",
                ProviderTypeId = 2,
                ProviderId = "new-provider"
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserEmail") != null &&
                        p.GetType().GetProperty("ProviderTypeId") != null &&
                        p.GetType().GetProperty("ProviderId") != null &&
                        p.GetType().GetProperty("UserEmail")!.GetValue(p)!.ToString() == user.UserEmail &&
                        (int)p.GetType().GetProperty("ProviderTypeId")!.GetValue(p)! == user.ProviderTypeId &&
                        p.GetType().GetProperty("ProviderId")!.GetValue(p)!.ToString() == user.ProviderId),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.UpdateUserProviderType(user);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task UpdateUserTermsAndCondition_Should_Update_TermsAndConditions()
        {
            // Arrange
            var user = new Users
            {
                UserId = 1,
                TermsAndConditions = true
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        p.GetType().GetProperty("TermsAndConditions") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == user.UserId &&
                        (bool)p.GetType().GetProperty("TermsAndConditions")!.GetValue(p)! == user.TermsAndConditions),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.UpdateUserTermsAndCondition(user);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task UpdateUserProfileSettings_Should_Update_ProfileSettings()
        {
            // Arrange
            var user = new Users
            {
                UserId = 1,
                PreferredContactEmail = "<EMAIL>",
                PushNotificationEnabled = true,
                EmailNotificationEnabled = false
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        p.GetType().GetProperty("PreferredContactEmail") != null &&
                        p.GetType().GetProperty("PushNotificationEnabled") != null &&
                        p.GetType().GetProperty("EmailNotificationEnabled") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == user.UserId &&
                        p.GetType().GetProperty("PreferredContactEmail")!.GetValue(p)!.ToString() == user.PreferredContactEmail &&
                        (bool)p.GetType().GetProperty("PushNotificationEnabled")!.GetValue(p)! == user.PushNotificationEnabled &&
                        (bool)p.GetType().GetProperty("EmailNotificationEnabled")!.GetValue(p)! == user.EmailNotificationEnabled),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.UpdateUserProfileSettings(user);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task UpdateUserProfileSettings_Should_Update_WithoutPreferredContactEmail_When_NullOrEmpty()
        {
            // Arrange
            var user = new Users
            {
                UserId = 2,
                PreferredContactEmail = null,
                PushNotificationEnabled = false,
                EmailNotificationEnabled = true
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UPDATE Users SET PushNotificationEnabled = @PushNotificationEnabled , EmailNotificationEnabled = @EmailNotificationEnabled WHERE UserId = @UserId")),
                    It.Is<object>(p =>
                        p.GetType().GetProperty("UserId") != null &&
                        p.GetType().GetProperty("PushNotificationEnabled") != null &&
                        p.GetType().GetProperty("EmailNotificationEnabled") != null &&
                        (int)p.GetType().GetProperty("UserId")!.GetValue(p)! == user.UserId &&
                        (bool)p.GetType().GetProperty("PushNotificationEnabled")!.GetValue(p)! == user.PushNotificationEnabled &&
                        (bool)p.GetType().GetProperty("EmailNotificationEnabled")!.GetValue(p)! == user.EmailNotificationEnabled
                    ),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.UpdateUserProfileSettings(user);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task GetAllUsers_Should_Return_ActiveUsers()
        {
            // Arrange
            var users = new List<Users>
            {
                new Users { UserId = 1, ProviderId = "p1", ProviderTypeId = 1, UserEmail = "<EMAIL>" },
                new Users { UserId = 2, ProviderId = "p2", ProviderTypeId = 2, UserEmail = "<EMAIL>" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<Users>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("FROM USERS Where IsActive = 1")),
                    null,
                    null,
                    null,
                    null))
                .ReturnsAsync(users);

            // Act
            var result = await _repository.GetAllUsers();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("<EMAIL>", result[0].UserEmail);
        }

        [Fact]
        public async Task GetDataSourcesByUserId_Should_Return_DataSources()
        {
            // Arrange
            var userId = 1;
            // Ensure the correct type is used instead of the namespace 'DataSource'
            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
            {
                new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "ds1", ManifestJson = "{}" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("SELECT DISTINCT DS.DataSourceId")),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ReturnsAsync(dataSources);

            // Act
            var result = await _repository.GetDataSourcesByUserId(userId);

            // Assert
            Assert.Single(result);
            Assert.Equal("ds1", result[0].Name);
        }

        [Fact]
        public async Task GetDataSourcesByUserId_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            var userId = 1;
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<MRI.OTA.Core.Entities.DataSource>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ThrowsAsync(new System.Exception("db error"));

            // Act
            var result = await _repository.GetDataSourcesByUserId(userId);

            // Assert
            Assert.Empty(result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting data sources for user ID")),
                    It.IsAny<System.Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Fact]
        public async Task GetUserCount_Should_Return_Count()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("SELECT COUNT(*) FROM Users WHERE IsActive = 1")),
                    null,
                    null,
                    null,
                    null))
                .ReturnsAsync(5);

            // Act
            var result = await _repository.GetUserCount();

            // Assert
            Assert.Equal(5, result);
        }

        [Fact]
        public async Task GetUserCount_Should_Return_Zero_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    null,
                    null,
                    null,
                    null))
                .ThrowsAsync(new System.Exception("db error"));

            // Act
            var result = await _repository.GetUserCount();

            // Assert
            Assert.Equal(0, result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting user count")),
                    It.IsAny<System.Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Fact]
        public async Task GetUsersBatch_Should_Return_Users()
        {
            // Arrange
            var users = new List<Users>
            {
                new Users { UserId = 1, ProviderId = "p1", ProviderTypeId = 1, UserEmail = "<EMAIL>" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<Users>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("OFFSET @Skip ROWS")),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ReturnsAsync(users);

            // Act
            var result = await _repository.GetUsersBatch(0, 10);

            // Assert
            Assert.Single(result);
            Assert.Equal("<EMAIL>", result[0].UserEmail);
        }

        [Fact]
        public async Task GetUsersBatch_Should_Return_EmptyList_On_Exception()
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<Users>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ThrowsAsync(new System.Exception("db error"));

            // Act
            var result = await _repository.GetUsersBatch(0, 10);

            // Assert
            Assert.Empty(result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting users batch")),
                    It.IsAny<System.Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.AtLeastOnce);
        }

        [Fact]
        public async Task AddUpdateUserDevice_Should_Update_When_Device_Exists()
        {
            // Arrange
            var deviceModel = new UserDeviceDetail
            {
                DeviceId = "dev1",
                DeviceType = 1,
                DeviceToken = "token"
            };
            int userId = 10;
            int existingDeviceDetailId = 5;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("SELECT TOP(1) UserDeviceDetailId")),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ReturnsAsync(existingDeviceDetailId);

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("UPDATE UserDeviceDetails")),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ReturnsAsync(1);

            // Act
            var result = await _repository.AddUpdateUserDevice(deviceModel, userId);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task AddUpdateUserDevice_Should_Add_When_Device_Not_Exists()
        {
            // Arrange
            var deviceModel = new UserDeviceDetail
            {
                DeviceId = "dev2",
                DeviceType = 2,
                DeviceToken = "token2"
            };
            int userId = 20;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("SELECT TOP(1) UserDeviceDetailId")),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ReturnsAsync(0);

            // Act
            var result = await _repository.AddUpdateUserDevice(deviceModel, userId);

            // Assert
            Assert.Equal(0, result);
        }

        [Fact]
        public async Task AddUpdateUserDevice_Should_Return_MinusOne_On_Exception()
        {
            // Arrange
            var deviceModel = new UserDeviceDetail
            {
                DeviceId = "dev3",
                DeviceType = 3,
                DeviceToken = "token3"
            };
            int userId = 30;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ThrowsAsync(new Exception("db error"));

            // Act
            var result = await _repository.AddUpdateUserDevice(deviceModel, userId);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task DeleteUserDeviceInfo_Should_Return_True_On_Success()
        {
            // Arrange
            int userId = 1;
            string deviceId = "dev1";

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("DELETE FROM")),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ReturnsAsync(new List<int>());

            // Act
            var result = await _repository.DeleteUserDeviceInfo(userId, deviceId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task DeleteUserDeviceInfo_Should_Throw_On_Exception()
        {
            // Arrange
            int userId = 2;
            string deviceId = "dev2";

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    null,
                    null,
                    null))
                .ThrowsAsync(new Exception("db error"));

            // Act & Assert
            await Assert.ThrowsAsync<Exception>(() => _repository.DeleteUserDeviceInfo(userId, deviceId));
        }

        [Fact]
        public async Task DeleteAccount_Should_Return_True_On_Success()
        {
            // Arrange
            int userId = 1;
            string providerId = "provider1";

            var mockTransaction = new Mock<IDbTransaction>();
            _mockConnection.Setup(c => c.BeginTransaction()).Returns(mockTransaction.Object);

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(q => q.Contains("DELETE FROM")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    null,
                    null))
                .ReturnsAsync(new int[0]);

            // Act
            var result = await _repository.DeleteAccount(userId, providerId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task DeleteAccount_Should_Throw_And_LogError_On_Exception()
        {
            int userId = 2;
            string providerId = "provider2";

            var mockTransaction = new Mock<IDbTransaction>();
            _mockConnection.Setup(c => c.BeginTransaction()).Returns(mockTransaction.Object);

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    null,
                    null))
                .ThrowsAsync(new Exception("db error"));

            await Assert.ThrowsAnyAsync<Exception>(() => _repository.DeleteAccount(userId, providerId));
            _mockLogger.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error Deleting user account")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }
    }
}
