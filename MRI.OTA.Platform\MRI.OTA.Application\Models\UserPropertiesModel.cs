﻿using Microsoft.AspNetCore.Http;

namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// Model for UserProperties
    /// </summary>
    public class UserPropertiesModel
    {
        /// <summary>
        /// userid
        /// </summary>
        public int Userid { get; set; }
        /// <summary>
        /// PropertyId
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// PropertyName
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// DefaultImageId
        /// </summary>
        public int? DefaultImageId { get; set; }

        /// <summary>
        /// Bedrooms
        /// </summary>
        public int Bedrooms { get; set; }

        /// <summary>
        /// BathRooms
        /// </summary>
        public int BathRooms { get; set; }

        /// <summary>
        /// CarSpaces
        /// </summary>
        public int CarSpaces { get; set; }

        /// <summary>
        /// FloorArea
        /// </summary>
        public int FloorArea { get; set; }

        /// <summary>
        /// LandArea
        /// </summary>
        public int LandArea { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// SRCEntitytId
        /// </summary>
        public string? SRCEntitytId { get; set; }

        /// <summary>
        /// SRCManagementId
        /// </summary>
        public string? SRCAgencyId { get; set; }

        public string? SRCManagementId { get; set; }

        public string? SRCTenancyId { get; set; }

        public string? Currency { get; set; }

        /// <summary>
        /// SRCServiceId
        /// </summary>
        public string? SRCServiceId { get; set; }

        /// <summary>
        /// OccupancyType
        /// </summary>
        public string? OccupancyType { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Suburb
        /// </summary>
        public string? Suburb { get; set; }
        /// <summary>
        /// StreetAddress
        /// </summary>
        public string? StreetAddress { get; set; }
        /// <summary>
        /// City
        /// </summary>
        public string? City { get; set; }
        /// <summary>
        /// State
        /// </summary>
        public int? StateId { get; set; }

        /// <summary>
        /// Country
        /// </summary>
        public string? CountryCode { get; set; }

        /// <summary>
        /// Country
        /// </summary>
        public string? CountryName { get; set; }

        /// <summary>
        /// StateCode
        /// </summary>
        public string? StateCode { get; set; }

        /// <summary>
        /// StateName
        /// </summary>
        public string? StateName { get; set; }

        /// <summary>
        /// AdministrativeArea
        /// </summary>
        public string? AdministrativeArea { get; set; }

        /// <summary>
        /// Locale
        /// </summary>
        public string? Locale { get; set; }

        /// <summary>
        /// PostalCode
        /// </summary>
        public string? PostalCode { get; set; }
        /// <summary>
        /// RuralDelivery
        /// </summary>
        public string? RuralDelivery { get; set; }
        /// <summary>
        /// PostOfficeName
        /// </summary>
        public string? PostOfficeName { get; set; }

        /// <summary>
        /// PropertyRelationshipId
        /// </summary>
        public int PropertyRelationshipId { get; set; }

        /// <summary>
        /// PropertyType
        /// </summary>
        public string? PropertyType { get; set; }

        /// <summary>
        /// LotNumber
        /// </summary>
        public string? LotNumber { get; set; }

        /// <summary>
        /// OccupancyStatus
        /// </summary>
        public string? OccupancyStatus { get; set; }

        /// <summary>
        /// UserPropertiesNickNameId
        /// </summary>
        public int? UserPropertiesNickNameId { get; set; }

        /// <summary>
        /// PropertyOwnershipDetailsModel
        /// </summary>
        public PropertyOwnershipDetailsModel? PropertyOwnershipDetails { get; set; }

        /// <summary>
        /// PropertyManagerInformation
        /// </summary>
        public PropertyManagerInformationModel? PropertyManagerInformation { get; set; }

        /// <summary>
        /// PropertyFinancialInformation
        /// </summary>
        public PropertyFinancialInformationModel? PropertyFinancialInformation { get; set; }

    }
}
