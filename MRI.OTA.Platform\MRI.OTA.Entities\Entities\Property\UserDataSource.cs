﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class UserDataSource
    {
        /// <summary>
        /// UserDataSourceId
        /// </summary>
        [ExcludeColumn]
        public int UserDataSourceId { get; set; }

        /// <summary>
        /// DataSourceId
        /// </summary>
        public int DataSourceId { get; set; }

        /// <summary>
        /// AccessKey
        /// </summary>
        public string? AccessKey { get; set; }

        /// <summary>
        /// AccessToken
        /// </summary>
        public string? AccessToken { get; set; }
    }
}
