﻿using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.Image
{
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/images")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class ImageController : ControllerBase
    {
        private readonly IImageStorageService _imageStorageService;
        private readonly ILogger<ImageController> _logger;
        private readonly IConfiguration _configuration;

        public ImageController(
            IImageStorageService imageStorageService,
            ILogger<ImageController> logger,
            IConfiguration configuration)
        {
            _imageStorageService = imageStorageService;
            _logger = logger;
            _configuration = configuration;
        }

        /// <summary>
        /// Uploads images to the cloud.
        /// </summary>
        /// <param name="images"></param>
        /// <returns></returns>
        [HttpPost]
        public async Task<IActionResult> UploadImages([FromBody] ImageModel images)
        {
            try
            {
                if (images.Files == null || !images.Files.Any())
                {
                    return BadRequest("No images uploaded");
                }

                var allowedExtensions = _configuration.GetSection("ImageUploadSettings:AllowedExtensions").Get<List<string>>();
                var maxFileSize = Convert.ToInt32(_configuration["ImageUploadSettings:MaxFileSizeInBytes"]) * 1024 * 1024; // 5MB

                foreach (var image in images.Files)
                {
                    if (string.IsNullOrEmpty(image.Base64Content))
                    {
                        return BadRequest("One or more images are empty");
                    }

                    // Validate file extension
                    var fileExtension = Path.GetExtension(image.FileName).ToLowerInvariant();
                    if (!allowedExtensions.Contains(fileExtension))
                    {
                        return BadRequest($"Invalid file type: {image.FileName}");
                    }

                    // Calculate size of base64 string (approximate)
                    var base64Data = image.Base64Content;
                    if (base64Data.Contains(","))
                    {
                        base64Data = base64Data.Split(',')[1]; // Remove data URL prefix if present
                    }
                    
                    // Calculate approximate file size
                    var fileSize = (base64Data.Length * 3) / 4; // Base64 encoding increases size by ~33%
                    if (fileSize > maxFileSize)
                    {
                        return BadRequest($"File size exceeds maximum limit of 5MB: {image.FileName}");
                    }
                }

                var imagesResult = await _imageStorageService.UploadImages(images);

                if (imagesResult == null || imagesResult.Count == 0)
                {
                    return BadRequest(new ApiResponse<object>(false, "Item not added", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be added." }));
                }
                else
                {
                    return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item added successfully", imagesResult));
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error uploading images");
                return StatusCode(500, "An error occurred while uploading the images");
            }
        }

        /// <summary>
        /// Update default images to the cloud.
        /// </summary>
        /// <param name="defaultImageModel"></param>
        /// <returns></returns>
        [HttpPut]
        public async Task<IActionResult> UpdateDefaultImages(UpdateDefaultImageModel defaultImageModel)
        {
            var result = await _imageStorageService.UpdateDefaultImage(defaultImageModel);
            if (result == Constants.Success)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item updated successfully", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "The item could not be updated.", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be updated." }));
            }
        }

        /// <summary>
        /// Delete images on cloud storage
        /// </summary>
        /// <param name="propertyImagesId"></param>
        /// <returns></returns>
        [HttpDelete]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "Property images data deleted successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> DeleteProperty([FromHeader] int propertyImagesId)
        {
            var result = await _imageStorageService.DeletePropertyImage(propertyImagesId);
            if (result == Constants.Success)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item deleted successfully", null!));
            }
            else if(result == Constants.DefaultImageExists)
            {
                return Ok(new ApiResponse<object>(false, "The default image could not be deleted.", null!, StatusCodes.Status200OK, new List<string> { "The default image could not be deleted." }));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "The item could not be deleted.", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be deleted." }));
            }
        }
    }
}
