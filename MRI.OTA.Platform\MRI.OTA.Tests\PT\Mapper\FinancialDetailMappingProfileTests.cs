using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.PT.Mapper
{
    public class FinancialDetailMappingProfileTests
    {
        private readonly IMapper _mapper;

        public FinancialDetailMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<FinancialDetailMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_FinancialDetailResponse_To_FinancialDetail()
        {
            // Arrange
            var source = new FinancialDetailResponse
            {
                AgencyId = "AGENCY123",
                UserId = "USER456",
                ManagementId = "OWN789",
                PropertyId = "PROP001",
                OwnershipTotalAvailableBalance = 5000.50m,
                PropertyOutstandingFees = 250.00m,
                PropertyOutstandingInvoices = 750.00m,
                PropertyOverdueInvoices = 500.00m,
                LastPaymentAmount = 1000.00m,
                LastStatementDate = new DateTime(2025, 5, 15)
            };

            // Act
            var result = _mapper.Map<FinancialDetail>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(source.AgencyId, result.SRCAgencyId);
            Assert.Equal(source.UserId, result.SRCUserId);
            Assert.Equal(source.ManagementId, result.SRCManagementId);
            Assert.Equal(source.PropertyId, result.SRCPropertyId);
            Assert.Equal(-1, result.PropertyId);
            Assert.Equal(source.OwnershipTotalAvailableBalance, result.OwnershipTotalAvailableBalance);
            Assert.Equal(source.PropertyOutstandingFees, result.PropertyOutstandingFees);
            Assert.Equal(source.PropertyOutstandingInvoices, result.PropertyOutstandingInvoices);
            Assert.Equal(source.PropertyOverdueInvoices, result.PropertyOverdueInvoices);
            Assert.Equal(source.LastPaymentAmount, result.LastPaymentAmount);
            Assert.Equal(source.LastStatementDate, result.LastStatementDate);
        }

        [Fact]
        public void Should_Handle_Null_Decimal_Values()
        {
            // Arrange
            var source = new FinancialDetailResponse
            {
                AgencyId = "AGENCY123",
                UserId = "USER456",
                ManagementId = "OWN789",
                PropertyId = "PROP001",
                OwnershipTotalAvailableBalance = null!,
                PropertyOutstandingFees = null,
                PropertyOutstandingInvoices = null,
                PropertyOverdueInvoices = null,
                LastPaymentAmount = null
            };

            // Act
            var result = _mapper.Map<FinancialDetail>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(null!, result.OwnershipTotalAvailableBalance);
            Assert.Equal(null!, result.PropertyOutstandingFees);
            Assert.Equal(null!, result.PropertyOutstandingInvoices);
            Assert.Equal(null!, result.PropertyOverdueInvoices);
            Assert.Equal(null!, result.LastPaymentAmount);
        }

        [Fact]
        public void Should_Handle_Null_LastStatementDate()
        {
            // Arrange
            var source = new FinancialDetailResponse
            {
                AgencyId = "AGENCY123",
                UserId = "USER456",
                ManagementId = "OWN789",
                PropertyId = "PROP001",
                LastStatementDate = null
            };

            // Act
            var result = _mapper.Map<FinancialDetail>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(default, result.LastStatementDate);
        }

        [Fact]
        public void Should_Handle_Minimum_Required_Properties()
        {
            // Arrange
            var source = new FinancialDetailResponse
            {
                AgencyId = "AGENCY123",
                UserId = "USER456",
                ManagementId = "OWN789",
                PropertyId = "PROP001"
                // All other properties left as default
            };

            // Act
            var result = _mapper.Map<FinancialDetail>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(source.AgencyId, result.SRCAgencyId);
            Assert.Equal(source.UserId, result.SRCUserId);
            Assert.Equal(source.ManagementId, result.SRCManagementId);
            Assert.Equal(source.PropertyId, result.SRCPropertyId);
            Assert.Equal(-1, result.PropertyId);
            Assert.Equal(null!, result.OwnershipTotalAvailableBalance);
            Assert.Equal(null!, result.PropertyOutstandingFees);
            Assert.Equal(null!, result.PropertyOutstandingInvoices);
            Assert.Equal(null!, result.PropertyOverdueInvoices);
            Assert.Equal(null!, result.LastPaymentAmount);
            Assert.Equal(default, result.LastStatementDate);
        }

        [Fact]
        public void Should_Handle_Negative_Financial_Values()
        {
            // Arrange
            var source = new FinancialDetailResponse
            {
                AgencyId = "AGENCY123",
                UserId = "USER456",
                ManagementId = "OWN789",
                PropertyId = "PROP001",
                OwnershipTotalAvailableBalance = -1000.00m,
                PropertyOutstandingFees = -250.00m,
                PropertyOutstandingInvoices = -750.00m,
                PropertyOverdueInvoices = -500.00m,
                LastPaymentAmount = -1500.00m
            };

            // Act
            var result = _mapper.Map<FinancialDetail>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(source.OwnershipTotalAvailableBalance, result.OwnershipTotalAvailableBalance);
            Assert.Equal(source.PropertyOutstandingFees, result.PropertyOutstandingFees);
            Assert.Equal(source.PropertyOutstandingInvoices, result.PropertyOutstandingInvoices);
            Assert.Equal(source.PropertyOverdueInvoices, result.PropertyOverdueInvoices);
            Assert.Equal(source.LastPaymentAmount, result.LastPaymentAmount);
        }

        [Fact]
        public void Should_Handle_Multiple_Financial_Details()
        {
            // Arrange
            var source = new List<FinancialDetailResponse>
            {
                new FinancialDetailResponse
                {
                    AgencyId = "AGENCY1",
                    UserId = "USER1",
                    ManagementId = "OWN1",
                    PropertyId = "PROP1",
                    OwnershipTotalAvailableBalance = 1000.00m,
                    PropertyOutstandingFees = 200.00m,
                    LastStatementDate = new DateTime(2025, 1, 1)
                },
                new FinancialDetailResponse
                {
                    AgencyId = "AGENCY2",
                    UserId = "USER2",
                    ManagementId = "OWN2",
                    PropertyId = "PROP2",
                    OwnershipTotalAvailableBalance = 2000.00m,
                    PropertyOutstandingFees = 400.00m,
                    LastStatementDate = new DateTime(2025, 2, 1)
                }
            };

            // Act 
            var result = _mapper.Map<List<FinancialDetail>>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            
            // Verify first financial detail
            Assert.Equal(source[0].AgencyId, result[0].SRCAgencyId);
            Assert.Equal(source[0].UserId, result[0].SRCUserId);
            Assert.Equal(source[0].OwnershipTotalAvailableBalance, result[0].OwnershipTotalAvailableBalance);
            Assert.Equal(source[0].LastStatementDate, result[0].LastStatementDate);
            
            // Verify second financial detail
            Assert.Equal(source[1].AgencyId, result[1].SRCAgencyId);
            Assert.Equal(source[1].UserId, result[1].SRCUserId);
            Assert.Equal(source[1].OwnershipTotalAvailableBalance, result[1].OwnershipTotalAvailableBalance);
            Assert.Equal(source[1].LastStatementDate, result[1].LastStatementDate);
        }

        [Fact]
        public void Should_Return_Empty_List_For_Null_Source()
        {
            // Arrange
            List<FinancialDetailResponse> source = null;

            // Act
            var result = _mapper.Map<List<FinancialDetail>>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void Configuration_Should_Be_Valid()
        {
            // Arrange & Act & Assert
            _mapper.ConfigurationProvider.AssertConfigurationIsValid();
        }
    }
}
