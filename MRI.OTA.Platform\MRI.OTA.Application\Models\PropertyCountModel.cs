﻿namespace MRI.OTA.Application.Models
{

    /// <summary>
    /// Model for property counts by status
    /// </summary>
    public class PropertyCountModel
    {
        /// <summary>
        /// Count of active properties
        /// </summary>
        public int ActiveCount { get; set; }

        /// <summary>
        /// Count of inactive properties
        /// </summary>
        public int InactiveCount { get; set; }

        /// <summary>
        /// Total count of properties
        /// </summary>
        public int? TotalCount => ActiveCount + InactiveCount;

        /// <summary>
        /// Counts by data source
        /// </summary>
        public DataSourceCountModel[] CountsBySource { get; set; }
    }

    /// <summary>
    /// Model for property counts by data source
    /// </summary>
    public class DataSourceCountModel
    {
        /// <summary>
        /// Data source ID
        /// </summary>
        public int DataSourceId { get; set; }

        /// <summary>
        /// Data source name
        /// </summary>
        public string DataSourceName { get; set; }

        /// <summary>
        /// Count of active properties
        /// </summary>
        public int ActiveCount { get; set; }

        /// <summary>
        /// Count of inactive properties
        /// </summary>
        public int InactiveCount { get; set; }

        /// <summary>
        /// Total count of properties
        /// </summary>
        public int TotalCount => ActiveCount + InactiveCount;
    }

}
