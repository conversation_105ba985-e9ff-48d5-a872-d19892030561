﻿using System.Net;
using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;

namespace MRI.OTA.API.ExceptionHandler
{
    public class GlobalExceptionHandler : Microsoft.AspNetCore.Diagnostics.IExceptionHandler
    {
        private readonly ILogger<GlobalExceptionHandler> _logger;
        private readonly IHostEnvironment _env;

        /// <summary>
        /// ExceptionMiddleware
        /// </summary>
        /// <param name="logger"></param>
        /// <param name="env"></param>
        public GlobalExceptionHandler(ILogger<GlobalExceptionHandler> logger, IHostEnvironment env)
        {
            _logger = logger;
            _env = env;
        }

        public async ValueTask<bool> TryHandleAsync(
        HttpContext httpContext,
        Exception exception,
        CancellationToken cancellationToken)
        {
            _logger.LogError(
                exception, "Exception occurred: {Message}", exception.Message);

            var response = new ApiResponse<object>(false, MessagesConstants.InternalServerErrorMessage, data: null!, StatusCodes.Status500InternalServerError, new List<string> { MessagesConstants.InternalServerErrorMessage });

            httpContext.Response.ContentType = "application/json";
            httpContext.Response.StatusCode = StatusCodes.Status500InternalServerError;
            await httpContext.Response.WriteAsJsonAsync(JsonSerializer.Serialize(response), cancellationToken);

            return true;
        }
    }
}
