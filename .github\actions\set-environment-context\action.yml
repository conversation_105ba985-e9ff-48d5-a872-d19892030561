name: Set deployment environment context

description: Sets environment-specific context variables for deployment

inputs:
  environment:
    description: 'Environment name (dev, qa, prod)'
    required: true

runs:
  using: "composite"
  steps:
    - name: Set deployment environment context
      shell: bash
      run: |
        echo "ENVIRONMENT=${{ inputs.environment }}" >> $GITHUB_ENV
        echo "RG_NAME=rg-nw02-shrdplt-${{ inputs.environment }}-ota" >> $GITHUB_ENV
        echo "CONTAINER_APP=ca-nw02-shrdplt-${{ inputs.environment }}-api" >> $GITHUB_ENV 