﻿namespace MRI.OTA.Common.Interfaces
{
    /// <summary>
    /// Interface for base service
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <typeparam name="TModel"></typeparam>
    /// <typeparam name="TK<PERSON>"></typeparam>
    public interface IBaseService<TEntity, TModel, TKey> where TEntity : class
    {
        /// <summary>
        /// Get all
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<TModel>> GetAllAsync();

        /// <summary>
        /// Get table data
        /// </summary>
        /// <param name="tablename"></param>
        /// <returns></returns>
        Task<IEnumerable<TEntity>> GetDataByTableName(string tablename);
        /// <summary>
        /// Get by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<TModel> GetByIdAsync(TKey id, string idColumnName);
        /// <summary>
        /// Add Entity
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<int> AddAsync(TModel model);
        /// <summary>
        ///  Update Entity
        /// </summary>
        /// <param name="model"></param>
        /// <returns></returns>
        Task<int> UpdateAsync(TKey id, string idColumnName, TModel model);
        /// <summary>
        /// Delete Entity
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<int> DeleteAsync(TKey id, string idColumnName);
    }
}
