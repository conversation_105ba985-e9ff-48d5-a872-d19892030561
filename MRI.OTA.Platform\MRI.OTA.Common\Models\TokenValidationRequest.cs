using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace MRI.OTA.Common.Models
{
    /// <summary>
    /// Request model for token validation
    /// </summary>
    public class TokenValidationRequest
    {
        /// <summary>
        /// The JWT token to validate
        /// </summary>
        [JsonPropertyName("token")]
        [Required(ErrorMessage = "Token is required")]
        public string Token { get; set; } = string.Empty;
    }
} 