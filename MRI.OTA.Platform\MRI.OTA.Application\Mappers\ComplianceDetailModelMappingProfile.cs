using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    /// <summary>
    /// Mapping profile for ComplianceDetail entity to ComplianceDetailModel
    /// </summary>
    public class ComplianceDetailModelMappingProfile : Profile
    {
        public ComplianceDetailModelMappingProfile() : base("ComplianceDetailModelMappingProfile")
        {
            // Map from ComplianceDetail entity to ComplianceDetailModel
            CreateMap<ComplianceDetail, ComplianceDetailModel>()
                .ForMember(dest => dest.ManagementId, opt => opt.MapFrom(src => src.SRCManagementId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => src.SRCPropertyId))
                .ForMember(dest => dest.ComplianceId, opt => opt.MapFrom(src => src.SRCComplianceId))
                .ForMember(dest => dest.ComplianceName, opt => opt.MapFrom(src => src.ComplianceName))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate))
                .ForMember(dest => dest.ServicedBy, opt => opt.MapFrom(src => src.ServicedBy));

            // Reverse mapping from ComplianceDetailModel to ComplianceDetail entity
            CreateMap<ComplianceDetailModel, ComplianceDetail>()
                .ForMember(dest => dest.ComplianceDetailId, opt => opt.Ignore()) // Auto-generated
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.PropertyId, opt => opt.Ignore()) // Will be set separately
                .ForMember(dest => dest.SRCComplianceId, opt => opt.MapFrom(src => src.ComplianceId))
                .ForMember(dest => dest.ComplianceName, opt => opt.MapFrom(src => src.ComplianceName))
                .ForMember(dest => dest.Status, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.ExpiryDate, opt => opt.MapFrom(src => src.ExpiryDate))
                .ForMember(dest => dest.ServicedBy, opt => opt.MapFrom(src => src.ServicedBy));
        }
    }
} 