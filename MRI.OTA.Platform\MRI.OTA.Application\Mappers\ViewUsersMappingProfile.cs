﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.Application.Mappers
{
    /// <summary>
    /// _mapper for  user entity and user model
    /// </summary>
    public class ViewUsersMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for user mapper
        /// </summary>
        public ViewUsersMappingProfile() : base("ViewUsersMappingProfile")
        {
            CreateMap<Users, UserModel>()
            .ReverseMap();
        }
    }
}
