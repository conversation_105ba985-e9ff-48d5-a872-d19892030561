using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    /// <summary>
    /// Mapping profile for InspectionDetail entity to InspectionDetailModel application model
    /// </summary>
    public class InspectionDetailAppMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for inspection detail mapper
        /// </summary>
        public InspectionDetailAppMappingProfile() : base("InspectionDetailAppMappingProfile")
        {
            CreateMap<InspectionDetail, InspectionDetailModel>()
                .ReverseMap();
        }
    }
} 