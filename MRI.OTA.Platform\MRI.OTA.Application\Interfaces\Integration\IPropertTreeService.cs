﻿using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Models.Integration;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.Integration.Models;
using static MRI.OTA.Common.Constants.Constants;

namespace MRI.OTA.Application.Interfaces.Integration
{
    public interface IPropertTreeService
    {
        ProxyRequestModel CreateAssociatePortfolioProxyRequest(DataSourceManifest manifest, string portfolioId, string? providerId, string? providerType);
        ProxyRequestModel CreateProxyRequest(DataSourceManifest manifest, IntegrationEndPointsType sectionType, string[] userId, string? modifiedSince = null);

        ProxyRequestModel CreateProxyRequest(DataSourceManifest manifest, IntegrationEndPointsType sectionType, string? modifiedSince = null, string[]? managementIds = null, string[]? tenancyIds = null, string[]? agencyIds = null);
        Task<(bool, List<UserProperties>)> ProcessGetPropertiesProxyResponse(IActionResult propertyResponse, DataSource dataSource, AcceptInvitationModel acceptInvitation);
        Task<int> ProcessGetPropertiesOtherDataResponse(PropertyTreeResponseBundle responseBundle, DataSource dataSource, List<UserProperties> userPropertiesList, string InviteCode = null!);
        public Task<bool> RemoveUserAccount(int userId, string providerId);
        Task<bool> SyncPropertyData();
        Task<bool> SyncAgencyData();
        Task<bool> SyncAgencyPartnerData();
        Task<bool> SyncDocumentData();
        Task<bool> SyncManagementData();
        Task<bool> SyncComplianceData();
        Task<bool> SyncTenanciesTenantData();
        Task<bool> SyncInspectionsData();
        Task<bool> SyncMaintenanceData();
        Task<bool> SyncFinancialsData();
        Task<bool> SyncTenanciesOwnerData();
    }
}



