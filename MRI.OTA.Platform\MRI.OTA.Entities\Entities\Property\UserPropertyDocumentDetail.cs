﻿using static MRI.OTA.Common.Constants.Constants;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class UserPropertyDocumentDetail
    {
        public int PropertyId { get; set; }
        public int UserId { get; set; }
        public string? StreetAddress { get; set; }
        public string? City { get; set; }
        public string? CountryName { get; set; }
        public string? StateName { get; set; }
        public string? PropertyType { get; set; }
        public int PropertyRelationshipId { get; set; }
        public string? PropertyName { get; set; }
        public string? SRCManagementId { get; set; }
        public string? SRCTenancyId { get; set; }
        public string? DocumentName { get; set; }
        public string? DocumentLink { get; set; }
        public string? DocumentType { get; set; }
        public string? MetaType { get; set; }
        public string? MetaNumber { get; set; }
        public DateTime? MetaDate { get; set; }
        public string? MetaStatus { get; set; }
        public decimal? MetaAmount { get; set; }
        public decimal? MetaOwing { get; set; }
        public string? MetaCurrency { get; set; }
        public string? MetaPeriod { get; set; }
        public DateTime? SharedDate { get; set; }
        public DateTime? LastUpdatedDate { get; set; }
        public string? PropertyNickName { get; set; }
        public string PropertyRelationshipType
        {
            get
            {
                return ((PropertyRelationShipType?)PropertyRelationshipId) switch
                {
                    PropertyRelationShipType.Owner => "Owner",
                    PropertyRelationShipType.OwnerManaged => "Owner Managed",
                    PropertyRelationShipType.Tenant => "Tenant",
                    PropertyRelationShipType.CoTenant => "Co-Tenant",
                    PropertyRelationShipType.OwnerOccupier => "Owner Occupier",
                    PropertyRelationShipType.ProspectiveOwner => "Prospective Owner",
                    PropertyRelationShipType.ProspectiveTenant => "Prospective Tenant",
                    _ => "Unknown"
                };
            }
        }
    }
}
