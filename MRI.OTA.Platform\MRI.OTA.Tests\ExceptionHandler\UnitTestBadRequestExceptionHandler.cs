﻿using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.ExceptionHandler;
using MRI.OTA.Common.Models;

namespace MRI.OTA.UnitTestCases.ExceptionHandler
{
    public class UnitTestBadRequestExceptionHandler
    {
        private readonly Mock<ILogger<BadRequestExceptionHandler>> _loggerMock;
        private readonly BadRequestExceptionHandler _handler;

        public UnitTestBadRequestExceptionHandler()
        {
            _loggerMock = new Mock<ILogger<BadRequestExceptionHandler>>();
            _handler = new BadRequestExceptionHandler(_loggerMock.Object);
        }

        [Fact]
        public async Task TryHandleAsync_ReturnsFalse_ForNonBadRequestException()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var exception = new Exception("Some error");

            // Act
            var result = await _handler.TryHandleAsync(context, exception, CancellationToken.None);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task TryHandleAsync_ReturnsTrue_AndWritesResponse_ForBadRequestException()
        {
            // Arrange
            var context = new DefaultHttpContext();
            var responseStream = new MemoryStream();
            context.Response.Body = responseStream;
            var exception = new BadRequestException("Invalid input");

            // Act
            var result = await _handler.TryHandleAsync(context, exception, CancellationToken.None);

            // Assert
            Assert.True(result);
            Assert.Equal("application/json; charset=utf-8", context.Response.ContentType);
            Assert.Equal(StatusCodes.Status400BadRequest, context.Response.StatusCode);
        }

        [Fact]
        public async Task TryHandleAsync_LogsError_ForBadRequestException()
        {
            // Arrange
            var context = new DefaultHttpContext();
            context.Response.Body = new MemoryStream();
            var exception = new BadRequestException("Test bad request");

            // Act
            await _handler.TryHandleAsync(context, exception, CancellationToken.None);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Exception occurred:")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()
                ),
                Times.Once
            );
        }
    }
}
