﻿using MRI.OTA.Application.Models;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Interfaces
{
    /// <summary>
    /// Interface for Property Service
    /// </summary>
    public interface IPropertyService : IBaseService<UserProperties, UserPropertiesModel, int>
    {
        public Task<int> AddProperty(UserPropertiesModel userProperties);

        public Task<int> UpdateProperty(UserPropertiesModel userProperties);

        public Task<List<ViewUserProperties>> GetAllProperties(SearchCriteriaModel searchCriteria);

        public Task<ViewUserProperties?> GetPropertyById(int propertyId);

        public Task<int> DeleteProperty(int propertyId);

        public Task<List<ViewUserPropertiesNickName>> GetPropertyRelations(int userPropertiesNickNameId);

        public Task<List<ViewUserPropertiesNickName>> GetPropertyNickNames(int userId);

        public Task<int> UpdatePropertyStatus(PropertyStatusModel propertyStatus);

        /// <summary>
        /// Update property portfolio - creates or updates property-nickname relationships
        /// </summary>
        /// <param name="portfolioRequest">Portfolio request containing userid, propertyid, nicknameid, and nickname</param>
        /// <returns>Returns affected rows count</returns>
        public Task<int> UpdatePropertyPortfolio(PropertyPortfolioModel portfolioRequest);

        /// <summary>
        /// Get count of active and inactive properties grouped by agency ID with comprehensive agency information
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of agency count models with comprehensive agency information</returns>
        public Task<List<ViewAgencyPropertyCount>> GetPropertyCountsByAgency(int userId);

        public Task<PropertyCountModel> GetPropertyCountsByDataSource(int userId);

        /// <summary>
        /// Get maintenance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of maintenance details</returns>
        public Task<List<MaintenanceDetailModel>> GetMaintenanceDetails(int propertyId, string? managementId, string? tenancyId);

        /// <summary>
        /// Get compliance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of compliance details</returns>
        public Task<List<ComplianceDetailModel>> GetCompliance(string managementId, int propertyId);

        /// <summary>
        /// Get inspections list based on tenancyId and propertyId
        /// </summary>
        /// <param name="tenancyId">The tenancy ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of inspection details</returns>
        public Task<List<InspectionDetailModel>> GetInspections(int propertyId, string? managementId, string? tenancyId);

        public Task<List<UserPropertyDocumentDetail>> GetDocuments(GetDocumentRequestModel requestModel);

        /// <summary>
        /// Get property manager information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property manager information view model</returns>
        public Task<PropertyManagerViewModel?> GetPropertyManagerInformation(string? managementId, int? propertyId, string? srcPropertyId);

        /// <summary>
        /// Get property financial information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property financial information view model</returns>
        public Task<PropertyFinancialViewModel?> GetPropertyFinancialInformation(string? managementId, int? propertyId, string? srcPropertyId);
        public Task<TenanciesTenantDetailResponse> GetTenantOwnerDetail(string? tenancyId, string? srcPropertyId, int? propertyId);
    }
}
