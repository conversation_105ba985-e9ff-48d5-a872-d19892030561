﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Application.Models;
using MRI.OTA.Application.Services;
using MRI.OTA.Common.Models;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.Tests.Property.Service
{
    public class UnitTestPropertyService
    {
        private readonly Mock<IPropertyRepository> _propertyRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger<PropertyService>> _loggerMock;
        private readonly TaskContext _taskContext;
        private readonly PropertyService _propertyService;
        private readonly Mock<IMasterRepository> _masterRepositoryMock;

        public UnitTestPropertyService()
        {
            _propertyRepositoryMock = new Mock<IPropertyRepository>();
            _masterRepositoryMock = new Mock<IMasterRepository>();
            _mapperMock = new Mock<IMapper>();
            _loggerMock = new Mock<ILogger<PropertyService>>();
            _taskContext = new TaskContext { UserId = 1 };
            _propertyService = new PropertyService(_loggerMock.Object, _propertyRepositoryMock.Object, _masterRepositoryMock.Object, _mapperMock.Object, _taskContext);
        }

        [Fact]
        public async Task GetAllProperties_ShouldReturnProperties_WithPagination()
        {
            // Arrange
            var searchCriteria = new SearchCriteriaModel { OffSet = 0, Limit = 10, ShowAllRecords = false };
            var properties = new List<ViewUserProperties> { new ViewUserProperties { UserId = _taskContext.UserId, PropertyId = 1 } };

            _propertyRepositoryMock.Setup(repo => repo.GetAllProperties(_taskContext.UserId, searchCriteria.OffSet, searchCriteria.Limit, searchCriteria.ShowAllRecords))
                                   .ReturnsAsync(properties);

            _propertyRepositoryMock.Setup(repo => repo.GetPropertyImages(It.IsAny<List<int>>())).ReturnsAsync(new List<PropertyImages>()); // Mock the image fetch

            // Act
            var result = await _propertyService.GetAllProperties(searchCriteria);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(properties, result);
            _propertyRepositoryMock.Verify(repo => repo.GetAllProperties(_taskContext.UserId, searchCriteria.OffSet, searchCriteria.Limit, searchCriteria.ShowAllRecords), Times.Once);
        }

        [Fact]
        public async Task AddProperty_ShouldReturnPropertyId()
        {
            // Arrange
            var userPropertiesModel = new UserPropertiesModel { PropertyId = 1 };
            var userProperties = new UserProperties { PropertyId = 1 };
            _mapperMock.Setup(mapper => mapper.Map<UserProperties>(userPropertiesModel)).Returns(userProperties);
            _propertyRepositoryMock.Setup(repo => repo.AddProperty(userProperties)).ReturnsAsync(1);

            // Act
            var result = await _propertyService.AddProperty(userPropertiesModel);

            // Assert
            Assert.Equal(1, result);
            _mapperMock.Verify(mapper => mapper.Map<UserProperties>(userPropertiesModel), Times.Once);
            _propertyRepositoryMock.Verify(repo => repo.AddProperty(userProperties), Times.Once);
        }
        [Fact]
        public async Task AddProperty_ShouldReturnMinusOne_WhenExceptionThrown()
        {
            // Arrange
            var userPropertiesModel = new UserPropertiesModel { PropertyId = 1 };
            _mapperMock.Setup(mapper => mapper.Map<UserProperties>(userPropertiesModel))
                       .Throws(new Exception("Test exception"));

            // Act
            var result = await _propertyService.AddProperty(userPropertiesModel);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task UpdateProperty_ShouldReturnPropertyId()
        {
            // Arrange
            var userPropertiesModel = new UserPropertiesModel { PropertyId = 1 };
            var userProperties = new UserProperties { PropertyId = 1 };
            _mapperMock.Setup(mapper => mapper.Map<UserProperties>(userPropertiesModel)).Returns(userProperties);
            _propertyRepositoryMock.Setup(repo => repo.UpdateProperty(userProperties)).ReturnsAsync(1);

            // Act
            var result = await _propertyService.UpdateProperty(userPropertiesModel);

            // Assert
            Assert.Equal(1, result);
            _mapperMock.Verify(mapper => mapper.Map<UserProperties>(userPropertiesModel), Times.Once);
            _propertyRepositoryMock.Verify(repo => repo.UpdateProperty(userProperties), Times.Once);
        }

        [Fact]
        public async Task UpdateProperty_ShouldReturnMinusOne_WhenExceptionThrown()
        {
            // Arrange
            var userPropertiesModel = new UserPropertiesModel { PropertyId = 1 };
            _mapperMock.Setup(mapper => mapper.Map<UserProperties>(userPropertiesModel))
                       .Throws(new Exception("Test exception"));

            // Act
            var result = await _propertyService.UpdateProperty(userPropertiesModel);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task GetPropertyById_ShouldReturnProperty()
        {
            // Arrange
            var property = new ViewUserProperties { PropertyId = 1, UserId = _taskContext.UserId };
            var modules = new List<ViewModuleRelationship> { new ViewModuleRelationship { ModuleId = 1, ModuleName = "Test Module", PropertyRelationshipName = "Owner" } };
            var images = new List<PropertyImages> { new PropertyImages { PropertyId = 1, PropertyImagesId = 1 } };

            _propertyRepositoryMock.Setup(repo => repo.GetPropertyById(_taskContext.UserId, 1))
                                  .ReturnsAsync(property);
            _masterRepositoryMock.Setup(repo => repo.GetModulesList(It.IsAny<int?>()))
                                .ReturnsAsync(modules);
            _propertyRepositoryMock.Setup(repo => repo.GetPropertyImages(It.IsAny<List<int>>()))
                                  .ReturnsAsync(images);

            // Act
            var result = await _propertyService.GetPropertyById(1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(property.PropertyId, result.PropertyId);
            Assert.Equal(modules, result.ModuleList);
            Assert.Equal(images, result.PropertyImages);
        }

        [Fact]
        public async Task GetPropertyById_ShouldReturnNull_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(repo => repo.GetPropertyById(_taskContext.UserId, 1))
                                  .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _propertyService.GetPropertyById(1);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPropertyRelations_ShouldReturnRelations()
        {
            // Arrange
            var relations = new List<ViewUserPropertiesNickName> { new ViewUserPropertiesNickName { UserId = 1 } };
            _propertyRepositoryMock.Setup(repo => repo.GetPropertyRelations(1))
                                  .ReturnsAsync(relations);

            // Act
            var result = await _propertyService.GetPropertyRelations(1);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(relations, result);
        }

        [Fact]
        public async Task GetPropertyRelations_ShouldReturnNull_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(repo => repo.GetPropertyRelations(1))
                                  .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _propertyService.GetPropertyRelations(1);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPropertyNickNames_ShouldReturnNickNames()
        {
            // Arrange
            var nickNames = new List<ViewUserPropertiesNickName> { new ViewUserPropertiesNickName { UserId = 1 } };
            _propertyRepositoryMock.Setup(repo => repo.GetPropertyNickNames(_taskContext.UserId))
                                  .ReturnsAsync(nickNames);

            // Act
            var result = await _propertyService.GetPropertyNickNames(_taskContext.UserId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(nickNames, result);
        }

        [Fact]
        public async Task GetPropertyNickNames_ShouldReturnNull_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(repo => repo.GetPropertyNickNames(_taskContext.UserId))
                                  .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _propertyService.GetPropertyNickNames(_taskContext.UserId);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task DeleteProperty_ShouldReturnSuccess()
        {
            // Arrange
            _propertyRepositoryMock.Setup(repo => repo.DeleteProperty(1))
                                  .ReturnsAsync(1);

            // Act
            var result = await _propertyService.DeleteProperty(1);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task DeleteProperty_ShouldReturnMinusOne_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(repo => repo.DeleteProperty(1))
                                  .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _propertyService.DeleteProperty(1);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task GetAllProperties_ShouldReturnAllRecords_WhenShowAllRecordsIsTrue()
        {
            // Arrange
            var searchCriteria = new SearchCriteriaModel { OffSet = 0, Limit = 10, ShowAllRecords = true };
            var properties = new List<ViewUserProperties>
            {
                new ViewUserProperties { UserId = _taskContext.UserId, PropertyId = 1 },
                new ViewUserProperties { UserId = _taskContext.UserId, PropertyId = 2 },
                new ViewUserProperties { UserId = _taskContext.UserId, PropertyId = 3 }
            };

            _propertyRepositoryMock.Setup(repo => repo.GetAllProperties(_taskContext.UserId, searchCriteria.OffSet, searchCriteria.Limit, searchCriteria.ShowAllRecords))
                                   .ReturnsAsync(properties);

            _propertyRepositoryMock.Setup(repo => repo.GetPropertyImages(It.IsAny<List<int>>())).ReturnsAsync(new List<PropertyImages>()); // Mock the image fetch

            // Act
            var result = await _propertyService.GetAllProperties(searchCriteria);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(properties, result);
            Assert.Equal(3, result.Count);
            _propertyRepositoryMock.Verify(repo => repo.GetAllProperties(_taskContext.UserId, searchCriteria.OffSet, searchCriteria.Limit, searchCriteria.ShowAllRecords), Times.Once);
        }

        [Fact]
        public async Task GetAllProperties_ShouldReturnEmptyList_WhenExceptionThrown()
        {
            // Arrange
            var searchCriteria = new SearchCriteriaModel { OffSet = 0, Limit = 10, ShowAllRecords = false };
            _propertyRepositoryMock.Setup(repo => repo.GetAllProperties(_taskContext.UserId, searchCriteria.OffSet, searchCriteria.Limit, searchCriteria.ShowAllRecords))
                                  .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _propertyService.GetAllProperties(searchCriteria);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_ShouldReturnCorrectCounts_WhenRepositoryReturnsData()
        {
            // Arrange
            var userId = 1;
            var countsByAgency = new List<ViewAgencyPropertyCount>
               {
                   new ViewAgencyPropertyCount
                   {
                       AgencyId = "AGENCY1",
                       BusinessRegisteredName = "Agency One Ltd",
                       ActiveCount = 5,
                       InactiveCount = 3
                   },
                   new ViewAgencyPropertyCount
                   {
                       AgencyId = "SELF_SOURCE",
                       ActiveCount = 2,
                       InactiveCount = 1
                   }
               };

            _propertyRepositoryMock
                .Setup(repo => repo.GetPropertyCountsByAgency(userId))
                .ReturnsAsync(countsByAgency);

            // Act
            var result = await _propertyService.GetPropertyCountsByAgency(userId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<ViewAgencyPropertyCount>>(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("AGENCY1", result[0].AgencyId);
            Assert.Equal("Agency One Ltd", result[0].BusinessRegisteredName);
            Assert.Equal(5, result[0].ActiveCount);
            Assert.Equal(3, result[0].InactiveCount);
            Assert.Equal("SELF_SOURCE", result[1].AgencyId);
            Assert.Equal(2, result[1].ActiveCount);
            Assert.Equal(1, result[1].InactiveCount);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_ShouldReturnEmptyList_WhenRepositoryThrowsException()
        {
            // Arrange
            var userId = 1;

            _propertyRepositoryMock
                .Setup(repo => repo.GetPropertyCountsByAgency(userId))
                .ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _propertyService.GetPropertyCountsByAgency(userId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<ViewAgencyPropertyCount>>(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_ShouldReturnEmptyList_WhenNoDataSources()
        {
            // Arrange
            var emptyCounts = new List<ViewAgencyPropertyCount>();

            _propertyRepositoryMock.Setup(repo => repo.GetPropertyCountsByAgency(_taskContext.UserId))
                                  .ReturnsAsync(emptyCounts);

            // Act
            var result = await _propertyService.GetPropertyCountsByAgency(_taskContext.UserId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<ViewAgencyPropertyCount>>(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_ShouldReturnEmptyList_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(repo => repo.GetPropertyCountsByAgency(_taskContext.UserId))
                                  .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _propertyService.GetPropertyCountsByAgency(_taskContext.UserId);

            // Assert
            Assert.NotNull(result);
            Assert.IsType<List<ViewAgencyPropertyCount>>(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task UpdatePropertyStatus_ShouldReturnResult_WhenRepositorySucceeds()
        {
            // Arrange
            var model = new PropertyStatusModel { UserId = 1, PropertyId = 2, IsActive = true };
            _propertyRepositoryMock.Setup(r => r.UpdatePropertyStatus(model.UserId, model.PropertyId, model.IsActive)).ReturnsAsync(1);

            // Act
            var result = await _propertyService.UpdatePropertyStatus(model);

            // Assert
            Assert.Equal(1, result);
            _propertyRepositoryMock.Verify(r => r.UpdatePropertyStatus(model.UserId, model.PropertyId, model.IsActive), Times.Once);
        }

        [Fact]
        public async Task UpdatePropertyStatus_ShouldReturnMinusOne_AndLogError_WhenExceptionThrown()
        {
            // Arrange
            var model = new PropertyStatusModel { UserId = 1, PropertyId = 2, IsActive = true };
            _propertyRepositoryMock.Setup(r => r.UpdatePropertyStatus(model.UserId, model.PropertyId, model.IsActive)).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.UpdatePropertyStatus(model);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_ShouldReturnResult_WhenRepositorySucceeds()
        {
            // Arrange
            var model = new PropertyPortfolioModel { UserId = 1, PropertyId = 2, NicknameId = 3, Nickname = "Test" };
            _propertyRepositoryMock.Setup(r => r.UpdatePropertyPortfolio(model.UserId, model.PropertyId, model.NicknameId, model.Nickname)).ReturnsAsync(1);

            // Act
            var result = await _propertyService.UpdatePropertyPortfolio(model);

            // Assert
            Assert.Equal(1, result);
            _propertyRepositoryMock.Verify(r => r.UpdatePropertyPortfolio(model.UserId, model.PropertyId, model.NicknameId, model.Nickname), Times.Once);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_ShouldReturnMinusOne_AndLogError_WhenExceptionThrown()
        {
            // Arrange
            var model = new PropertyPortfolioModel { UserId = 1, PropertyId = 2, NicknameId = 3, Nickname = "Test" };
            _propertyRepositoryMock.Setup(r => r.UpdatePropertyPortfolio(model.UserId, model.PropertyId, model.NicknameId, model.Nickname)).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.UpdatePropertyPortfolio(model);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task GetMaintenanceDetails_ReturnsMappedList_WhenRepositorySucceeds()
        {
            // Arrange
            var entities = new List<ViewMaintenanceDetail> { new ViewMaintenanceDetail { PropertyId = 1 } };
            var mapped = new List<MaintenanceDetailModel> { new MaintenanceDetailModel { PropertyId = "1" } };
            _propertyRepositoryMock.Setup(r => r.GetMaintenanceDetails(1, "mgmt", null)).ReturnsAsync(entities);
            _mapperMock.Setup(m => m.Map<List<MaintenanceDetailModel>>(entities)).Returns(mapped);

            // Act
            var result = await _propertyService.GetMaintenanceDetails(1, "mgmt", null);

            // Assert
            Assert.Equal(mapped, result);
        }

        [Fact]
        public async Task GetMaintenanceDetails_ReturnsEmptyList_WhenRepositoryReturnsNull()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetMaintenanceDetails(1, "mgmt", null)).ReturnsAsync((List<ViewMaintenanceDetail>)null);

            // Act
            var result = await _propertyService.GetMaintenanceDetails(1, "mgmt", null);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetMaintenanceDetails_ReturnsEmptyList_AndLogsError_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetMaintenanceDetails(1, "mgmt", null)).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.GetMaintenanceDetails(1, "mgmt", null);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetCompliance_ReturnsMappedList_WhenRepositorySucceeds()
        {
            // Arrange
            var entities = new List<ComplianceDetail> { new ComplianceDetail { PropertyId = 1 } };
            var mapped = new List<ComplianceDetailModel> { new ComplianceDetailModel { PropertyId = "1" } };
            _propertyRepositoryMock.Setup(r => r.GetCompliance("mgmt", 1)).ReturnsAsync(entities);
            _mapperMock.Setup(m => m.Map<List<ComplianceDetailModel>>(entities)).Returns(mapped);

            // Act
            var result = await _propertyService.GetCompliance("mgmt", 1);

            // Assert
            Assert.Equal(mapped, result);
        }

        [Fact]
        public async Task GetCompliance_ReturnsEmptyList_WhenRepositoryReturnsNull()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetCompliance("mgmt", 1)).ReturnsAsync((List<ComplianceDetail>)null);

            // Act
            var result = await _propertyService.GetCompliance("mgmt", 1);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetCompliance_ReturnsEmptyList_AndLogsError_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetCompliance("mgmt", 1)).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.GetCompliance("mgmt", 1);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetInspections_ReturnsMappedList_WhenRepositorySucceeds()
        {
            // Arrange
            var entities = new List<InspectionDetail> { new InspectionDetail { PropertyId = 1 } };
            var mapped = new List<InspectionDetailModel> { new InspectionDetailModel { PropertyId = 1 } };
            _propertyRepositoryMock.Setup(r => r.GetInspections(1, "tenancy", null)).ReturnsAsync(entities);
            _mapperMock.Setup(m => m.Map<List<InspectionDetailModel>>(entities)).Returns(mapped);

            // Act
            var result = await _propertyService.GetInspections(1, "tenancy", null);

            // Assert
            Assert.Equal(mapped, result);
        }

        [Fact]
        public async Task GetInspections_ReturnsEmptyList_WhenRepositoryReturnsNull()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetInspections(1, "tenancy", null)).ReturnsAsync((List<InspectionDetail>)null);

            // Act
            var result = await _propertyService.GetInspections(1, "tenancy", null);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetInspections_ReturnsEmptyList_AndLogsError_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetInspections(1, "tenancy", null)).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.GetInspections(1, "tenancy", null);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetDocuments_ReturnsList_WhenRepositorySucceeds()
        {
            // Arrange
            var request = new GetDocumentRequestModel { ManagementId = "mgmt" };
            var docs = new List<UserPropertyDocumentDetail> { new UserPropertyDocumentDetail { PropertyId = 1 } };
            _propertyRepositoryMock.Setup(r => r.GetDocument(request, _taskContext.UserId)).ReturnsAsync(docs);

            // Act
            var result = await _propertyService.GetDocuments(request);

            // Assert
            Assert.Equal(docs, result);
        }

        [Fact]
        public async Task GetDocuments_ReturnsEmptyList_WhenRepositoryReturnsNull()
        {
            // Arrange
            var request = new GetDocumentRequestModel { ManagementId = "mgmt" };
            _propertyRepositoryMock.Setup(r => r.GetDocument(request, _taskContext.UserId)).ReturnsAsync((List<UserPropertyDocumentDetail>)null);

            // Act
            var result = await _propertyService.GetDocuments(request);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetDocuments_ReturnsEmptyList_AndLogsError_WhenExceptionThrown()
        {
            // Arrange
            var request = new GetDocumentRequestModel { ManagementId = "mgmt" };
            _propertyRepositoryMock.Setup(r => r.GetDocument(request, _taskContext.UserId)).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.GetDocuments(request);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_ReturnsMapped_WhenRepositoryReturnsEntity()
        {
            // Arrange
            var entity = new PropertyManagerWithAgencyDetails { PropertyManagerInformationId = 1 };
            var mapped = new PropertyManagerViewModel { PropertyManagerInformationId = 1 };
            _propertyRepositoryMock.Setup(r => r.GetPropertyManagerInformation("mgmt", 1, "src")).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<PropertyManagerViewModel>(entity)).Returns(mapped);

            // Act
            var result = await _propertyService.GetPropertyManagerInformation("mgmt", 1, "src");

            // Assert
            Assert.Equal(mapped, result);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_ReturnsNull_AndLogsWarning_WhenEntityIsNull()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetPropertyManagerInformation("mgmt", 1, "src")).ReturnsAsync((PropertyManagerWithAgencyDetails)null);

            // Act
            var result = await _propertyService.GetPropertyManagerInformation("mgmt", 1, "src");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_ReturnsNull_AndLogsError_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetPropertyManagerInformation("mgmt", 1, "src")).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.GetPropertyManagerInformation("mgmt", 1, "src");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_ReturnsMapped_WhenRepositoryReturnsEntity()
        {
            // Arrange
            var entity = new PropertyFinancialWithAgencyDetails { PropertyFinancialInformationId = 1 };
            var mapped = new PropertyFinancialViewModel { PropertyFinancialInformationId = 1 };
            _propertyRepositoryMock.Setup(r => r.GetPropertyFinancialInformation("mgmt", 1, "src")).ReturnsAsync(entity);
            _mapperMock.Setup(m => m.Map<PropertyFinancialViewModel>(entity)).Returns(mapped);

            // Act
            var result = await _propertyService.GetPropertyFinancialInformation("mgmt", 1, "src");

            // Assert
            Assert.Equal(mapped, result);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_ReturnsNull_WhenEntityIsNull()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetPropertyFinancialInformation("mgmt", 1, "src")).ReturnsAsync((PropertyFinancialWithAgencyDetails)null);

            // Act
            var result = await _propertyService.GetPropertyFinancialInformation("mgmt", 1, "src");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_ReturnsNull_AndLogsError_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetPropertyFinancialInformation("mgmt", 1, "src")).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.GetPropertyFinancialInformation("mgmt", 1, "src");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_ReturnsResult_WhenRepositorySucceeds()
        {
            // Arrange
            var response = new TenanciesTenantDetailResponse { PropertyId = 1 };
            _propertyRepositoryMock.Setup(r => r.GetTenantOwnerDetail("tenancy", "src", 1)).ReturnsAsync(response);

            // Act
            var result = await _propertyService.GetTenantOwnerDetail("tenancy", "src", 1);

            // Assert
            Assert.Equal(response, result);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_ReturnsNull_AndLogsError_WhenExceptionThrown()
        {
            // Arrange
            _propertyRepositoryMock.Setup(r => r.GetTenantOwnerDetail("tenancy", "src", 1)).Throws(new Exception("DB error"));

            // Act
            var result = await _propertyService.GetTenantOwnerDetail("tenancy", "src", 1);

            // Assert
            Assert.Null(result);
        }
    }
}
