﻿using MRI.OTA.Application.Models;
using MRI.OTA.Application.Validators;

namespace MRI.OTA.UnitTestCases.Validators
{
    public class UserProfileSettingsModelValidatorTest
    {
        private readonly UserProfileSettingsModelValidator _validator = new UserProfileSettingsModelValidator();

        [Fact]
        public void Should_Have_Error_When_UserId_Is_Zero()
        {
            var model = new UserProfileSettingsModel { UserId = 0 };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "UserId" &&
                e.ErrorMessage == "User ID must be greater than 0"
            );
        }

        [Fact]
        public void Should_Not_Have_Error_When_UserId_Is_Valid()
        {
            var model = new UserProfileSettingsModel { UserId = 1 };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "UserId");
        }

        [Fact]
        public void Should_Have_Error_When_Email_Is_Invalid()
        {
            var model = new UserProfileSettingsModel { UserId = 1, PreferredContactEmail = "invalid-email" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "PreferredContactEmail" &&
                e.ErrorMessage == "Please provide a valid email address"
            );
        }

        [Fact]
        public void Should_Have_Error_When_Email_Too_Long()
        {
            var email = new string('a', 250) + "@test.com";
            var model = new UserProfileSettingsModel { UserId = 1, PreferredContactEmail = email };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "PreferredContactEmail" &&
                e.ErrorMessage == "Email cannot exceed 255 characters"
            );
        }

        [Fact]
        public void Should_Not_Have_Error_When_Email_Is_Valid_And_Length_Is_Valid()
        {
            var model = new UserProfileSettingsModel { UserId = 1, PreferredContactEmail = "<EMAIL>" };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "PreferredContactEmail");
        }

        [Fact]
        public void Should_Not_Have_Error_When_Email_Is_Null_Or_Empty()
        {
            var model = new UserProfileSettingsModel { UserId = 1, PreferredContactEmail = null };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "PreferredContactEmail");
        }

        [Fact]
        public void Should_Have_Error_When_PushNotificationEnabled_Is_Null()
        {
            var model = new UserProfileSettingsModel { UserId = 1, PushNotificationEnabled = null };
            var result = _validator.Validate(model);
            // This rule only applies if HasValue is true, so this should not error
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "PushNotificationEnabled");
        }

        [Fact]
        public void Should_Not_Have_Error_When_PushNotificationEnabled_Is_True()
        {
            var model = new UserProfileSettingsModel { UserId = 1, PushNotificationEnabled = true };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "PushNotificationEnabled");
        }

        [Fact]
        public void Should_Not_Have_Error_When_PushNotificationEnabled_Is_False()
        {
            var model = new UserProfileSettingsModel { UserId = 1, PushNotificationEnabled = false };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "PushNotificationEnabled");
        }

        [Fact]
        public void Should_Have_Error_When_EmailNotificationEnabled_Is_Null()
        {
            var model = new UserProfileSettingsModel { UserId = 1, EmailNotificationEnabled = null };
            var result = _validator.Validate(model);
            // This rule only applies if HasValue is true, so this should not error
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "EmailNotificationEnabled");
        }

        [Fact]
        public void Should_Not_Have_Error_When_EmailNotificationEnabled_Is_True()
        {
            var model = new UserProfileSettingsModel { UserId = 1, EmailNotificationEnabled = true };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "EmailNotificationEnabled");
        }

        [Fact]
        public void Should_Not_Have_Error_When_EmailNotificationEnabled_Is_False()
        {
            var model = new UserProfileSettingsModel { UserId = 1, EmailNotificationEnabled = false };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "EmailNotificationEnabled");
        }
    }
}
