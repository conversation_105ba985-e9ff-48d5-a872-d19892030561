﻿using MRI.OTA.Application.Models;
using MRI.OTA.Application.Validators;

namespace MRI.OTA.UnitTestCases.Validators
{
    public class IdTokenModelValidatorTest
    {
        private readonly IdTokenModelValidator _validator = new IdTokenModelValidator();

        [Fact]
        public void Should_Have_Error_When_IdToken_Is_Empty()
        {
            var model = new IdTokenModel { IdToken = "" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "IdToken" &&
                e.ErrorMessage == "ID token is required"
            );
        }

        [Fact]
        public void Should_Have_Error_When_IdToken_Is_Null()
        {
            var model = new IdTokenModel { IdToken = null };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "IdToken" &&
                e.ErrorMessage == "ID token is required"
            );
        }

        [Fact]
        public void Should_Have_Error_When_IdToken_Is_Not_Jwt_Format()
        {
            var model = new IdTokenModel { IdToken = "not.a.jwt" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "IdToken" &&
                e.ErrorMessage == "ID token must be a valid JWT format"
            );
        }

        [Fact]
        public void Should_Not_Have_Error_When_IdToken_Is_Valid_Jwt()
        {
            // Create a valid JWT-like string (three base64url-encoded parts)
            string base64url = System.Convert.ToBase64String(System.Text.Encoding.UTF8.GetBytes("header")).TrimEnd('=').Replace('+', '-').Replace('/', '_');
            string jwt = $"{base64url}.{base64url}.{base64url}";
            var model = new IdTokenModel { IdToken = jwt };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "IdToken");
        }
    }
}
