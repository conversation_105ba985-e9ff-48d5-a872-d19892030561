using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication
{
    /// <summary>
    /// Resolver for authentication strategies
    /// </summary>
    public class AuthenticationStrategyResolver
    {
        private readonly IEnumerable<IAuthenticationStrategy> _strategies;
        private readonly ILogger _logger;
        private readonly IResponseGenerator _responseGenerator;

        /// <summary>
        /// Constructor for AuthenticationStrategyResolver
        /// </summary>
        /// <param name="strategies">The authentication strategies</param>
        /// <param name="logger">The logger</param>
        /// <param name="responseGenerator">The response generator</param>
        public AuthenticationStrategyResolver(
            IEnumerable<IAuthenticationStrategy> strategies, 
            ILogger logger,
            IResponseGenerator responseGenerator)
        {
            _strategies = strategies ?? throw new ArgumentNullException(nameof(strategies));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _responseGenerator = responseGenerator ?? throw new ArgumentNullException(nameof(responseGenerator));
        }

        /// <summary>
        /// Resolves and executes the appropriate authentication strategy
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task<bool> ResolveAndExecuteStrategy(HttpContext context)
        {
            foreach (var strategy in _strategies)
            {
                if (strategy.CanHandle(context))
                {
                    var (handled, authenticated) = await strategy.AuthenticateAsync(context);
                    if (handled)
                    {
                        return true;
                    }
                }
            }

            // If no strategy handled the request, handle it as unauthenticated
            _logger.LogWarning("Unauthenticated request to {Path}", context.Request.Path);
            await _responseGenerator.WriteUnauthorizedResponse(
                context,
                "Authentication required. Please provide valid credentials.",
                StatusCodes.Status401Unauthorized);
            
            return true;
        }
    }
}
