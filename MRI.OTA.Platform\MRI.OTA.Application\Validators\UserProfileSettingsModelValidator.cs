using FluentValidation;
using MRI.OTA.Application.Models;

namespace MRI.OTA.Application.Validators
{
    /// <summary>
    /// Validator for UserProfileSettingsModel
    /// </summary>
    public class UserProfileSettingsModelValidator : AbstractValidator<UserProfileSettingsModel>
    {
        public UserProfileSettingsModelValidator()
        {
            RuleFor(x => x.UserId)
                .GreaterThan(0)
                .WithMessage("User ID must be greater than 0");

            RuleFor(x => x.PreferredContactEmail)
                .EmailAddress()
                .WithMessage("Please provide a valid email address")
                .MaximumLength(255)
                .WithMessage("Email cannot exceed 255 characters")
                .When(x => !string.IsNullOrEmpty(x.PreferredContactEmail));

            RuleFor(x => x.PushNotificationEnabled)
                .NotNull()
                .WithMessage("Push notification preference must be specified")
                .When(x => x.PushNotificationEnabled.HasValue);

            RuleFor(x => x.EmailNotificationEnabled)
                .NotNull()
                .WithMessage("Email notification preference must be specified")
                .When(x => x.EmailNotificationEnabled.HasValue);
        }
    }
} 