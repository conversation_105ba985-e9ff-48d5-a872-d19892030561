using MRI.OTA.Application.Services;

namespace MRI.OTA.Application.Interfaces.Integration
{
    /// <summary>
    /// Interface for batch operation logging with Application Insights integration
    /// </summary>
    public interface IBatchOperationLogger
    {
        /// <summary>
        /// Create a batch operation context with correlation ID and telemetry tracking
        /// </summary>
        /// <param name="operationName">Name of the batch operation</param>
        /// <param name="dataSourceName">Name of the data source being processed</param>
        /// <param name="batchSize">Size of the batch</param>
        /// <returns>BatchOperationContext for tracking</returns>
        BatchOperationContext CreateBatchOperationContext(string operationName, string dataSourceName = null, int? batchSize = null);

        /// <summary>
        /// Log batch processing start for a specific data source
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="dataSourceName">Name of the data source</param>
        /// <param name="totalBatches">Total number of batches to process</param>
        void LogBatchProcessingStart(BatchOperationContext context, string dataSourceName, int totalBatches);

        /// <summary>
        /// Log individual batch execution
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="batchSize">Size of current batch</param>
        /// <param name="itemsProcessed">Number of items processed in this batch</param>
        void LogBatchExecution(BatchOperationContext context, int batchIndex, int batchSize, int itemsProcessed);

        /// <summary>
        /// Log individual batch execution with specific entity IDs
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="entityIds">Array of entity IDs being processed in this batch</param>
        /// <param name="entityType">Type of entity (e.g., AgencyId, ManagementId, TenancyId)</param>
        void LogBatchExecutionWithIds(BatchOperationContext context, int batchIndex, string[] entityIds, string entityType);

        /// <summary>
        /// Log batch execution success
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="rowsAffected">Number of rows affected by the batch</param>
        void LogBatchSuccess(BatchOperationContext context, int batchIndex, int rowsAffected);

        /// <summary>
        /// Log batch execution success with specific entity IDs
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="rowsAffected">Number of rows affected by the batch</param>
        /// <param name="entityIds">Array of entity IDs that were successfully processed</param>
        /// <param name="entityType">Type of entity processed</param>
        void LogBatchSuccessWithIds(BatchOperationContext context, int batchIndex, int rowsAffected, string[] entityIds, string entityType);

        /// <summary>
        /// Log batch execution error
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="exception">Exception that occurred</param>
        /// <param name="additionalInfo">Additional error context</param>
        void LogBatchError(BatchOperationContext context, int batchIndex, Exception exception, string additionalInfo = null);

        /// <summary>
        /// Log batch execution error with specific entity IDs
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="batchIndex">Current batch index</param>
        /// <param name="exception">Exception that occurred</param>
        /// <param name="entityIds">Array of entity IDs that failed processing</param>
        /// <param name="entityType">Type of entity that failed</param>
        /// <param name="additionalInfo">Additional error context</param>
        void LogBatchErrorWithIds(BatchOperationContext context, int batchIndex, Exception exception, string[] entityIds, string entityType, string additionalInfo = null);

        /// <summary>
        /// Log batch operation completion
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="success">Whether the operation was successful</param>
        /// <param name="totalItemsProcessed">Total number of items processed across all batches</param>
        /// <param name="totalBatches">Total number of batches processed</param>
        void LogBatchOperationComplete(BatchOperationContext context, bool success, int totalItemsProcessed = 0, int totalBatches = 0);

        /// <summary>
        /// Log data source processing metrics
        /// </summary>
        /// <param name="context">Batch operation context</param>
        /// <param name="dataSourceName">Name of the data source</param>
        /// <param name="totalRecords">Total records processed for this data source</param>
        /// <param name="successfulBatches">Number of successful batches</param>
        /// <param name="failedBatches">Number of failed batches</param>
        void LogDataSourceMetrics(BatchOperationContext context, string dataSourceName, int totalRecords, int successfulBatches, int failedBatches);
    }
} 