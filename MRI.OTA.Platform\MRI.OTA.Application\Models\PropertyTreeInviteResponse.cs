﻿
namespace MRI.OTA.Application.Models
{
    public class PropertyTreeInviteResponse
    {

        /// <summary>
        /// PropertyId
        /// </summary>
        public string? PropertyId { get; set; }


        public string? UserId { get; set; }

        /// <summary>
        /// AgencyId
        /// </summary>
        public string? AgencyId { get; set; }

        /// <summary>
        /// ManagementId
        /// </summary>
        public string? ManagementId { get; set; }

        /// <summary>
        /// TenancyId
        /// </summary>
        public string? TenancyId { get; set; }
        /// <summary>
        /// OwnershipName
        /// </summary>
        public string? OwnershipName { get; set; }

        /// <summary>
        /// PropertyName
        /// </summary>
        public string? PropertyName { get; set; }

        /// <summary>
        /// SRCServiceId
        /// </summary>
        public string? SRCServiceId { get; set; }

        /// <summary>
        /// OccupancyType
        /// </summary>
        public string? OccupancyType { get; set; }

        /// <summary>
        /// Suburb
        /// </summary>
        public string? PTSuburb { get; set; }

        /// <summary>
        /// UserRelationshipToProperty
        /// </summary>
        public string?  UserRelationshipToProperty { get; set; }

        /// <summary>
        /// PropertyType
        /// </summary>
        public string? PropertyType { get; set; }

        /// <summary>
        /// LotNumber
        /// </summary>
        public string? LotNumber { get; set; }

        /// <summary>
        /// OccupancyStatus
        /// </summary>
        public string? OccupancyStatus { get; set; }


        /// <summary>
        /// DefaultImageLink
        /// </summary>
        public string? DefaultImageLink { get; set; }

        /// <summary>
        /// SRCManagementId
        /// </summary>
        public string? SRCAgencyId { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; } = true;

        /// <summary>
        /// Bedrooms
        /// </summary>
        public int? Bedrooms { get; set; }

        /// <summary>
        /// BathRooms
        /// </summary>
        public int? BathRooms { get; set; }

        /// <summary>
        /// CarSpaces
        /// </summary>
        public int? CarSpaces { get; set; }

        /// <summary>
        /// FloorArea
        /// </summary>
        public int? FloorArea { get; set; }

        /// <summary>
        /// LandArea
        /// </summary>
        public int? LandArea { get; set; }

        /// <summary>
        /// Description
        /// </summary>
        public string? Description { get; set; }

        /// <summary>
        /// PropertyOwnershipDetailsId
        /// </summary>
        public int? PropertyOwnershipDetailsId { get; set; }

        /// <summary>
        /// PropertyRelationshipId
        /// </summary>
        public int? PropertyRelationshipId { get; set; }

        /// <summary>
        /// OwnershipPercentage
        /// </summary>
        public decimal? OwnershipPercentage { get; set; } = 100; // Default value
        /// <summary>
        /// OwnershipStartDate
        /// </summary>
        public DateTime? OwnershipStartDate { get; set; }

        /// <summary>
        /// OwnershipEndDate
        /// </summary>
        public DateTime? OwnershipEndDate { get; set; }
        /// <summary>
        /// PurchasePrice
        /// </summary>
        public decimal? PurchasePrice { get; set; }
        /// <summary>
        /// RentAmount
        /// </summary>
        public decimal? RentAmount { get; set; }
        /// <summary>
        /// LeaseStartDate
        /// </summary>
        public DateTime? LeaseStartDate { get; set; }
        /// <summary>
        /// LeaseEndDate
        /// </summary>
        public DateTime? LeaseEndDate { get; set; }
        /// <summary>
        /// SecurityDeposit
        /// </summary>
        public decimal? SecurityDeposit { get; set; }
       
        /// <summary>
        /// StreetAddress
        /// </summary>
        public string? StreetAddress { get; set; }
        /// <summary>
        /// City
        /// </summary>
        public string? City { get; set; }
        /// <summary>
        /// State
        /// </summary>
        public int? StateID { get; set; }

        /// <summary>
        /// StateName
        /// </summary>
        public string? StateName { get; set; }
        /// <summary>
        /// Country
        /// </summary>
        public string? CountryCode { get; set; }

        /// <summary>
        /// CountryName
        /// </summary>
        public string? CountryName { get; set; }

        /// <summary>
        /// PostalCode
        /// </summary>
        public string? PostalCode { get; set; }
        /// <summary>
        /// RuralDelivery
        /// </summary>
        public string? RuralDelivery { get; set; }
        /// <summary>
        /// PostOfficeName
        /// </summary>
        public string? PostOfficeName { get; set; }

        /// <summary>
        /// AdministrativeArea
        /// </summary>
        public string? AdministrativeArea { get; set; }

        /// <summary>
        /// UserDataSourceId
        /// </summary>
        public int? UserDataSourceId { get; set; }

        /// <summary>
        /// DataSourceId
        /// </summary>
        public int? DataSourceId { get; set; }

        /// <summary>
        /// AccessKey
        /// </summary>
        public string? AccessKey { get; set; }

        /// <summary>
        /// AccessToken
        /// </summary>
        public string? AccessToken { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// PropertyManagerInformationId
        /// </summary>
        public int? PropertyManagerInformationId { get; set; }

        /// <summary>
        /// ManagementType
        /// </summary>
        public string? ManagementType { get; set; }
        /// <summary>
        /// AgencyName
        /// </summary>
        public string? AgencyName { get; set; }
        /// <summary>
        /// TenancyContactName    
        /// </summary>
        public string? PropertyManagerName { get; set; }
        /// <summary>
        /// PropertyManagerMobile
        /// </summary>
        public string? PropertyManagerMobile { get; set; }
        /// <summary>
        /// PropertyManagerPhone
        /// </summary>
        public string? PropertyManagerPhone { get; set; }
        /// <summary>
        /// PropertyManagerEmail
        /// </summary>
        public string? PropertyManagerEmail { get; set; }
        /// <summary>
        /// AuthorityStartDate
        /// </summary>
        public DateTime? AuthorityStartDate { get; set; }
        /// <summary>
        /// AuthorityEndDate
        /// </summary>
        public DateTime? AuthorityEndDate { get; set; }
        /// <summary>
        /// Ownership
        /// </summary>
        public string? Ownership { get; set; }
        /// <summary>
        /// ExpenditureLimit
        /// </summary>
        public decimal? ExpenditureLimit { get; set; }
        /// <summary>
        ///  ExpenditureNotes   
        /// </summary>
        public string? ExpenditureNotes { get; set; }

        /// <summary>
        /// PropertyFinancialInformationId
        /// </summary>
        public int? PropertyFinancialInformationId { get; set; }
        /// <summary>
        /// TenancyName
        /// </summary>
        public string? TenancyName { get; set; }
        /// <summary>
        /// LeaseStart
        /// </summary>
        public DateTime? LeaseStart { get; set; }
        /// <summary>
        /// LeaseEnd
        /// </summary>
        public DateTime? LeaseEnd { get; set; }
        /// <summary>
        /// VacateDate
        /// </summary>
        public DateTime? VacateDate { get; set; }
        /// <summary>
        /// Rent
        /// </summary>
        public decimal? Rent { get; set; }
        /// <summary>
        /// IncreaseRent
        /// </summary>
        public decimal? IncreaseRent { get; set; }
        /// <summary>
        /// IncreaseDate
        /// </summary>
        public DateTime? IncreaseDate { get; set; }
        /// <summary>
        /// OptionsDate
        /// </summary>
        public DateTime? OptionsDate { get; set; }
        /// <summary>
        /// OptionsDetail
        /// </summary>
        public string? OptionsDetail { get; set; }
        /// <summary>
        /// Arrears
        /// </summary>
        public decimal? Arrears { get; set; }
        /// <summary>
        /// PayToDate
        /// </summary>
        public DateTime? PayToDate { get; set; }
        /// <summary>
        /// AmountToVacate
        /// </summary>
        public decimal? AmountToVacate { get; set; }
        /// <summary>
        /// OutstandingInvoices
        /// </summary>
        public decimal? OutstandingInvoices { get; set; }
        /// <summary>
        /// InvoiceFeesArrears
        /// </summary>
        public decimal? InvoiceFeesArrears { get; set; }
        /// <summary>
        /// RentCharge
        /// </summary>
        public decimal? RentCharge { get; set; }
        /// <summary>
        /// WeeklyRent
        /// </summary>
        public decimal? WeeklyRent { get; set; }
        /// <summary>
        /// LastPaid
        /// </summary>
        public DateTime? LastPaid { get; set; }
        /// <summary>
        /// HeldFunds
        /// </summary>
        public decimal? HeldFunds { get; set; }

        /// <summary>
        /// PropertyRelationshipName
        /// </summary>
        public string? PropertyRelationshipName { get; set; }
        public string? StateCode { get; set; }
        public string? Currency { get; set; }
        public string? BuildingNumber { get; set; }
        public string? UserRelationshipStatus { get; set; }
        public string? Locale { get; set; }
        public string? Unit { get; set; }
        public string? StreetNumber { get; set; }
    }
}
