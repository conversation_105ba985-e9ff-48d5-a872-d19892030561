using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;
using System.Security.Claims;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies
{
    /// <summary>
    /// Strategy for handling Access token authentication
    /// </summary>
    public class AccessTokenStrategy : IAuthenticationStrategy
    {
        private readonly RequestDelegate _next;
        private readonly IJwtTokenValidator _jwtTokenValidator;
        private readonly ILogger _logger;
        private readonly IResponseGenerator _responseGenerator;

        /// <summary>
        /// Constructor for AccessTokenStrategy
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="jwtTokenValidator">The JWT token validator</param>
        /// <param name="logger">The logger</param>
        /// <param name="responseGenerator">The response generator</param>
        public AccessTokenStrategy(
            RequestDelegate next, 
            IJwtTokenValidator jwtTokenValidator, 
            ILogger logger, 
            IResponseGenerator responseGenerator)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _jwtTokenValidator = jwtTokenValidator ?? throw new ArgumentNullException(nameof(jwtTokenValidator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _responseGenerator = responseGenerator ?? throw new ArgumentNullException(nameof(responseGenerator));
        }

        /// <summary>
        /// Determines if this strategy can handle the current request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>True if this strategy can handle the request, false otherwise</returns>
        public bool CanHandle(HttpContext context)
        {
            var authAccessKey = context.Request.Headers["AccessKey"].ToString();
            var authAccessToken = context.Request.Headers["AccessToken"].ToString();
            return !string.IsNullOrEmpty(authAccessToken) && !string.IsNullOrEmpty(authAccessKey);
        }

        /// <summary>
        /// Authenticates the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>A tuple indicating if the request was handled and authenticated</returns>
        public async Task<(bool Handled, bool Authenticated)> AuthenticateAsync(HttpContext context)
        {
            var authAccessKey = context.Request.Headers["AccessKey"].ToString();
            var authAccessToken = context.Request.Headers["AccessToken"].ToString();

            try
            {
                _logger.LogInformation("Attempting access token authentication for request to {Path}", context.Request.Path);

                var isValid = await _jwtTokenValidator.ValidateAccessToken(authAccessKey, authAccessToken);

                if (isValid)
                {
                    // Set the user as authenticated to prevent 401 responses
                    var identity = new ClaimsIdentity(new Claim[] {
                        new Claim(ClaimTypes.Name, authAccessKey),
                        new Claim(ClaimTypes.Actor, authAccessToken)
                    }, "AccessToken");
                    
                    context.User = new ClaimsPrincipal(identity);
                    
                    // Log successful authentication
                    _logger.LogInformation("Access token authentication successful for {Path}", context.Request.Path);
                    
                    await _next(context);
                    return (true, true);
                }
                else
                {
                    _logger.LogWarning("Access token validation failed for {Path}", context.Request.Path);
                    await _responseGenerator.WriteUnauthorizedResponse(context, "Invalid access token", StatusCodes.Status401Unauthorized);
                    return (true, false);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Access Token validation failed");
                throw;
            }
        }
    }
}
