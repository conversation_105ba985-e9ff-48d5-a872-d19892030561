﻿using System;
using System.Net.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MRI.OTA.Integration.Http;
using MRI.OTA.Integration.Services;

namespace MRI.OTA.Integration.Configuration
{
    public static class IntegrationModule
    {
        public static void ConfigureIntegration(this IServiceCollection services, IConfiguration configuration)
        {
            services.AddHttpClient("PooledHttpClient")
                .ConfigurePrimaryHttpMessageHandler(() => new SocketsHttpHandler
                {
                    PooledConnectionLifetime = TimeSpan.FromMinutes(10),
                    MaxConnectionsPerServer = 10,
                    AutomaticDecompression = System.Net.DecompressionMethods.GZip | System.Net.DecompressionMethods.Deflate
                });
            services.AddSingleton<IHttpClientFactoryManager, HttpClientFactoryManager>();
            services.AddSingleton<IProxyService, ProxyService>();
        }
    }
}
