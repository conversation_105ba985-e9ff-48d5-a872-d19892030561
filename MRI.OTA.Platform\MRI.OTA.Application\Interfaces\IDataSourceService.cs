﻿using MRI.OTA.Application.Models;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.Application.Interfaces
{
    /// <summary>
    /// DataSource service
    /// </summary>
    public interface IDataSourceService : IBaseService<DataSource, DataSourceModel, int>
    {
        public Task<DataSourceModel> GetUserDataSource(string accessKey, string accessSecret);

        public Task<int> UpdateDataSource(string accessKey, string accessSecret, DataSourceUpdateModel dataSourceModel);

    }
}
