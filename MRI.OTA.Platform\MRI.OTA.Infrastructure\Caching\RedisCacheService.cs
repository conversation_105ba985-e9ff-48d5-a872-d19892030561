﻿﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using MRI.OTA.Infrastructure.Caching.Configuration;
using MRI.OTA.Infrastructure.Caching.Interfaces;
using System.Text.Json;

namespace MRI.OTA.Infrastructure.Caching
{
    /// <summary>
    /// Implementation of ICacheService using Redis
    /// </summary>
    public class RedisCacheService : ICacheService
    {
        private readonly IDistributedCache _distributedCache;
        private readonly RedisConfiguration _redisConfig;
        private readonly ILogger<RedisCacheService> _logger;
        private readonly JsonSerializerOptions _jsonOptions;

        /// <summary>
        /// Constructor for RedisCacheService
        /// </summary>
        /// <param name="distributedCache">The distributed cache instance</param>
        /// <param name="redisConfigOptions">Redis configuration options</param>
        /// <param name="logger">Logger instance</param>
        public RedisCacheService(
            IDistributedCache distributedCache,
            IOptions<RedisConfiguration> redisConfigOptions,
            ILogger<RedisCacheService> logger)
        {
            _distributedCache = distributedCache ?? throw new ArgumentNullException(nameof(distributedCache));
            _redisConfig = redisConfigOptions?.Value ?? throw new ArgumentNullException(nameof(redisConfigOptions));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            _jsonOptions = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true,
                WriteIndented = false
            };
        }

        /// <inheritdoc />
        public async Task<T?> GetAsync<T>(string key)
        {
            try
            {
                var cachedData = await _distributedCache.GetStringAsync(key);

                if (string.IsNullOrEmpty(cachedData))
                {
                    return default;
                }

                return JsonSerializer.Deserialize<T>(cachedData, _jsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error retrieving data from Redis cache for key {Key}", key);
                return default;
            }
        }

        /// <inheritdoc />
        public async Task SetAsync<T>(string key, T value, int? expirationMinutes = null)
        {
            if (value == null) return;

            try
            {
                var serializedData = JsonSerializer.Serialize(value, _jsonOptions);
                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(
                        expirationMinutes ?? _redisConfig.DefaultExpirationMinutes)
                };

                await _distributedCache.SetStringAsync(key, serializedData, options);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error setting data in Redis cache for key {Key}", key);
            }
        }

        /// <inheritdoc />
        public async Task RemoveAsync(string key)
        {
            try
            {
                await _distributedCache.RemoveAsync(key);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error removing data from Redis cache for key {Key}", key);
            }
        }

        /// <inheritdoc />
        public async Task<bool> ExistsAsync(string key)
        {
            try
            {
                var cachedData = await _distributedCache.GetStringAsync(key);
                return !string.IsNullOrEmpty(cachedData);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking existence in Redis cache for key {Key}", key);
                return false;
            }
        }

        /// <inheritdoc />
        public async Task<bool> RefreshExpirationAsync(string key, int? expirationMinutes = null)
        {
            try
            {
                var cachedData = await _distributedCache.GetStringAsync(key);
                
                if (string.IsNullOrEmpty(cachedData))
                {
                    return false;
                }

                var options = new DistributedCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(
                        expirationMinutes ?? _redisConfig.DefaultExpirationMinutes)
                };

                await _distributedCache.SetStringAsync(key, cachedData, options);
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing expiration in Redis cache for key {Key}", key);
                return false;
            }
        }

    }
}
