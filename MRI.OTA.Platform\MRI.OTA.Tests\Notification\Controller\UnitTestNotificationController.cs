﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.Controllers.Notification.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Services;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.UnitTestCases.Notification.Controller
{
    public class UnitTestNotificationController
    {
        private readonly Mock<INotificationService> _mockNotificationService;
        private readonly NotificationController _controller;
        private readonly Mock<INotificationRepository> _repoMock;
        private readonly NotificationService _service;

        public UnitTestNotificationController()
        {
            _mockNotificationService = new Mock<INotificationService>();
            _controller = new NotificationController(_mockNotificationService.Object);
            _repoMock = new Mock<INotificationRepository>();
            var loggerMock = new Mock<ILogger<NotificationService>>();
            var configMock = new Mock<IConfiguration>();
            _service = new NotificationService(loggerMock.Object, configMock.Object, _repoMock.Object);

        }

        #region SendNotification Tests

        [Fact]
        public async Task SendNotification_ReturnsOkResult_WhenNotificationIsSentSuccessfully()
        {
            // Arrange
            var notificationRequest = new NotificationRequestModel
            {
                Title = "Test Notification",
                Body = "This is a test notification",
                UserEmail = "<EMAIL>",
                DeviceToken = "test-device-token",
                Data = new Dictionary<string, string> { { "key", "value" } }
            };
            _mockNotificationService.Setup(service => service.SendPushNotificationAsync(notificationRequest))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.SendNotification(notificationRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Notification sent successfully", apiResponse.Message);
        }

        [Fact]
        public async Task SendNotification_ReturnsBadRequestResult_WhenNotificationIsNotSent()
        {
            // Arrange
            var notificationRequest = new NotificationRequestModel
            {
                Title = "Test Notification",
                Body = "This is a test notification",
                UserEmail = "<EMAIL>",
                DeviceToken = "test-device-token",
                Data = new Dictionary<string, string> { { "key", "value" } }
            };
            _mockNotificationService.Setup(service => service.SendPushNotificationAsync(notificationRequest))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.SendNotification(notificationRequest);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("Notification not sent", apiResponse.Message);
            Assert.Contains("The notification could not be sent.", apiResponse.Errors);
        }

        #endregion

        #region SendAllNotification Tests

        [Fact]
        public async Task SendAllNotification_ReturnsOkResult_WhenBroadcastNotificationIsSentSuccessfully()
        {
            // Arrange
            var notificationRequest = new NotificationRequestModel
            {
                Title = "Test Broadcast Notification",
                Body = "This is a test broadcast notification",
                UserEmail = "<EMAIL>",
                Data = new Dictionary<string, string> { { "key", "value" } }
            };
            _mockNotificationService.Setup(service => service.BroadcastPushNotificationAsync(notificationRequest))
                .ReturnsAsync(true);

            // Act
            var result = await _controller.SendAllNotification(notificationRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Notification sent successfully", apiResponse.Message);
        }

        [Fact]
        public async Task SendAllNotification_ReturnsBadRequestResult_WhenBroadcastNotificationIsNotSent()
        {
            // Arrange
            var notificationRequest = new NotificationRequestModel
            {
                Title = "Test Broadcast Notification",
                Body = "This is a test broadcast notification",
                UserEmail = "<EMAIL>",
                Data = new Dictionary<string, string> { { "key", "value" } }
            };
            _mockNotificationService.Setup(service => service.BroadcastPushNotificationAsync(notificationRequest))
                .ReturnsAsync(false);

            // Act
            var result = await _controller.SendAllNotification(notificationRequest);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("Notification not sent", apiResponse.Message);
            Assert.Contains("The notification could not be sent.", apiResponse.Errors);
        }

        [Fact]
        public async Task GetUsersForNotificationBySRCPropertyId_ReturnsEmptyList_WhenInputIsNullOrEmpty()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<NotificationService>>();
            var configMock = new Mock<IConfiguration>();
            var repoMock = new Mock<INotificationRepository>();
            var service = new NotificationService(loggerMock.Object, configMock.Object, repoMock.Object);

            // Act
            var resultNull = await service.GetUsersForNotificationBySRCId(Array.Empty<string>(), null!);
            var resultEmpty = await service.GetUsersForNotificationBySRCId(Array.Empty<string>(), null!);

            // Assert
            Assert.Empty(resultNull);
            Assert.Empty(resultEmpty);
        }

        [Fact]
        public async Task GetUsersForNotificationBySRCPropertyId_ReturnsNull_WhenRepositoryReturnsNullOrEmpty()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<NotificationService>>();
            var configMock = new Mock<IConfiguration>();
            var repoMock = new Mock<INotificationRepository>();
            var service = new NotificationService(loggerMock.Object, configMock.Object, repoMock.Object);

            var ids = new[] { "id1" };
            repoMock.Setup(r => r.GetUsersForNotificationBySRCId(ids, null!)).ReturnsAsync(new List<NotificationUserList>());

            // Act
            var resultNull = await service.GetUsersForNotificationBySRCId(ids, null!);

            repoMock.Setup(r => r.GetUsersForNotificationBySRCId(ids, null!)).ReturnsAsync(new List<NotificationUserList>());
            var resultEmpty = await service.GetUsersForNotificationBySRCId(ids, null!);

            // Assert
            Assert.Null(resultNull);
            Assert.Null(resultEmpty);
        }

        [Fact]
        public async Task GetUsersForNotificationBySRCPropertyId_ReturnsList_WhenRepositoryReturnsList()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<NotificationService>>();
            var configMock = new Mock<IConfiguration>();
            var repoMock = new Mock<INotificationRepository>();
            var service = new NotificationService(loggerMock.Object, configMock.Object, repoMock.Object);

            var ids = new[] { "id1" };
            var users = new List<(int, int, string, string)> { (1, 1, "dev1", "token1") };
            repoMock.Setup(r => r.GetUsersForNotificationBySRCId(ids, "maintenance")).ReturnsAsync(new List<NotificationUserList>());

            // Act
            var result = await service.GetUsersForNotificationBySRCId(ids, "maintenance");

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetUsersForNotificationBySRCPropertyId_ReturnsEmptyList_OnException()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<NotificationService>>();
            var configMock = new Mock<IConfiguration>();
            var repoMock = new Mock<INotificationRepository>();
            var service = new NotificationService(loggerMock.Object, configMock.Object, repoMock.Object);

            var ids = new[] { "id1" };
            repoMock.Setup(r => r.GetUsersForNotificationBySRCId(ids, null!)).ThrowsAsync(new Exception("db error"));

            // Act
            var result = await service.GetUsersForNotificationBySRCId(ids, null!);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetNotificationDetailByCategory_ReturnsNull_WhenCategoriesEmpty()
        {
            // Arrange
            var categories = Array.Empty<string>();

            // Act
            var result = await _service.GetNotificationDetailByCategory(categories);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetNotificationDetailByCategory_ReturnsNull_WhenRepositoryReturnsNull()
        {
            // Arrange
            var categories = new[] { "cat1" };
            _repoMock.Setup(r => r.GetNotificationDetailByCategory(categories)).ReturnsAsync((List<NotificationMaster>)null);

            // Act
            var result = await _service.GetNotificationDetailByCategory(categories);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task GetNotificationDetailByCategory_ReturnsList_WhenRepositoryReturnsList()
        {
            // Arrange
            var categories = new[] { "cat1" };
            var notifications = new List<NotificationMaster> { new NotificationMaster { NotificationId = 1 } };
            _repoMock.Setup(r => r.GetNotificationDetailByCategory(categories)).ReturnsAsync(notifications);

            // Act
            var result = await _service.GetNotificationDetailByCategory(categories);

            // Assert
            Assert.NotNull(result);
            Assert.Single(result);
            Assert.Equal(1, result[0].NotificationId);
        }

        [Fact]
        public async Task GetNotificationDetailByCategory_ReturnsNull_OnException()
        {
            // Arrange
            var categories = new[] { "cat1" };
            _repoMock.Setup(r => r.GetNotificationDetailByCategory(categories)).ThrowsAsync(new Exception("db error"));

            // Act
            var result = await _service.GetNotificationDetailByCategory(categories);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task SendPushNotificationAsync_ReturnsFalse_WhenDeviceTokenIsNull()
        {
            // Arrange
            var request = new NotificationRequestModel { DeviceToken = null };

            // Act
            var result = await _service.SendPushNotificationAsync(request);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SendPushNotificationAsync_ReturnsFalse_WhenDeviceTokenIsEmpty()
        {
            // Arrange
            var request = new NotificationRequestModel { DeviceToken = "" };

            // Act
            var result = await _service.SendPushNotificationAsync(request);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SendPushNotificationAsync_ReturnsFalse_WhenMessageSentAndUserIdNotPresent()
        {
            // Arrange
            var request = new NotificationRequestModel
            {
                DeviceToken = "token",
                Title = "t",
                Body = "b",
                UserId = null,
                NotificationId = null,
                Data = new Dictionary<string, string>()
            };

            // Act
            var result = await _service.SendPushNotificationAsync(request);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task BroadcastPushNotificationAsync_ReturnsFalse_WhenMessageSent()
        {
            // Arrange
            var request = new NotificationRequestModel
            {
                Title = "Test",
                Body = "Test body",
                Data = new Dictionary<string, string>()
            };

            // Act
            var result = await _service.BroadcastPushNotificationAsync(request);

            // Assert
            Assert.False(result);
        }
        #endregion
    }
}
