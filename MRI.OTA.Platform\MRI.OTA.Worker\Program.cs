using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Builder;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Hosting;
using MRI.OTA.Worker.Interface;
using MRI.OTA.Worker.Service;

var builder = FunctionsApplication.CreateBuilder(args);

builder.ConfigureFunctionsWebApplication();

builder.Services
    .AddApplicationInsightsTelemetryWorkerService()
    .ConfigureFunctionsApplicationInsights();

var telemetryConfiguration = TelemetryConfiguration.CreateDefault();
telemetryConfiguration.ConnectionString = Environment.GetEnvironmentVariable("ApplicationInsights__ConnectionString");
var telemetryClient = new TelemetryClient(telemetryConfiguration);

builder.Services.AddHttpClient("WorkerClient", client =>
{
    client.DefaultRequestHeaders.Add("Accept", "application/json");
    client.Timeout = TimeSpan.FromSeconds(Convert.ToInt32(Environment.GetEnvironmentVariable("WorkerTimeOut")));
});

builder.Services.AddSingleton(telemetryClient);
builder.Services.AddScoped<IAzureB2CTokenService, AzureB2CTokenService>();
builder.Services.AddScoped<ISyncPropertyTreeDataService, SyncPropertyTreeDataService>();

builder.Build().Run();
