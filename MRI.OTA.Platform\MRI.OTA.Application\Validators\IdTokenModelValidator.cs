using FluentValidation;
using MRI.OTA.Application.Models;

namespace MRI.OTA.Application.Validators
{
    /// <summary>
    /// Validator for IdTokenModel
    /// </summary>
    public class IdTokenModelValidator : AbstractValidator<IdTokenModel>
    {
        public IdTokenModelValidator()
        {
            RuleFor(x => x.IdToken)
                .NotEmpty()
                .WithMessage("ID token is required")
                .Must(BeValidJwtFormat)
                .WithMessage("ID token must be a valid JWT format");
        }

        /// <summary>
        /// Validates basic JWT format (three parts separated by dots)
        /// </summary>
        /// <param name="token">The token to validate</param>
        /// <returns>True if token has basic JWT structure</returns>
        private bool BeValidJwtFormat(string? token)
        {
            if (string.IsNullOrEmpty(token))
                return false;

            var parts = token.Split('.');
            return parts.Length == 3 && 
                   parts.All(part => !string.IsNullOrEmpty(part) && IsBase64UrlEncoded(part));
        }

        /// <summary>
        /// Checks if a string is base64 URL encoded
        /// </summary>
        /// <param name="input">The string to check</param>
        /// <returns>True if string appears to be base64 URL encoded</returns>
        private bool IsBase64UrlEncoded(string input)
        {
            try
            {
                // Convert base64url to base64
                var base64 = input.Replace('-', '+').Replace('_', '/');
                switch (base64.Length % 4)
                {
                    case 2: base64 += "=="; break;
                    case 3: base64 += "="; break;
                }
                Convert.FromBase64String(base64);
                return true;
            }
            catch
            {
                return false;
            }
        }
    }
} 