﻿using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Helper;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;
using System.Text;
using static MRI.OTA.Common.Constants.Constants;

namespace MRI.OTA.DBCore.Repositories
{
    public class NotificationRepository : BaseRepository<NotificationMaster, int>, INotificationRepository
    {
        private readonly ILogger<IntegrationRepository> _logger;
        protected readonly IDapperWrapper _dapperWrapper;

        public NotificationRepository(IDbConnectionFactory dbConnection, ILogger<IntegrationRepository> logger, IDapperWrapper dapperWrapper)
            : base(dbConnection, logger, dapperWrapper)
        {
            _logger = logger;
            _dapperWrapper = dapperWrapper;
        }

        public async Task<List<NotificationMaster>> GetNotificationDetailByCategory(string[] categories)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT [NotificationId], [Category], [Title], [Body], [Priority], [Path] ");
                query.Append($"FROM {Constants.NotificationMasterTableName} ");
                query.Append($"WHERE Category IN @Category");

                var parameters = new { Category = categories };

                var result = await QueryAsync<NotificationMaster>(query.ToString(), parameters);
                return result.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting notification data for category {Category}", categories);
                return null;
            }
        }
        public async Task<int> AddUserNotificationDetails(UserNotificationDetails userNotificationDetails)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT TOP(1) UserNotificationDetailId FROM UserNotificationDetails ");
                query.Append($"WHERE NotificationId = @NotificationId AND UserId = @UserId AND Body = @Body ");
                query.Append($"AND CreatedDateTime BETWEEN DATEADD(MINUTE, -20, GETUTCDATE()) AND GETUTCDATE() ");
                query.Append($"ORDER BY CreatedDateTime DESC ");
                var parameters = new { NotificationId = userNotificationDetails.NotificationId, UserId = userNotificationDetails.UserId, Body = userNotificationDetails.Body };
                var result = await GetByIdAsync<int>(query.ToString(), parameters);
                // We are inserting notification only once per user for specific notification type.
                if (result > 0)
                    return result;
                else
                {
                    var propUserDataDic = ConvertToDictionary(userNotificationDetails);
                    return await AddAsync(Constants.UserNotificationDetailsTableName, "UserNotificationDetailId", propUserDataDic);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error inserting user notification data for userid {Category}", userNotificationDetails.UserId);
                return -1;
            }
        }
        
        public Task<int> UpdateUserNotification(int userId)
        {
            throw new NotImplementedException();
        }
        public async Task<List<NotificationUserList>> GetUsersForNotificationBySRCId(string[] srcIds, string dataTypeName)
        {
            try
            {
                // Input validation to prevent SQL injection
                if (!ValidateInputParameters(srcIds, dataTypeName))
                {
                    return new List<NotificationUserList>();
                }
                // Use pre-built queries for better performance and security
                var (query, parameters) = BuildOptimizedQuery(srcIds, dataTypeName);

                var results = await QueryAsync<NotificationUserList>(query, parameters);
                return results.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Users For Notification of data type {DataTypeName} for SRCIDs {SRCIDs}", dataTypeName, srcIds);
                return new List<NotificationUserList>();
            }
        }

        private static readonly Dictionary<string, string> QueryCache = new()
        {
            [nameof(DataTypeName.maintenance)] = @"SELECT UD.UserId, UD.DeviceType, UD.DeviceId, UD.DeviceToken, I.PropertyNickName
                                                  FROM UserDeviceDetails UD
                                                  INNER JOIN Users US ON US.UserId = UD.UserId
                                                  INNER JOIN (
                                                      SELECT UP.UserId, COALESCE(PN.PropertyNickName, UP.PropertyName) AS PropertyNickName
                                                      FROM UserProperties UP
                                                      LEFT JOIN UserPropertiesNickName PN ON PN.UserPropertiesNickNameId = UP.UserPropertiesNickNameId AND PN.IsActive = 1
                                                      WHERE UP.SRCEntitytId IN @SRCIds
                                                      GROUP BY UP.UserId, COALESCE(PN.PropertyNickName, UP.PropertyName)
                                                  ) AS I ON UD.UserId = I.UserId
                                                  WHERE US.IsActive = 1 AND US.PushNotificationEnabled = 1 AND UD.IsActive = 1
                                                  ORDER BY UD.UserId",

            [nameof(DataTypeName.inspections)] = @"SELECT UD.UserId, UD.DeviceType, UD.DeviceId, UD.DeviceToken, I.PropertyNickName, I.InspectionDate
                                                  FROM UserDeviceDetails UD
                                                  INNER JOIN Users US ON US.UserId = UD.UserId
                                                  INNER JOIN (
                                                      SELECT UP.UserId, COALESCE(PN.PropertyNickName, UP.PropertyName) AS PropertyNickName, ID.InspectionDate
                                                      FROM UserProperties UP
                                                      INNER JOIN InspectionDetails ID ON ID.SRCPropertyId = UP.SRCEntitytId
                                                      LEFT JOIN UserPropertiesNickName PN ON PN.UserPropertiesNickNameId = UP.UserPropertiesNickNameId AND PN.IsActive = 1
                                                      WHERE ID.SRCInspectionId IN @SRCIds
                                                      GROUP BY UP.UserId, COALESCE(PN.PropertyNickName, UP.PropertyName), ID.InspectionDate
                                                  ) AS I ON UD.UserId = I.UserId
                                                  WHERE US.IsActive = 1 AND US.PushNotificationEnabled = 1 AND UD.IsActive = 1
                                                  ORDER BY UD.UserId",

            [nameof(DataTypeName.documents)] = @"SELECT UD.UserId, UD.DeviceType, UD.DeviceId, UD.DeviceToken, I.DocumentName, I.DocumentLink
                                                FROM UserDeviceDetails UD
                                                INNER JOIN Users US ON US.UserId = UD.UserId
                                                INNER JOIN (
                                                    SELECT UP.UserId, DD.DocumentName, DD.DocumentLink
                                                    FROM UserProperties UP
                                                    INNER JOIN DocumentDetails DD ON (DD.SRCManagementId = UP.SRCManagementId OR DD.SRCTenancyId = UP.SRCTenancyId)
                                                    WHERE DD.SRCDocumentId IN @SRCIds
                                                    AND (DD.SRCManagementId IS NOT NULL OR DD.SRCTenancyId IS NOT NULL)
                                                    GROUP BY UP.UserId, DD.DocumentName, DD.DocumentLink
                                                ) AS I ON UD.UserId = I.UserId
                                                WHERE US.IsActive = 1 AND US.PushNotificationEnabled = 1 AND UD.IsActive = 1
                                                ORDER BY UD.UserId"
        };
        private bool ValidateInputParameters(string[] srcIds, string dataTypeName)
        {
            if (srcIds == null || srcIds.Length == 0)
            {
                _logger.LogWarning("srcIds is null or empty in GetUsersForNotificationBySRCId");
                return false;
            }

            if (string.IsNullOrWhiteSpace(dataTypeName))
            {
                _logger.LogWarning("dataTypeName is null or empty in GetUsersForNotificationBySRCId");
                return false;
            }

            // Check for SQL injection in dataTypeName
            if (SqlInjectionPreventionHelper.ContainsSqlInjection(dataTypeName))
            {
                SqlInjectionPreventionHelper.LogPotentialSqlInjection(dataTypeName, "GetUsersForNotificationBySRCId", _logger);
                return false;
            }

            // Check for SQL injection in srcIds array
            if (SqlInjectionPreventionHelper.ContainsSqlInjectionInArray(srcIds))
            {
                SqlInjectionPreventionHelper.LogPotentialSqlInjection(string.Join(",", srcIds), "GetUsersForNotificationBySRCId", _logger);
                return false;
            }

            if (!ValidDataTypeNames.Contains(dataTypeName))
            {
                _logger.LogWarning("Invalid dataTypeName: {DataTypeName} in GetUsersForNotificationBySRCId", dataTypeName);
                return false;
            }

            return true;
        }
        private static readonly HashSet<string> ValidDataTypeNames = new()
        {
            nameof(DataTypeName.maintenance),
            nameof(DataTypeName.inspections),
            nameof(DataTypeName.documents)
        };
        private (string query, object parameters) BuildOptimizedQuery(string[] srcIds, string dataTypeName)
        {
            // Use cached query for better performance
            if (!QueryCache.TryGetValue(dataTypeName, out string query))
            {
                throw new ArgumentException($"Unsupported data type: {dataTypeName}", nameof(dataTypeName));
            }

            var parameters = new { SRCIds = srcIds };
            return (query, parameters);
        }
    }
}
