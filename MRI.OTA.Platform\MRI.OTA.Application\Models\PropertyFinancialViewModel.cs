using System.ComponentModel.DataAnnotations;

namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// View model for Property Financial Information API response
    /// </summary>
    public class PropertyFinancialViewModel
    {
        /// <summary>
        /// Property Financial Information ID
        /// </summary>
        public int PropertyFinancialInformationId { get; set; }

        /// <summary>
        /// Property ID
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// Name of the tenancy
        /// </summary>
        public string? TenancyName { get; set; }

        /// <summary>
        /// Lease start date
        /// </summary>
        public DateTime? LeaseStart { get; set; }

        /// <summary>
        /// Lease end date
        /// </summary>
        public DateTime? LeaseEnd { get; set; }

        /// <summary>
        /// Vacate date
        /// </summary>
        public DateTime? VacateDate { get; set; }

        /// <summary>
        /// Rent amount
        /// </summary>
        public decimal? Rent { get; set; }

        /// <summary>
        /// Increased rent amount
        /// </summary>
        public decimal? IncreaseRent { get; set; }

        /// <summary>
        /// Date of rent increase
        /// </summary>
        public DateTime? IncreaseDate { get; set; }

        /// <summary>
        /// Options date
        /// </summary>
        public DateTime? OptionsDate { get; set; }

        /// <summary>
        /// Options detail
        /// </summary>
        public string? OptionsDetail { get; set; }

        /// <summary>
        /// Arrears amount
        /// </summary>
        public decimal? Arrears { get; set; }

        /// <summary>
        /// Pay to date
        /// </summary>
        public DateTime? PayToDate { get; set; }

        /// <summary>
        /// Amount to vacate
        /// </summary>
        public decimal? AmountToVacate { get; set; }

        /// <summary>
        /// Outstanding invoices amount
        /// </summary>
        public decimal? OutstandingInvoices { get; set; }

        /// <summary>
        /// Invoice fees arrears amount
        /// </summary>
        public decimal? InvoiceFeesArrears { get; set; }

        /// <summary>
        /// Rent charge amount
        /// </summary>
        public decimal? RentCharge { get; set; }

        /// <summary>
        /// Weekly rent amount
        /// </summary>
        public decimal? WeeklyRent { get; set; }

        /// <summary>
        /// Last paid date
        /// </summary>
        public DateTime? LastPaid { get; set; }

        /// <summary>
        /// Held funds amount
        /// </summary>
        public decimal? HeldFunds { get; set; }

        /// <summary>
        /// Source Agency ID
        /// </summary>
        public string? SRCAgencyId { get; set; }

        /// <summary>
        /// Source Management ID
        /// </summary>
        public string? SRCManagementId { get; set; }

        /// <summary>
        /// Source Property ID
        /// </summary>
        public string? SRCPropertyId { get; set; }

        /// <summary>
        /// Name of the agency managing the property
        /// </summary>
        public string? AgencyName { get; set; }

        /// <summary>
        /// Business registered name from Agency Details
        /// </summary>
        public string? BusinessRegisteredName { get; set; }

        /// <summary>
        /// Business name from Agency Details
        /// </summary>
        public string? BusinessName { get; set; }

        /// <summary>
        /// Ownership total available balance
        /// </summary>
        public decimal? OwnershipTotalAvailableBalance { get; set; }

        /// <summary>
        /// Property outstanding fees
        /// </summary>
        public decimal? PropertyOutstandingFees { get; set; }

        /// <summary>
        /// Property outstanding invoices
        /// </summary>
        public decimal? PropertyOutstandingInvoices { get; set; }

        /// <summary>
        /// Property overdue invoices
        /// </summary>
        public decimal? PropertyOverdueInvoices { get; set; }

        /// <summary>
        /// Last payment amount
        /// </summary>
        public decimal? LastPaymentAmount { get; set; }

        /// <summary>
        /// Currency
        /// </summary>
        public string? Currency { get; set; }
        public string? CurrencySymbol { get; set; }

        /// <summary>
        /// Last statement date
        /// </summary>
        public DateTime? LastStatementDate { get; set; }
    }
} 