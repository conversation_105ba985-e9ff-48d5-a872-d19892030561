﻿

namespace MRI.OTA.Application.Models
{
    public class UserProfileSettingsModel
    {
        /// <summary>
        /// User id
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// PreferredContactEmail
        /// </summary>
        public string? PreferredContactEmail { get; set; }

        /// <summary>
        /// PushNotificationEnabled
        /// </summary>
        public bool? PushNotificationEnabled { get; set; }

        /// <summary>
        /// EmailNotificationEnabled
        /// </summary>
        public bool? EmailNotificationEnabled { get; set; }
    }
}
