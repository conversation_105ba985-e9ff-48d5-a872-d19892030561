﻿using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Mvc;
using Moq;
using MRI.OTA.API.Controllers.Integration.v1;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Common.Models;
using MRI.OTA.UnitTestCases.Invitations;

namespace MRI.OTA.Tests.Integration.Controller
{
    public class IntegrationControllerTests
    {
        private readonly Mock<IPropertTreeService> _mockPropertTreeService;
        private readonly IntegrationController _controller;
        private readonly TelemetryClient _telemetryClient;

        public IntegrationControllerTests()
        {
            // Create a TelemetryClient for testing with NullChannel to avoid actual telemetry
            var telemetryConfiguration = new TelemetryConfiguration();
            telemetryConfiguration.TelemetryChannel = new NullTelemetryChannel();
            _telemetryClient = new TelemetryClient(telemetryConfiguration);

            _mockPropertTreeService = new Mock<IPropertTreeService>();
            _controller = new IntegrationController(_mockPropertTreeService.Object, _telemetryClient);
        }

        [Fact]
        public async Task SyncPropertyData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncPropertyData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncPropertyData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("property data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncPropertyData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncPropertyData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncPropertyData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("property data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncAgencyData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncAgencyData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncAgencyData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("agency data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncAgencyData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncAgencyData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncAgencyData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("agency data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncAgencyPartnerData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncAgencyPartnerData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncAgencyPartnerData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("agency partner data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncAgencyPartnerData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncAgencyPartnerData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncAgencyPartnerData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("agency partner data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncDocumentData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncDocumentData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncDocumentData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("document data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncDocumentData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncDocumentData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncDocumentData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("document data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncManagementData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncManagementData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncManagementData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("management data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncManagementData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncManagementData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncManagementData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("management data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncComplianceData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncComplianceData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncComplianceData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("compliance data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncComplianceData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncComplianceData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncComplianceData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("compliance data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncTenanciesTenantData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncTenanciesTenantData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncTenanciesTenantData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("tenancies tenant data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncTenanciesTenantData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncTenanciesTenantData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncTenanciesTenantData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("tenancies tenant data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncInspectionsData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncInspectionsData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncInspectionsData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("inspections data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncInspectionsData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncInspectionsData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncInspectionsData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("inspections data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncMaintenanceData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncMaintenanceData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncMaintenanceData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("maintenance data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncMaintenanceData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncMaintenanceData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncMaintenanceData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("maintenance data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncFinancialsData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncFinancialsData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncFinancialsData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("financials data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncFinancialsData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncFinancialsData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncFinancialsData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("financials data not synced.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncTenanciesOwnerData_WhenServiceReturnsTrue_ShouldReturnOk()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncTenanciesOwnerData()).ReturnsAsync(true);

            // Act
            var result = await _controller.SyncTenanciesOwnerData();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal("tenancies owner data synced successfully.", apiResponse.Message);
        }

        [Fact]
        public async Task SyncTenanciesOwnerData_WhenServiceReturnsFalse_ShouldReturnBadRequest()
        {
            // Arrange
            _mockPropertTreeService.Setup(s => s.SyncTenanciesOwnerData()).ReturnsAsync(false);

            // Act
            var result = await _controller.SyncTenanciesOwnerData();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal("tenancies owner data not synced.", apiResponse.Message);
        }
    }
}
