name: Set Function App deployment environment context

description: Sets environment-specific context variables

inputs:
  environment:
    required: true
  app_name:
    required: true
  app_rg:
    required: true
  client_secret:
    required: true

  AzureWebJobsStorage:
    required: true
  FUNCTIONS_WORKER_RUNTIME:
    required: true
  PROPERTY_DATA_TIMER:
    required: true
  AGENCY_DATA_TIMER:
    required: true
  BaseUrl:
    required: true
  WorkerTimeOut:
    required: true
  TenantName:
    required: true
  ClientId:
    required: true
  SignUpSignInPolicyId:
    required: true
  ApplicationInsights__ConnectionString:
    required: true

runs:
  using: "composite"
  steps:
    - shell: bash
      run: |
        set -e

        echo "Setting environment variables for ${{ inputs.environment }}..."

        echo "ENVIRONMENT=${{ inputs.environment }}" >> "$GITHUB_ENV"
        echo "APP_NAME=${{ inputs.app_name }}" >> "$GITHUB_ENV"
        echo "APP_RG=${{ inputs.app_rg }}" >> "$GITHUB_ENV"
        echo "CLIENT_SECRET=${{ inputs.client_secret }}" >> "$GITHUB_ENV"
        echo "AzureWebJobsStorage=${{ inputs.AzureWebJobsStorage }}" >> "$GITHUB_ENV"
        echo "FUNCTIONS_WORKER_RUNTIME=${{ inputs.FUNCTIONS_WORKER_RUNTIME }}" >> "$GITHUB_ENV"
        echo "PROPERTY_DATA_TIMER=${{ inputs.PROPERTY_DATA_TIMER }}" >> "$GITHUB_ENV"
        echo "AGENCY_DATA_TIMER=${{ inputs.AGENCY_DATA_TIMER }}" >> "$GITHUB_ENV"
        echo "BaseUrl=${{ inputs.BaseUrl }}" >> "$GITHUB_ENV"
        echo "WorkerTimeOut=${{ inputs.WorkerTimeOut }}" >> "$GITHUB_ENV"
        echo "TenantName=${{ inputs.TenantName }}" >> "$GITHUB_ENV"
        echo "ClientId=${{ inputs.ClientId }}" >> "$GITHUB_ENV"
        echo "SignUpSignInPolicyId=${{ inputs.SignUpSignInPolicyId }}" >> "$GITHUB_ENV"
        echo "ApplicationInsights__ConnectionString=${{ inputs.ApplicationInsights__ConnectionString }}" >> "$GITHUB_ENV"

        echo "::notice:: ENVIRONMENT=${{ inputs.environment }}"
        echo "::notice:: APP_NAME=${{ inputs.app_name }}"
        echo "::notice:: APP_RG=${{ inputs.app_rg }}"
        echo "::notice:: ClientId=${{ inputs.ClientId }}"
        echo "::notice:: BaseUrl=${{ inputs.BaseUrl }}"
        echo "::notice:: ✅ Environment variables set successfully"
