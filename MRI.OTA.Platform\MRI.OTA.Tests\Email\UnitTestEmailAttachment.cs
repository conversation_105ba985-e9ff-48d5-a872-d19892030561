using MRI.OTA.Email;
using System.Text;
using Xunit;

namespace MRI.OTA.UnitTestCases.Email
{
    public class UnitTestEmailAttachment
    {
        [Fact]
        public void Constructor_WithValidParameters_SetsAllProperties()
        {
            // Arrange
            var fileName = "test.pdf";
            var content = Encoding.UTF8.GetBytes("Test file content");
            var contentType = "application/pdf";

            // Act
            var attachment = new EmailAttachment(fileName, content, contentType);

            // Assert
            Assert.Equal(fileName, attachment.FileName);
            Assert.Equal(content, attachment.Content);
            Assert.Equal(contentType, attachment.ContentType);
        }

        [Fact]
        public void Constructor_WithEmptyFileName_SetsEmptyFileName()
        {
            // Arrange
            var fileName = "";
            var content = Encoding.UTF8.GetBytes("Test content");
            var contentType = "text/plain";

            // Act
            var attachment = new EmailAttachment(fileName, content, contentType);

            // Assert
            Assert.Equal(fileName, attachment.FileName);
            Assert.Equal(content, attachment.Content);
            Assert.Equal(contentType, attachment.ContentType);
        }

        [Fact]
        public void Constructor_WithEmptyContent_SetsEmptyContent()
        {
            // Arrange
            var fileName = "empty.txt";
            var content = new byte[0];
            var contentType = "text/plain";

            // Act
            var attachment = new EmailAttachment(fileName, content, contentType);

            // Assert
            Assert.Equal(fileName, attachment.FileName);
            Assert.Equal(content, attachment.Content);
            Assert.Equal(contentType, attachment.ContentType);
        }

        [Fact]
        public void Constructor_WithEmptyContentType_SetsEmptyContentType()
        {
            // Arrange
            var fileName = "test.txt";
            var content = Encoding.UTF8.GetBytes("Test content");
            var contentType = "";

            // Act
            var attachment = new EmailAttachment(fileName, content, contentType);

            // Assert
            Assert.Equal(fileName, attachment.FileName);
            Assert.Equal(content, attachment.Content);
            Assert.Equal(contentType, attachment.ContentType);
        }

        [Fact]
        public void FileName_Property_CanBeSet()
        {
            // Arrange
            var attachment = new EmailAttachment("initial.txt", new byte[0], "text/plain");
            var newFileName = "updated.txt";

            // Act
            attachment.FileName = newFileName;

            // Assert
            Assert.Equal(newFileName, attachment.FileName);
        }

        [Fact]
        public void Content_Property_CanBeSet()
        {
            // Arrange
            var attachment = new EmailAttachment("test.txt", new byte[0], "text/plain");
            var newContent = Encoding.UTF8.GetBytes("New content");

            // Act
            attachment.Content = newContent;

            // Assert
            Assert.Equal(newContent, attachment.Content);
        }

        [Fact]
        public void ContentType_Property_CanBeSet()
        {
            // Arrange
            var attachment = new EmailAttachment("test.txt", new byte[0], "text/plain");
            var newContentType = "application/json";

            // Act
            attachment.ContentType = newContentType;

            // Assert
            Assert.Equal(newContentType, attachment.ContentType);
        }

        [Theory]
        [InlineData("document.pdf", "application/pdf")]
        [InlineData("image.jpg", "image/jpeg")]
        [InlineData("data.json", "application/json")]
        [InlineData("style.css", "text/css")]
        [InlineData("script.js", "application/javascript")]
        public void Constructor_WithVariousFileTypes_SetsPropertiesCorrectly(string fileName, string contentType)
        {
            // Arrange
            var content = Encoding.UTF8.GetBytes("Sample content");

            // Act
            var attachment = new EmailAttachment(fileName, content, contentType);

            // Assert
            Assert.Equal(fileName, attachment.FileName);
            Assert.Equal(content, attachment.Content);
            Assert.Equal(contentType, attachment.ContentType);
        }

        [Fact]
        public void Constructor_WithLargeContent_HandlesCorrectly()
        {
            // Arrange
            var fileName = "large-file.bin";
            var content = new byte[1024 * 1024]; // 1MB
            for (int i = 0; i < content.Length; i++)
            {
                content[i] = (byte)(i % 256);
            }
            var contentType = "application/octet-stream";

            // Act
            var attachment = new EmailAttachment(fileName, content, contentType);

            // Assert
            Assert.Equal(fileName, attachment.FileName);
            Assert.Equal(content.Length, attachment.Content.Length);
            Assert.Equal(contentType, attachment.ContentType);
            Assert.Equal(content, attachment.Content);
        }
    }
}
