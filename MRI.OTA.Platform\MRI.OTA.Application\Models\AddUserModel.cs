﻿namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// Add user model
    /// </summary>
    public class AddUserModel
    {
        /// <summary>
        /// UserId
        /// </summary>
        public int UserId { get; set; }
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// DisplayName
        /// </summary>
        public string? DisplayName { get; set; }

        /// <summary>
        /// ProviderTypeId
        /// </summary>
        public int ProviderTypeId { get; set; }

        /// <summary>
        /// ProviderId
        /// </summary>
        public string? ProviderId { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }
    }
}
