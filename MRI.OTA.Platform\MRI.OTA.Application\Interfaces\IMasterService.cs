﻿using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Models.Master;

using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.Application.Interfaces
{
    public interface IMasterService
    {
        public Task<List<T>> GetDataByTableName<T>(string tableName);

        public Task<List<CountryModel>> GetAllCountriesAsync();

        public Task<List<StateModel>> GetAllStatesAsync();

        public Task<List<AdminAreaModel>> GetAllAdminAreas();

        public Task<List<OccupancyTypesModel>> GetAllOccupancyTypes();

        public Task<List<PropertyRelationshipsModel>> GetPropertyRelationships();

        public Task<List<OccupancyStatusTypeModel>> GetOccupancyStatusType();

        public Task<List<ViewModuleRelationship>> GetModulesList(int? propertyRelationshipId);
    }
}
