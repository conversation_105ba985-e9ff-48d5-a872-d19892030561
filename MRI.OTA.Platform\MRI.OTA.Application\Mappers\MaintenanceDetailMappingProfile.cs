﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.Common.Constants;

namespace MRI.OTA.Application.Mappers
{
    public class MaintenanceDetailMappingProfile : Profile
    {
        public MaintenanceDetailMappingProfile() : base("MaintenanceDetailMappingProfile")
        {
            // Map from MaintenanceJobs to MaintenanceDetail
            CreateMap<MaintenanceJobResponse, MaintenanceDetail>()
                .ForMember(dest => dest.MaintenanceDetailId, opt => opt.Ignore()) // Auto-generated
                .ForMember(dest => dest.SRCManagementId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.SRCTenancyId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1)) // Default value, will be updated
                .ForMember(dest => dest.SRCPropertyId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.SRCJobId, opt => opt.MapFrom(src => src.JobId))
                .ForMember(dest => dest.JobSummary, opt => opt.MapFrom(src => src.JobSummary))
                .ForMember(dest => dest.JobStatus, opt => opt.MapFrom(src => src.JobStatus))
                .ForMember(dest => dest.SRCRequestId, opt => opt.MapFrom(src => src.RequestId))
                .ForMember(dest => dest.RequestSummary, opt => opt.MapFrom(src => src.RequestSummary))
                .ForMember(dest => dest.RequestStatus, opt => opt.MapFrom(src => src.RequestStatus))
                .ForMember(dest => dest.RequestRaisedBy, opt => opt.MapFrom(src => src.RequestRaisedBy))
                .ForMember(dest => dest.RequestRaisedDate, opt => opt.MapFrom(src => src.RequestRaisedDate))
                .ForMember(dest => dest.ImageLink, opt => opt.MapFrom(src => src.ImageLink));

            // Map from MaintenanceDetailResponse to List<MaintenanceDetail>
            // This handles the flattening of the response structure
            CreateMap<MaintenanceDetailResponse, List<MaintenanceDetail>>()
                .ConvertUsing((src, dest, context) =>
                {
                    if (src?.MaintenanceJobs == null || src.MaintenanceJobs.Count == 0)
                        return [];

                    var maintenanceDetails = new List<MaintenanceDetail>();

                    foreach (var job in src.MaintenanceJobs)
                    {
                        var maintenanceDetail = context.Mapper.Map<MaintenanceDetail>(job);

                        // Set the parent-level properties
                        maintenanceDetail.SRCManagementId = src.ManagementId;
                        maintenanceDetail.SRCTenancyId = src.TenancyId;
                        maintenanceDetail.SRCPropertyId = src.PropertyId;

                        maintenanceDetails.Add(maintenanceDetail);
                    }

                    return maintenanceDetails;
                });

            // Map from List<MaintenanceDetailResponse> to List<MaintenanceDetail>
            // This handles the flattening of multiple responses, each containing multiple jobs
            CreateMap<List<MaintenanceDetailResponse>, List<MaintenanceDetail>>()
                .ConvertUsing((src, dest, context) =>
                {
                    if (src == null || src.Count == 0)
                        return [];

                    var allMaintenanceDetails = new List<MaintenanceDetail>();

                    foreach (var response in src)
                    {
                        // Use the existing single response mapping to flatten each response
                        var responseDetails = context.Mapper.Map<List<MaintenanceDetail>>(response);
                        allMaintenanceDetails.AddRange(responseDetails);
                    }

                    return allMaintenanceDetails;
                });

            // Legacy mapping for backward compatibility
            CreateMap<MaintenanceDetailResponse, MaintenanceDetail>()
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
                .ForMember(dest => dest.SRCTenancyId, opt => opt.MapFrom(src => src.TenancyId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1))
                .ForMember(dest => dest.SRCJobId, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().JobId
                        : null))
                .ForMember(dest => dest.JobSummary, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().JobSummary
                        : null))
                .ForMember(dest => dest.JobStatus, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().JobStatus
                        : null))
                .ForMember(dest => dest.SRCRequestId, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().RequestId
                        : null))
                .ForMember(dest => dest.RequestSummary, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().RequestSummary
                        : null))
                .ForMember(dest => dest.RequestStatus, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().RequestStatus
                        : null))
                .ForMember(dest => dest.RequestRaisedBy, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().RequestRaisedBy
                        : null))
                .ForMember(dest => dest.RequestRaisedDate, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().RequestRaisedDate
                        : default))
                .ForMember(dest => dest.ImageLink, opt => opt.MapFrom(src =>
                    src.MaintenanceJobs != null && src.MaintenanceJobs.Count > 0
                        ? src.MaintenanceJobs.First().ImageLink
                        : null))
                .ReverseMap();
        }
    }
}
