using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Routing;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies;
using System.IO;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares.Authentication.Strategies
{
    public class UnitTestAnonymousEndpointStrategy
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly AnonymousEndpointStrategy _strategy;

        public UnitTestAnonymousEndpointStrategy()
        {
            _nextMock = new Mock<RequestDelegate>();
            _loggerMock = new Mock<ILogger>();

            _strategy = new AnonymousEndpointStrategy(_nextMock.Object, _loggerMock.Object);
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenNextIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AnonymousEndpointStrategy(null!, _loggerMock.Object));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenLoggerIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new AnonymousEndpointStrategy(_nextMock.Object, null!));
        }

        [Fact]
        public void CanHandle_ReturnsTrue_WhenEndpointHasAllowAnonymousAttribute()
        {
            // Arrange
            var context = CreateHttpContext();
            var endpoint = new Endpoint(
                c => Task.CompletedTask,
                new EndpointMetadataCollection(
                    new Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute()),
                "test");
            context.SetEndpoint(endpoint);

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenEndpointHasNoAllowAnonymousAttribute()
        {
            // Arrange
            var context = CreateHttpContext();
            var endpoint = new Endpoint(
                c => Task.CompletedTask,
                new EndpointMetadataCollection(),
                "test");
            context.SetEndpoint(endpoint);

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenNoEndpoint()
        {
            // Arrange
            var context = CreateHttpContext();
            // No endpoint set

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenEndpointIsNull()
        {
            // Arrange
            var context = CreateHttpContext();
            context.SetEndpoint(null);

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task AuthenticateAsync_CallsNext()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/health";

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledAndAuthenticated()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/health";

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.True(authenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_LogsDebug()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/health";

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Debug,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Anonymous access allowed")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_HandlesNextMiddlewareException()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/health";

            _nextMock.Setup(n => n(It.IsAny<HttpContext>()))
                .ThrowsAsync(new InvalidOperationException("Next middleware error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _strategy.AuthenticateAsync(context));
        }

        [Fact]
        public async Task AuthenticateAsync_DoesNotModifyUserPrincipal()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/health";
            var originalUser = context.User;

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.Equal(originalUser, context.User);
        }

        [Fact]
        public async Task AuthenticateAsync_DoesNotModifyResponseHeaders()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/health";
            var originalHeaderCount = context.Response.Headers.Count;

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.Equal(originalHeaderCount, context.Response.Headers.Count);
        }

        [Theory]
        [InlineData("GET")]
        [InlineData("POST")]
        [InlineData("PUT")]
        [InlineData("DELETE")]
        [InlineData("PATCH")]
        [InlineData("HEAD")]
        [InlineData("OPTIONS")]
        public async Task AuthenticateAsync_HandlesAllHttpMethods(string method)
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Method = method;
            context.Request.Path = "/health";

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.True(authenticated);
        }



        [Fact]
        public async Task AuthenticateAsync_MaintainsRequestPath()
        {
            // Arrange
            var context = CreateHttpContext();
            var originalPath = "/health/check";
            context.Request.Path = originalPath;

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.Equal(originalPath, context.Request.Path.Value);
        }

        [Fact]
        public async Task AuthenticateAsync_MaintainsRequestMethod()
        {
            // Arrange
            var context = CreateHttpContext();
            var originalMethod = "POST";
            context.Request.Method = originalMethod;
            context.Request.Path = "/health";

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.Equal(originalMethod, context.Request.Method);
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.Response.Body = new MemoryStream();
            return context;
        }
    }
}
