﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class TenanciesTenant
    {
        [ExcludeColumn]
        public int PropertyFinancialInformationId { get; set; }
        public string SRCManagementId { get; set; }
        public string SRCPropertyId { get; set; }
        public int PropertyId { get; set; }
        public string SRCTenancyId { get; set; }
        public string TenancyName { get; set; }

        public string TenancyStatus { get; set; }

        public bool IsActive { get; set; } = true;
        public DateTime? LeaseStart { get; set; }
        public DateTime? LeaseEnd { get; set; }
        public DateTime? VacateDate { get; set; }
        public decimal? AmountToVacate { get; set; }
        public DateTime? PayToDate { get; set; }
        public decimal? Rent { get; set; }
        public decimal? Arrears { get; set; }
        public decimal? OutstandingInvoices { get; set; }
        public decimal? IncreaseRent { get; set; }
        public DateTime? IncreaseDate { get; set; }
        public int? RentPeriod { get; set; }

        public string? Currency { get; set; }
        [ExcludeColumn]
        public TenanciesPropertyManagerDetails TenanciesPropertyManagerDetails { get; set; }
    }
}
