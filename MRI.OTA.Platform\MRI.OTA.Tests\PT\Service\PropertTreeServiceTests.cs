using AutoMapper;
using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Application.Models;
using MRI.OTA.Application.Models.Integration;
using MRI.OTA.Application.Services;
using MRI.OTA.Application.Services.Integration;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Integration.Models;
using MRI.OTA.Integration.Services;
using Newtonsoft.Json;
using System.Reflection;
using System.Text.Json;
using static MRI.OTA.Common.Constants.Constants;

namespace MRI.OTA.UnitTestCases.PT.Service
{
    public class PropertTreeServiceTests
    {
        private readonly Mock<ILogger<PropertTreeService>> _loggerMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<IPropertyRepository> _propertyRepositoryMock;
        private readonly Mock<IUserRepository> _userRepositoryMock;
        private readonly Mock<IProxyService> _proxyServiceMock;
        private readonly Mock<TaskContext> _taskContextMock;
        private readonly PropertTreeService _propertTreeService;
        private readonly Mock<IAPITrackingService> _mockAPITrackingService;
        private readonly Mock<IDataSourceRepository> _dataSourceRepositoryMock;
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly Mock<IIntegrationRepository> _integrationRepositoryMock;
        private readonly Mock<INotificationService> _notificationServiceMock;
        private readonly Mock<IBatchOperationLogger> _batchLoggerMock;

        public PropertTreeServiceTests()
        {
            _loggerMock = new Mock<ILogger<PropertTreeService>>();
            _mapperMock = new Mock<IMapper>();
            _propertyRepositoryMock = new Mock<IPropertyRepository>();
            _userRepositoryMock = new Mock<IUserRepository>();
            _proxyServiceMock = new Mock<IProxyService>();
            _taskContextMock = new Mock<TaskContext>();
            _mockAPITrackingService = new Mock<IAPITrackingService>();
            _dataSourceRepositoryMock = new Mock<IDataSourceRepository>();
            _configurationMock = new Mock<IConfiguration>();
            _integrationRepositoryMock = new Mock<IIntegrationRepository>();
            _notificationServiceMock = new Mock<INotificationService>();
            _batchLoggerMock = new Mock<IBatchOperationLogger>();
            _propertTreeService = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);
        }

        [Fact]
        public async Task SyncPropertyData_ShouldReturnTrue()
        {
            // Arrange
            var batchSize = 50;
            var mockConfigSection = new Mock<IConfigurationSection>();
            mockConfigSection.Setup(x => x.Value).Returns(batchSize.ToString());
            _configurationMock.Setup(x => x.GetSection("ApplicationOption:BatchSize")).Returns(mockConfigSection.Object);

            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
            {
                new MRI.OTA.Core.Entities.DataSource
                {
                    DataSourceId = 1,
                    Name = "Property tree",
                    AccessSecret = "test-token",
                    ManifestJson = "{\"propertyList\":{\"endpoint\":\"/properties\",\"method\":\"POST\"},\"baseUrl\":\"http://test.com\"}"
                }
            };

            var totalUserCount = 2;
            var users = new List<MRI.OTA.Core.Entities.Users>
            {
                new MRI.OTA.Core.Entities.Users { UserId = 1, ProviderId = "user1" },
                new MRI.OTA.Core.Entities.Users { UserId = 2, ProviderId = "user2" }
            };

            // Setup repository mocks
            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ReturnsAsync(dataSources);
            _userRepositoryMock.Setup(r => r.GetUserCount()).ReturnsAsync(totalUserCount);
            _userRepositoryMock.Setup(r => r.GetUsersBatch(It.IsAny<int>(), It.IsAny<int>()))
                              .ReturnsAsync(users);

            // Setup proxy service mock
            var responseData = new List<PropertyTreeInviteResponse>
            {
                new PropertyTreeInviteResponse { PropertyId = "prop1", PropertyName = "Test Property" }
            };
            var jsonResponse = JsonConvert.SerializeObject(responseData);
            var objectResult = new ObjectResult(JsonDocument.Parse(jsonResponse)) { StatusCode = 200 };

            _proxyServiceMock.Setup(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()))
                            .ReturnsAsync(objectResult);

            // Setup mapper mock
            var mappedProperties = new List<UserProperties>
            {
                new UserProperties { SRCEntitytId = "prop1", PropertyName = "Test Property", ProviderId = "user1" }
            };

            _mapperMock.Setup(m => m.Map(It.IsAny<List<PropertyTreeInviteResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<UserProperties>>>>()))
                       .Returns(mappedProperties);

            // Setup property repository mock
            _propertyRepositoryMock.Setup(r => r.BulkUpsertUserProperties(It.IsAny<List<UserProperties>>(), It.IsAny<int>()))
                                   .ReturnsAsync(new List<SQLQueryMergeResult>());

            // Act
            var result = await _propertTreeService.SyncPropertyData();

            // Assert
            Assert.True(result);
            _dataSourceRepositoryMock.Verify(r => r.GetAllDataSource(), Times.Once);
            // Remove this verification as ExecuteBatchOperation doesn't call GetUserCount directly
            // _userRepositoryMock.Verify(r => r.GetUserCount(), Times.Once);
        }

        // Additional test cases for better coverage
        [Fact]
        public async Task SyncPropertyData_ExceptionThrown_ShouldReturnFalse()
        {
            // Arrange
            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _propertTreeService.SyncPropertyData();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SyncAgencyData_ShouldReturnTrue()
        {
            // Arrange
            var batchSize = 50;
            var configSectionMock = new Mock<IConfigurationSection>();
            configSectionMock.Setup(cs => cs.Value).Returns(batchSize.ToString());
            _configurationMock.Setup(c => c.GetSection("ApplicationOption:BatchSize")).Returns(configSectionMock.Object);

            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
            {
                new MRI.OTA.Core.Entities.DataSource
                {
                    DataSourceId = 1,
                    Name = "Property tree",
                    AccessSecret = "test-token",
                    ManifestJson = "{\"agencyList\":{\"endpoint\":\"/agencies\",\"method\":\"POST\"},\"baseUrl\":\"http://test.com\"}"
                }
            };

            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ReturnsAsync(dataSources);
            _integrationRepositoryMock.Setup(r => r.GetUniqueAgenciesCount(It.IsAny<int>())).ReturnsAsync(1);
            _integrationRepositoryMock.Setup(r => r.GetUniqueAgencies(It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>()))
                                     .ReturnsAsync(new List<(string AgencyId, string AgencyName, int DataSourceId)> { ("agency1", "Test Agency", 1) });

            var responseData = new List<AgencyDetails> { new AgencyDetails { AgencyId = "agency1" } };
            var jsonResponse = JsonConvert.SerializeObject(responseData);
            var objectResult = new ObjectResult(JsonDocument.Parse(jsonResponse)) { StatusCode = 200 };

            _proxyServiceMock.Setup(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()))
                            .ReturnsAsync(objectResult);

            _integrationRepositoryMock.Setup(r => r.BulkUpsertAgencyDetails(It.IsAny<List<AgencyDetails>>(), It.IsAny<int>()))
                                     .ReturnsAsync(new List<SQLQueryMergeResult>());

            // Act
            var result = await _propertTreeService.SyncAgencyData();

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task SyncAgencyData_ExceptionThrown_ShouldReturnFalse()
        {
            // Arrange
            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _propertTreeService.SyncAgencyData();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SyncManagementData_ShouldReturnTrue()
        {
            // Arrange
            var batchSize = 50;
            var configSectionMock = new Mock<IConfigurationSection>();
            configSectionMock.Setup(cs => cs.Value).Returns(batchSize.ToString());
            _configurationMock.Setup(c => c.GetSection("ApplicationOption:BatchSize")).Returns(configSectionMock.Object);

            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
            {
                new MRI.OTA.Core.Entities.DataSource
                {
                    DataSourceId = 1,
                    Name = "Property tree",
                    AccessSecret = "test-token",
                    ManifestJson = "{\"managementList\":{\"endpoint\":\"/management\",\"method\":\"POST\"},\"baseUrl\":\"http://test.com\"}"
                }
            };

            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ReturnsAsync(dataSources);
            _integrationRepositoryMock.Setup(r => r.GetUniqueManagementOrTenancyCount(It.IsAny<int>(), It.IsAny<string>(), null))
                                     .ReturnsAsync(1);
            _integrationRepositoryMock.Setup(r => r.GetUniqueIdsFromUserProperty(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>(), null))
                                     .ReturnsAsync(new List<(string Id, int DataSourceId)> { ("mgmt1", 1) });

            var responseData = new List<ManagementResponse> { new ManagementResponse { ManagementId = "mgmt1" } };
            var jsonResponse = JsonConvert.SerializeObject(responseData);
            var objectResult = new ObjectResult(JsonDocument.Parse(jsonResponse)) { StatusCode = 200 };

            _proxyServiceMock.Setup(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()))
                            .ReturnsAsync(objectResult);

            _mapperMock.Setup(m => m.Map(It.IsAny<List<ManagementResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<PropertyManagerInformation>>>>()))
                       .Returns(new List<PropertyManagerInformation> { new PropertyManagerInformation { SRCManagementId = "mgmt1" } });

            _integrationRepositoryMock.Setup(r => r.BulkUpsertPropertyManagerInformation(It.IsAny<List<PropertyManagerInformation>>(), It.IsAny<int>()))
                                     .ReturnsAsync(new List<SQLQueryMergeResult>());

            // Act
            var result = await _propertTreeService.SyncManagementData();

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task SyncManagementData_ExceptionThrown_ShouldReturnFalse()
        {
            // Arrange
            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _propertTreeService.SyncManagementData();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SyncDocumentData_ShouldReturnTrue()
        {
            // Arrange
            var batchSize = 50;
            var configSectionMock = new Mock<IConfigurationSection>();
            configSectionMock.Setup(cs => cs.Value).Returns(batchSize.ToString());
            _configurationMock.Setup(c => c.GetSection("ApplicationOption:BatchSize")).Returns(configSectionMock.Object);

            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
            {
                new MRI.OTA.Core.Entities.DataSource
                {
                    DataSourceId = 1,
                    Name = "Property tree",
                    AccessSecret = "test-token",
                    ManifestJson = "{\"documentList\":{\"endpoint\":\"/documents\",\"method\":\"POST\"},\"baseUrl\":\"http://test.com\"}"
                }
            };

            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ReturnsAsync(dataSources);
            _integrationRepositoryMock.Setup(r => r.GetUniqueManagementOrTenancyCount(It.IsAny<int>(), It.IsAny<string>(), null))
                                     .ReturnsAsync(1);
            _integrationRepositoryMock.Setup(r => r.GetUniqueIdsFromUserProperty(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>(), null))
                                     .ReturnsAsync(new List<(string Id, int DataSourceId)> { ("doc1", 1) });

            var responseData = new List<DocumentDetailResponse> { new DocumentDetailResponse { DocumentId = "doc1" } };
            var jsonResponse = JsonConvert.SerializeObject(responseData);
            var objectResult = new ObjectResult(JsonDocument.Parse(jsonResponse)) { StatusCode = 200 };

            _proxyServiceMock.Setup(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()))
                            .ReturnsAsync(objectResult);

            _integrationRepositoryMock.Setup(r => r.BulkUpsertDocumentDetails(It.IsAny<List<DocumentDetail>>(), It.IsAny<int>(), It.IsAny<string>()))
                                     .ReturnsAsync(new List<SQLQueryMergeResult> { new SQLQueryMergeResult() });

            // Act
            var result = await _propertTreeService.SyncDocumentData();

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task SyncDocumentData_ExceptionThrown_ShouldReturnFalse()
        {
            // Arrange
            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _propertTreeService.SyncDocumentData();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SyncComplianceData_ShouldReturnTrue()
        {
            // Arrange
            var batchSize = 50;
            var configSectionMock = new Mock<IConfigurationSection>();
            configSectionMock.Setup(cs => cs.Value).Returns(batchSize.ToString());
            _configurationMock.Setup(c => c.GetSection("ApplicationOption:BatchSize")).Returns(configSectionMock.Object);

            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
            {
                new MRI.OTA.Core.Entities.DataSource
                {
                    DataSourceId = 1,
                    Name = "Property tree",
                    AccessSecret = "test-token",
                    ManifestJson = "{\"complianceList\":{\"endpoint\":\"/compliance\",\"method\":\"POST\"},\"baseUrl\":\"http://test.com\"}"
                }
            };

            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ReturnsAsync(dataSources);
            _integrationRepositoryMock.Setup(r => r.GetUniqueManagementOrTenancyCount(It.IsAny<int>(), It.IsAny<string>(), null))
                                     .ReturnsAsync(1);
            _integrationRepositoryMock.Setup(r => r.GetUniqueIdsFromUserProperty(It.IsAny<string>(), It.IsAny<int>(), It.IsAny<int>(), It.IsAny<int>(), null))
                                     .ReturnsAsync(new List<(string Id, int DataSourceId)> { ("comp1", 1) });

            var responseData = new List<ComplianceDetailResponse> { new ComplianceDetailResponse { ManagementId = "comp1", PropertyId = "prop1" } };
            var jsonResponse = JsonConvert.SerializeObject(responseData);
            var objectResult = new ObjectResult(JsonDocument.Parse(jsonResponse)) { StatusCode = 200 };

            _proxyServiceMock.Setup(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()))
                            .ReturnsAsync(objectResult);

            _mapperMock.Setup(m => m.Map(It.IsAny<List<ComplianceDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<ComplianceDetail>>>>()))
                       .Returns(new List<ComplianceDetail> { new ComplianceDetail { SRCComplianceId = "comp1" } });

            _integrationRepositoryMock.Setup(r => r.BulkUpsertComplianceDetails(It.IsAny<List<ComplianceDetail>>(), It.IsAny<int>()))
                                     .ReturnsAsync(new List<SQLQueryMergeResult>());

            // Act
            var result = await _propertTreeService.SyncComplianceData();

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task SyncComplianceData_ExceptionThrown_ShouldReturnFalse()
        {
            // Arrange
            _dataSourceRepositoryMock.Setup(r => r.GetAllDataSource()).ThrowsAsync(new Exception("Database error"));

            // Act
            var result = await _propertTreeService.SyncComplianceData();

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CreateAssociatePortfolioProxyRequest_ShouldReturnCorrectProxyRequestModel()
        {
            // Arrange
            var manifest = new DataSourceManifest
            {
                BaseUrl = "http://test.com/",
                AssociatePortfolio = new EndpointDefinition { Endpoint = "associate-portfolio", Method = "POST" }
            };
            var portfolioId = "portfolio123";
            var providerId = "provider456";
            var providerType = "type789";

            // Act
            var result = _propertTreeService.CreateAssociatePortfolioProxyRequest(manifest, portfolioId, providerId, providerType);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("http://test.com/associate-portfolio", result.FullUrl);
            Assert.Equal("POST", result.Method);
            Assert.IsType<AssociatePortfolioModel>(result.Body);
            var body = Assert.IsType<AssociatePortfolioModel>(result.Body);
            Assert.Equal(portfolioId, body.PortfolioId);
            Assert.Equal(providerId, body.UserId);
            Assert.Equal(providerType, body.ReferrerServiceId);
            Assert.True(Guid.TryParse(result.RequestId, out _));
        }

        [Theory]
        [InlineData(IntegrationEndPointsType.OwnerPropertyList, "properties-owner")]
        // Add other IntegrationEndPointsTypes as needed
        public void CreateProxyRequest_ShouldReturnCorrectProxyRequestModel_ForGivenSectionType(IntegrationEndPointsType sectionType, string expectedEndpoint)
        {
            // Arrange
            var manifest = new DataSourceManifest
            {
                BaseUrl = "http://test.com/",
                PropertyList = new EndpointDefinition { Endpoint = expectedEndpoint, Method = "POST" },
                TenanciesTenantList = new EndpointDefinition(),
                ManagementList = new EndpointDefinition(),
            };
            var userIds = new[] { "user1", "user2" };
            var modifiedSince = "2023-01-01";

            // Act
            var result = _propertTreeService.CreateProxyRequest(manifest, sectionType, userIds, modifiedSince);

            // Assert
            Assert.NotNull(result);
            Assert.Equal($"http://test.com/{expectedEndpoint}", result.FullUrl);
            Assert.Equal("POST", result.Method);
            Assert.IsType<PropertyTreeRequest>(result.Body);
            var body = Assert.IsType<PropertyTreeRequest>(result.Body);
            Assert.Equal(userIds, body.UserIds);
            Assert.Equal(modifiedSince, body.ModifiedSince);
            Assert.True(Guid.TryParse(result.RequestId, out _));
        }

        [Fact]
        public async Task ProcessGetPropertiesProxyResponse_ShouldReturnFalse_WhenResponseIsNotObjectResult()
        {
            // Arrange
            var response = new BadRequestResult();
            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Test" };
            var acceptInvitation = new AcceptInvitationModel { UserId = 1 };

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesProxyResponse(response, dataSource, acceptInvitation);

            // Assert
            Assert.False(result.Item1);
            Assert.Empty(result.Item2);
        }

        [Fact]
        public async Task ProcessGetPropertiesProxyResponse_ShouldReturnFalse_WhenStatusCodeIsNot200()
        {
            // Arrange
            var response = new ObjectResult("Error") { StatusCode = 500 };
            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Test" };
            var acceptInvitation = new AcceptInvitationModel { UserId = 1 };

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesProxyResponse(response, dataSource, acceptInvitation);

            // Assert
            Assert.False(result.Item1);
            Assert.Empty(result.Item2);
        }

        [Fact]
        public async Task ProcessGetPropertiesProxyResponse_ShouldReturnTrueAndEmptyList_WhenNoPropertiesInResponse()
        {
            // Arrange
            var emptyData = new List<PropertyTreeInviteResponse>();
            var response = CreateMockObjectResult(emptyData);
            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Test" };
            var acceptInvitation = new AcceptInvitationModel { UserId = 1 };

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesProxyResponse(response, dataSource, acceptInvitation);

            // Assert
            Assert.True(result.Item1);
            Assert.Empty(result.Item2);
        }

        [Fact]
        public async Task ProcessGetPropertiesProxyResponse_ShouldProcessPropertiesSuccessfully()
        {
            // Arrange
            var data = new List<PropertyTreeInviteResponse>
        {
            new PropertyTreeInviteResponse { PropertyId = "1", PropertyName = "Test Property" }
        };
            var response = CreateMockDocumentObjectResult(data);
            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Property tree" };
            var acceptInvitation = new AcceptInvitationModel { UserId = 123 };

            var mappedProperties = new List<UserProperties>
        {
            new UserProperties { SRCEntitytId = "1", PropertyName = "Test Property" }
        };

            // Setup the mapper to return the mapped properties
            _mapperMock.Setup(m => m.Map(It.IsAny<List<PropertyTreeInviteResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<UserProperties>>>>()))
                       .Returns(mappedProperties);

            // Setup the property repository to return a PropertyId when adding a property
            _propertyRepositoryMock.Setup(r => r.AddProperty(It.IsAny<UserProperties>()))
                                   .ReturnsAsync(1);

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesProxyResponse(response, dataSource, acceptInvitation);

            // Assert
            Assert.True(result.Item1);
            Assert.Single(result.Item2);
            Assert.Equal(1, result.Item2[0].DataSourceId);
            Assert.Equal(123, result.Item2[0].UserId);
            Assert.Equal(1, result.Item2[0].PropertyId);

            // Verify that the mapper was called with the right parameters
            _mapperMock.Verify(m => m.Map(It.IsAny<List<PropertyTreeInviteResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<UserProperties>>>>()), Times.Once);

            // Verify that the property repository was called to add the property
            _propertyRepositoryMock.Verify(r => r.AddProperty(It.IsAny<UserProperties>()), Times.Once);
        }

        [Fact]
        public async Task ProcessGetPropertiesProxyResponse_ShouldReturnFalse_OnException()
        {
            // Arrange
            var data = new List<PropertyTreeInviteResponse>
        {
            new PropertyTreeInviteResponse { PropertyId = "1", PropertyName = "Test Property" }
        };
            var response = CreateMockDocumentObjectResult(data);
            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Property tree" };
            var acceptInvitation = new AcceptInvitationModel { UserId = 123 };

            // Setup the mapper to throw an exception
            _mapperMock.Setup(m => m.Map(It.IsAny<List<PropertyTreeInviteResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<UserProperties>>>>()))
                       .Throws(new Exception("Mapping error"));

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesProxyResponse(response, dataSource, acceptInvitation);

            // Assert
            Assert.False(result.Item1);
            Assert.Empty(result.Item2);
        }

        private ObjectResult CreateMockObjectResult<T>(List<T> data, int statusCode = 200) where T : class
        {
            var jsonData = JsonConvert.SerializeObject(data);
            return new ObjectResult(JsonDocument.Parse(jsonData)) { StatusCode = statusCode };
        }

        private ObjectResult CreateMockDocumentObjectResult<T>(List<T> data, int statusCode = 200) where T : class
        {
            var jsonData = JsonConvert.SerializeObject(data);
            return new ObjectResult(jsonData) { StatusCode = statusCode };
        }

        [Fact]
        public async Task ProcessGetPropertiesOtherDataResponse_ShouldProcessOwnerDataSuccessfully()
        {
            // Arrange
            var userProperties = new List<UserProperties>
        {
            new UserProperties
            {
                UserId = 1, // Add UserId to prevent null reference
                SRCEntitytId = "prop1",
                SRCManagementId = "mgmt1",
                PropertyRelationshipId = (int)PropertyRelationShipType.Owner
            }
        };

            var responseBundle = new PropertyTreeResponseBundle(
                CreateMockObjectResult(new List<ManagementResponse> { new ManagementResponse { PropertyId = "prop1", ManagementId = "mgmt1" } }),
                CreateMockObjectResult(new List<TenanciesTenantResponse>()),
                CreateMockObjectResult(new List<TenanciesOwnerResponse> { new TenanciesOwnerResponse { PropertyId = "prop1", ManagementId = "mgmt1" } }),
                CreateMockObjectResult(new List<MaintenanceDetailResponse>()),
                CreateMockObjectResult(new List<InspectionDetailResponse>()),
                CreateMockObjectResult(new List<ComplianceDetailResponse>()),
                CreateMockObjectResult(new List<FinancialDetailResponse>()),
                CreateMockDocumentObjectResult(new List<DocumentDetailResponse>())
            );

            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Property tree" };

            // Setup mapper mocks
            _mapperMock.Setup(m => m.Map(It.IsAny<List<TenanciesTenantResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<TenanciesTenant>>>>()))
                       .Returns(new List<TenanciesTenant>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<TenanciesOwnerResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<TenanciesOwner>>>>()))
                       .Returns(new List<TenanciesOwner> { new TenanciesOwner { SRCPropertyId = "prop1", SRCManagementId = "mgmt1" } });
            _mapperMock.Setup(m => m.Map(It.IsAny<List<MaintenanceDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<MaintenanceDetail>>>>()))
                       .Returns(new List<MaintenanceDetail>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<InspectionDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<InspectionDetail>>>>()))
                       .Returns(new List<InspectionDetail>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<ComplianceDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<ComplianceDetail>>>>()))
                       .Returns(new List<ComplianceDetail>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<FinancialDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<FinancialDetail>>>>()))
                       .Returns(new List<FinancialDetail>());

            // Setup property repository mock
            _propertyRepositoryMock.Setup(r => r.AddPropertyOtherDetails(It.IsAny<UserProperties>()))
                                   .ReturnsAsync(1);

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesOtherDataResponse(responseBundle, dataSource, userProperties);

            // Assert
            Assert.Equal(1, result); // Constants.Success
            _propertyRepositoryMock.Verify(r => r.AddPropertyOtherDetails(It.IsAny<UserProperties>()), Times.Once);
        }

        [Fact]
        public async Task ProcessGetPropertiesOtherDataResponse_ShouldProcessTenantDataSuccessfully()
        {
            // Arrange
            var userProperties = new List<UserProperties>
        {
            new UserProperties
            {
                UserId = 1, // Add UserId to prevent null reference
                SRCEntitytId = "prop1",
                SRCTenancyId = "tenant1",
                PropertyRelationshipId = (int)PropertyRelationShipType.Tenant
            }
        };

            var responseBundle = new PropertyTreeResponseBundle(
                CreateMockObjectResult(new List<ManagementResponse>()),
                CreateMockObjectResult(new List<TenanciesTenantResponse> { new TenanciesTenantResponse { PropertyId = "prop1", TenancyId = "tenant1" } }),
                CreateMockObjectResult(new List<TenanciesOwnerResponse>()),
                CreateMockObjectResult(new List<MaintenanceDetailResponse>()),
                CreateMockObjectResult(new List<InspectionDetailResponse>()),
                CreateMockObjectResult(new List<ComplianceDetailResponse>()),
                CreateMockObjectResult(new List<FinancialDetailResponse>()),
                CreateMockDocumentObjectResult(new List<DocumentDetailResponse>())
            );

            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Property tree" };

            // Setup mapper mocks
            _mapperMock.Setup(m => m.Map(It.IsAny<List<TenanciesTenantResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<TenanciesTenant>>>>()))
                       .Returns(new List<TenanciesTenant> { new TenanciesTenant { SRCPropertyId = "prop1", SRCTenancyId = "tenant1" } });
            _mapperMock.Setup(m => m.Map(It.IsAny<List<TenanciesOwnerResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<TenanciesOwner>>>>()))
                       .Returns(new List<TenanciesOwner>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<MaintenanceDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<MaintenanceDetail>>>>()))
                       .Returns(new List<MaintenanceDetail>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<InspectionDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<InspectionDetail>>>>()))
                       .Returns(new List<InspectionDetail>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<ComplianceDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<ComplianceDetail>>>>()))
                       .Returns(new List<ComplianceDetail>());
            _mapperMock.Setup(m => m.Map(It.IsAny<List<FinancialDetailResponse>>(), It.IsAny<Action<IMappingOperationOptions<object, List<FinancialDetail>>>>()))
                       .Returns(new List<FinancialDetail>());

            // Setup property repository mock
            _propertyRepositoryMock.Setup(r => r.AddPropertyOtherDetails(It.IsAny<UserProperties>()))
                                   .ReturnsAsync(1);

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesOtherDataResponse(responseBundle, dataSource, userProperties);

            // Assert
            Assert.Equal(1, result); // Constants.Success
            _propertyRepositoryMock.Verify(r => r.AddPropertyOtherDetails(It.IsAny<UserProperties>()), Times.Once);
        }

        [Fact]
        public async Task ProcessGetPropertiesOtherDataResponse_ShouldReturnError_OnDeserializationFailure()
        {
            // Arrange
            var userProperties = new List<UserProperties>
        {
            new UserProperties { UserId = 1, SRCEntitytId = "prop1" }
        };

            // Create an invalid response that will cause deserialization to fail
            var invalidResponse = new ObjectResult("invalid json") { StatusCode = 200 };

            var responseBundle = new PropertyTreeResponseBundle(
                invalidResponse,
                invalidResponse,
                invalidResponse,
                invalidResponse,
                invalidResponse,
                invalidResponse,
                invalidResponse,
                invalidResponse
            );

            var dataSource = new MRI.OTA.Core.Entities.DataSource { DataSourceId = 1, Name = "Property tree" };

            // Setup API tracking service mock
            _mockAPITrackingService.Setup(a => a.LogAPITrackingDetails(It.IsAny<MRI.OTA.DBCore.Entities.APITrackingDetail>()))
                                   .Returns(Task.CompletedTask);

            // Act
            var result = await _propertTreeService.ProcessGetPropertiesOtherDataResponse(responseBundle, dataSource, userProperties);

            // Assert
            Assert.Equal(1, result); // Constants.Error
            _mockAPITrackingService.Verify(a => a.LogAPITrackingDetails(It.IsAny<MRI.OTA.DBCore.Entities.APITrackingDetail>()), Times.Exactly(7));
        }

        [Fact]
        public async Task RemoveUserAccount_ShouldReturnTrue_WhenNoDataSourcesRequiringDisconnect()
        {
            // Arrange
            var userId = 1;
            var providerId = "provider123";
            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
        {
            new MRI.OTA.Core.Entities.DataSource { Name = "SelfSource", ManifestJson = "{}" }
        };

            _userRepositoryMock.Setup(r => r.GetDataSourcesByUserId(userId)).ReturnsAsync(dataSources);

            // Act
            var result = await _propertTreeService.RemoveUserAccount(userId, providerId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task RemoveUserAccount_ShouldReturnTrue_WhenDisconnectSucceeds()
        {
            // Arrange
            var userId = 1;
            var providerId = "provider123";
            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
        {
            new MRI.OTA.Core.Entities.DataSource
            {
                DataSourceId = 1,
                Name = "Property tree",
                AccessSecret = "secret",
                ManifestJson = JsonConvert.SerializeObject(new DataSourceManifest
                {
                    BaseUrl = "http://test.com",
                    DisConnectUser = new EndpointDefinition { Endpoint = "/disconnect", Method = "DELETE" }
                })
            }
        };

            _userRepositoryMock.Setup(r => r.GetDataSourcesByUserId(userId)).ReturnsAsync(dataSources);
            _proxyServiceMock.Setup(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()))
                            .ReturnsAsync(new ObjectResult("Success") { StatusCode = 200 });

            // Act
            var result = await _propertTreeService.RemoveUserAccount(userId, providerId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task RemoveUserAccount_ShouldSkipDisconnect_WhenManifestSectionMissing()
        {
            // Arrange
            var userId = 1;
            var providerId = "provider123";
            var dataSources = new List<MRI.OTA.Core.Entities.DataSource>
        {
            new MRI.OTA.Core.Entities.DataSource
            {
                DataSourceId = 1,
                Name = "Property tree",
                AccessSecret = "secret",
                ManifestJson = JsonConvert.SerializeObject(new DataSourceManifest
                {
                    BaseUrl = "http://test.com"
                    // No DisConnectUser section
                })
            }
        };

            _userRepositoryMock.Setup(r => r.GetDataSourcesByUserId(userId)).ReturnsAsync(dataSources);

            // Act
            var result = await _propertTreeService.RemoveUserAccount(userId, providerId);

            // Assert
            Assert.True(result);
            _proxyServiceMock.Verify(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public void CreateAgencyPartnerProxyRequest_ValidInput_ReturnsCorrectProxyRequestModel()
        {
            // Arrange
            var manifest = new DataSourceManifest
            {
                BaseUrl = "http://test.com"
            };
            var agencyId = "agency123";

            var service = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);

            // Act
            var result = service.GetType()
                .GetMethod("CreateAgencyPartnerProxyRequest", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance)
                .Invoke(service, new object[] { manifest, agencyId }) as ProxyRequestModel;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("GET", result.Method);
            Assert.Contains(agencyId, result.FullUrl);
            Assert.True(Guid.TryParse(result.RequestId, out _));
            Assert.Null(result.Body);
        }

        [Fact]
        public void CreateDisConnectUserProxyRequest_ValidInput_ReturnsCorrectProxyRequestModel()
        {
            // Arrange
            var manifest = new DataSourceManifest { BaseUrl = "http://test.com" };
            var providerId = "provider123";
            // Ensure the endpoint exists in the dictionary for the test
            Constants.integrationEndpoints["DisconnectUser"] = "/disconnect";

            var service = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);

            // Use reflection to access the private method
            var method = typeof(PropertTreeService).GetMethod("CreateDisConnectUserProxyRequest", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act
            var result = method.Invoke(service, new object[] { manifest, providerId }) as ProxyRequestModel;

            // Assert
            Assert.NotNull(result);
            Assert.Equal("DELETE", result.Method);
            Assert.Contains(manifest.BaseUrl, result.FullUrl);
            Assert.Contains("/disconnect", result.FullUrl);
            Assert.Contains(providerId, result.FullUrl);
            Assert.True(Guid.TryParse(result.RequestId, out _));
            Assert.Null(result.Body);
        }

        [Fact]
        public void CreateDisConnectUserProxyRequest_ThrowsArgumentNullException_WhenManifestIsNull()
        {
            // Arrange
            var providerId = "provider123";
            var service = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);

            var method = typeof(PropertTreeService).GetMethod("CreateDisConnectUserProxyRequest", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act & Assert
            var ex = Assert.Throws<TargetInvocationException>(() =>
                method.Invoke(service, new object?[] { null, providerId }));
            Assert.IsType<ArgumentNullException>(ex.InnerException);
        }

        [Fact]
        public void CreateDisConnectUserProxyRequest_ThrowsInvalidOperationException_WhenEndpointNotFound()
        {
            // Arrange
            var manifest = new DataSourceManifest { BaseUrl = "http://test.com" };
            var providerId = "provider123";
            // Remove the endpoint if it exists
            if (Constants.integrationEndpoints.ContainsKey("DisconnectUser"))
                Constants.integrationEndpoints.Remove("DisconnectUser");

            var service = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);

            var method = typeof(PropertTreeService).GetMethod("CreateDisConnectUserProxyRequest", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

            // Act & Assert
            var ex = Assert.Throws<TargetInvocationException>(() =>
                method.Invoke(service, new object[] { manifest, providerId }));
            Assert.IsType<InvalidOperationException>(ex.InnerException);
        }

        [Fact]
        public void CreateProxyRequest_ValidInput_ReturnsCorrectProxyRequestModel()
        {
            // Arrange
            var manifest = new DataSourceManifest { BaseUrl = "http://test.com/" };
            var sectionType = IntegrationEndPointsType.ManagementList;
            var managementIds = new[] { "mgmt1", "mgmt2" };
            var modifiedSince = "2024-01-01T00:00:00Z";
            // Ensure endpoint exists in the dictionary for the test
            Constants.integrationEndpoints["Management"] = "management";

            var service = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);

            // Act
            var result = service.CreateProxyRequest(manifest, sectionType, modifiedSince, managementIds);

            // Assert
            Assert.NotNull(result);
            Assert.Equal("POST", result.Method);
            Assert.Equal("http://test.com/management", result.FullUrl);
            Assert.True(Guid.TryParse(result.RequestId, out _));
            Assert.IsType<ManagementRequestModel>(result.Body);
            var body = result.Body as ManagementRequestModel;
            Assert.Equal(managementIds, body.ManagementIds);
            Assert.Equal(modifiedSince, body.ModifiedSince);
        }

        [Fact]
        public void CreateProxyRequest_ThrowsArgumentException_WhenEndpointTypeIsUnknown()
        {
            // Arrange
            var manifest = new DataSourceManifest { BaseUrl = "http://test.com/" };
            // Use an invalid enum value
            var invalidSectionType = (IntegrationEndPointsType)999;

            var service = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);

            // Act & Assert
            Assert.Throws<ArgumentException>(() =>
                service.CreateProxyRequest(manifest, invalidSectionType));
        }

        [Fact]
        public void CreateProxyRequest_ThrowsInvalidOperationException_WhenEndpointNotFound()
        {
            // Arrange
            var manifest = new DataSourceManifest { BaseUrl = "http://test.com/" };
            var sectionType = IntegrationEndPointsType.ManagementList;
            // Remove endpoint if exists
            if (Constants.integrationEndpoints.ContainsKey("Management"))
                Constants.integrationEndpoints.Remove("Management");

            var service = new PropertTreeService(
                _loggerMock.Object,
                _mapperMock.Object,
                _propertyRepositoryMock.Object,
                _userRepositoryMock.Object,
                _proxyServiceMock.Object,
                _taskContextMock.Object,
                _mockAPITrackingService.Object,
                _dataSourceRepositoryMock.Object,
                _configurationMock.Object,
                _integrationRepositoryMock.Object,
                _notificationServiceMock.Object,
                _batchLoggerMock.Object);

            // Act & Assert
            Assert.Throws<InvalidOperationException>(() =>
                service.CreateProxyRequest(manifest, sectionType));
        }

        [Fact]
        public async Task SyncDataGeneric_ReturnsFalse_WhenEndpointNotSupported()
        {
            // Arrange
            var syncConfig = new PropertTreeService.SyncConfig
            {
                SectionType = (IntegrationEndPointsType)999, // Not supported
                DataTypeName = "test",
                ColumnName = "col"
            };
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest() };
            var service = GetService();

            // Act
            var result = await InvokeSyncDataGeneric(service, syncConfig, config);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SyncDataGeneric_ReturnsTrue_WhenNoDataToSync()
        {
            // Arrange
            var syncConfig = new PropertTreeService.SyncConfig
            {
                SectionType = IntegrationEndPointsType.ManagementList,
                DataTypeName = "test",
                ColumnName = "col"
            };
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest() };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueManagementOrTenancyCount(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int?>())).ReturnsAsync(0);
            var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeSyncDataGeneric(service, syncConfig, config);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task SyncDataGeneric_ReturnsFalse_OnException()
        {
            // Arrange
            var syncConfig = new PropertTreeService.SyncConfig
            {
                SectionType = IntegrationEndPointsType.ManagementList,
                DataTypeName = "test",
                ColumnName = "col"
            };
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest() };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueManagementOrTenancyCount(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int?>())).ThrowsAsync(new Exception("fail"));
            var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeSyncDataGeneric(service, syncConfig, config);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task ProcessDocumentDataByColumn_ReturnsTrue_WhenNoData()
        {
            // Arrange
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest(), BatchSize = 10 };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueManagementOrTenancyCount(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int?>())).ReturnsAsync(0);
            var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeProcessDocumentDataByColumn(service, config, "col");

            // Assert
            Assert.True(result.Count == 0);
        }

        [Fact]
        public async Task ProcessDocumentDataByColumn_ReturnsFalse_OnException()
        {
            // Arrange
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest(), BatchSize = 10 };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueManagementOrTenancyCount(It.IsAny<int>(), It.IsAny<string>(), It.IsAny<int?>()))
                .ThrowsAsync(new Exception("fail")); var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeProcessDocumentDataByColumn(service, config, "col");

            // Assert
            Assert.False(result == null);
        }

        [Fact]
        public async Task ProcessAgencyPartnerDataSync_ReturnsTrue_WhenNoAgencies()
        {
            // Arrange
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest(), BatchSize = 10 };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueAgenciesCount(It.IsAny<int>())).ReturnsAsync(0);
            var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeProcessAgencyPartnerDataSync(service, config);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ProcessAgencyPartnerDataSync_ReturnsFalse_OnException()
        {
            // Arrange
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest(), BatchSize = 10 };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueAgenciesCount(It.IsAny<int>())).ThrowsAsync(new Exception("fail"));
            var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeProcessAgencyPartnerDataSync(service, config);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task ProcessAgencyDataSync_ReturnsTrue_WhenNoAgencies()
        {
            // Arrange
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest(), BatchSize = 10 };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueAgenciesCount(It.IsAny<int>())).ReturnsAsync(0);
            var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeProcessAgencyDataSync(service, config);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task ProcessAgencyDataSync_ReturnsFalse_OnException()
        {
            // Arrange
            var config = new BatchProcessingConfig { DataSourceName = "ds", DataSourceId = 1, Manifest = new DataSourceManifest(), BatchSize = 10 };
            var integrationRepo = new Mock<IIntegrationRepository>();
            integrationRepo.Setup(r => r.GetUniqueAgenciesCount(It.IsAny<int>())).ThrowsAsync(new Exception("fail"));
            var service = GetService(integrationRepo: integrationRepo.Object);

            // Act
            var result = await InvokeProcessAgencyDataSync(service, config);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void ValidateAndExtractResponse_ReturnsFalse_WhenNotObjectResult()
        {
            // Arrange
            var method = typeof(PropertTreeService)
                .GetMethod("ValidateAndExtractResponse", BindingFlags.NonPublic | BindingFlags.Static);

            // Act
            var resultObj = method.Invoke(null, new object[] { new BadRequestResult(), "op" });
            Assert.NotNull(resultObj);
            var output = ((bool, string))resultObj;

            // Assert
            Assert.False(output.Item1);
            Assert.Equal(string.Empty, output.Item2);
        }

        [Fact]
        public void ValidateAndExtractResponse_ReturnsFalse_WhenStatusCodeNot200()
        {
            // Arrange
            var objectResult = new ObjectResult("error") { StatusCode = 500 };
            var method = typeof(PropertTreeService)
                .GetMethod("ValidateAndExtractResponse", BindingFlags.NonPublic | BindingFlags.Static);

            // Act
            var resultObj = method.Invoke(null, new object[] { objectResult, "op" });
            Assert.NotNull(resultObj);
            var output = ((bool, string))resultObj;

            // Assert
            Assert.False(output.Item1);
            Assert.Equal(string.Empty, output.Item2);
        }

        [Fact]
        public void ValidateAndExtractResponse_ReturnsTrueAndContent_WhenJsonDocument()
        {
            // Arrange
            var json = JsonDocument.Parse("[{\"a\":1}]");
            var objectResult = new ObjectResult(json) { StatusCode = 200 };
            var method = typeof(PropertTreeService)
                .GetMethod("ValidateAndExtractResponse", BindingFlags.NonPublic | BindingFlags.Static);

            // Act
            var resultObj = method.Invoke(null, new object[] { objectResult, "op" });
            Assert.NotNull(resultObj);
            var output = ((bool, string))resultObj;

            // Assert
            Assert.True(output.Item1);
            Assert.Contains("a", output.Item2);
        }

        [Fact]
        public void ValidateAndExtractResponse_ReturnsTrueAndContent_WhenString()
        {
            // Arrange
            var objectResult = new ObjectResult("test-string") { StatusCode = 200 };
            var method = typeof(PropertTreeService)
                .GetMethod("ValidateAndExtractResponse", BindingFlags.NonPublic | BindingFlags.Static);

            // Act
            var resultObj = method.Invoke(null, new object[] { objectResult, "op" });
            Assert.NotNull(resultObj);
            var output = ((bool, string))resultObj;

            // Assert
            Assert.True(output.Item1);
            Assert.Equal("test-string", output.Item2);
        }

        // --- Helper methods to invoke private/protected methods ---

        private PropertTreeService GetService(
            IIntegrationRepository? integrationRepo = null)
        {
            return new PropertTreeService(
                new Mock<ILogger<PropertTreeService>>().Object,
                new Mock<IMapper>().Object,
                new Mock<IPropertyRepository>().Object,
                new Mock<IUserRepository>().Object,
                new Mock<IProxyService>().Object,
                new Mock<TaskContext>().Object,
                new Mock<IAPITrackingService>().Object,
                new Mock<IDataSourceRepository>().Object,
                new Mock<IConfiguration>().Object,
                integrationRepo ?? new Mock<IIntegrationRepository>().Object,
                new Mock<INotificationService>().Object,
                new Mock<IBatchOperationLogger>().Object
            );
        }

        private async Task<bool> InvokeSyncDataGeneric(PropertTreeService service, PropertTreeService.SyncConfig syncConfig, BatchProcessingConfig config)
        {
            var method = typeof(PropertTreeService).GetMethod("SyncDataGeneric", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var task = (Task<bool>)method.MakeGenericMethod(typeof(object), typeof(object)).Invoke(service, new object[] { syncConfig, config });
            return await task;
        }

        private async Task<List<SQLQueryMergeResult>> InvokeProcessDocumentDataByColumn(PropertTreeService service, BatchProcessingConfig config, string columnName)
        {
            var method = typeof(PropertTreeService).GetMethod("ProcessDocumentDataByColumn", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var task = (Task<List<SQLQueryMergeResult>>)method.Invoke(service, new object[] { config, columnName });
            return await task;
        }

        private async Task<bool> InvokeProcessAgencyPartnerDataSync(PropertTreeService service, BatchProcessingConfig config)
        {
            var method = typeof(PropertTreeService).GetMethod("ProcessAgencyPartnerDataSync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var task = (Task<bool>)method.Invoke(service, new object[] { config });
            return await task;
        }

        private async Task<bool> InvokeProcessAgencyDataSync(PropertTreeService service, BatchProcessingConfig config)
        {
            var method = typeof(PropertTreeService).GetMethod("ProcessAgencyDataSync", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            var task = (Task<bool>)method.Invoke(service, new object[] { config });
            return await task;
        }
    }
}