﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.Common.Database
{
    public class SqlConnectionConfiguration
    {
        /// <summary>
        /// Maximum number of connections allowed in the pool
        /// </summary>
        public int MaxPoolSize { get; set; }

        /// <summary>
        /// Minimum number of connections maintained in the pool
        /// </summary>
        public int MinPoolSize { get; set; }

        /// <summary>
        /// Time in seconds to wait for a connection to become available
        /// </summary>
        public int ConnectionTimeout { get; set; }

        /// <summary>
        /// Time in seconds to wait for a command to complete
        /// </summary>
        public int CommandTimeout { get; set; }

        /// <summary>
        /// Whether to enable retry logic for failed connections
        /// </summary>
        public bool EnableRetryLogic { get; set; }

        /// <summary>
        /// Maximum number of retry attempts for failed connections
        /// </summary>
        public int MaxRetryAttempts { get; set; }

        /// <summary>
        /// Time to wait between retry attempts
        /// </summary>
        public TimeSpan RetryInterval { get; set; }
    }
}
