namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// Application model for Maintenance Detail
    /// </summary>
    public class MaintenanceDetailModel
    {
        /// <summary>
        /// Management ID
        /// </summary>
        public string ManagementId { get; set; }
        public string TenancyId { get; set; }

        /// <summary>
        /// Property ID
        /// </summary>
        public string PropertyId { get; set; }

        /// <summary>
        /// Job ID
        /// </summary>
        public string JobId { get; set; }

        /// <summary>
        /// Job Summary
        /// </summary>
        public string JobSummary { get; set; }

        /// <summary>
        /// Job Status
        /// </summary>
        public string? JobStatus { get; set; }

        /// <summary>
        /// Request ID
        /// </summary>
        public string RequestId { get; set; }

        /// <summary>
        /// Request Summary
        /// </summary>
        public string RequestSummary { get; set; }

        /// <summary>
        /// Request Status
        /// </summary>
        public string? RequestStatus { get; set; }

        /// <summary>
        /// Request Raised By
        /// </summary>
        public string RequestRaisedBy { get; set; }

        /// <summary>
        /// Request Raised Date
        /// </summary>
        public DateTime RequestRaisedDate { get; set; }

        /// <summary>
        /// Image Link
        /// </summary>
        public string ImageLink { get; set; }

        public string? AgencyId { get; set; }

        public string? AgencyName { get; set; }

        /// <summary>
        /// Days since request raised (calculated field)
        /// </summary>
        public int DaysSinceRaised => (DateTime.Now - RequestRaisedDate).Days;
    }
} 