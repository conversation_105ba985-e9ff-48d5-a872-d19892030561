﻿using Microsoft.AspNetCore.Hosting.Server;
using Newtonsoft.Json.Linq;

namespace MRI.OTA.Common.Constants
{
    public static class MessagesConstants
    {
        public const string EmailNotFound = "User email address not found.";

        public const string ItemNotCreatedError = "The item could not be created.";
        public const string ItemNotCreated = "Item not created";
        public const string ItemNotDeleted = "Item not deleted";
        public const string ItemDeleted = "Item deleted successfully";
        public const string ItemCreated = "Item created successfully";
        public const string ItemNotUpdated = "Item not updated";
        public const string ItemUpdatedSucess = "Item updated successfully";
        public const string ItemNotFound = "Item not found";
        public const string ItemSpecifiedIdNotExists = "The item with the specified ID does not exist.";
        public const string TokenExpiredMessage = "The access token has expired.";
        public const string TokenValidationFailedMessage = "Token validation failed.";
        public const string TokenInvalidSignatureMessage = "The token signature is invalid.";
        public const string TokenInvalidIssuerMessage = "The token issuer is not trusted.";
        public const string InternalServerErrorMessage = "An internal server error occurred.Please try again later.";
        public const string InvalidApiKeyMessage = "Invalid api key or secret.";
    }
}
