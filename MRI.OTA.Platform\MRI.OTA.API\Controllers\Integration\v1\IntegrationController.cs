﻿using Asp.Versioning;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;
using System.Net.Mime;

namespace MRI.OTA.API.Controllers.Integration.v1
{
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/integrations")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class IntegrationController : ControllerBase
    {
        private readonly IPropertTreeService _propertTreeService;
        private readonly TelemetryClient _telemetryClient;

        /// <summary>
        /// Constructor for integration controller
        /// </summary>
        /// <param name="propertyService"></param>
        public IntegrationController(IPropertTreeService propertTreeService, TelemetryClient telemetryClient)
        {
            _propertTreeService = propertTreeService;
            _telemetryClient = telemetryClient;
        }

        /// <summary>
        /// Synced agency data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-agency")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "agency data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncAgencyData()
        {
            // Create operation ID for tracking this request in AppInsights
            string operationId = Guid.NewGuid().ToString();

            // Create a new RequestTelemetry
            var requestTelemetry = new RequestTelemetry
            {
                Name = "SyncAgencyData",
                Id = operationId
            };
            try
            {
                // Add custom properties to the telemetry
                _telemetryClient.TrackTrace("Starting sync agency process", SeverityLevel.Information,
                    new Dictionary<string, string>
                    {
                        { "Process", "Agency Sync process started" },
                        { "OperationId", operationId }
                    });
                var result = await _propertTreeService.SyncAgencyData();
                if (result)
                {
                    _telemetryClient.TrackTrace("Agency sync process completed successfully", SeverityLevel.Information,
                      new Dictionary<string, string> { { "OperationId", operationId } });

                    // Track successful request
                    requestTelemetry.Success = true;
                    requestTelemetry.ResponseCode = StatusCodes.Status200OK.ToString();
                    _telemetryClient.TrackRequest(requestTelemetry);

                    return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "agency data synced successfully.", null!));
                }
                else
                {
                    _telemetryClient.TrackTrace("Agency sync process failed", SeverityLevel.Warning,
                       new Dictionary<string, string> { { "OperationId", operationId } });

                    // Track failed request
                    requestTelemetry.Success = false;
                    requestTelemetry.ResponseCode = StatusCodes.Status400BadRequest.ToString();
                    _telemetryClient.TrackRequest(requestTelemetry);

                    return BadRequest(new ApiResponse<object>(false, "agency data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "agency data not synced." }));
                }
            }
            catch (Exception ex)
            {

                _telemetryClient.TrackException(ex, new Dictionary<string, string>
                {
                    { "ExceptionType", ex.GetType().Name },
                    { "OperationId", operationId }
                });

                // Track exception request
                requestTelemetry.Success = false;
                requestTelemetry.ResponseCode = StatusCodes.Status500InternalServerError.ToString();
                _telemetryClient.TrackRequest(requestTelemetry);

                return StatusCode(StatusCodes.Status500InternalServerError,
                    new ApiResponse<object>(false, "Internal Server Error", null!, StatusCodes.Status500InternalServerError, new List<string> { "An error occurred while processing your request." }));
            }
        }
         
        /// <summary>
        /// Synced agency partner data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-agencypartner")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "agency partner data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncAgencyPartnerData()
        {
            var result = await _propertTreeService.SyncAgencyPartnerData();
            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "agency partner data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "agency partner data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "agency partner data not synced." }));
            }
        }

        /// <summary>
        /// Synced property data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-property")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "property data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncPropertyData()
        {
            var result = await _propertTreeService.SyncPropertyData();
            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "property data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "property data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "property data not synced." }));
            }
        }

        /// <summary>
        /// Synced management data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-management")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "management data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncManagementData()
        {
            var result = await _propertTreeService.SyncManagementData();
            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "management data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "management data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "management data not synced." }));
            }
        }

        /// <summary>
        /// Synced financials data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-financials")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "financials data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncFinancialsData()
        {
            var result = await _propertTreeService.SyncFinancialsData();

            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "financials data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "financials data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "financials data not synced." }));
            }
        }

        /// <summary>
        /// Synced tenancies owner data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-tenancies-owner")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "tenancies owner data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncTenanciesOwnerData()
        {
            var result = await _propertTreeService.SyncTenanciesOwnerData();

            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "tenancies owner data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "tenancies owner data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "tenancies owner data not synced." }));
            }
        }

        /// <summary>
        /// Synced tenancies tenant data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-tenancies-tenant")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "tenancies tenant data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncTenanciesTenantData()
        {
            var result = await _propertTreeService.SyncTenanciesTenantData();

            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "tenancies tenant data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "tenancies tenant data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "tenancies tenant data not synced." }));
            }
        }

        /// <summary>
        /// Synced maintenance data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-maintenance")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "maintenance data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncMaintenanceData()
        {
            var result = await _propertTreeService.SyncMaintenanceData();

            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "maintenance data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "maintenance data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "maintenance data not synced." }));
            }
        }

        /// <summary>
        /// Synced inspections data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-inspections")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "inspections data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncInspectionsData()
        {
            var result = await _propertTreeService.SyncInspectionsData();

            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "inspections data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "inspections data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "inspections data not synced." }));
            }
        }

        /// <summary>
        /// Synced compliance data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-compliance")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "compliance data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncComplianceData()
        {
            var result = await _propertTreeService.SyncComplianceData();

            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "compliance data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "compliance data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "compliance data not synced." }));
            }
        }

        /// <summary>
        /// Synced document data
        /// </summary>
        /// <returns></returns>
        [HttpGet("sync-document")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "document data synced successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SyncDocumentData()
        {
            var result = await _propertTreeService.SyncDocumentData();

            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "document data synced successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "document data not synced.", null!, StatusCodes.Status400BadRequest, new List<string> { "document data not synced." }));
            }
        }
    }
}
