name: my-place-api

on:
  push:
    branches:
      - develop
  pull_request:
    branches:
      - develop
  workflow_dispatch:
    inputs:
      environment:
        description: 'Environment to deploy to (dev, qa, prod)'
        required: true
        default: 'qa'
      imageTagOverride:
        description: 'Optional image tag override'
        required: false
        default: ''

env:
  IMAGE_NAME: 'apacsharedplatform/mriota/api'

jobs:
  build-and-test:
    name: 'Build & Test'
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      - name: Restore dependencies
        run: dotnet restore MRI.OTA.Platform/MRI.OTA.API/MRI.OTA.API.csproj
 
      - name: Build API Solution
        run: dotnet build MRI.OTA.Platform/MRI.OTA.API/MRI.OTA.API.csproj --configuration Release --no-restore

      - name: Run Unit Tests with Coverage
        run: dotnet test MRI.OTA.Platform/MRI.OTA.Tests/MRI.OTA.UnitTestCases.csproj --configuration Release --collect:"XPlat Code Coverage" --logger "console;verbosity=detailed"

      - name: Install ReportGenerator
        run: dotnet tool install -g dotnet-reportgenerator-globaltool

      - name: Generate Coverage Report
        run: reportgenerator -reports:"**/coverage.cobertura.xml" -targetdir:"coveragereport" -reporttypes:Html

      - name: Upload Coverage Report
        uses: actions/upload-artifact@v4
        with:
          name: coverage-report
          path: coveragereport

  # Remove the centralized build-and-push-image job
  # Instead, in deploy-dev and deploy-qa, after the replace-config-vars step, add Docker build and push steps

  deploy-dev:
    name: 'Deploy to Dev'
    needs: build-and-test
    runs-on: ubuntu-latest
    environment: dev
    if: |
      github.event_name == 'workflow_dispatch' ||
      (github.event_name == 'push' && github.ref == 'refs/heads/develop')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set deployment environment context
        uses: ./.github/actions/set-environment-context
        with:
          environment: 'dev'

      - name: Debug Available Variables
        run: |
          echo "🔍 Checking available variables for Dev environment..."
          echo "OTA_DB_CONNECTION_STRING: ${{ vars.OTA_DB_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "AZURE_TENANT_ID: ${{ vars.AZURE_TENANT_ID != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_TENANT_NAME: ${{ vars.OTA_TENANT_NAME != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_CLIENT_ID: ${{ vars.OTA_CLIENT_ID != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_CLIENT_SECRET: ${{ secrets.OTA_CLIENT_SECRET != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_B2C_INSTANCE: ${{ vars.OTA_B2C_INSTANCE != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_B2C_DOMAIN: ${{ vars.OTA_B2C_DOMAIN != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SIGNUP_SIGNIN_POLICYID: ${{ vars.OTA_SIGNUP_SIGNIN_POLICYID != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_API_ALLOWED_ORIGINS: ${{ vars.OTA_API_ALLOWED_ORIGINS != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_APP_INSIGHT_CONNECTION_STRING: ${{ vars.OTA_APP_INSIGHT_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_STORAGE_CONNECTION_STRING: ${{ vars.OTA_STORAGE_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_STORAGE_CONTAINER_NAME: ${{ vars.OTA_STORAGE_CONTAINER_NAME != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_AZURE_COMMUNICATION_CONNECTION_STRING: ${{ vars.OTA_AZURE_COMMUNICATION_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_AZURE_COMMUNICATION_FROM_EMAIL: ${{ vars.OTA_AZURE_COMMUNICATION_FROM_EMAIL != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SENDGRID_API_KEY: ${{ vars.OTA_SENDGRID_API_KEY != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SENDGRID_FROM_EMAIL: ${{ vars.OTA_SENDGRID_FROM_EMAIL != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SENDGRID_FROM_NAME: ${{ vars.OTA_SENDGRID_FROM_NAME != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_REDIS_CONNECTION_STRING: ${{ vars.OTA_REDIS_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_REDIS_DEFAULT_EXPIRATION_MINUTES: ${{ vars.OTA_REDIS_DEFAULT_EXPIRATION_MINUTES != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_BASE_URL: ${{ vars.OTA_BASE_URL != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_DEFAULT_IMAGE_URL: ${{ vars.OTA_DEFAULT_IMAGE_URL != '' && 'SET' || 'NOT SET' }}"
          echo "ota_batch_size: ${{ vars.ota_batch_size != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_type: ${{ vars.ota_firebase_type != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_project_id: ${{ vars.ota_firebase_project_id != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_private_key_id: ${{ vars.ota_firebase_private_key_id != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_private_key: ${{ vars.ota_firebase_private_key != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_client_email: ${{ vars.ota_firebase_client_email != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_client_id: ${{ vars.ota_firebase_client_id != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_auth_uri: ${{ vars.ota_firebase_auth_uri != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_token_uri: ${{ vars.ota_firebase_token_uri != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_auth_provider_x509_cert_url: ${{ vars.ota_firebase_auth_provider_x509_cert_url != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_client_x509_cert_url: ${{ vars.ota_firebase_client_x509_cert_url != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_universe_domain: ${{ vars.ota_firebase_universe_domain != '' && 'SET' || 'NOT SET' }}"

      - name: Show Original appsettings.Production.json
        run: |
          echo "📄 Original appsettings.Production.json before replacement:"
          if [ -f "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json" ]; then
            cat "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json"
          else
            echo "appsettings.Production.json not found"
          fi

      - name: Replace appsettings placeholders
        uses: ./.github/actions/replace-config-vars
        with:
          ota-db-connection-string: ${{ vars.OTA_DB_CONNECTION_STRING }}
          azure-tenant-id: ${{ vars.AZURE_TENANT_ID }}
          ota-tenant-name: ${{ vars.OTA_TENANT_NAME }}
          ota-client-id: ${{ vars.OTA_CLIENT_ID }}
          ota-client-secret: ${{ secrets.OTA_CLIENT_SECRET }}
          ota-b2c-instance: ${{ vars.OTA_B2C_INSTANCE }}
          ota-b2c-domain: ${{ vars.OTA_B2C_DOMAIN }}
          ota-signup-signin-policyid: ${{ vars.OTA_SIGNUP_SIGNIN_POLICYID }}
          ota-api-allowed-origins: ${{ vars.OTA_API_ALLOWED_ORIGINS }}
          ota-app-insight-connection-string: ${{ vars.OTA_APP_INSIGHT_CONNECTION_STRING }}
          ota-storage-connection-string: ${{ vars.OTA_STORAGE_CONNECTION_STRING }}
          ota-storage-container-name: ${{ vars.OTA_STORAGE_CONTAINER_NAME }}
          ota-azure-communication-connection-string: ${{ vars.OTA_AZURE_COMMUNICATION_CONNECTION_STRING }}
          ota-azure-communication-from-email: ${{ vars.OTA_AZURE_COMMUNICATION_FROM_EMAIL }}
          ota-sendgrid-api-key: ${{ vars.OTA_SENDGRID_API_KEY }}
          ota-sendgrid-from-email: ${{ vars.OTA_SENDGRID_FROM_EMAIL }}
          ota-sendgrid-from-name: ${{ vars.OTA_SENDGRID_FROM_NAME }}
          ota-redis-connection-string: ${{ vars.OTA_REDIS_CONNECTION_STRING }}
          ota-redis-default-expiration-minutes: ${{ vars.OTA_REDIS_DEFAULT_EXPIRATION_MINUTES }}
          ota-base-url: ${{ vars.OTA_BASE_URL }}
          ota-default-image-url: ${{ vars.OTA_DEFAULT_IMAGE_URL }}
          ota-batch-size: ${{ vars.ota_batch_size }}
          ota-firebase-type: ${{ vars.ota_firebase_type }}
          ota-firebase-project-id: ${{ vars.ota_firebase_project_id }}
          ota-firebase-private-key-id: ${{ vars.ota_firebase_private_key_id }}
          ota-firebase-private-key: ${{ vars.ota_firebase_private_key }}
          ota-firebase-client-email: ${{ vars.ota_firebase_client_email }}
          ota-firebase-client-id: ${{ vars.ota_firebase_client_id }}
          ota-firebase-auth-uri: ${{ vars.ota_firebase_auth_uri }}
          ota-firebase-token-uri: ${{ vars.ota_firebase_token_uri }}
          ota-firebase-auth-provider-x509-cert-url: ${{ vars.ota_firebase_auth_provider_x509_cert_url }}
          ota-firebase-client-x509-cert-url: ${{ vars.ota_firebase_client_x509_cert_url }}
          ota-firebase-universe-domain: ${{ vars.ota_firebase_universe_domain }}

      - name: Display appsettings.json (Dev)
        run: |
          echo "=== DEV APPSETTINGS.JSON ==="
          if [ -f "MRI.OTA.Platform/MRI.OTA.API/appsettings.json" ]; then
            cat "MRI.OTA.Platform/MRI.OTA.API/appsettings.json"
          else
            echo "appsettings.json not found"
          fi
          echo ""
          echo "=== DEV APPSETTINGS.PRODUCTION.JSON ==="
          if [ -f "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json" ]; then
            cat "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json"
          else
            echo "appsettings.Production.json not found"
          fi

      - name: Debug ACR Endpoint
        run: |
          echo "ACR_ENDPOINT: ${{ secrets.ACR_ENDPOINT }}"
          echo "IMAGE_NAME: ${{ env.IMAGE_NAME }}"
          echo "IMAGE_TAG: ${{ needs.build-and-push-image.outputs.image-tag }}"
          echo "Full image: ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ needs.build-and-push-image.outputs.image-tag }}"

      - name: Docker Login to Azure
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_ENDPOINT }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push Docker Image (Dev)
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile
          push: true
          tags: |
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:dev-latest
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:dev-${{ github.run_id }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Deploy to Azure Container App (Dev)
        uses: ./.github/actions/deploy-container-app
        with:
          image-tag: dev-${{ github.run_id }}
          acr-endpoint: ${{ secrets.ACR_ENDPOINT }}
          image-name: ${{ env.IMAGE_NAME }}

      - name: Wait for Container App to be ready
        run: |
          echo "⏳ Waiting for Container App to be ready..."
          sleep 30
          echo "✅ Container App deployment initiated"

      - name: Get Container App URL
        id: get-app-url
        run: |
          APP_URL=$(az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.configuration.ingress.fqdn" -o tsv)
          echo "url=https://$APP_URL" >> $GITHUB_OUTPUT
          echo "🌐 Application URL: https://$APP_URL"

      - name: Health Check - Container App Status
        run: |
          echo "🔍 Checking Container App status..."
          az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.runningStatus" -o table
          
          # Check if container is running
          RUNNING_STATUS=$(az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.runningStatus" -o tsv)
          if [ "$RUNNING_STATUS" = "Running" ]; then
            echo "✅ Container App is running successfully"
          else
            echo "❌ Container App is not running. Status: $RUNNING_STATUS"
            exit 1
          fi

      - name: Health Check - Application Endpoint
        run: |
          echo "🔍 Testing application health endpoint..."
          APP_URL=$(az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.configuration.ingress.fqdn" -o tsv)
          
          # Wait for application to be ready
          echo "⏳ Waiting for application to be ready..."
          for i in {1..10}; do
            if curl -f -s "https://$APP_URL/healthz" > /dev/null 2>&1; then
              echo "✅ Health endpoint is responding"
              break
            else
              echo "⏳ Attempt $i/10: Application not ready yet, waiting..."
              sleep 30
            fi
          done
          
          # Final health check
          if curl -f -s "https://$APP_URL/healthz" > /dev/null 2>&1; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed - application is not responding"
            exit 1
          fi

      - name: Deployment Summary (Dev)
        run: |
          echo "## Dev Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Deployment Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Deployment Details:**" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: Dev" >> $GITHUB_STEP_SUMMARY
          echo "- Container App: ${{ env.CONTAINER_APP }}" >> $GITHUB_STEP_SUMMARY
          echo "- Resource Group: ${{ env.RG_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo "- Image: ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ needs.build-and-push-image.outputs.image-tag }}" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Running" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Health Check Results:**" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Container App Status: Running" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Application Endpoint: Responding" >> $GITHUB_STEP_SUMMARY

  validate-qa-deployment:
    name: 'Validate QA Deployment'
    needs: deploy-dev
    runs-on: ubuntu-latest
    if: |
      github.event_name == 'workflow_dispatch' ||
      (github.event_name == 'push' && github.ref == 'refs/heads/develop')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Validate Dev Deployment
        run: |
          echo "✅ Dev deployment completed successfully"
          echo "📋 QA deployment validation checklist:"
          echo "   - Dev environment is stable"
          echo "   - All tests passed"
          echo "   - Docker image built successfully"
          echo "   - Ready for QA approval"

      - name: Create QA Deployment Summary
        run: |
          echo "## QA Deployment Summary" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Build Information:**" >> $GITHUB_STEP_SUMMARY
          echo "- Commit: ${{ github.sha }}" >> $GITHUB_STEP_SUMMARY
          echo "- Branch: ${{ github.ref_name }}" >> $GITHUB_STEP_SUMMARY
          echo "- Image Tag: ${{ github.event.inputs.imageTagOverride || format('revision-{0}', github.run_id) }}" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Validation Results:**" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Build completed successfully" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Unit tests passed" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Dev deployment successful" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Next Steps:**" >> $GITHUB_STEP_SUMMARY
          echo "1. Review the deployment summary above" >> $GITHUB_STEP_SUMMARY
          echo "2. Approve QA deployment in the next job" >> $GITHUB_STEP_SUMMARY
          echo "3. Monitor QA deployment progress" >> $GITHUB_STEP_SUMMARY

  deploy-qa:
    name: 'Deploy to QA (Requires Approval)'
    needs: [validate-qa-deployment]
    runs-on: ubuntu-latest
    environment: 
      name: qa
      url: ${{ steps.get-app-url.outputs.url }}
    if: |
      github.event_name == 'workflow_dispatch' ||
      (github.event_name == 'push' && github.ref == 'refs/heads/develop')

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Pre-deployment Validation
        run: |
          echo "🔍 Pre-deployment validation for QA environment"
          echo "✅ All previous validations passed"
          echo "✅ Manual approval received"
          echo "🚀 Proceeding with QA deployment..."

      - name: Set deployment environment context
        uses: ./.github/actions/set-environment-context
        with:
          environment: 'qa'

      - name: Debug Available Variables (QA)
        run: |
          echo "🔍 Checking available variables for QA environment..."
          echo "OTA_DB_CONNECTION_STRING: ${{ vars.OTA_DB_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "AZURE_TENANT_ID: ${{ vars.AZURE_TENANT_ID != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_TENANT_NAME: ${{ vars.OTA_TENANT_NAME != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_CLIENT_ID: ${{ vars.OTA_CLIENT_ID != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_CLIENT_SECRET: ${{ secrets.OTA_CLIENT_SECRET != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_B2C_INSTANCE: ${{ vars.OTA_B2C_INSTANCE != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_B2C_DOMAIN: ${{ vars.OTA_B2C_DOMAIN != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SIGNUP_SIGNIN_POLICYID: ${{ vars.OTA_SIGNUP_SIGNIN_POLICYID != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_API_ALLOWED_ORIGINS: ${{ vars.OTA_API_ALLOWED_ORIGINS != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_APP_INSIGHT_CONNECTION_STRING: ${{ vars.OTA_APP_INSIGHT_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_STORAGE_CONNECTION_STRING: ${{ vars.OTA_STORAGE_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_STORAGE_CONTAINER_NAME: ${{ vars.OTA_STORAGE_CONTAINER_NAME != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_AZURE_COMMUNICATION_CONNECTION_STRING: ${{ vars.OTA_AZURE_COMMUNICATION_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_AZURE_COMMUNICATION_FROM_EMAIL: ${{ vars.OTA_AZURE_COMMUNICATION_FROM_EMAIL != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SENDGRID_API_KEY: ${{ vars.OTA_SENDGRID_API_KEY != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SENDGRID_FROM_EMAIL: ${{ vars.OTA_SENDGRID_FROM_EMAIL != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_SENDGRID_FROM_NAME: ${{ vars.OTA_SENDGRID_FROM_NAME != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_REDIS_CONNECTION_STRING: ${{ vars.OTA_REDIS_CONNECTION_STRING != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_REDIS_DEFAULT_EXPIRATION_MINUTES: ${{ vars.OTA_REDIS_DEFAULT_EXPIRATION_MINUTES != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_BASE_URL: ${{ vars.OTA_BASE_URL != '' && 'SET' || 'NOT SET' }}"
          echo "OTA_DEFAULT_IMAGE_URL: ${{ vars.OTA_DEFAULT_IMAGE_URL != '' && 'SET' || 'NOT SET' }}"
          echo "ota_batch_size: ${{ vars.ota_batch_size != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_type: ${{ vars.ota_firebase_type != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_project_id: ${{ vars.ota_firebase_project_id != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_private_key_id: ${{ vars.ota_firebase_private_key_id != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_private_key: ${{ vars.ota_firebase_private_key != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_client_email: ${{ vars.ota_firebase_client_email != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_client_id: ${{ vars.ota_firebase_client_id != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_auth_uri: ${{ vars.ota_firebase_auth_uri != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_token_uri: ${{ vars.ota_firebase_token_uri != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_auth_provider_x509_cert_url: ${{ vars.ota_firebase_auth_provider_x509_cert_url != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_client_x509_cert_url: ${{ vars.ota_firebase_client_x509_cert_url != '' && 'SET' || 'NOT SET' }}"
          echo "ota_firebase_universe_domain: ${{ vars.ota_firebase_universe_domain != '' && 'SET' || 'NOT SET' }}"

      - name: Show Original appsettings.Production.json (QA)
        run: |
          echo "📄 Original appsettings.Production.json before replacement (QA):"
          if [ -f "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json" ]; then
            cat "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json"
          else
            echo "appsettings.Production.json not found"
          fi

      - name: Replace appsettings placeholders
        uses: ./.github/actions/replace-config-vars
        with:
          ota-db-connection-string: ${{ vars.OTA_DB_CONNECTION_STRING }}
          azure-tenant-id: ${{ vars.AZURE_TENANT_ID }}
          ota-tenant-name: ${{ vars.OTA_TENANT_NAME }}
          ota-client-id: ${{ vars.OTA_CLIENT_ID }}
          ota-client-secret: ${{ secrets.OTA_CLIENT_SECRET }}
          ota-b2c-instance: ${{ vars.OTA_B2C_INSTANCE }}
          ota-b2c-domain: ${{ vars.OTA_B2C_DOMAIN }}
          ota-signup-signin-policyid: ${{ vars.OTA_SIGNUP_SIGNIN_POLICYID }}
          ota-api-allowed-origins: ${{ vars.OTA_API_ALLOWED_ORIGINS }}
          ota-app-insight-connection-string: ${{ vars.OTA_APP_INSIGHT_CONNECTION_STRING }}
          ota-storage-connection-string: ${{ vars.OTA_STORAGE_CONNECTION_STRING }}
          ota-storage-container-name: ${{ vars.OTA_STORAGE_CONTAINER_NAME }}
          ota-azure-communication-connection-string: ${{ vars.OTA_AZURE_COMMUNICATION_CONNECTION_STRING }}
          ota-azure-communication-from-email: ${{ vars.OTA_AZURE_COMMUNICATION_FROM_EMAIL }}
          ota-sendgrid-api-key: ${{ vars.OTA_SENDGRID_API_KEY }}
          ota-sendgrid-from-email: ${{ vars.OTA_SENDGRID_FROM_EMAIL }}
          ota-sendgrid-from-name: ${{ vars.OTA_SENDGRID_FROM_NAME }}
          ota-redis-connection-string: ${{ vars.OTA_REDIS_CONNECTION_STRING }}
          ota-redis-default-expiration-minutes: ${{ vars.OTA_REDIS_DEFAULT_EXPIRATION_MINUTES }}
          ota-base-url: ${{ vars.OTA_BASE_URL }}
          ota-default-image-url: ${{ vars.OTA_DEFAULT_IMAGE_URL }}
          ota-batch-size: ${{ vars.ota_batch_size }}
          ota-firebase-type: ${{ vars.ota_firebase_type }}
          ota-firebase-project-id: ${{ vars.ota_firebase_project_id }}
          ota-firebase-private-key-id: ${{ vars.ota_firebase_private_key_id }}
          ota-firebase-private-key: ${{ vars.ota_firebase_private_key }}
          ota-firebase-client-email: ${{ vars.ota_firebase_client_email }}
          ota-firebase-client-id: ${{ vars.ota_firebase_client_id }}
          ota-firebase-auth-uri: ${{ vars.ota_firebase_auth_uri }}
          ota-firebase-token-uri: ${{ vars.ota_firebase_token_uri }}
          ota-firebase-auth-provider-x509-cert-url: ${{ vars.ota_firebase_auth_provider_x509_cert_url }}
          ota-firebase-client-x509-cert-url: ${{ vars.ota_firebase_client_x509_cert_url }}
          ota-firebase-universe-domain: ${{ vars.ota_firebase_universe_domain }}

      - name: Display appsettings.json (QA)
        run: |
          echo "=== QA APPSETTINGS.JSON ==="
          if [ -f "MRI.OTA.Platform/MRI.OTA.API/appsettings.json" ]; then
            cat "MRI.OTA.Platform/MRI.OTA.API/appsettings.json"
          else
            echo "appsettings.json not found"
          fi
          echo ""
          echo "=== QA APPSETTINGS.PRODUCTION.JSON ==="
          if [ -f "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json" ]; then
            cat "MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json"
          else
            echo "appsettings.Production.json not found"
          fi

      - name: Debug ACR Endpoint
        run: |
          echo "ACR_ENDPOINT: ${{ secrets.ACR_ENDPOINT }}"
          echo "IMAGE_NAME: ${{ env.IMAGE_NAME }}"
          echo "IMAGE_TAG: ${{ needs.build-and-push-image.outputs.image-tag }}"
          echo "Full image: ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ needs.build-and-push-image.outputs.image-tag }}"

      - name: Docker Login to Azure
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_ENDPOINT }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push Docker Image (QA)
        uses: docker/build-push-action@v3
        with:
          context: .
          file: Dockerfile
          push: true
          tags: |
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:qa-latest
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:qa-${{ github.run_id }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Deploy to Azure Container App (QA)
        uses: ./.github/actions/deploy-container-app
        with:
          image-tag: qa-${{ github.run_id }}
          acr-endpoint: ${{ secrets.ACR_ENDPOINT }}
          image-name: ${{ env.IMAGE_NAME }}

      - name: Wait for Container App to be ready
        run: |
          echo "⏳ Waiting for Container App to be ready..."
          sleep 30
          echo "✅ Container App deployment initiated"

      - name: Get Container App URL
        id: get-app-url
        run: |
          APP_URL=$(az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.configuration.ingress.fqdn" -o tsv)
          echo "url=https://$APP_URL" >> $GITHUB_OUTPUT
          echo "🌐 Application URL: https://$APP_URL"

      - name: Health Check - Container App Status
        run: |
          echo "🔍 Checking Container App status..."
          az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.runningStatus" -o table
          
          # Check if container is running
          RUNNING_STATUS=$(az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.runningStatus" -o tsv)
          if [ "$RUNNING_STATUS" = "Running" ]; then
            echo "✅ Container App is running successfully"
          else
            echo "❌ Container App is not running. Status: $RUNNING_STATUS"
            exit 1
          fi

      - name: Health Check - Application Endpoint
        run: |
          echo "🔍 Testing application health endpoint..."
          APP_URL=$(az containerapp show --name ${{ env.CONTAINER_APP }} --resource-group ${{ env.RG_NAME }} --query "properties.configuration.ingress.fqdn" -o tsv)
          
          # Wait for application to be ready
          echo "⏳ Waiting for application to be ready..."
          for i in {1..10}; do
            if curl -f -s "https://$APP_URL/healthz" > /dev/null 2>&1; then
              echo "✅ Health endpoint is responding"
              break
            else
              echo "⏳ Attempt $i/10: Application not ready yet, waiting..."
              sleep 30
            fi
          done
          
          # Final health check
          if curl -f -s "https://$APP_URL/healthz" > /dev/null 2>&1; then
            echo "✅ Health check passed"
          else
            echo "❌ Health check failed - application is not responding"
            exit 1
          fi

      - name: Deployment Summary (QA)
        run: |
          echo "## QA Deployment Report" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Deployment Status:** ✅ Success" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Deployment Details:**" >> $GITHUB_STEP_SUMMARY
          echo "- Environment: QA" >> $GITHUB_STEP_SUMMARY
          echo "- Container App: ${{ env.CONTAINER_APP }}" >> $GITHUB_STEP_SUMMARY
          echo "- Resource Group: ${{ env.RG_NAME }}" >> $GITHUB_STEP_SUMMARY
          echo "- Image: ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ needs.build-and-push-image.outputs.image-tag }}" >> $GITHUB_STEP_SUMMARY
          echo "- Status: Running" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Health Check Results:**" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Container App Status: Running" >> $GITHUB_STEP_SUMMARY
          echo "- ✅ Application Endpoint: Responding" >> $GITHUB_STEP_SUMMARY
          echo "" >> $GITHUB_STEP_SUMMARY
          echo "**Next Steps:**" >> $GITHUB_STEP_SUMMARY
          echo "1. Test the QA environment" >> $GITHUB_STEP_SUMMARY
          echo "2. Verify all functionality works as expected" >> $GITHUB_STEP_SUMMARY
          echo "3. Report any issues found" >> $GITHUB_STEP_SUMMARY
