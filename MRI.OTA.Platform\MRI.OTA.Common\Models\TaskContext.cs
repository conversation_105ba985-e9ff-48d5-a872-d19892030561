﻿namespace MRI.OTA.Common.Models
{
    public class TaskContext
    {
        public Guid RequestId { get; set; } = Guid.NewGuid(); // Unique ID for the request
        public int UserId { get; set; } // Current user ID
        public string? Email { get; set; } // Email of the user

        public string? AccessToken { get; set; } // Access Token

        public string? AccessKey { get; set; } // Access Key

        public DateTime RequestStartTime { get; set; } = DateTime.UtcNow; // Request start time
    }
}
