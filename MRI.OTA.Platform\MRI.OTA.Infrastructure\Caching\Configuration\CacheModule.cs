﻿﻿using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using MRI.OTA.Infrastructure.Caching.Interfaces;

namespace MRI.OTA.Infrastructure.Caching.Configuration
{
    /// <summary>
    /// Module for configuring caching services
    /// </summary>
    public static class CacheModule
    {
        /// <summary>
        /// Configures Redis caching services
        /// </summary>
        /// <param name="services">The service collection</param>
        /// <param name="configuration">The configuration</param>
        /// <returns>The service collection for chaining</returns>
        public static IServiceCollection ConfigureRedisCache(this IServiceCollection services, IConfiguration configuration)
        {
            // Bind Redis configuration from appsettings
            var redisConfig = new RedisConfiguration();
            configuration.GetSection("RedisConfig").Bind(redisConfig);
            
            services.Configure<RedisConfiguration>(configuration.GetSection("RedisConfig"));
            if ( !string.IsNullOrEmpty(redisConfig.ConnectionString))
            {
                services.AddStackExchangeRedisCache(options =>
                {
                    options.Configuration = redisConfig.ConnectionString;
                });

                // Register the Redis cache service
                services.AddSingleton<ICacheService, RedisCacheService>();
            }

            return services;
        }
    }
}
