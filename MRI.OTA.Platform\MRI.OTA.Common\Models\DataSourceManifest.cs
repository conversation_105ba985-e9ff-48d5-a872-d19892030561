﻿using System.Text.Json;
using System.Text.Json.Serialization;

namespace MRI.OTA.Common.Models
{
    public class DataSourceManifest
    {
        [JsonPropertyName("baseUrl")]
        public string BaseUrl { get; set; }

        [JsonPropertyName("apiKey")]
        public string ApiKey { get; set; }

        [JsonPropertyName("property:acceptInvite")]
        public EndpointDefinition PropertyAcceptInvite { get; set; }

        [JsonPropertyName("property:associatePortfolio")]
        public EndpointDefinition AssociatePortfolio { get; set; }

        [JsonPropertyName("property:disConnectUser")]
        public EndpointDefinition DisConnectUser { get; set; }

        [JsonPropertyName("property:list")]
        public EndpointDefinition PropertyList { get; set; }

        [JsonPropertyName("property:details")]
        public EndpointDefinition PropertyDetails { get; set; }

        [JsonPropertyName("management:list")]
        public EndpointDefinition ManagementList { get; set; }

        [JsonPropertyName("tenancies-tenant:list")]
        public EndpointDefinition TenanciesTenantList { get; set; }
        [JsonPropertyName("tenancies-owner:list")]
        public EndpointDefinition TenanciesOwnerList { get; set; }

        [JsonPropertyName("maintenance:list")]
        public EndpointDefinition MaintenanceList { get; set; }
        [JsonPropertyName("inspection:list")]
        public EndpointDefinition InspectionList { get; set; }
        [JsonPropertyName("compliance:list")]
        public EndpointDefinition ComplianceList { get; set; }

        [JsonPropertyName("financial:list")]
        public EndpointDefinition FinancialList { get; set; }
        [JsonPropertyName("documents:list")]
        public EndpointDefinition DocumentList { get; set; }

        [JsonPropertyName("agency:list")]
        public EndpointDefinition AgencyList { get; set; }

        [JsonPropertyName("agencyPartner:list")]
        public EndpointDefinition AgencyPartnerList { get; set; }

        public static DataSourceManifest FromJson(string json)
        {
            var options = new JsonSerializerOptions
            {
                PropertyNameCaseInsensitive = true
            };

            return JsonSerializer.Deserialize<DataSourceManifest>(json, options);
        }
    }
}
