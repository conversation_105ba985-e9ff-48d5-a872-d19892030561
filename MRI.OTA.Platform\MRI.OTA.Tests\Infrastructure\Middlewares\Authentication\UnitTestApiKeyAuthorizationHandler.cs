using Moq;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Infrastructure.Caching.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication;
using MRI.OTA.Common.Models;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares.Authentication
{
    public class UnitTestApiKeyAuthorizationHandler
    {
        private readonly Mock<ICacheService> _cacheServiceMock;
        private readonly Mock<IDataSourceRepository> _dataSourceRepositoryMock;
        private readonly ApiKeyAuthorizationHandler _handler;

        public UnitTestApiKeyAuthorizationHandler()
        {
            _cacheServiceMock = new Mock<ICacheService>();
            _dataSourceRepositoryMock = new Mock<IDataSourceRepository>();

            _handler = new ApiKeyAuthorizationHandler(
                _dataSourceRepositoryMock.Object,
                _cacheServiceMock.Object);
        }

        [Fact]
        public void Constructor_CreatesInstance_WhenDataSourceRepositoryIsNull()
        {
            // Arrange & Act
            var handler = new ApiKeyAuthorizationHandler(
                null!,
                _cacheServiceMock.Object);

            // Assert
            Assert.NotNull(handler);
        }

        [Fact]
        public void Constructor_CreatesInstance_WhenCacheServiceIsNull()
        {
            // Arrange & Act
            var handler = new ApiKeyAuthorizationHandler(
                _dataSourceRepositoryMock.Object,
                null!);

            // Assert
            Assert.NotNull(handler);
        }

        [Fact]
        public void Constructor_CreatesInstance_WhenValidParametersProvided()
        {
            // Arrange & Act
            var handler = new ApiKeyAuthorizationHandler(
                _dataSourceRepositoryMock.Object,
                _cacheServiceMock.Object);

            // Assert
            Assert.NotNull(handler);
        }

        [Fact]
        public void Handler_ImplementsAuthorizationHandler()
        {
            // Arrange & Act & Assert
            Assert.IsAssignableFrom<Microsoft.AspNetCore.Authorization.AuthorizationHandler<ApiKeyRequirement>>(_handler);
        }
    }
}
