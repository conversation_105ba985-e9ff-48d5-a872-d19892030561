using System.Data;
using System.Text;
using Dapper;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.DBCore.Repositories
{
    /// <summary>
    /// Integration repository - handles agency and other integration data
    /// </summary>
    public class IntegrationRepository : BaseRepository<AgencyDetails, int>, IIntegrationRepository
    {
        private readonly ILogger<IntegrationRepository> _logger;
        protected readonly IDapperWrapper _dapperWrapper;
        private readonly IAPITrackingRepository _apiTrackingRepository;

        /// <summary>
        /// Constructor for IntegrationRepository
        /// </summary>
        /// <param name="dbConnection"></param>
        /// <param name="logger"></param>
        /// <param name="dapperWrapper"></param>
        public IntegrationRepository(IDbConnectionFactory dbConnection, ILogger<IntegrationRepository> logger, IDapperWrapper dapperWrapper, IAPITrackingRepository apiTrackingRepository) 
            : base(dbConnection, logger, dapperWrapper)
        {
            _logger = logger;
            _dapperWrapper = dapperWrapper;
            _apiTrackingRepository = apiTrackingRepository;
        }

        #region Common Helper Methods

        /// <summary>
        /// Common validation for bulk operations
        /// </summary>
        /// <typeparam name="T">Type of the list items</typeparam>
        /// <param name="list">List to validate</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <returns>True if valid, false otherwise</returns>
        private bool ValidateBulkOperationInput<T>(List<T> list, string operationName)
        {
            if (list == null || !list.Any())
            {
                _logger.LogWarning("No {OperationName} provided for bulk upsert", operationName);
                return false;
            }
            return true;
        }

        /// <summary>
        /// Executes a MERGE operation with common error handling and logging
        /// </summary>
        /// <param name="mergeQuery">The MERGE SQL query</param>
        /// <param name="parameters">Query parameters</param>
        /// <param name="operationName">Name of the operation for logging</param>
        /// <param name="itemCount">Count of items being processed</param>
        /// <param name="dataSourceId">Data source ID for logging</param>
        /// <returns>Number of rows affected</returns>
        private async Task<List<SQLQueryMergeResult>> ExecuteMergeOperation(string mergeQuery, DynamicParameters parameters, string operationName, int itemCount, int dataSourceId)
        {
            try
            {
                var rowsAffected = await QueryAsync<SQLQueryMergeResult>(mergeQuery, parameters);
                
                _logger.LogInformation("Bulk upserted {RowsAffected} {OperationName} for {ItemCount} items with DataSourceId {DataSourceId}", 
                    rowsAffected.Count(), operationName, itemCount, dataSourceId);
                
                return rowsAffected.Any() ? rowsAffected.ToList() : new List<SQLQueryMergeResult>();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in bulk upsert for {ItemCount} {OperationName}", itemCount, operationName);
                throw;
            }
        }

        /// <summary>
        /// Common wrapper for bulk upsert operations
        /// </summary>
        /// <typeparam name="T">Type of the entity list</typeparam>
        /// <param name="list">List of entities to upsert</param>
        /// <param name="dataSourceId">Data source ID</param>
        /// <param name="operationName">Name of the operation</param>
        /// <param name="tableValuedParameterName">Name of the table-valued parameter</param>
        /// <param name="tableValuedParameterType">Name of the table-valued parameter type</param>
        /// <param name="mergeQuery">MERGE SQL query</param>
        /// <param name="dataTableCreator">Function to create DataTable from the list</param>
        /// <returns>Number of rows affected</returns>
        private async Task<List<SQLQueryMergeResult>> ExecuteBulkUpsert<T>(
            List<T> list, 
            int dataSourceId, 
            string operationName,
            string tableValuedParameterName,
            string tableValuedParameterType,
            string mergeQuery,
            Func<List<T>, DataTable> dataTableCreator)
        {
            try
            {
                if (!ValidateBulkOperationInput(list, operationName))
                    return null;

                var dataTable = dataTableCreator(list);
                var parameters = new DynamicParameters();
                parameters.Add($"@{tableValuedParameterName}", dataTable.AsTableValuedParameter(tableValuedParameterType));
                parameters.Add("@DataSourceId", dataSourceId);

                return await ExecuteMergeOperation(mergeQuery, parameters, operationName, list.Count, dataSourceId);
            }
            catch(Exception ex)
            {
                _logger.LogError(ex, "Error in bulk upsert for {OperationName}", operationName);
                throw;
            }
        }

        #endregion

        #region DataTable Creation Methods
        // Individual DataTable creation methods for each entity type
        // These methods encapsulate the specific column definitions and data population logic
        // for each bulk operation, keeping the main methods clean and focused
        #endregion

        /// <summary>
        /// Get unique agencies from properties for a specific data source
        /// </summary>
        /// <param name="dataSourceId">The data source ID to filter agencies</param>
        /// <param name="batchSize">Number of agencies to return per batch</param>
        /// <param name="skip">Number of agencies to skip for pagination</param>
        /// <returns>List of unique agencies with their details</returns>
        public async Task<List<(string AgencyId, string AgencyName, int DataSourceId)>> GetUniqueAgencies(int dataSourceId, int batchSize = 50, int skip = 0)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT DISTINCT UP.SRCAgencyId as AgencyId, ");
                query.Append($"COALESCE(PM.AgencyName, 'Unknown Agency') as AgencyName, ");
                query.Append($"DS.DataSourceId ");
                query.Append($"FROM {Constants.UserPropertyTableName} UP ");
                query.Append($"INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId ");
                query.Append($"INNER JOIN {Constants.DataSourceTableName} DS ON UD.DataSourceId = DS.DataSourceId ");
                query.Append($"LEFT JOIN {Constants.PropertyManagerInformationTableName} PM ON UP.PropertyId = PM.PropertyId ");
                query.Append($"WHERE DS.DataSourceId = @DataSourceId ");
                query.Append($"AND UP.SRCAgencyId IS NOT NULL ");
                query.Append($"AND UP.SRCAgencyId != '' ");
                query.Append($"AND UP.IsActive = 1 ");
                query.Append($"ORDER BY UP.SRCAgencyId ");
                query.Append($"OFFSET @Skip ROWS FETCH NEXT @BatchSize ROWS ONLY");

                var parameters = new { DataSourceId = dataSourceId, Skip = skip, BatchSize = batchSize };
                
                var results = await QueryAsync<(string AgencyId, string AgencyName, int DataSourceId)>(
                    query.ToString(), parameters);
                
                return results.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unique agencies for dataSourceId {DataSourceId}, skip {Skip}, batchSize {BatchSize}", 
                    dataSourceId, skip, batchSize);
                return new List<(string AgencyId, string AgencyName, int DataSourceId)>();
            }
        }

        public async Task<List<(string Id, int DataSourceId)>> GetUniqueIdsFromUserProperty(string columnName, int dataSourceId, int batchSize = 50, int skip = 0, int? propertyRelationshipId = null)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT DISTINCT UP.{columnName} as Id, ");
                query.Append($"DS.DataSourceId ");
                query.Append($"FROM {Constants.UserPropertyTableName} UP ");
                query.Append($"INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId ");
                query.Append($"INNER JOIN {Constants.DataSourceTableName} DS ON UD.DataSourceId = DS.DataSourceId ");
                query.Append($"WHERE DS.DataSourceId = @DataSourceId ");
                query.Append($"AND UP.IsActive = 1 ");
                query.Append($"AND UP.{columnName} IS NOT NULL ");
                query.Append($"AND UP.{columnName} != '' ");
                
                // Add PropertyRelationshipId filter when specified
                // For TenanciesOwnerList API: PropertyRelationshipId = 1
                // For TenanciesTenantList API: PropertyRelationshipId = 3
                if (propertyRelationshipId.HasValue)
                {
                    query.Append($"AND UP.PropertyRelationshipId = @PropertyRelationshipId ");
                }
                
                query.Append($"ORDER BY UP.{columnName} ");
                query.Append($"OFFSET @Skip ROWS FETCH NEXT @BatchSize ROWS ONLY");

                var parameters = new DynamicParameters();
                parameters.Add("@DataSourceId", dataSourceId);
                parameters.Add("@Skip", skip);
                parameters.Add("@BatchSize", batchSize);
                if (propertyRelationshipId.HasValue)
                {
                    parameters.Add("@PropertyRelationshipId", propertyRelationshipId.Value);
                }

                var results = await QueryAsync<(string Id, int DataSourceId)>(
                    query.ToString(), parameters);

                return results.ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unique document's {ColumnName} for dataSourceId {DataSourceId}, skip {Skip}, batchSize {BatchSize}, propertyRelationshipId {PropertyRelationshipId}",
                    columnName, dataSourceId, skip, batchSize, propertyRelationshipId);
                return new List<(string Id, int DataSourceId)>();
            }
        }

        /// <summary>
        /// Get total count of unique agencies for a specific data source
        /// </summary>
        /// <param name="dataSourceId">The data source ID to filter agencies</param>
        /// <returns>Total count of unique agencies</returns>
        public async Task<int> GetUniqueAgenciesCount(int dataSourceId)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT COUNT(DISTINCT UP.SRCAgencyId) ");
                query.Append($"FROM {Constants.UserPropertyTableName} UP ");
                query.Append($"INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId ");
                query.Append($"INNER JOIN {Constants.DataSourceTableName} DS ON UD.DataSourceId = DS.DataSourceId ");
                query.Append($"WHERE DS.DataSourceId = @DataSourceId ");
                query.Append($"AND UP.SRCAgencyId IS NOT NULL ");
                query.Append($"AND UP.SRCAgencyId != '' ");
                query.Append($"AND UP.IsActive = 1");

                var parameters = new { DataSourceId = dataSourceId };
                
                var count = await QueryFirstOrDefaultAsync<int>(query.ToString(), parameters);
                
                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unique agencies count for dataSourceId {DataSourceId}", dataSourceId);
                return 0;
            }
        }
        public async Task<int> GetUniqueManagementOrTenancyCount(int dataSourceId, string columnName, int? propertyRelationshipId = null)
        {
            try
            {
                StringBuilder query = new StringBuilder();
                query.Append($"SELECT COUNT(DISTINCT UP.{columnName}) ");
                query.Append($"FROM {Constants.UserPropertyTableName} UP ");
                query.Append($"INNER JOIN {Constants.UserDataSourceTableName} UD ON UP.UserDataSourceId = UD.UserDataSourceId ");
                query.Append($"INNER JOIN {Constants.DataSourceTableName} DS ON UD.DataSourceId = DS.DataSourceId ");
                query.Append($"WHERE DS.DataSourceId = @DataSourceId ");
                query.Append($"AND UP.{columnName} IS NOT NULL ");
                query.Append($"AND UP.{columnName} != '' ");
                query.Append($"AND UP.IsActive = 1");
                
                // Add PropertyRelationshipId filter when specified
                // For TenanciesOwnerList API: PropertyRelationshipId = 1
                // For TenanciesTenantList API: PropertyRelationshipId = 3
                if (propertyRelationshipId.HasValue)
                {
                    query.Append($" AND UP.PropertyRelationshipId = @PropertyRelationshipId");
                }

                var parameters = new DynamicParameters();
                parameters.Add("@DataSourceId", dataSourceId);
                if (propertyRelationshipId.HasValue)
                {
                    parameters.Add("@PropertyRelationshipId", propertyRelationshipId.Value);
                }

                var count = await QueryFirstOrDefaultAsync<int>(query.ToString(), parameters);

                return count;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting unique {ColumnName} count for dataSourceId {DataSourceId}, propertyRelationshipId {PropertyRelationshipId}", columnName, dataSourceId, propertyRelationshipId);
                return 0;
            }
        }

        /// <summary>
        /// Bulk upsert agency details using MERGE statement based on AgencyId - insert if not exists, update if exists
        /// </summary>
        /// <param name="agencyDetailsList">List of agency details to upsert</param>
        /// <param name="dataSourceId">The data source ID</param>
        /// <returns>Number of rows affected</returns>
        public async Task<List<SQLQueryMergeResult>> BulkUpsertAgencyDetails(List<AgencyDetails> agencyDetailsList, int dataSourceId)
        {
            try
            {
                return await ExecuteBulkUpsert(
               agencyDetailsList,
               dataSourceId,
               "agency details",
               "AgencyData",
               "AgencyDetailsType",
               $@"MERGE {Constants.AgencyDetailsTableName} AS Target
                   USING @AgencyData AS Source
                   ON Target.AgencyId = Source.AgencyId AND Target.DataSourceId = Source.DataSourceId
                   WHEN MATCHED THEN
                       UPDATE SET
                           MriId = Source.MriId,
                           BusinessRegisteredName = Source.BusinessRegisteredName,
                           BusinessName = Source.BusinessName,
                           BusinessRegistrationNumber = Source.BusinessRegistrationNumber,
                           Phone = Source.Phone,
                           Email = Source.Email,
                           DarkLogoLink = Source.DarkLogoLink,
                           LightLogoLink = Source.LightLogoLink,
                           BrandingBackgroundColor = Source.BrandingBackgroundColor,                                
                           CountryCode = Source.CountryCode,
                           CountryName = Source.CountryName,
                           StateCode = Source.StateCode,
                           StateName = Source.StateName,
                           Suburb = Source.Suburb,
                           PostalCode = Source.PostalCode,
                           AdministrativeArea = Source.AdministrativeArea,
                           BuildingNumber = Source.BuildingNumber,
                           LotNumber = Source.LotNumber,
                           StreetAddress = Source.StreetAddress,
                           City = Source.City,
                           Locale = Source.Locale,
                           RuralDelivery = Source.RuralDelivery,
                           PostOfficeName = Source.PostOfficeName,
                           ModifiedDate = GETUTCDATE()
                   WHEN NOT MATCHED THEN
                       INSERT (AgencyId, MriId, DataSourceId, BusinessRegisteredName, BusinessName, 
                              BusinessRegistrationNumber, Phone, Email, DarkLogoLink, LightLogoLink, 
                              BrandingBackgroundColor, CountryCode, CountryName, StateCode, StateName, 
                              Suburb, PostalCode, AdministrativeArea, BuildingNumber, LotNumber, 
                              StreetAddress, City, Locale, RuralDelivery, PostOfficeName)
                       VALUES (Source.AgencyId, Source.MriId, Source.DataSourceId, Source.BusinessRegisteredName, 
                              Source.BusinessName, Source.BusinessRegistrationNumber, Source.Phone, Source.Email, 
                              Source.DarkLogoLink, Source.LightLogoLink, Source.BrandingBackgroundColor, 
                              Source.CountryCode, Source.CountryName, Source.StateCode, Source.StateName, 
                              Source.Suburb, Source.PostalCode, Source.AdministrativeArea, Source.BuildingNumber, 
                              Source.LotNumber, Source.StreetAddress, Source.City, Source.Locale, 
                              Source.RuralDelivery, Source.PostOfficeName)
                   OUTPUT 
                       $action AS MergeAction,
                       inserted.MriId AS SRCId,
                       null AS SRCManagementId,
                       inserted.AgencyId AS SRCPropertyId,
                       inserted.DataSourceId AS PropertyId,
                       0 AS UserId;",
               CreateAgencyDetailsDataTable);
            }
            catch (Exception ex)
            {

                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetAgencyDetails,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(agencyDetailsList)
                });
                return null;
            }
           
        }

        /// <summary>
        /// Creates DataTable for AgencyDetails bulk operations
        /// </summary>
        /// <param name="agencyDetailsList">List of agency details</param>
        /// <returns>DataTable populated with agency details</returns>
        private DataTable CreateAgencyDetailsDataTable(List<AgencyDetails> agencyDetailsList)
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("AgencyId", typeof(string));
            dataTable.Columns.Add("MriId", typeof(string));
            dataTable.Columns.Add("DataSourceId", typeof(int));
            dataTable.Columns.Add("BusinessRegisteredName", typeof(string));
            dataTable.Columns.Add("BusinessName", typeof(string));
            dataTable.Columns.Add("BusinessRegistrationNumber", typeof(string));
            dataTable.Columns.Add("Phone", typeof(string));
            dataTable.Columns.Add("Email", typeof(string));
            dataTable.Columns.Add("DarkLogoLink", typeof(string));
            dataTable.Columns.Add("LightLogoLink", typeof(string));
            dataTable.Columns.Add("BrandingBackgroundColor", typeof(string));
            dataTable.Columns.Add("CountryCode", typeof(string));
            dataTable.Columns.Add("CountryName", typeof(string));
            dataTable.Columns.Add("StateCode", typeof(string));
            dataTable.Columns.Add("StateName", typeof(string));
            dataTable.Columns.Add("Suburb", typeof(string));
            dataTable.Columns.Add("PostalCode", typeof(string));
            dataTable.Columns.Add("AdministrativeArea", typeof(string));
            dataTable.Columns.Add("BuildingNumber", typeof(string));
            dataTable.Columns.Add("LotNumber", typeof(string));
            dataTable.Columns.Add("StreetAddress", typeof(string));
            dataTable.Columns.Add("City", typeof(string));
            dataTable.Columns.Add("Locale", typeof(string));
            dataTable.Columns.Add("RuralDelivery", typeof(string));
            dataTable.Columns.Add("PostOfficeName", typeof(string));

            foreach (var agency in agencyDetailsList)
            {
                dataTable.Rows.Add(
                    agency.AgencyId ?? (object)DBNull.Value,
                    agency.MriId ?? (object)DBNull.Value,
                    agency.DataSourceId,
                    agency.BusinessRegisteredName ?? (object)DBNull.Value,
                    agency.BusinessName ?? (object)DBNull.Value,
                    agency.BusinessRegistrationNumber ?? (object)DBNull.Value,
                    agency.Phone ?? (object)DBNull.Value,
                    agency.Email ?? (object)DBNull.Value,
                    agency.DarkLogoLink ?? (object)DBNull.Value,
                    agency.LightLogoLink ?? (object)DBNull.Value,
                    agency.BrandingBackgroundColor ?? (object)DBNull.Value,
                    agency.CountryCode ?? (object)DBNull.Value,
                    agency.CountryName ?? (object)DBNull.Value,
                    agency.StateCode ?? (object)DBNull.Value,
                    agency.StateName ?? (object)DBNull.Value,
                    agency.Suburb ?? (object)DBNull.Value,
                    agency.PostalCode ?? (object)DBNull.Value,
                    agency.AdministrativeArea ?? (object)DBNull.Value,
                    agency.BuildingNumber ?? (object)DBNull.Value,
                    agency.LotNumber ?? (object)DBNull.Value,
                    agency.StreetAddress ?? (object)DBNull.Value,
                    agency.City ?? (object)DBNull.Value,
                    agency.Locale ?? (object)DBNull.Value,
                    agency.RuralDelivery ?? (object)DBNull.Value,
                    agency.PostOfficeName ?? (object)DBNull.Value);
            }

            return dataTable;
        }

        /// <summary>
        /// Bulk upsert agency partners using MERGE statement based on PartnerId - insert if not exists, update if exists
        /// </summary>
        /// <param name="agencyPartnersList">List of agency partners to upsert</param>
        /// <param name="dataSourceId">The data source ID (not used but kept for interface consistency)</param>
        /// <returns>Number of rows affected</returns>
        public async Task<List<SQLQueryMergeResult>> BulkUpsertAgencyPartners(List<AgencyPartners> agencyPartnersList, int dataSourceId)
        {
            try
            {
                return await ExecuteBulkUpsert(
                agencyPartnersList,
                dataSourceId,
                "agency partners",
                "AgencyPartnersData",
                "AgencyPartnersType",
                $@"MERGE {Constants.AgencyPartnersTableName} AS Target
                   USING @AgencyPartnersData AS Source
                   ON Target.PartnerId = Source.PartnerId AND Target.AgencyId = Source.AgencyId
                   WHEN MATCHED THEN
                       UPDATE SET
                           PartnerName = Source.PartnerName,
                           ModifiedDate = GETUTCDATE()
                   WHEN NOT MATCHED THEN
                       INSERT (AgencyId, PartnerId, PartnerName)
                       VALUES (Source.AgencyId, Source.PartnerId, Source.PartnerName)
                   OUTPUT 
                       $action AS MergeAction,
                       inserted.AgencyId AS SRCId,
                       inserted.PartnerId AS SRCManagementId,    
                       null AS SRCPropertyId,
                       0 AS PropertyId,
                       0 AS UserId;",
                CreateAgencyPartnersDataTable);
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetAgencyPartnersDetails,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(agencyPartnersList)
                });
                return null;
            }
            
        }

        /// <summary>
        /// Creates DataTable for AgencyPartners bulk operations
        /// </summary>
        /// <param name="agencyPartnersList">List of agency partners</param>
        /// <returns>DataTable populated with agency partners</returns>
        private DataTable CreateAgencyPartnersDataTable(List<AgencyPartners> agencyPartnersList)
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("AgencyId", typeof(string));
            dataTable.Columns.Add("PartnerId", typeof(string)); // This maps to PartnerAgencyId property
            dataTable.Columns.Add("PartnerName", typeof(string)); // This maps to PartnerAgencyName property

            foreach (var partner in agencyPartnersList)
            {
                dataTable.Rows.Add(
                    partner.AgencyId ?? (object)DBNull.Value,
                    partner.PartnerId ?? (object)DBNull.Value,  // PartnerId in table
                    partner.PartnerName ?? (object)DBNull.Value  // PartnerName in table
                );
            }

            return dataTable;
        }
        public async Task<List<SQLQueryMergeResult>> BulkUpsertDocumentDetails(List<DocumentDetail> list, int dataSourceId, string columnName)
        {
            if (!ValidateBulkOperationInput(list, "document details"))
                return null;
            try
            {
                var dataTable = CreateDocumentDetailsDataTable(list);
                var parameters = new DynamicParameters();
                parameters.Add("@DocumentData", dataTable.AsTableValuedParameter("DocumentDetailsType"));

                string mergeQuery = $@"
                MERGE {Constants.DocumentDetailsTableName} AS Target
                USING @DocumentData AS Source
                ON Target.SRCDocumentId = Source.SRCDocumentId AND Target.{columnName} = Source.{columnName}
                WHEN MATCHED AND Source.DocumentDetailId = -1 THEN
                    DELETE
                WHEN MATCHED AND Source.DocumentDetailId = 0 THEN
                    UPDATE SET
                        SRCManagementId = Source.SRCManagementId,
                        SRCTenancyId = Source.SRCTenancyId,
                        DocumentName = Source.DocumentName,
                        DocumentLink = Source.DocumentLink,
                        DocumentType = Source.DocumentType,
                        MetaType = Source.MetaType,
                        MetaNumber = Source.MetaNumber,
                        MetaDate = Source.MetaDate,
                        MetaStatus = Source.MetaStatus,
                        MetaAmount = Source.MetaAmount,
                        MetaOwing = Source.MetaOwing,
                        MetaCurrency = Source.MetaCurrency,
                        MetaPeriod = Source.MetaPeriod,
                        SharedDate = Source.SharedDate,
                        LastUpdatedDate = Source.LastUpdatedDate,
                        ModifiedDate = GETUTCDATE()
                WHEN NOT MATCHED BY TARGET AND Source.DocumentDetailId = 0 THEN
                    INSERT (
                        SRCManagementId, SRCTenancyId, SRCDocumentId, DocumentName, DocumentLink, 
                        DocumentType, MetaType, MetaNumber, MetaDate, MetaStatus, MetaAmount, 
                        MetaOwing, MetaCurrency, MetaPeriod, SharedDate, LastUpdatedDate, CreatedDate, ModifiedDate
                    )
                    VALUES (
                        Source.SRCManagementId, Source.SRCTenancyId, Source.SRCDocumentId, Source.DocumentName, Source.DocumentLink, 
                        Source.DocumentType, Source.MetaType, Source.MetaNumber, Source.MetaDate, Source.MetaStatus, Source.MetaAmount, 
                        Source.MetaOwing, Source.MetaCurrency, Source.MetaPeriod, Source.SharedDate, Source.LastUpdatedDate, GETUTCDATE(), GETUTCDATE()
                    )
                OUTPUT 
                       $action AS MergeAction,
                       inserted.SRCDocumentId AS SRCId,
                       inserted.SRCManagementId AS SRCManagementId,
                       inserted.SRCTenancyId AS SRCPropertyId,
                       0 AS PropertyId,
                       0 AS UserId;";

                return await ExecuteMergeOperation(mergeQuery, parameters, "document details", list.Count, dataSourceId);
            }
            catch (Exception ex)
            {

                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetDocuments,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(list)
                });
                return null;
            }
           
        }

        /// <summary>
        /// Creates DataTable for DocumentDetails bulk operations
        /// </summary>
        /// <param name="list">List of document details</param>
        /// <returns>DataTable populated with document details</returns>
        private DataTable CreateDocumentDetailsDataTable(List<DocumentDetail> list)
        {
            var dataTable = new DataTable("DocumentDetails");

            dataTable.Columns.Add("DocumentDetailId", typeof(int));
            dataTable.Columns.Add("SRCManagementId", typeof(string));
            dataTable.Columns.Add("SRCTenancyId", typeof(string));
            dataTable.Columns.Add("SRCDocumentId", typeof(string));
            dataTable.Columns.Add("DocumentName", typeof(string));
            dataTable.Columns.Add("DocumentLink", typeof(string));
            dataTable.Columns.Add("DocumentType", typeof(string));
            dataTable.Columns.Add("ModifiedBy", typeof(int));
            dataTable.Columns["ModifiedBy"].AllowDBNull = true;
            dataTable.Columns.Add("ModifiedDate", typeof(DateTime));
            dataTable.Columns.Add("MetaType", typeof(string));
            dataTable.Columns.Add("MetaNumber", typeof(string));
            dataTable.Columns.Add("MetaDate", typeof(DateTime));
            dataTable.Columns["MetaDate"].AllowDBNull = true;
            dataTable.Columns.Add("MetaStatus", typeof(string));
            dataTable.Columns.Add("MetaAmount", typeof(decimal));
            dataTable.Columns["MetaAmount"].AllowDBNull = true;
            dataTable.Columns.Add("MetaOwing", typeof(decimal));
            dataTable.Columns["MetaOwing"].AllowDBNull = true;
            dataTable.Columns.Add("MetaCurrency", typeof(string));
            dataTable.Columns["MetaCurrency"].AllowDBNull = true;
            dataTable.Columns.Add("MetaPeriod", typeof(string));
            dataTable.Columns.Add("SharedDate", typeof(DateTime));
            dataTable.Columns.Add("LastUpdatedDate", typeof(DateTime));

            foreach (var detail in list)
            {
                dataTable.Rows.Add(
                    detail.isRemove ? -1 : 0,
                    detail.SRCManagementId ?? (object)DBNull.Value,
                    detail.SRCTenancyId ?? (object)DBNull.Value,
                    detail.SRCDocumentId ?? (object)DBNull.Value,
                    detail.DocumentName ?? (object)DBNull.Value,
                    detail.DocumentLink ?? (object)DBNull.Value,
                    detail.DocumentType ?? (object)DBNull.Value,
                    null,
                    DateTime.UtcNow,
                    detail.MetaType ?? (object)DBNull.Value,
                    detail.MetaNumber ?? (object)DBNull.Value,
                    detail.MetaDate.HasValue ? detail.MetaDate.Value : (object)DBNull.Value,
                    detail.MetaStatus ?? (object)DBNull.Value,
                    detail.MetaAmount.HasValue ? detail.MetaAmount.Value : (object)DBNull.Value,
                    detail.MetaOwing.HasValue ? detail.MetaOwing.Value : (object)DBNull.Value,
                    detail.MetaCurrency ?? (object)DBNull.Value,
                    detail.MetaPeriod ?? (object)DBNull.Value,
                    detail.SharedDate,
                    detail.LastUpdatedDate);
            }

            return dataTable;
        }

        /// <summary>
        /// Bulk upsert property manager information using MERGE statement based on SRCAgencyId and SRCManagementId - insert if not exists, update if exists
        /// </summary>
        /// <param name="propertyManagerInfoList">List of property manager information to upsert</param>
        /// <param name="dataSourceId">The data source ID</param>
        /// <returns>Number of rows affected</returns>
        public async Task<List<SQLQueryMergeResult>> BulkUpsertPropertyManagerInformation(List<PropertyManagerInformation> propertyManagerInfoList, int dataSourceId)
        {
            try
            {
                return await ExecuteBulkUpsert(
                propertyManagerInfoList,
                dataSourceId,
                "property manager information",
                "PropertyManagerData",
                "PropertyManagerInformationType",
                $@"MERGE {Constants.PropertyManagerInformationTableName} AS Target
                   USING @PropertyManagerData AS Source
                   ON Target.SRCAgencyId = Source.SRCAgencyId AND Target.SRCManagementId = Source.SRCManagementId
                   WHEN MATCHED THEN
                       UPDATE SET
                           SRCPropertyId = Source.SRCPropertyId,
                           ManagementType = Source.ManagementType,
                           AgencyName = Source.AgencyName,
                           PropertyManagerName = Source.PropertyManagerName,
                           PropertyManagerMobile = Source.PropertyManagerMobile,
                           PropertyManagerPhone = Source.PropertyManagerPhone,
                           PropertyManagerEmail = Source.PropertyManagerEmail,
                           ContactRole = Source.ContactRole,
                           AuthorityStartDate = Source.AuthorityStartDate,
                           AuthorityEndDate = Source.AuthorityEndDate,
                           Ownership = Source.Ownership,
                           ExpenditureLimit = Source.ExpenditureLimit,
                           ExpenditureNotes = Source.ExpenditureNotes,
                           ModifiedDate = GETUTCDATE()
                   WHEN NOT MATCHED THEN
                       INSERT (PropertyId, SRCAgencyId, SRCManagementId, SRCPropertyId, ManagementType, AgencyName, 
                               PropertyManagerName, PropertyManagerMobile, PropertyManagerPhone, PropertyManagerEmail, ContactRole,
                               AuthorityStartDate, AuthorityEndDate, Ownership, ExpenditureLimit, ExpenditureNotes)
                       VALUES ((SELECT TOP 1 UP.PropertyId 
                               FROM {Constants.UserPropertyTableName} UP 
                               WHERE UP.SRCEntitytId = Source.SRCPropertyId AND UP.SRCManagementId = Source.SRCManagementId AND UP.SRCAgencyId = Source.SRCAgencyId
                               AND UP.IsActive = 1), Source.SRCAgencyId, Source.SRCManagementId, Source.SRCPropertyId, 
                               Source.ManagementType, Source.AgencyName, Source.PropertyManagerName, 
                               Source.PropertyManagerMobile, Source.PropertyManagerPhone, Source.PropertyManagerEmail, Source.ContactRole,
                               Source.AuthorityStartDate, Source.AuthorityEndDate, Source.Ownership, 
                               Source.ExpenditureLimit, Source.ExpenditureNotes)
                    OUTPUT 
                           $action AS MergeAction,
                           inserted.SRCAgencyId AS SRCId,
                           inserted.SRCManagementId AS SRCManagementId,
                           inserted.SRCPropertyId AS SRCPropertyId,
                           inserted.PropertyId AS PropertyId,
                           0 AS UserId;",
                CreatePropertyManagerInformationDataTable);
            }
            catch (Exception ex)
            {

                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetManagement,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(propertyManagerInfoList)
                });
                return null;
            }
            
        }

        /// <summary>
        /// Creates DataTable for PropertyManagerInformation bulk operations
        /// </summary>
        /// <param name="propertyManagerInfoList">List of property manager information</param>
        /// <returns>DataTable populated with property manager information</returns>
        private DataTable CreatePropertyManagerInformationDataTable(List<PropertyManagerInformation> propertyManagerInfoList)
        {
            var dataTable = new DataTable();
            dataTable.Columns.Add("SRCAgencyId", typeof(string));
            dataTable.Columns.Add("SRCManagementId", typeof(string));
            dataTable.Columns.Add("SRCPropertyId", typeof(string));
            dataTable.Columns.Add("ManagementType", typeof(string));
            dataTable.Columns.Add("AgencyName", typeof(string));
            dataTable.Columns.Add("PropertyManagerName", typeof(string));
            dataTable.Columns.Add("PropertyManagerMobile", typeof(string));
            dataTable.Columns.Add("PropertyManagerPhone", typeof(string));
            dataTable.Columns.Add("PropertyManagerEmail", typeof(string));
            dataTable.Columns.Add("ContactRole", typeof(string));
            dataTable.Columns.Add("AuthorityStartDate", typeof(DateTime));
            dataTable.Columns.Add("AuthorityEndDate", typeof(DateTime));
            dataTable.Columns.Add("Ownership", typeof(string));
            dataTable.Columns.Add("ExpenditureLimit", typeof(decimal));
            dataTable.Columns.Add("ExpenditureNotes", typeof(string));

            // Log the actual values being processed for debugging
            _logger.LogInformation("Processing {Count} property manager records. First record - SRCAgencyId: '{SRCAgencyId}', SRCManagementId: '{SRCManagementId}', SRCPropertyId: '{SRCPropertyId}'", 
                propertyManagerInfoList.Count,
                propertyManagerInfoList.FirstOrDefault()?.SRCAgencyId ?? "NULL",
                propertyManagerInfoList.FirstOrDefault()?.SRCManagementId ?? "NULL", 
                propertyManagerInfoList.FirstOrDefault()?.SRCPropertyId ?? "NULL");

            foreach (var manager in propertyManagerInfoList)
            {
                dataTable.Rows.Add(
                    manager.SRCAgencyId ?? (object)DBNull.Value,
                    manager.SRCManagementId ?? (object)DBNull.Value,
                    manager.SRCPropertyId ?? (object)DBNull.Value,
                    manager.ManagementType ?? (object)DBNull.Value,
                    manager.AgencyName ?? (object)DBNull.Value,
                    manager.PropertyManagerName ?? (object)DBNull.Value,
                    manager.PropertyManagerMobile ?? (object)DBNull.Value,
                    manager.PropertyManagerPhone ?? (object)DBNull.Value,
                    manager.PropertyManagerEmail ?? (object)DBNull.Value,
                    manager.ContactRole ?? (object)DBNull.Value,
                    manager.AuthorityStartDate ?? (object)DBNull.Value,
                    manager.AuthorityEndDate ?? (object)DBNull.Value,
                    manager.Ownership ?? (object)DBNull.Value,
                    manager.ExpenditureLimit ?? (object)DBNull.Value,
                    manager.ExpenditureNotes ?? (object)DBNull.Value);
            }

            return dataTable;
        }
        
        public async Task<List<SQLQueryMergeResult>> BulkUpsertComplianceDetails(List<ComplianceDetail> list, int dataSourceId)
        {
            try
            {
                return await ExecuteBulkUpsert(
                list,
                dataSourceId,
                "compliance details",
                "ComplianceData",
                "ComplianceType",
                $@"MERGE {Constants.ComplianceDetailsTableName} AS Target
                   USING @ComplianceData AS Source
                   ON Target.SRCManagementId = Source.SRCManagementId AND Target.SRCPropertyId = Source.SRCPropertyId AND Target.SRCComplianceId = Source.SRCComplianceId
                   WHEN MATCHED THEN
                       UPDATE SET
                           ComplianceName = Source.ComplianceName,
                           ExpiryDate = Source.ExpiryDate,
                           ServicedBy = Source.ServicedBy,
                           Status = Source.Status,
                           ModifiedDate = GETUTCDATE()
                   WHEN NOT MATCHED BY TARGET THEN
                       INSERT (
                           PropertyId, SRCManagementId, SRCPropertyId, SRCComplianceId, ComplianceName, Status, ExpiryDate, 
                           ServicedBy, CreatedDate
                       )
                       VALUES (
                           (SELECT TOP 1 UP.PropertyId 
                               FROM {Constants.UserPropertyTableName} UP 
                               WHERE UP.SRCEntitytId = Source.SRCPropertyId AND UP.SRCManagementId = Source.SRCManagementId AND UP.IsActive = 1
                           ), 
                           Source.SRCManagementId, Source.SRCPropertyId, Source.SRCComplianceId, Source.ComplianceName, Source.Status, Source.ExpiryDate, 
                           Source.ServicedBy, GETUTCDATE()
                       )
                   OUTPUT 
                           $action AS MergeAction,
                           inserted.SRCComplianceId AS SRCId,
                           inserted.SRCManagementId AS SRCManagementId,    
                           inserted.SRCPropertyId AS SRCPropertyId,
                           inserted.PropertyId AS PropertyId,
                           0 AS UserId;",
                CreateComplianceDetailsDataTable);
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetCompliance,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(list)
                });
                return null;
            }
        }

        /// <summary>
        /// Creates DataTable for ComplianceDetails bulk operations
        /// </summary>
        /// <param name="list">List of compliance details</param>
        /// <returns>DataTable populated with compliance details</returns>
        private DataTable CreateComplianceDetailsDataTable(List<ComplianceDetail> list)
        {
            var dataTable = new DataTable("ComplianceDetails");

            dataTable.Columns.Add("SRCManagementId", typeof(string));
            dataTable.Columns.Add("SRCPropertyId", typeof(string));
            dataTable.Columns.Add("SRCComplianceId", typeof(string));
            dataTable.Columns.Add("ComplianceName", typeof(string));
            dataTable.Columns.Add("Status", typeof(string));
            dataTable.Columns.Add("ExpiryDate", typeof(DateTime));
            dataTable.Columns.Add("ServicedBy", typeof(string));

            foreach (var detail in list)
            {
                dataTable.Rows.Add(
                    detail.SRCManagementId ?? (object)DBNull.Value,
                    detail.SRCPropertyId ?? (object)DBNull.Value,
                    detail.SRCComplianceId ?? (object)DBNull.Value,
                    detail.ComplianceName ?? (object)DBNull.Value,
                    detail.Status ?? (object)DBNull.Value,
                    detail.ExpiryDate,
                    detail.ServicedBy ?? (object)DBNull.Value);
            }

            return dataTable;
        }

        public async Task<List<SQLQueryMergeResult>> BulkUpsertTenanciesTenants(List<TenanciesTenant> tenanciesTenantList, int dataSourceId)
        {
            if (!ValidateBulkOperationInput(tenanciesTenantList, "tenancies tenant information"))
                return null;

            try
            {
                var totalRowsAffected = new List<SQLQueryMergeResult>();

                // First, handle the PropertyFinancialInformation upsert
                totalRowsAffected.AddRange(await ExecuteTenanciesTenantsFinancialUpsert(tenanciesTenantList, dataSourceId));

                // Second, handle the PropertyManagerInformation upsert for TenanciesPropertyManagerDetails
                var tenanciesWithPropertyManagerDetails = tenanciesTenantList.Where(t => t.TenanciesPropertyManagerDetails != null).ToList();
                if (tenanciesWithPropertyManagerDetails.Any())
                {
                    totalRowsAffected.AddRange(await ExecuteTenanciesPropertyManagerUpsert(tenanciesWithPropertyManagerDetails, dataSourceId));
                }

                _logger.LogInformation("Total bulk upsert completed: {TotalRowsAffected} rows affected for {TenantCount} tenants with DataSourceId {DataSourceId}", 
                    totalRowsAffected.Count(), tenanciesTenantList.Count, dataSourceId);

                return totalRowsAffected.ToList();
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetTenanciesTenant,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(tenanciesTenantList)
                });
                _logger.LogError(ex, "Error in bulk upsert for {TenantCount} tenancies tenant information", tenanciesTenantList.Count);
                return null;
            }
        }

        /// <summary>
        /// Executes the financial information upsert for tenancies tenants
        /// </summary>
        /// <param name="tenanciesTenantList">List of tenancies tenants</param>
        /// <param name="dataSourceId">Data source ID</param>
        /// <returns>Number of rows affected</returns>
        private async Task<List<SQLQueryMergeResult>> ExecuteTenanciesTenantsFinancialUpsert(List<TenanciesTenant> tenanciesTenantList, int dataSourceId)
        {
            var tenancyDataTable = CreateTenanciesTenantsDataTable(tenanciesTenantList);
            var tenancyParameters = new DynamicParameters();
            tenancyParameters.Add("@TenanciesTenantsData", tenancyDataTable.AsTableValuedParameter("TenanciesTenantDetailsType"));
            tenancyParameters.Add("@DataSourceId", dataSourceId);

            string tenancyMergeQuery = $@"
                MERGE {Constants.PropertyFinancialInformationTableName} AS Target
                USING @TenanciesTenantsData AS Source
                ON Target.SRCTenancyId = Source.SRCTenancyId
                WHEN MATCHED THEN
                    UPDATE SET
                        SRCManagementId = Source.SRCManagementId,
                        SRCPropertyId = Source.SRCPropertyId,
                        TenancyName = Source.TenancyName,
                        TenancyStatus = Source.TenancyStatus,
                        IsActive = Source.IsActive,
                        LeaseStart = Source.LeaseStart,
                        LeaseEnd = Source.LeaseEnd,
                        VacateDate = Source.VacateDate,
                        AmountToVacate = Source.AmountToVacate,
                        PayToDate = Source.PayToDate,
                        Rent = Source.Rent,
                        Arrears = Source.Arrears,
                        OutstandingInvoices = Source.OutstandingInvoices,
                        IncreaseRent = Source.IncreaseRent,
                        IncreaseDate = Source.IncreaseDate,
                        RentPeriod = Source.RentPeriod,
                        Currency = Source.Currency,
                        ModifiedDate = GETUTCDATE()
                WHEN NOT MATCHED THEN
                    INSERT (PropertyId, SRCManagementId, SRCPropertyId, SRCTenancyId, 
                            TenancyName, TenancyStatus, IsActive, LeaseStart, LeaseEnd, VacateDate, AmountToVacate, PayToDate,
                            Rent, Arrears, OutstandingInvoices, IncreaseRent, IncreaseDate, RentPeriod, Currency)
                    VALUES ((SELECT TOP 1 UP.PropertyId 
                            FROM {Constants.UserPropertyTableName} UP 
                            WHERE UP.SRCEntitytId = Source.SRCPropertyId AND UP.SRCTenancyId = Source.SRCTenancyId
                            AND UP.IsActive = 1), Source.SRCManagementId, Source.SRCPropertyId, Source.SRCTenancyId, 
                            Source.TenancyName, Source.TenancyStatus, Source.IsActive, Source.LeaseStart, Source.LeaseEnd, Source.VacateDate, Source.AmountToVacate, Source.PayToDate,
                            Source.Rent, Source.Arrears, Source.OutstandingInvoices, Source.IncreaseRent, Source.IncreaseDate, Source.RentPeriod, Source.Currency)
                                   OUTPUT 
                           $action AS MergeAction,
                           inserted.SRCTenancyId AS SRCId,
                           inserted.SRCManagementId AS SRCManagementId,
                           inserted.SRCPropertyId AS SRCPropertyId,
                           inserted.PropertyId AS PropertyId,
                           0 AS UserId;";

            var rowsAffected = await ExecuteMergeOperation(tenancyMergeQuery, tenancyParameters, "tenancies tenant information", tenanciesTenantList.Count, dataSourceId);
            return rowsAffected;
        }

        /// <summary>
        /// Executes the property manager information upsert for tenancies
        /// </summary>
        /// <param name="tenanciesWithPropertyManagerDetails">List of tenancies with property manager details</param>
        /// <param name="dataSourceId">Data source ID</param>
        /// <returns>Number of rows affected</returns>
        private async Task<List<SQLQueryMergeResult>> ExecuteTenanciesPropertyManagerUpsert(List<TenanciesTenant> tenanciesWithPropertyManagerDetails, int dataSourceId)
        {
            var propertyManagerDataTable = CreateTenanciesPropertyManagerDataTable(tenanciesWithPropertyManagerDetails);
            var propertyManagerParameters = new DynamicParameters();
            propertyManagerParameters.Add("@PropertyManagerData", propertyManagerDataTable.AsTableValuedParameter("PropertyManagerInformationType"));
            propertyManagerParameters.Add("@DataSourceId", dataSourceId);

            string propertyManagerMergeQuery = $@"
                MERGE {Constants.PropertyManagerInformationTableName} AS Target
                USING @PropertyManagerData AS Source
                ON Target.SRCManagementId = Source.SRCManagementId AND Target.SRCPropertyId = Source.SRCPropertyId
                WHEN MATCHED THEN
                    UPDATE SET
                        PropertyManagerName = Source.PropertyManagerName,
                        PropertyManagerMobile = Source.PropertyManagerMobile,
                        PropertyManagerPhone = Source.PropertyManagerPhone,
                        PropertyManagerEmail = Source.PropertyManagerEmail,
                        ContactRole = Source.ContactRole,
                        AuthorityStartDate = Source.AuthorityStartDate,
                        AuthorityEndDate = Source.AuthorityEndDate,
                        Ownership = Source.Ownership,
                        ExpenditureLimit = Source.ExpenditureLimit,
                        ExpenditureNotes = Source.ExpenditureNotes,
                        ModifiedDate = GETUTCDATE()
                WHEN NOT MATCHED THEN
                    INSERT (PropertyId, SRCAgencyId, SRCManagementId, SRCPropertyId, ManagementType, AgencyName, 
                            PropertyManagerName, PropertyManagerMobile, PropertyManagerPhone, PropertyManagerEmail, ContactRole,
                            AuthorityStartDate, AuthorityEndDate, Ownership, ExpenditureLimit, ExpenditureNotes)
                    VALUES ((SELECT TOP 1 UP.PropertyId 
                            FROM {Constants.UserPropertyTableName} UP 
                            WHERE UP.SRCEntitytId = Source.SRCPropertyId AND UP.SRCManagementId = Source.SRCManagementId
                            AND UP.IsActive = 1), Source.SRCAgencyId, Source.SRCManagementId, Source.SRCPropertyId, 
                            Source.ManagementType, Source.AgencyName, Source.PropertyManagerName, 
                            Source.PropertyManagerMobile, Source.PropertyManagerPhone, Source.PropertyManagerEmail, Source.ContactRole,
                            Source.AuthorityStartDate, Source.AuthorityEndDate, Source.Ownership, 
                            Source.ExpenditureLimit, Source.ExpenditureNotes)
                                   OUTPUT 
                           $action AS MergeAction,
                           inserted.SRCAgencyId AS SRCId,
                           inserted.SRCManagementId AS SRCManagementId,    
                           inserted.SRCPropertyId AS SRCPropertyId,
                           inserted.PropertyId AS PropertyId,
                           0 AS UserId;";

            var rowsAffected = await ExecuteMergeOperation(propertyManagerMergeQuery, propertyManagerParameters, "tenancies property manager details", tenanciesWithPropertyManagerDetails.Count, dataSourceId);
            return rowsAffected;
        }

        /// <summary>
        /// Creates DataTable for TenanciesTenants financial information
        /// </summary>
        /// <param name="tenanciesTenantList">List of tenancies tenants</param>
        /// <returns>DataTable populated with tenant financial data</returns>
        private DataTable CreateTenanciesTenantsDataTable(List<TenanciesTenant> tenanciesTenantList)
        {
            var tenancyDataTable = new DataTable();
            tenancyDataTable.Columns.Add("SRCManagementId", typeof(string));
            tenancyDataTable.Columns.Add("SRCPropertyId", typeof(string));
            tenancyDataTable.Columns.Add("SRCTenancyId", typeof(string));
            tenancyDataTable.Columns.Add("TenancyName", typeof(string));
            tenancyDataTable.Columns.Add("TenancyStatus", typeof(string));
            tenancyDataTable.Columns.Add("IsActive", typeof(bool));
            tenancyDataTable.Columns.Add("LeaseStart", typeof(DateTime));
            tenancyDataTable.Columns.Add("LeaseEnd", typeof(DateTime));
            tenancyDataTable.Columns.Add("VacateDate", typeof(DateTime));
            tenancyDataTable.Columns.Add("AmountToVacate", typeof(decimal));
            tenancyDataTable.Columns.Add("PayToDate", typeof(DateTime));
            tenancyDataTable.Columns.Add("Rent", typeof(decimal));
            tenancyDataTable.Columns.Add("Arrears", typeof(decimal));
            tenancyDataTable.Columns.Add("OutstandingInvoices", typeof(decimal));
            tenancyDataTable.Columns.Add("IncreaseRent", typeof(decimal));
            tenancyDataTable.Columns.Add("IncreaseDate", typeof(DateTime));
            tenancyDataTable.Columns.Add("RentPeriod", typeof(int));
            tenancyDataTable.Columns.Add("Currency", typeof(string));

            foreach (var tenant in tenanciesTenantList)
            {
                tenancyDataTable.Rows.Add(
                    tenant.SRCManagementId ?? (object)DBNull.Value,
                    tenant.SRCPropertyId ?? (object)DBNull.Value,
                    tenant.SRCTenancyId ?? (object)DBNull.Value,
                    tenant.TenancyName ?? (object)DBNull.Value,
                    tenant.TenancyStatus ?? (object)DBNull.Value,
                    tenant.IsActive,
                    tenant.LeaseStart ?? (object)DBNull.Value,
                    tenant.LeaseEnd ?? (object)DBNull.Value,
                    tenant.VacateDate ?? (object)DBNull.Value,
                    tenant.AmountToVacate ?? (object)DBNull.Value,
                    tenant.PayToDate ?? (object)DBNull.Value,
                    tenant.Rent ?? (object)DBNull.Value,
                    tenant.Arrears ?? (object)DBNull.Value,
                    tenant.OutstandingInvoices ?? (object)DBNull.Value,
                    tenant.IncreaseRent ?? (object)DBNull.Value,
                    tenant.IncreaseDate ?? (object)DBNull.Value,
                    tenant.RentPeriod ?? (object)DBNull.Value,
                    tenant.Currency ?? (object)DBNull.Value);
            }

            return tenancyDataTable;
        }

        /// <summary>
        /// Creates DataTable for TenanciesPropertyManager information
        /// </summary>
        /// <param name="tenanciesWithPropertyManagerDetails">List of tenancies with property manager details</param>
        /// <returns>DataTable populated with property manager data</returns>
        private DataTable CreateTenanciesPropertyManagerDataTable(List<TenanciesTenant> tenanciesWithPropertyManagerDetails)
        {
            var propertyManagerDataTable = new DataTable();
            propertyManagerDataTable.Columns.Add("SRCAgencyId", typeof(string));
            propertyManagerDataTable.Columns.Add("SRCManagementId", typeof(string));
            propertyManagerDataTable.Columns.Add("SRCPropertyId", typeof(string));
            propertyManagerDataTable.Columns.Add("ManagementType", typeof(string));
            propertyManagerDataTable.Columns.Add("AgencyName", typeof(string));
            propertyManagerDataTable.Columns.Add("PropertyManagerName", typeof(string));
            propertyManagerDataTable.Columns.Add("PropertyManagerMobile", typeof(string));
            propertyManagerDataTable.Columns.Add("PropertyManagerPhone", typeof(string));
            propertyManagerDataTable.Columns.Add("PropertyManagerEmail", typeof(string));
            propertyManagerDataTable.Columns.Add("ContactRole", typeof(string));
            propertyManagerDataTable.Columns.Add("AuthorityStartDate", typeof(DateTime));
            propertyManagerDataTable.Columns.Add("AuthorityEndDate", typeof(DateTime));
            propertyManagerDataTable.Columns.Add("Ownership", typeof(string));
            propertyManagerDataTable.Columns.Add("ExpenditureLimit", typeof(decimal));
            propertyManagerDataTable.Columns.Add("ExpenditureNotes", typeof(string));

            foreach (var tenant in tenanciesWithPropertyManagerDetails)
            {
                var manager = tenant.TenanciesPropertyManagerDetails;
                propertyManagerDataTable.Rows.Add(
                    manager.SRCAgencyId ?? (object)DBNull.Value,
                    manager.SRCManagementId ?? (object)DBNull.Value,
                    manager.SRCPropertyId ?? (object)DBNull.Value,
                    (object)DBNull.Value, // ManagementType - not available in TenanciesPropertyManagerDetails
                    (object)DBNull.Value, // AgencyName - not available in TenanciesPropertyManagerDetails
                    manager.PropertyManagerName ?? (object)DBNull.Value,
                    manager.PropertyManagerMobile ?? (object)DBNull.Value,
                    (object)DBNull.Value, // PropertyManagerPhone - not available in TenanciesPropertyManagerDetails
                    manager.PropertyManagerEmail ?? (object)DBNull.Value,
                    manager.ContactRole ?? (object)DBNull.Value,
                    manager.AuthorityStartDate ?? (object)DBNull.Value,
                    manager.AuthorityEndDate ?? (object)DBNull.Value,
                    manager.Ownership ?? (object)DBNull.Value,
                    (object)DBNull.Value, // ExpenditureLimit - not available in TenanciesPropertyManagerDetails
                    (object)DBNull.Value  // ExpenditureNotes - not available in TenanciesPropertyManagerDetails
                );
            }

            return propertyManagerDataTable;
        }
        public async Task<List<SQLQueryMergeResult>> BulkUpsertInspectionsDetails(List<InspectionDetail> list, int dataSourceId)
        {
            try
            {
                return await ExecuteBulkUpsert(
                list,
                dataSourceId,
                "inspection details",
                "InspectionsData",
                "InspectionDetailsType",
                $@"MERGE {Constants.InspectionDetailsTableName} AS Target
                   USING @InspectionsData AS Source
                   ON (Target.SRCManagementId = Source.SRCManagementId OR Target.SRCTenancyId = Source.SRCTenancyId) AND Target.SRCPropertyId = Source.SRCPropertyId AND Target.SRCInspectionId = Source.SRCInspectionId
                   WHEN MATCHED THEN
                       UPDATE SET
                           InspectionStatus = Source.InspectionStatus,
                           InspectionDate = Source.InspectionDate,
                           InspectionStartTime = Source.InspectionStartTime,
                           InspectionEndTime = Source.InspectionEndTime,
                           Summary = Source.Summary,
                           ModifiedDate = GETUTCDATE()
                   WHEN NOT MATCHED BY TARGET THEN
                       INSERT (
                           PropertyId, SRCManagementId, SRCTenancyId, SRCPropertyId, SRCInspectionId, InspectionStatus, InspectionDate, InspectionStartTime, InspectionEndTime,
                           Summary, CreatedDate
                       )
                       VALUES (
                           (SELECT TOP 1 UP.PropertyId 
                               FROM {Constants.UserPropertyTableName} UP 
                               WHERE UP.SRCEntitytId = Source.SRCPropertyId AND UP.SRCTenancyId = Source.SRCTenancyId AND UP.IsActive = 1
                           ), 
                           Source.SRCManagementId, Source.SRCTenancyId, Source.SRCPropertyId, Source.SRCInspectionId, Source.InspectionStatus, Source.InspectionDate, Source.InspectionStartTime, Source.InspectionEndTime,
                           Source.Summary, GETUTCDATE()
                       )
                   OUTPUT 
                           $action AS MergeAction,
                           inserted.SRCInspectionId AS SRCId,
                           inserted.SRCTenancyId AS SRCManagementId,
                           inserted.SRCPropertyId AS SRCPropertyId,
                           inserted.PropertyId AS PropertyId,
                           0 AS UserId;",
                CreateInspectionDetailsDataTable);
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetInspections,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(list)
                });
                return null;
            }
            
        }

        /// <summary>
        /// Creates DataTable for InspectionDetails bulk operations
        /// </summary>
        /// <param name="list">List of inspection details</param>
        /// <returns>DataTable populated with inspection details</returns>
        private DataTable CreateInspectionDetailsDataTable(List<InspectionDetail> list)
        {
            var dataTable = new DataTable("InspectionsDetails");

            dataTable.Columns.Add("SRCManagementId", typeof(string));
            dataTable.Columns.Add("SRCTenancyId", typeof(string));
            dataTable.Columns.Add("SRCPropertyId", typeof(string));
            dataTable.Columns.Add("SRCInspectionId", typeof(string));
            dataTable.Columns.Add("InspectionStatus", typeof(string));
            dataTable.Columns.Add("InspectionDate", typeof(DateTime));
            dataTable.Columns.Add("InspectionStartTime", typeof(DateTime));
            dataTable.Columns.Add("InspectionEndTime", typeof(DateTime));
            dataTable.Columns.Add("Summary", typeof(string));

            foreach (var detail in list)
            {
                dataTable.Rows.Add(
                    detail.SRCManagementId ?? (object)DBNull.Value,
                    detail.SRCTenancyId ?? (object)DBNull.Value,
                    detail.SRCPropertyId ?? (object)DBNull.Value,
                    detail.SRCInspectionId ?? (object)DBNull.Value,
                    detail.InspectionStatus ?? (object)DBNull.Value,
                    detail.InspectionDate,
                    detail.InspectionStartTime,
                    detail.InspectionEndTime,
                    detail.Summary ?? (object)DBNull.Value);
            }

            return dataTable;
        }
        public async Task<List<SQLQueryMergeResult>> BulkUpsertMaintenanceDetails(List<MaintenanceDetail> list, int dataSourceId)
        {
            try
            {
                return await ExecuteBulkUpsert(
                list,
                dataSourceId,
                "maintenance details",
                "MaintenanceData",
                "MaintenanceDetailsType",
                $@"MERGE {Constants.MaintenanceDetailsTableName} AS Target
                   USING @MaintenanceData AS Source
                   ON (Target.SRCManagementId = Source.SRCManagementId OR Target.SRCTenancyId = Source.SRCTenancyId) AND Target.SRCPropertyId = Source.SRCPropertyId AND Target.SRCJobId = Source.SRCJobId
                   WHEN MATCHED THEN
                       UPDATE SET
                           JobSummary = Source.JobSummary,
                           JobStatus = Source.JobStatus,
                           SRCRequestId = Source.SRCRequestId,
                           RequestSummary = Source.RequestSummary,
                           RequestStatus = Source.RequestStatus,
                           RequestRaisedBy = Source.RequestRaisedBy,
                           RequestRaisedDate = Source.RequestRaisedDate,
                           ImageLink = Source.ImageLink,
                           ModifiedDate = GETUTCDATE()
                   WHEN NOT MATCHED BY TARGET THEN
                       INSERT (
                           PropertyId, SRCManagementId, SRCTenancyId, SRCPropertyId, SRCJobId, JobSummary, JobStatus, SRCRequestId, RequestSummary, RequestStatus,
                           RequestRaisedBy, RequestRaisedDate, ImageLink, CreatedDate
                       )
                       VALUES (
                           (SELECT TOP 1 UP.PropertyId 
                               FROM {Constants.UserPropertyTableName} UP 
                               WHERE UP.SRCEntitytId = Source.SRCPropertyId AND UP.SRCManagementId = Source.SRCManagementId AND UP.IsActive = 1
                           ), 
                           Source.SRCManagementId, Source.SRCTenancyId, Source.SRCPropertyId, Source.SRCJobId, Source.JobSummary, Source.JobStatus, 
                           Source.SRCRequestId, Source.RequestSummary, Source.RequestStatus, Source.RequestRaisedBy, Source.RequestRaisedDate, 
                           Source.ImageLink, GETUTCDATE()
                       )
                   OUTPUT 
                           $action AS MergeAction,
                           inserted.SRCJobId AS SRCId,
                           inserted.SRCManagementId AS SRCManagementId,    
                           inserted.SRCPropertyId AS SRCPropertyId,
                           inserted.PropertyId AS PropertyId,
                           0 AS UserId;",
                CreateMaintenanceDetailsDataTable);
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetMaintenance,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(list)
                });
                return null;
            }
        }

        /// <summary>
        /// Creates DataTable for MaintenanceDetails bulk operations
        /// </summary>
        /// <param name="list">List of maintenance details</param>
        /// <returns>DataTable populated with maintenance details</returns>
        private DataTable CreateMaintenanceDetailsDataTable(List<MaintenanceDetail> list)
        {
            var dataTable = new DataTable("MaintenanceDetails");

            dataTable.Columns.Add("SRCManagementId", typeof(string));
            dataTable.Columns.Add("SRCTenancyId", typeof(string));
            dataTable.Columns.Add("SRCPropertyId", typeof(string));
            dataTable.Columns.Add("SRCJobId", typeof(string));
            dataTable.Columns.Add("JobSummary", typeof(string));
            dataTable.Columns.Add("JobStatus", typeof(string));
            dataTable.Columns.Add("SRCRequestId", typeof(string));
            dataTable.Columns.Add("RequestSummary", typeof(string));
            dataTable.Columns.Add("RequestStatus", typeof(string));
            dataTable.Columns.Add("RequestRaisedBy", typeof(string));
            dataTable.Columns.Add("RequestRaisedDate", typeof(DateTime));
            dataTable.Columns.Add("ImageLink", typeof(string));

            foreach (var detail in list)
            {
                dataTable.Rows.Add(
                    detail.SRCManagementId ?? (object)DBNull.Value,
                    detail.SRCTenancyId ?? (object)DBNull.Value,
                    detail.SRCPropertyId ?? (object)DBNull.Value,
                    detail.SRCJobId ?? (object)DBNull.Value,
                    detail.JobSummary ?? (object)DBNull.Value,
                    detail.JobStatus ?? (object)DBNull.Value,
                    detail.SRCRequestId ?? (object)DBNull.Value,
                    detail.RequestSummary ?? (object)DBNull.Value,
                    detail.RequestStatus ?? (object)DBNull.Value,
                    detail.RequestRaisedBy ?? (object)DBNull.Value,
                    detail.RequestRaisedDate,
                    detail.ImageLink ?? (object)DBNull.Value);
            }

            return dataTable;
        }
        public async Task<List<SQLQueryMergeResult>> BulkUpsertFinancialsDetails(List<FinancialDetail> list, int dataSourceId)
        {
            if (!ValidateBulkOperationInput(list, "financials details"))
                return null;
            try
            {
                var dataTable = CreateFinancialDetailsDataTable(list);
                var parameters = new DynamicParameters();
                parameters.Add("@FinancialData", dataTable.AsTableValuedParameter("FinancialDetailsType"));

                string mergeQuery = $@"
                MERGE {Constants.PropertyFinancialInformationTableName} AS Target
                USING @FinancialData AS Source
                ON Target.SRCManagementId = Source.SRCManagementId AND Target.SRCPropertyId = Source.SRCPropertyId
                WHEN MATCHED THEN
                    UPDATE SET
                        OwnershipTotalAvailableBalance = Source.OwnershipTotalAvailableBalance,
                        PropertyOutstandingFees = Source.PropertyOutstandingFees,
                        PropertyOutstandingInvoices = Source.PropertyOutstandingInvoices,
                        PropertyOverdueInvoices = Source.PropertyOverdueInvoices,
                        LastPaymentAmount = Source.LastPaymentAmount,
                        Currency = Source.Currency,
                        LastStatementDate = Source.LastStatementDate,
                        ModifiedDate = GETUTCDATE()
                OUTPUT 
                       $action AS MergeAction,
                       null AS SRCId,
                       inserted.SRCManagementId AS SRCManagementId,
                       inserted.SRCPropertyId AS SRCPropertyId,
                       0 AS PropertyId,
                       0 AS UserId;";

                return await ExecuteMergeOperation(mergeQuery, parameters, "financials details", list.Count, dataSourceId);
            }
            catch (Exception ex)
            {

                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetFinancials,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(list)
                });
                return null;
            }
        }

        /// <summary>
        /// Creates DataTable for FinancialDetails bulk operations
        /// </summary>
        /// <param name="list">List of financial details</param>
        /// <returns>DataTable populated with financial details</returns>
        private DataTable CreateFinancialDetailsDataTable(List<FinancialDetail> list)
        {
            var dataTable = new DataTable("FinancialDetails");

            dataTable.Columns.Add("SRCManagementId", typeof(string));
            dataTable.Columns.Add("SRCPropertyId", typeof(string));
            dataTable.Columns.Add("OwnershipTotalAvailableBalance", typeof(decimal));
            dataTable.Columns.Add("PropertyOutstandingFees", typeof(decimal));
            dataTable.Columns.Add("PropertyOutstandingInvoices", typeof(decimal));
            dataTable.Columns.Add("PropertyOverdueInvoices", typeof(decimal));
            dataTable.Columns.Add("LastPaymentAmount", typeof(decimal)); 
            dataTable.Columns.Add("Currency", typeof(string));
            dataTable.Columns.Add("LastStatementDate", typeof(DateTime));
            
            foreach (var detail in list)
            {
                dataTable.Rows.Add(
                    detail.SRCManagementId ?? (object)DBNull.Value,
                    detail.SRCPropertyId ?? (object)DBNull.Value,
                    detail.OwnershipTotalAvailableBalance ?? (object)DBNull.Value,
                    detail.PropertyOutstandingFees ?? (object)DBNull.Value,
                    detail.PropertyOutstandingInvoices ?? (object)DBNull.Value,
                    detail.PropertyOverdueInvoices ?? (object)DBNull.Value,
                    detail.LastPaymentAmount ?? (object)DBNull.Value,
                    detail.Currency ?? (object)DBNull.Value,
                    detail.LastStatementDate ?? (object)DBNull.Value);
            }

            return dataTable;
        }

        public async Task<List<SQLQueryMergeResult>> BulkUpsertTenanciesOwners(List<TenanciesOwner> tenanciesOwnerList, int dataSourceId)
        {
            try
            {
                return await ExecuteBulkUpsert(
                tenanciesOwnerList,
                dataSourceId,
                "tenancies owner information",
                "TenanciesOwnersData",
                "TenanciesOwnerDetailsType",
                $@"MERGE {Constants.PropertyFinancialInformationTableName} AS Target
                   USING @TenanciesOwnersData AS Source
                   ON Target.SRCManagementId = Source.SRCManagementId AND Target.SRCPropertyId = Source.SRCPropertyId
                   WHEN MATCHED THEN
                       UPDATE SET
                           SRCAgencyId = Source.SRCAgencyId,
                           SRCTenancyId = Source.SRCTenancyId,
                           TenancyName = Source.TenancyName,
                           TenancyStatus = Source.TenancyStatus,
                           IsActive = Source.IsActive,
                           LeaseStart = Source.LeaseStart,
                           LeaseEnd = Source.LeaseEnd,
                           Rent = Source.Rent,
                           RentPeriod = Source.RentPeriod,
                           Currency = Source.Currency,
                           OwnershipTotalAvailableBalance = Source.OwnershipTotalAvailableBalance,
                           PropertyOutstandingFees = Source.PropertyOutstandingFees,
                           PropertyOutstandingInvoices = Source.PropertyOutstandingInvoices,
                           PropertyOverdueInvoices = Source.PropertyOverdueInvoices,
                           LastPaymentAmount = Source.LastPaymentAmount,
                           LastStatementDate = Source.LastStatementDate,
                           ModifiedDate = GETUTCDATE()
                   WHEN NOT MATCHED THEN
                       INSERT (PropertyId, SRCAgencyId, SRCManagementId, SRCPropertyId, SRCTenancyId, 
                               TenancyName, TenancyStatus, IsActive, LeaseStart, LeaseEnd, Rent, RentPeriod, Currency,
                               OwnershipTotalAvailableBalance, PropertyOutstandingFees, PropertyOutstandingInvoices,
                               PropertyOverdueInvoices, LastPaymentAmount, LastStatementDate, CreatedDate, ModifiedDate)
                       VALUES ((SELECT TOP 1 UP.PropertyId 
                               FROM {Constants.UserPropertyTableName} UP 
                               WHERE UP.SRCEntitytId = Source.SRCPropertyId AND UP.SRCManagementId = Source.SRCManagementId
                               AND UP.IsActive = 1), Source.SRCAgencyId, Source.SRCManagementId, Source.SRCPropertyId, Source.SRCTenancyId, 
                               Source.TenancyName, Source.TenancyStatus, Source.IsActive, Source.LeaseStart, Source.LeaseEnd, Source.Rent, Source.RentPeriod, Source.Currency,
                               Source.OwnershipTotalAvailableBalance, Source.PropertyOutstandingFees, Source.PropertyOutstandingInvoices,
                               Source.PropertyOverdueInvoices, Source.LastPaymentAmount, Source.LastStatementDate, GETUTCDATE(), GETUTCDATE())
                   OUTPUT 
                           $action AS MergeAction,
                           inserted.SRCTenancyId AS SRCId, 
                           inserted.SRCManagementId AS SRCManagementId,
                           inserted.SRCPropertyId AS SRCPropertyId,
                           inserted.PropertyId AS PropertyId,
                           0 AS UserId;",
                CreateTenanciesOwnersDataTable);
            }
            catch (Exception ex)
            {
                await _apiTrackingRepository.LogAPITrackingDetails(new APITrackingDetail
                {
                    APIDetailId = (int)Constants.APIDetail.GetTenanciesOwner,
                    InviteCode = null!,
                    UserId = null!,
                    IsCompleted = false,
                    ErrorInfo = "Sql merge statement error:  " + ex.Message,
                    IsDataSync = true,
                    AdditionalErrorInfo = System.Text.Json.JsonSerializer.Serialize(tenanciesOwnerList)
                });
                return null;
            }
        }

        /// <summary>
        /// Creates DataTable for TenanciesOwners bulk operations
        /// </summary>
        /// <param name="tenanciesOwnerList">List of tenancies owners</param>
        /// <returns>DataTable populated with tenancies owner data</returns>
        private DataTable CreateTenanciesOwnersDataTable(List<TenanciesOwner> tenanciesOwnerList)
        {
            var ownerDataTable = new DataTable();
            ownerDataTable.Columns.Add("SRCAgencyId", typeof(string));
            ownerDataTable.Columns.Add("SRCManagementId", typeof(string));
            ownerDataTable.Columns.Add("SRCPropertyId", typeof(string));
            ownerDataTable.Columns.Add("SRCTenancyId", typeof(string));
            ownerDataTable.Columns.Add("TenancyName", typeof(string));
            ownerDataTable.Columns.Add("TenancyStatus", typeof(string));
            ownerDataTable.Columns.Add("IsActive", typeof(bool));
            ownerDataTable.Columns.Add("LeaseStart", typeof(DateTime));
            ownerDataTable.Columns.Add("LeaseEnd", typeof(DateTime));
            ownerDataTable.Columns.Add("Rent", typeof(decimal));
            ownerDataTable.Columns.Add("RentPeriod", typeof(int));
            ownerDataTable.Columns.Add("Currency", typeof(string));
            ownerDataTable.Columns.Add("OwnershipTotalAvailableBalance", typeof(decimal));
            ownerDataTable.Columns.Add("PropertyOutstandingFees", typeof(decimal));
            ownerDataTable.Columns.Add("PropertyOutstandingInvoices", typeof(decimal));
            ownerDataTable.Columns.Add("PropertyOverdueInvoices", typeof(decimal));
            ownerDataTable.Columns.Add("LastPaymentAmount", typeof(decimal));
            ownerDataTable.Columns.Add("LastStatementDate", typeof(DateTime));

            foreach (var owner in tenanciesOwnerList)
            {
                ownerDataTable.Rows.Add(
                    owner.SRCAgencyId ?? (object)DBNull.Value,
                    owner.SRCManagementId ?? (object)DBNull.Value, // from managementId
                    owner.SRCPropertyId ?? (object)DBNull.Value,   // from propertyId
                    owner.SRCTenancyId ?? (object)DBNull.Value,    // from tenancyId
                    owner.TenancyName ?? (object)DBNull.Value,     // from tenancyName
                    owner.TenancyStatus ?? (object)DBNull.Value,   // from tenancyStatus -> TenancyStatus (string)
                    owner.IsActive,                                // from tenancyStatus (converted to bool for IsActive)
                    owner.LeaseStart ?? (object)DBNull.Value,      // from tenancyStartDate
                    owner.LeaseEnd ?? (object)DBNull.Value,        // from tenancyEndDate
                    owner.Rent ?? (object)DBNull.Value,            // from rent
                    owner.RentPeriod ?? (object)DBNull.Value,      // from rentPeriod
                    owner.Currency ?? (object)DBNull.Value,        // from currency
                    owner.OwnershipTotalAvailableBalance ?? (object)DBNull.Value,
                    owner.PropertyOutstandingFees ?? (object)DBNull.Value,
                    owner.PropertyOutstandingInvoices ?? (object)DBNull.Value,
                    owner.PropertyOverdueInvoices ?? (object)DBNull.Value,
                    owner.LastPaymentAmount ?? (object)DBNull.Value,
                    owner.LastStatementDate ?? (object)DBNull.Value);
            }

            // Log field mappings for debugging
            _logger.LogInformation("Bulk processing {OwnerCount} tenancies owners. Fields mapped: managementId->SRCManagementId, propertyId->SRCPropertyId, tenancyId->SRCTenancyId, tenancyName->TenancyName, tenancyStatus->TenancyStatus, tenancyStartDate->LeaseStart, tenancyEndDate->LeaseEnd, rent->Rent, rentPeriod->RentPeriod, currency->Currency", 
                tenanciesOwnerList.Count);

            return ownerDataTable;
        }
    }
} 