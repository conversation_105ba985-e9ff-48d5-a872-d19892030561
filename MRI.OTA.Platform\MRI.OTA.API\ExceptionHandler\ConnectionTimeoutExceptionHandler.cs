﻿using System.Text.Json;
using Microsoft.AspNetCore.Diagnostics;
using MRI.OTA.Common.Models;

namespace MRI.OTA.API.ExceptionHandler
{
    public class ConnectionTimeoutExceptionHandler : IExceptionHandler
    {
        private readonly ILogger<ConnectionTimeoutExceptionHandler> _logger;

        public ConnectionTimeoutExceptionHandler(ILogger<ConnectionTimeoutExceptionHandler> logger)
        {
            _logger = logger;
        }

        public async ValueTask<bool> TryHandleAsync(
            HttpContext httpContext,
            Exception exception,
            CancellationToken cancellationToken)
        {
            if (exception is not ConnectionTimeoutException connectionTimeoutException)
            {
                return false;
            }

            _logger.LogError(
                connectionTimeoutException,
                "Exception occurred: {Message}",
                connectionTimeoutException.Message);

            var response = new ApiResponse<object>(false, "Conflict.", data: null!, StatusCodes.Status408RequestTimeout, new List<string> { exception.Message });

            httpContext.Response.ContentType = "application/json";
            httpContext.Response.StatusCode = StatusCodes.Status408RequestTimeout;
            await httpContext.Response.WriteAsJsonAsync(JsonSerializer.Serialize(response), cancellationToken);

            return true;
        }
    }
}
