﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\MRI.OTA.Common\MRI.OTA.Common.csproj" />
  </ItemGroup>
	<ItemGroup>
		<!-- Core ASP.NET and Dependency Injection -->
		<PackageReference Include="Microsoft.Data.SqlClient" />
		<PackageReference Include="Serilog" />
		<PackageReference Include="Dapper" />
		<PackageReference Include="Serilog.Extensions.Hosting" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" />
	</ItemGroup>
</Project>
