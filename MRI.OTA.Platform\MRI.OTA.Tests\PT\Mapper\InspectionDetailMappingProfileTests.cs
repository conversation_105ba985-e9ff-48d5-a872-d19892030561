using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.Common.Constants;

namespace MRI.OTA.UnitTestCases.PT.Mapper
{
    public class InspectionDetailMappingProfileTests
    {
        private readonly IMapper _mapper;

        public InspectionDetailMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<InspectionDetailMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        #region InspectionListResponse to InspectionDetail Tests

        [Fact]
        public void Should_Map_InspectionListResponse_To_InspectionDetail()
        {
            // Arrange
            var source = new InspectionsListResponse
            {
                InspectionId = "INSP001",
                Status = "Open",
                InspectionDate = DateTime.Now,
                Summary = "Test Inspection"
            };

            // Act
            var result = _mapper.Map<InspectionDetail>(source);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(source.InspectionId, result.SRCInspectionId);
            Assert.Equal(source.Status, result.InspectionStatus);
            Assert.Equal(source.InspectionDate, result.InspectionDate);
            Assert.Equal(source.Summary, result.Summary);
            Assert.Equal(-1, result.PropertyId); // Default value
        }

        #endregion

        #region InspectionDetailResponse to List<InspectionDetail> Tests

        [Fact]
        public void Should_Map_InspectionDetailResponse_To_List_InspectionDetail()
        {
            // Arrange
            var response = new InspectionDetailResponse
            {
                TenancyId = "AGENCY123",
                PropertyId = "PROP456",
                Inspections = new List<InspectionsListResponse>
                {
                    new InspectionsListResponse
                    {
                        InspectionId = "INSP001",
                        Status = "Open",
                        InspectionDate = new DateTime(2025, 6, 1),
                        Summary = "Inspection 1"
                    },
                    new InspectionsListResponse
                    {
                        InspectionId = "INSP002",
                        Status = "Scheduled",
                        InspectionDate = new DateTime(2025, 6, 2),
                        Summary = "Inspection 2"
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<InspectionDetail>>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Check first inspection
            var first = result[0];
            Assert.Equal("AGENCY123", first.SRCTenancyId);
            Assert.Equal("PROP456", first.SRCPropertyId);
            Assert.Equal("INSP001", first.SRCInspectionId);
            Assert.Equal("Open", first.InspectionStatus);
            Assert.Equal(new DateTime(2025, 6, 1), first.InspectionDate);
            Assert.Equal("Inspection 1", first.Summary);
            Assert.Equal(-1, first.PropertyId);

            // Check second inspection
            var second = result[1];
            Assert.Equal("AGENCY123", second.SRCTenancyId);
            Assert.Equal("PROP456", second.SRCPropertyId);
            Assert.Equal("INSP002", second.SRCInspectionId);
            Assert.Equal("Scheduled", second.InspectionStatus);
            Assert.Equal(new DateTime(2025, 6, 2), second.InspectionDate);
            Assert.Equal("Inspection 2", second.Summary);
            Assert.Equal(-1, second.PropertyId);
        }

        [Fact]
        public void Should_Return_Empty_List_When_No_Inspections()
        {
            // Arrange
            var response = new InspectionDetailResponse
            {
                TenancyId = "AGENCY123",
                PropertyId = "PROP456",
                Inspections = new List<InspectionsListResponse>()
            };

            // Act
            var result = _mapper.Map<List<InspectionDetail>>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public void Should_Map_List_InspectionDetailResponse_To_List_InspectionDetail()
        {
            // Arrange
            var responses = new List<InspectionDetailResponse>
            {
                new InspectionDetailResponse
                {
                    TenancyId = "AGENCY1",
                    PropertyId = "PROP1",
                    Inspections = new List<InspectionsListResponse>
                    {
                        new InspectionsListResponse
                        {
                            InspectionId = "INSP1",
                            Status = "Open",
                            InspectionDate = new DateTime(2025, 6, 1),
                            Summary = "First Property Inspection"
                        }
                    }
                },
                new InspectionDetailResponse
                {
                    TenancyId = "AGENCY2",
                    PropertyId = "PROP2",
                    Inspections = new List<InspectionsListResponse>
                    {
                        new InspectionsListResponse
                        {
                            InspectionId = "INSP2",
                            Status = "Scheduled",
                            InspectionDate = new DateTime(2025, 6, 2),
                            Summary = "Second Property Inspection"
                        }
                    }
                }
            };

            // Act
            var result = _mapper.Map<List<InspectionDetail>>(responses);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);

            // Check first inspection
            var first = result[0];
            Assert.Equal("AGENCY1", first.SRCTenancyId);
            Assert.Equal("PROP1", first.SRCPropertyId);
            Assert.Equal("INSP1", first.SRCInspectionId);
            Assert.Equal("Open", first.InspectionStatus);
            Assert.Equal(new DateTime(2025, 6, 1), first.InspectionDate);
            Assert.Equal("First Property Inspection", first.Summary);

            // Check second inspection
            var second = result[1];
            Assert.Equal("AGENCY2", second.SRCTenancyId);
            Assert.Equal("PROP2", second.SRCPropertyId);
            Assert.Equal("INSP2", second.SRCInspectionId);
            Assert.Equal("Scheduled", second.InspectionStatus);
            Assert.Equal(new DateTime(2025, 6, 2), second.InspectionDate);
            Assert.Equal("Second Property Inspection", second.Summary);
        }

        #endregion

        #region Legacy Mapping Tests

        [Fact]
        public void Should_Map_Single_InspectionDetailResponse_Using_Legacy_Mapping()
        {
            // Arrange
            var response = new InspectionDetailResponse
            {
                TenancyId = "AGENCY123",
                PropertyId = "PROP456",
                Inspections = new List<InspectionsListResponse>
                {
                    new InspectionsListResponse
                    {
                        InspectionId = "INSP001",
                        Status = "Open",
                        InspectionDate = new DateTime(2025, 6, 1),
                        Summary = "Legacy Inspection"
                    }
                }
            };

            // Act
            var result = _mapper.Map<InspectionDetail>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(response.TenancyId, result.SRCTenancyId);
            Assert.Equal(response.PropertyId, result.SRCPropertyId);
            Assert.Equal(response.Inspections[0].InspectionId, result.SRCInspectionId);
            Assert.Equal(response.Inspections[0].Status, result.InspectionStatus);
            Assert.Equal(response.Inspections[0].InspectionDate, result.InspectionDate);
            Assert.Equal(response.Inspections[0].Summary, result.Summary);
            Assert.Equal(-1, result.PropertyId);
        }

        [Fact]
        public void Should_Handle_Empty_Inspections_In_Legacy_Mapping()
        {
            // Arrange
            var response = new InspectionDetailResponse
            {
                TenancyId = "AGENCY123",
                PropertyId = "PROP456",
                Inspections = new List<InspectionsListResponse>()
            };

            // Act
            var result = _mapper.Map<InspectionDetail>(response);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(response.TenancyId, result.SRCTenancyId);
            Assert.Equal(response.PropertyId, result.SRCPropertyId);
            Assert.Null(result.SRCInspectionId);
            Assert.Equal(null!, result.InspectionStatus);
            Assert.Equal(default, result.InspectionDate);
            Assert.Null(result.Summary);
            Assert.Equal(-1, result.PropertyId);
        }

        #endregion
    }
}
