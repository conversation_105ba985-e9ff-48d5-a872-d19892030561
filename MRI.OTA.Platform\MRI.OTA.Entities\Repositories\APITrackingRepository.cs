﻿using System.Transactions;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.DBCore.Repositories
{
    public class APITrackingRepository : BaseRepository<APITrackingDetail, int>, IAPITrackingRepository
    {
        private readonly ILogger<APITrackingRepository> _logger;
        protected readonly IDapperWrapper _dapperWrapper;
        public APITrackingRepository(IDbConnectionFactory connectionFactory, ILogger<APITrackingRepository> logger, IDapperWrapper dapperWrapper) : base(connectionFactory, logger, dapperWrapper)
        {
            _logger = logger;
            _dapperWrapper = dapperWrapper;
        }

        public async Task LogAPITrackingDetails(APITrackingDetail apiTrackingDetails)
        {
            await AddAsync(apiTrackingDetails);
        }
    }
}
