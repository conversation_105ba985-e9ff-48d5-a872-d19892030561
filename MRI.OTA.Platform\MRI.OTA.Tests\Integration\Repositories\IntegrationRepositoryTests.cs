using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.DBCore.Repositories;
using System.Data;

namespace MRI.OTA.Tests.Integration.Repositories
{
    public class IntegrationRepositoryTests
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<IntegrationRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<IDbTransaction> _mockTransaction;
        private readonly IntegrationRepository _repository;
        private readonly Mock<IAPITrackingRepository> _mockAPITrackingRepository;

        public IntegrationRepositoryTests()
        {
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<IntegrationRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();
            _mockTransaction = new Mock<IDbTransaction>();
            _mockAPITrackingRepository = new Mock<IAPITrackingRepository>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);
            _mockConnection.Setup(c => c.BeginTransaction()).Returns(_mockTransaction.Object);

            _repository = new IntegrationRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object,
                _mockAPITrackingRepository.Object
            );
        }

        #region GetUniqueAgencies Tests

        [Fact]
        public async Task GetUniqueAgencies_ShouldReturnAgencies_WhenDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var batchSize = 10;
            var skip = 0;
            var expectedAgencies = new List<(string AgencyId, string AgencyName, int DataSourceId)>
            {
                ("Agency1", "Test Agency 1", dataSourceId),
                ("Agency2", "Test Agency 2", dataSourceId)
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedAgencies);

            // Act
            var result = await _repository.GetUniqueAgencies(dataSourceId, batchSize, skip);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(expectedAgencies, result);
        }

        [Fact]
        public async Task GetUniqueAgencies_ShouldReturnEmptyList_WhenNoDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var batchSize = 10;
            var skip = 0;
            var expectedAgencies = new List<(string AgencyId, string AgencyName, int DataSourceId)>();

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedAgencies);

            // Act
            var result = await _repository.GetUniqueAgencies(dataSourceId, batchSize, skip);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        [Fact]
        public async Task GetUniqueAgencies_ShouldLogAndReturnEmptyList_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var batchSize = 10;
            var skip = 0;
            var exception = new Exception("Database error");

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.GetUniqueAgencies(dataSourceId, batchSize, skip);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Error getting unique agencies for dataSourceId {dataSourceId}")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        #endregion

        #region GetUniqueAgenciesCount Tests

        [Fact]
        public async Task GetUniqueAgenciesCount_ShouldReturnCount_WhenDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var expectedCount = 5;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedCount);

            // Act
            var result = await _repository.GetUniqueAgenciesCount(dataSourceId);

            // Assert
            Assert.Equal(expectedCount, result);
        }

        [Fact]
        public async Task GetUniqueAgenciesCount_ShouldReturnZero_WhenNoDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var expectedCount = 0;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedCount);

            // Act
            var result = await _repository.GetUniqueAgenciesCount(dataSourceId);

            // Assert
            Assert.Equal(expectedCount, result);
        }

        [Fact]
        public async Task GetUniqueAgenciesCount_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var exception = new Exception("Database error");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.GetUniqueAgenciesCount(dataSourceId);

            // Assert
            Assert.Equal(0, result);

            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains($"Error getting unique agencies count for dataSourceId {dataSourceId}")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        #endregion

        #region GetUniqueIdsFromUserProperty Tests

        [Fact]
        public async Task GetUniqueIdsFromUserProperty_ShouldReturnIds_WhenDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var columnName = "SRCManagementId";
            var batchSize = 10;
            var skip = 0;
            var expectedIds = new List<(string Id, int DataSourceId)>
            {
                ("Id1", dataSourceId),
                ("Id2", dataSourceId)
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedIds);

            // Act
            var result = await _repository.GetUniqueIdsFromUserProperty(columnName, dataSourceId, batchSize, skip);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(expectedIds, result);
        }

        [Fact]
        public async Task GetUniqueIdsFromUserProperty_With_PropertyRelationshipId_WhenDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var columnName = "SRCManagementId";
            var batchSize = 10;
            var skip = 0;
            var propertyRelationshipId = 1; // Example PropertyRelationshipId
            var expectedIds = new List<(string Id, int DataSourceId)>
            {
                ("Id1", dataSourceId),
                ("Id2", dataSourceId)
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedIds);

            // Act
            var result = await _repository.GetUniqueIdsFromUserProperty(columnName, dataSourceId, batchSize, skip, propertyRelationshipId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal(expectedIds, result);
        }

        [Fact]
        public async Task GetUniqueIdsFromUserProperty_ShouldReturnEmptyList_WhenNoDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var columnName = "SRCManagementId";
            var batchSize = 10;
            var skip = 0;
            var expectedIds = new List<(string Id, int DataSourceId)>();

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedIds);

            // Act
            var result = await _repository.GetUniqueIdsFromUserProperty(columnName, dataSourceId, batchSize, skip);

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
        }

        #endregion

        #region GetUniqueManagementOrTenancyCount Tests

        [Fact]
        public async Task GetUniqueManagementOrTenancyCount_ShouldReturnCount_WhenDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var columnName = "SRCManagementId";
            var expectedCount = 5;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedCount);

            // Act
            var result = await _repository.GetUniqueManagementOrTenancyCount(dataSourceId, columnName);

            // Assert
            Assert.Equal(expectedCount, result);
        }

        [Fact]
        public async Task GetUniqueManagementOrTenancyCount_With_PropertyRelat_ShouldReturnCount_WhenDataExists()
        {
            // Arrange
            var dataSourceId = 1;
            var columnName = "SRCManagementId";
            var expectedCount = 5;
            var propertyRelationshipId = 1; // Example PropertyRelationshipId
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedCount);

            // Act
            var result = await _repository.GetUniqueManagementOrTenancyCount(dataSourceId, columnName, propertyRelationshipId);

            // Assert
            Assert.Equal(expectedCount, result);
        }

        #endregion

        #region BulkUpsertAgencyDetails Tests
        [Fact]
        public async Task BulkUpsertAgencyDetails_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var agencyDetailsList = new List<AgencyDetails>
            {
                new AgencyDetails { AgencyId = "A001", BusinessName = "Test Agency 1" },
                new AgencyDetails { AgencyId = "A002", BusinessName = "Test Agency 2" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE AgencyDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertAgencyDetails(agencyDetailsList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertAgencyDetails_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var agencyDetailsList = new List<AgencyDetails>
            {
                new AgencyDetails { AgencyId = "A001", BusinessName = "Test Agency 1" }
            };
            var exception = new Exception("SQL merge error");

            // Simulate exception in ExecuteBulkUpsert by making CreateAgencyDetailsDataTable throw
            var repo = new IntegrationRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object,
                _mockAPITrackingRepository.Object);

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE AgencyDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await repo.BulkUpsertAgencyDetails(agencyDetailsList, dataSourceId);

            // Assert
            Assert.Equal(0, result?.Count());
        }

        [Fact]
        public async Task BulkUpsertAgencyDetails_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var agencyDetailsList = new List<AgencyDetails>();

            // Act
            var result = await _repository.BulkUpsertAgencyDetails(agencyDetailsList, dataSourceId);

            // Assert
            Assert.Equal(null!, result?.Count());
        }
        #endregion

        #region BulkUpsertAgencyPartners Tests
        [Fact]
        public async Task BulkUpsertAgencyPartners_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var agencyPartnersList = new List<AgencyPartners>
            {
                new AgencyPartners { AgencyId = "A001", PartnerId = "P001" },
                new AgencyPartners { AgencyId = "A001", PartnerId = "P002" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE AgencyPartners")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertAgencyPartners(agencyPartnersList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertAgencyPartners_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var agencyPartnersList = new List<AgencyPartners>
            {
                new AgencyPartners { AgencyId = "A001", PartnerId = "P001" }
            };
            var exception = new Exception("SQL merge error");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE AgencyPartners")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.BulkUpsertAgencyPartners(agencyPartnersList, dataSourceId);

            // Assert
            Assert.Equal(0, result?.Count());
        }

        [Fact]
        public async Task BulkUpsertAgencyPartners_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var agencyPartnersList = new List<AgencyPartners>();

            // Act
            var result = await _repository.BulkUpsertAgencyPartners(agencyPartnersList, dataSourceId);

            // Assert
            Assert.Equal(null!, result?.Count());
        }
        #endregion

        #region BulkUpsertPropertyManagerInformation Tests
        [Fact]
        public async Task BulkUpsertPropertyManagerInformation_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var propertyManagerInfoList = new List<PropertyManagerInformation>
            {
                new PropertyManagerInformation { SRCAgencyId = "A001", SRCManagementId = "M001" },
                new PropertyManagerInformation { SRCAgencyId = "A002", SRCManagementId = "M002" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE PropertyManagerInformation")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertPropertyManagerInformation(propertyManagerInfoList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertPropertyManagerInformation_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var propertyManagerInfoList = new List<PropertyManagerInformation>
            {
                new PropertyManagerInformation { SRCAgencyId = "A001", SRCManagementId = "M001" }
            };
            var exception = new Exception("SQL merge error");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE PropertyManagerInformation")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.BulkUpsertPropertyManagerInformation(propertyManagerInfoList, dataSourceId);

            // Assert
            Assert.Equal(0, result?.Count());
        }

        [Fact]
        public async Task BulkUpsertPropertyManagerInformation_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var propertyManagerInfoList = new List<PropertyManagerInformation>();

            // Act
            var result = await _repository.BulkUpsertPropertyManagerInformation(propertyManagerInfoList, dataSourceId);

            // Assert
            Assert.Equal(null!, result);
        }
        #endregion

        #region BulkUpsertTenanciesOwners Tests
        [Fact]
        public async Task BulkUpsertTenanciesOwners_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var tenanciesOwnersList = new List<TenanciesOwner>
            {
                new TenanciesOwner { SRCAgencyId = "A001", SRCManagementId = "M001" },
                new TenanciesOwner { SRCAgencyId = "A002", SRCManagementId = "M002" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE PropertyFinancialInformation")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertTenanciesOwners(tenanciesOwnersList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertTenanciesOwners_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var tenanciesOwnerList = new List<TenanciesOwner>
            {
                new TenanciesOwner
                {
                    PropertyId = 1001,
                    OwnershipTotalAvailableBalance = 500.00m,
                    RentPeriod = 1
                }
            };
            var exception = new Exception("SQL merge error");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE PropertyFinancialInformation")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.BulkUpsertTenanciesOwners(tenanciesOwnerList, dataSourceId);

            // Assert
            Assert.Equal(0, result?.Count());
        }

        [Fact]
        public async Task BulkUpsertTenanciesOwners_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var tenanciesOwnersList = new List<TenanciesOwner>();

            // Act
            var result = await _repository.BulkUpsertTenanciesOwners(tenanciesOwnersList, dataSourceId);

            // Assert
            Assert.Equal(null!, result);
        }
        #endregion

        #region BulkUpsertDocumentDetails Tests
        [Fact]
        public async Task BulkUpsertDocumentDetails_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var documentDetailsList = new List<DocumentDetail>
            {
                new DocumentDetail { SRCDocumentId = "D001", DocumentName = "Test Doc 1" },
                new DocumentDetail { SRCDocumentId = "D002", DocumentName = "Test Doc 2" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.Contains("MERGE DocumentDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertDocumentDetails(documentDetailsList, dataSourceId, "SRCManagementId");

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertDocumentDetails_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var documentDetailsList = new List<DocumentDetail>
            {
                new DocumentDetail { SRCDocumentId = "D001", DocumentName = "Test Doc 1" }
            };
            var exception = new Exception("SQL merge error");
            // Simulate exception in Dapper call inside ExecuteMergeOperation
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.Contains("MERGE DocumentDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.BulkUpsertDocumentDetails(documentDetailsList, dataSourceId, "SRCManagementId");

            // Assert
            Assert.Equal(0, result?.Count());
        }

        [Fact]
        public async Task BulkUpsertDocumentDetails_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var documentDetailsList = new List<DocumentDetail>();

            // Act
            var result = await _repository.BulkUpsertDocumentDetails(documentDetailsList, dataSourceId, "SRCManagementId");

            // Assert
            Assert.Equal(null!, result?.Count());
        }
        #endregion

        #region BulkUpsertComplianceDetails Tests
        [Fact]
        public async Task BulkUpsertComplianceDetails_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var complianceDetailsList = new List<ComplianceDetail>
            {
                new ComplianceDetail { SRCComplianceId = "C001", ComplianceName = "Test Compliance 1" },
                new ComplianceDetail { SRCComplianceId = "C002", ComplianceName = "Test Compliance 2" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE ComplianceDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertComplianceDetails(complianceDetailsList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertComplianceDetails_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var complianceDetailsList = new List<ComplianceDetail>();

            // Act
            var result = await _repository.BulkUpsertComplianceDetails(complianceDetailsList, dataSourceId);

            // Assert
            Assert.Equal(null!, result?.Count());
        }

        [Fact]
        public async Task BulkUpsertComplianceDetails_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var complianceDetailsList = new List<ComplianceDetail>
            {
                new ComplianceDetail { SRCComplianceId = "C001", ComplianceName = "Test Compliance 1" }
            };
            var exception = new Exception("SQL merge error");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE ComplianceDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.BulkUpsertComplianceDetails(complianceDetailsList, dataSourceId);

            // Assert
            Assert.Equal(0, result?.Count());
        }

        #endregion

        #region BulkUpsertTenanciesTenants Tests
        [Fact]
        public async Task BulkUpsertTenanciesTenants_ShouldReturnTotalRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var tenanciesTenantList = new List<TenanciesTenant>
            {
                new TenanciesTenant { SRCTenancyId = "T001", TenancyName = "Test Tenancy 1" },
                new TenanciesTenant { SRCTenancyId = "T002", TenancyName = "Test Tenancy 2", TenanciesPropertyManagerDetails = new TenanciesPropertyManagerDetails { SRCPropertyId = "P001" } }
            };
            var expectedFinancialRowsAffected = 2;
            var expectedManagerRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.Contains("MERGE PropertyFinancialInformation AS Target")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedFinancialRowsAffected);

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.Contains("MERGE PropertyManagerInformation AS Target")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedManagerRowsAffected);

            // Act
            var result = await _repository.BulkUpsertTenanciesTenants(tenanciesTenantList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertTenanciesTenants_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var tenanciesTenantList = new List<TenanciesTenant>();

            // Act
            var result = await _repository.BulkUpsertTenanciesTenants(tenanciesTenantList, dataSourceId);

            // Assert
            Assert.Equal(null!, result);
        }
        #endregion

        #region BulkUpsertInspectionsDetails Tests
        [Fact]
        public async Task BulkUpsertInspectionsDetails_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var inspectionDetailsList = new List<InspectionDetail>
            {
                new InspectionDetail { SRCInspectionId = "I001", Summary = "Test Inspection 1" },
                new InspectionDetail { SRCInspectionId = "I002", Summary = "Test Inspection 2" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE InspectionDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertInspectionsDetails(inspectionDetailsList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertInspectionsDetails_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var inspectionDetailsList = new List<InspectionDetail>
            {
                new InspectionDetail
                {
                    SRCTenancyId = "T001",
                    SRCPropertyId = "P001",
                    SRCInspectionId = "I001",
                    InspectionDate = DateTime.UtcNow,
                    InspectionStartTime = DateTime.UtcNow,
                    InspectionEndTime = DateTime.UtcNow
                }
            };
            var exception = new Exception("SQL merge error");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE InspectionDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.BulkUpsertInspectionsDetails(inspectionDetailsList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertInspectionsDetails_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var inspectionDetailsList = new List<InspectionDetail>();

            // Act
            var result = await _repository.BulkUpsertInspectionsDetails(inspectionDetailsList, dataSourceId);

            // Assert
            Assert.Equal(null!, result);
        }
        #endregion

        #region BulkUpsertMaintenanceDetails Tests
        [Fact]
        public async Task BulkUpsertMaintenanceDetails_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var maintenanceDetailsList = new List<MaintenanceDetail>
            {
                new MaintenanceDetail { SRCJobId = "J001", JobSummary = "Test Job 1" },
                new MaintenanceDetail { SRCJobId = "J002", JobSummary = "Test Job 2" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE MaintenanceDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertMaintenanceDetails(maintenanceDetailsList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertMaintenanceDetails_ShouldLogAndReturnZero_WhenExceptionOccurs()
        {
            // Arrange
            var dataSourceId = 1;
            var maintenanceDetailsList = new List<MaintenanceDetail>
            {
                new MaintenanceDetail
                {
                    SRCManagementId = "M001",
                    SRCPropertyId = "P001",
                    SRCJobId = "J001",
                    SRCRequestId = "R001"
                }
            };
            var exception = new Exception("SQL merge error");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.StartsWith("MERGE MaintenanceDetails")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(exception);

            // Act
            var result = await _repository.BulkUpsertMaintenanceDetails(maintenanceDetailsList, dataSourceId);

            // Assert
            Assert.Equal(0, result?.Count());
        }

        [Fact]
        public async Task BulkUpsertMaintenanceDetails_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var maintenanceDetailsList = new List<MaintenanceDetail>();

            // Act
            var result = await _repository.BulkUpsertMaintenanceDetails(maintenanceDetailsList, dataSourceId);

            // Assert
            Assert.Equal(null!, result);
        }
        #endregion

        #region BulkUpsertFinancialsDetails Tests
        [Fact]
        public async Task BulkUpsertFinancialsDetails_ShouldReturnRowsAffected_WhenCalledWithData()
        {
            // Arrange
            var dataSourceId = 1;
            var financialDetailsList = new List<FinancialDetail>
            {
                new FinancialDetail { SRCManagementId = "M001", Currency = "USD" },
                new FinancialDetail { SRCManagementId = "M002", Currency = "GBP" }
            };
            var expectedRowsAffected = 2;

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.Is<string>(s => s.Contains("MERGE PropertyFinancialInformation")),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.BulkUpsertFinancialsDetails(financialDetailsList, dataSourceId);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task BulkUpsertFinancialsDetails_ShouldReturnZero_WhenCalledWithEmptyList()
        {
            // Arrange
            var dataSourceId = 1;
            var financialDetailsList = new List<FinancialDetail>();

            // Act
            var result = await _repository.BulkUpsertFinancialsDetails(financialDetailsList, dataSourceId);

            // Assert
            Assert.Equal(null!, result);
        }
        #endregion

        [Fact]
        public async Task GetUniqueAgencies_LogsErrorAndReturnsEmpty_WhenExceptionThrown()
        {
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.GetUniqueAgencies(1);

            Assert.Empty(result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting unique agencies")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetUniqueIdsFromUserProperty_LogsErrorAndReturnsEmpty_WhenExceptionThrown()
        {
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<(string, int)>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.GetUniqueIdsFromUserProperty("SRCManagementId", 1);

            Assert.Empty(result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting unique document's")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetUniqueAgenciesCount_LogsErrorAndReturnsZero_WhenExceptionThrown()
        {
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.GetUniqueAgenciesCount(1);

            Assert.Equal(0, result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting unique agencies count")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task GetUniqueManagementOrTenancyCount_LogsErrorAndReturnsZero_WhenExceptionThrown()
        {
            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<int>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.GetUniqueManagementOrTenancyCount(1, "SRCManagementId");

            Assert.Equal(0, result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString().Contains("Error getting unique SRCManagementId count")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertAgencyDetails_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertAgencyDetails(new List<AgencyDetails> { new AgencyDetails { AgencyId = "A001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertAgencyPartners_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
               .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                   It.IsAny<IDbConnection>(),
                   It.IsAny<string>(),
                   It.IsAny<object>(),
                   It.IsAny<IDbTransaction>(),
                   It.IsAny<int?>(),
                   It.IsAny<CommandType?>()))
               .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertAgencyPartners(new List<AgencyPartners> { new AgencyPartners { AgencyId = "A001", PartnerId = "P001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertDocumentDetails_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
                  .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                      It.IsAny<IDbConnection>(),
                      It.IsAny<string>(),
                      It.IsAny<object>(),
                      It.IsAny<IDbTransaction>(),
                      It.IsAny<int?>(),
                      It.IsAny<CommandType?>()))
                  .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertDocumentDetails(new List<DocumentDetail> { new DocumentDetail { SRCDocumentId = "D001" } }, 1, "SRCManagementId");

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertPropertyManagerInformation_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
               .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                   It.IsAny<IDbConnection>(),
                   It.IsAny<string>(),
                   It.IsAny<object>(),
                   It.IsAny<IDbTransaction>(),
                   It.IsAny<int?>(),
                   It.IsAny<CommandType?>()))
               .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertPropertyManagerInformation(new List<PropertyManagerInformation> { new PropertyManagerInformation { SRCAgencyId = "A001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertComplianceDetails_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
              .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                  It.IsAny<IDbConnection>(),
                  It.IsAny<string>(),
                  It.IsAny<object>(),
                  It.IsAny<IDbTransaction>(),
                  It.IsAny<int?>(),
                  It.IsAny<CommandType?>()))
              .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertComplianceDetails(new List<ComplianceDetail> { new ComplianceDetail { SRCComplianceId = "C001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertTenanciesTenants_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
               .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                   It.IsAny<IDbConnection>(),
                   It.IsAny<string>(),
                   It.IsAny<object>(),
                   It.IsAny<IDbTransaction>(),
                   It.IsAny<int?>(),
                   It.IsAny<CommandType?>()))
               .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertTenanciesTenants(new List<TenanciesTenant> { new TenanciesTenant { SRCTenancyId = "T001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertInspectionsDetails_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
                 .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                     It.IsAny<IDbConnection>(),
                     It.IsAny<string>(),
                     It.IsAny<object>(),
                     It.IsAny<IDbTransaction>(),
                     It.IsAny<int?>(),
                     It.IsAny<CommandType?>()))
                 .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertInspectionsDetails(new List<InspectionDetail> { new InspectionDetail { SRCInspectionId = "I001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertMaintenanceDetails_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
                .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                    It.IsAny<IDbConnection>(),
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertMaintenanceDetails(new List<MaintenanceDetail> { new MaintenanceDetail { SRCJobId = "J001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertFinancialsDetails_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
                 .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                     It.IsAny<IDbConnection>(),
                     It.IsAny<string>(),
                     It.IsAny<object>(),
                     It.IsAny<IDbTransaction>(),
                     It.IsAny<int?>(),
                     It.IsAny<CommandType?>()))
                 .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertFinancialsDetails(new List<FinancialDetail> { new FinancialDetail { SRCManagementId = "M001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }

        [Fact]
        public async Task BulkUpsertTenanciesOwners_LogsErrorAndReturnsNull_WhenExceptionThrown()
        {
            _mockDapperWrapper
               .Setup(d => d.QueryAsync<SQLQueryMergeResult>(
                   It.IsAny<IDbConnection>(),
                   It.IsAny<string>(),
                   It.IsAny<object>(),
                   It.IsAny<IDbTransaction>(),
                   It.IsAny<int?>(),
                   It.IsAny<CommandType?>()))
               .ThrowsAsync(new Exception("Test exception"));

            var result = await _repository.BulkUpsertTenanciesOwners(new List<TenanciesOwner> { new TenanciesOwner { SRCAgencyId = "A001" } }, 1);

            Assert.Null(result);
            _mockAPITrackingRepository.Verify(
                x => x.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()),
                Times.Once);
        }
    }
}