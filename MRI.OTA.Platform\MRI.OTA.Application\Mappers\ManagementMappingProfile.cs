using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class ManagementMappingProfile : Profile
    {
        public ManagementMappingProfile() : base("ManagementMappingProfile")
        {
            CreateMap<ManagementResponse, PropertyManagerInformation>()
                .ForMember(dest => dest.SRCAgencyId, opt => opt.MapFrom(src => src.AgencyId))
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.ManagementType, opt => opt.MapFrom(src => "Standard")) // Default value
                .ForMember(dest => dest.AgencyName, opt => opt.MapFrom(src => "")) // Will be populated from external source
                .ForMember(dest => dest.PropertyManagerName, opt => opt.MapFrom(src => src.ManagementContactName))
                .ForMember(dest => dest.PropertyManagerMobile, opt => opt.MapFrom(src => src.ManagementContactNumber))
                .ForMember(dest => dest.PropertyManagerPhone, opt => opt.MapFrom(src => src.ManagementContactNumber))
                .ForMember(dest => dest.PropertyManagerEmail, opt => opt.MapFrom(src => src.ManagementContactEmail))
                .ForMember(dest => dest.ContactRole, opt => opt.MapFrom(src => src.ManagementContactRole))
                .ForMember(dest => dest.AuthorityStartDate, opt => opt.MapFrom(src => src.AuthorityStartDate))
                .ForMember(dest => dest.AuthorityEndDate, opt => opt.MapFrom(src => src.AuthorityEndDate))
                .ForMember(dest => dest.Ownership, opt => opt.MapFrom(src => src.OwnershipName))
                .ForMember(dest => dest.ExpenditureLimit, opt => opt.Ignore()) // Not available in source
                .ForMember(dest => dest.ExpenditureNotes, opt => opt.Ignore()) // Not available in source
                .ForMember(dest => dest.PropertyManagerInformationId, opt => opt.Ignore()) // Auto-generated
                .ForMember(dest => dest.PropertyId, opt => opt.Ignore()); // Will be handled separately
        }
    }
} 