﻿using MRI.OTA.Common.Interfaces;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.DBCore.Interfaces
{
    /// <summary>
    /// Interface for Property Repository
    /// </summary>
    public interface IPropertyRepository : IBaseRepository<UserProperties, int>
    {
        public Task<List<ViewUserProperties>> GetAllProperties(int userid, int? offSet, int? limit, bool? showAllRecords = false); 
        public Task<int> AddProperty(UserProperties userProperties);
        public Task<int> AddPropertyOtherDetails(UserProperties userProperties);

        public Task<int> UpdateProperty(UserProperties userProperties);

        public Task<ViewUserProperties> GetPropertyById(int userId, int propertyId);

        public Task<int> DeleteProperty(int propertyId);

        public Task<List<PropertyImages>> GetPropertyImages(List<int> propertyIds);

        public Task<List<ViewUserPropertiesNickName>> GetPropertyRelations(int userPropertiesNickNameId);

        public Task<List<ViewUserPropertiesNickName>> GetPropertyNickNames(int userId);

        public Task<int> UpdatePropertyStatus(int userId, int propertyId, bool isActive);

        /// <summary>
        /// Update property portfolio - creates or updates property-nickname relationships
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="propertyId">Property ID</param>
        /// <param name="nicknameId">Nickname ID - if 0, create new entry; if greater than 0, update existing entry</param>
        /// <param name="nickname">Nickname for the property</param>
        /// <returns>Returns affected rows count</returns>
        public Task<int> UpdatePropertyPortfolio(int userId, int propertyId, int nicknameId, string? nickname);

        /// <summary>
        /// Get count of active and inactive properties grouped by agency ID with comprehensive agency information
        /// </summary>
        /// <param name="userId">The user ID</param>
        /// <returns>List of agency count models with comprehensive agency information</returns>
        public Task<List<ViewAgencyPropertyCount>> GetPropertyCountsByAgency(int userId);

        public Task<List<(int DataSourceId, string DataSourceName, int ActiveCount, int InactiveCount)>> GetPropertyCountsByDataSource(int userId);

        /// <summary>
        /// Get maintenance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of maintenance details</returns>
        public Task<List<ViewMaintenanceDetail>> GetMaintenanceDetails(int propertyId, string? managementId, string? tenancyId);
        public Task<List<UserProperties>> GetPropertiesByUserIds(string[] userId);

        /// <summary>
        /// Bulk upsert user properties using MERGE statement based on SRCEntitytId - insert if not exists, update if exists
        /// </summary>
        /// <param name="userPropertiesList">List of user properties to upsert</param>
        /// <param name="dataSourceId">The data source ID</param>
        /// <returns>Number of rows affected</returns>
        public Task<List<SQLQueryMergeResult>> BulkUpsertUserProperties(List<UserProperties> userPropertiesList, int dataSourceId);

        /// <summary>
        /// Get compliance details based on managementId and propertyId
        /// </summary>
        /// <param name="managementId">The management ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of compliance details</returns>
        public Task<List<ComplianceDetail>> GetCompliance(string managementId, int propertyId);

        /// <summary>
        /// Get inspections list based on tenancyId and propertyId
        /// </summary>
        /// <param name="tenancyId">The tenancy ID</param>
        /// <param name="propertyId">The property ID</param>
        /// <returns>List of inspection details</returns>
        public Task<List<InspectionDetail>> GetInspections(int propertyId, string? managementId, string? tenancyId);
        public Task<List<UserPropertyDocumentDetail>> GetDocument(GetDocumentRequestModel requestModel, int userId);

        /// <summary>
        /// Get property manager information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property manager information with agency details</returns>
        public Task<PropertyManagerWithAgencyDetails?> GetPropertyManagerInformation(string? managementId, int? propertyId, string? srcPropertyId);

        /// <summary>
        /// Get property financial information based on managementId, propertyId, or SRCPropertyId
        /// </summary>
        /// <param name="managementId">The management ID (optional)</param>
        /// <param name="propertyId">The property ID (optional)</param>
        /// <param name="srcPropertyId">The source property ID (optional)</param>
        /// <returns>Property financial information with agency details</returns>
        public Task<PropertyFinancialWithAgencyDetails?> GetPropertyFinancialInformation(string? managementId, int? propertyId, string? srcPropertyId);
        public Task<TenanciesTenantDetailResponse> GetTenantOwnerDetail(string? tenancyId, string? srcPropertyId, int? propertyId);
        public Task<List<UserProperties>> CheckExsitingProperties(int userId, string[] srcAgencyIds, string[] srcPropertyIds, int[] propertyRelationshipIds, string[] srcManagementIds, string[] srcTenancyIds);
        
    }
}
