﻿using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.User.v1
{
    /// <summary>
    /// To perform operations related to users
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/users")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class UserController : ControllerBase
    {
        private readonly IUserService _userService;

        /// <summary>
        /// Constructor for user controller
        /// </summary>
        /// <param name="userService"></param>
        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        /// <summary>
        /// Get user details based on userid
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet("{id}")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(UserModel), description: "user data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<UserModel>> GetUserById(int id)
        {
            var user = await _userService.GetByIdAsync(id, "UserId");
            if (user == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", user));
        }

        /// <summary>
        /// Add new user
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "user data added successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> AddUser(AddUserModel user)
        {
            var result = await _userService.CreateUser(user);
            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, MessagesConstants.ItemCreated, result));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotCreated, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotCreatedError }));
            }
        }

        /// <summary>
        /// Create user from identity token
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost("user-profile")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "user data added successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> CreateUserProfile(IdTokenModel idTokenModel)
        {
            var result = await _userService.CreateUserProfile(idTokenModel);
            var resultDeviceInfo = (result != null && idTokenModel.DeviceDetail != null) ? await _userService.AddUpdateUserDevice(idTokenModel.DeviceDetail) : -1;

            if (result == null)
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotCreated, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotCreatedError }));
            }

            // User created successfully
            if (!string.IsNullOrEmpty(result.UserEmail) && result.UserId > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item created successfully", result));
            }

            // Email not found
            if (string.IsNullOrEmpty(result.UserEmail))
            {
                return Ok(new ApiResponse<object>(false, MessagesConstants.EmailNotFound, data: null!, StatusCodes.Status200OK, new List<string> { MessagesConstants.EmailNotFound }));
            }

            // Other error
            return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotCreated, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotCreatedError }));
            
        }

        /// <summary>
        /// Update users terms and conditions
        /// </summary>
        /// <param name="updateTermsConditionModel"></param>
        /// <returns></returns>
        [HttpPut("terms-condition")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "user data updated successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> UpdateUserTermsCondition(TermsConditionModel updateTermsConditionModel)
        {
            var result = await _userService.UpdateUserTermsAndCondition(updateTermsConditionModel);
            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, MessagesConstants.ItemUpdatedSucess, result));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotUpdated, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotUpdated }));
            }
        }

        /// <summary>
        /// Update users settings
        /// </summary>
        /// <param name="userProfileSettings"></param>
        /// <returns></returns>
        [HttpPut("user-setting")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "user data updated successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> UpdateUserProfileSettings(UserProfileSettingsModel userProfileSettings)
        {
            var result = await _userService.UpdateUserProfileSettings(userProfileSettings);
            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, MessagesConstants.ItemUpdatedSucess, result));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotUpdated, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotUpdated }));
            }
        }

        /// <summary>
        /// Delete user account
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="providerId"></param>
        /// <returns></returns>
        [HttpDelete("{userId}/{providerId}")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "user data deleted successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> DeleteAccount(int userId, string providerId)
        {
            var result = await _userService.DeleteAccount(userId, providerId);
            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, MessagesConstants.ItemDeleted, result));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotDeleted, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotDeleted }));
            }
        }

        [HttpDelete("delete-device/{deviceId}")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "user device info deleted successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> DeleteUserDeviceInfo(string deviceId)
        {
            var result = await _userService.DeleteUserDeviceInfo(deviceId);
            if (result)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, MessagesConstants.ItemDeleted, result));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotDeleted, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotDeleted }));
            }
        }
    }
}
