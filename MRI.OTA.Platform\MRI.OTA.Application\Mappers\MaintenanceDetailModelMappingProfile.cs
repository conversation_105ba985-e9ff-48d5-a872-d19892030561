using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    /// <summary>
    /// Mapping profile for MaintenanceDetail entity to MaintenanceDetailModel
    /// </summary>
    public class MaintenanceDetailModelMappingProfile : Profile
    {
        public MaintenanceDetailModelMappingProfile() : base("MaintenanceDetailModelMappingProfile")
        {
            // Map from MaintenanceDetail entity to MaintenanceDetailModel
            CreateMap<ViewMaintenanceDetail, MaintenanceDetailModel>()
                .ForMember(dest => dest.ManagementId, opt => opt.MapFrom(src => src.SRCManagementId))
                .ForMember(dest => dest.TenancyId, opt => opt.MapFrom(src => src.SRCTenancyId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => src.SRCPropertyId))
                .ForMember(dest => dest.JobId, opt => opt.MapFrom(src => src.SRCJobId))
                .ForMember(dest => dest.RequestId, opt => opt.MapFrom(src => src.SRCRequestId));

            // Reverse mapping from MaintenanceDetailModel to MaintenanceDetail entity
            CreateMap<MaintenanceDetailModel, ViewMaintenanceDetail>()
                .ForMember(dest => dest.MaintenanceDetailId, opt => opt.Ignore()) // Auto-generated
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
                .ForMember(dest => dest.SRCTenancyId, opt => opt.MapFrom(src => src.TenancyId))
                .ForMember(dest => dest.PropertyId, opt => opt.Ignore()) // Will be set separately
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.SRCJobId, opt => opt.MapFrom(src => src.JobId))
                .ForMember(dest => dest.SRCRequestId, opt => opt.MapFrom(src => src.RequestId));
        }
    }
} 