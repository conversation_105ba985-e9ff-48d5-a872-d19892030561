﻿using System.Diagnostics;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.ApplicationInsights;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;
using MRI.OTA.Common.Constants;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication
{
    /// <summary>
    /// Middleware for handling authentication
    /// </summary>
    public class AuthenticationMiddleware
    {
        private readonly ILogger<AuthenticationMiddleware> _logger;
        private readonly AuthenticationStrategyResolver _strategyResolver;
        private readonly AuthenticationExceptionHandler _exceptionHandler;
        private readonly AuthenticationLogger _authLogger;

        /// <summary>
        /// Constructor for authentication middleware
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="jwtTokenValidator">The JWT token validator</param>
        /// <param name="logger">The logger</param>
        /// <param name="telemetryClient">The telemetry client</param>
        public AuthenticationMiddleware(
           RequestDelegate next,
           IJwtTokenValidator jwtTokenValidator,
           ILogger<AuthenticationMiddleware> logger,
           TelemetryClient telemetryClient)
        {
            ArgumentNullException.ThrowIfNull(next);
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));

            // Create helper services
            var responseGenerator = new ResponseGenerator();
            _authLogger = new AuthenticationLogger(logger, telemetryClient);
            _exceptionHandler = new AuthenticationExceptionHandler(logger, telemetryClient, responseGenerator, _authLogger);

            // Create authentication strategies
            var strategies = new List<IAuthenticationStrategy>
            {
                new AnonymousEndpointStrategy(next, logger),
                new ApiKeyStrategy(next, logger, responseGenerator),
                new BearerTokenStrategy(next, jwtTokenValidator, logger, responseGenerator, _authLogger, Constants.ClientCredentialEndpoints),
                new AccessTokenStrategy(next, jwtTokenValidator, logger, responseGenerator)
            };

            // Create strategy resolver
            _strategyResolver = new AuthenticationStrategyResolver(strategies, logger, responseGenerator);
        }

        /// <summary>
        /// Middleware invocation
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task InvokeAsync(HttpContext context)
        {
            ArgumentNullException.ThrowIfNull(context);

            var stopwatch = Stopwatch.StartNew();
            var correlationId = _authLogger.GetOrGenerateCorrelationId(context);

            try
            {
                using (_logger.BeginScope(new Dictionary<string, object>
                {
                    ["CorrelationId"] = correlationId,
                    ["Path"] = context.Request.Path,
                    ["Method"] = context.Request.Method
                }))
                {
                    await _strategyResolver.ResolveAndExecuteStrategy(context);
                }
            }
            catch (Exception ex)
            {
                await _exceptionHandler.HandleAuthenticationException(context, ex);
            }
            finally
            {
                stopwatch.Stop();
                _logger.LogInformation(
                    "Authentication processing completed in {ElapsedMilliseconds}ms for {Path}",
                    stopwatch.ElapsedMilliseconds,
                    context.Request.Path);
            }
        }
    }
}
