﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.Application.Mappers
{
    public class AddUsersMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for add user mapper
        /// </summary>
        public AddUsersMappingProfile() : base("AddUsersMappingProfile")
        {
            CreateMap<Users, AddUserModel>()
            .ReverseMap();
        }
    }
}
