﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class UserPropertiesNickName
    {
        /// <summary>
        /// UserPropertiesNickNameId
        /// </summary>
        [ExcludeColumn]
        public int UserPropertiesNickNameId { get; set; }

        /// <summary>
        /// UserId
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// PropertyNickName
        /// </summary>
        public string? PropertyNickName { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }
    }
}
