﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class PropertyImages
    {
        /// <summary>
        /// PropertyImageId
        /// </summary>
        [ExcludeColumn]
        public int PropertyImagesId { get; set; }
        /// <summary>
        /// PropertyId
        /// </summary>
        public int PropertyId { get; set; }
        /// <summary>
        /// ImageUrl
        /// </summary>
        public string? ImageBlobUrl { get; set; }
    }
}
