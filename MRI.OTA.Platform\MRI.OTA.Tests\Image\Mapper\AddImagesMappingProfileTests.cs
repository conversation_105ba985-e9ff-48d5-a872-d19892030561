﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.Image.Mapper
{
    public class AddImagesMappingProfileTests
    {
        private readonly IMapper _mapper;

        public AddImagesMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<AddImagesMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_Tuple_To_PropertyImages_Correctly()
        {
            // Arrange  
            var source = (PropertyId: 123, ImageBlobUrl: "https://example.com/image.jpg");

            // Act  
            var result = _mapper.Map<PropertyImages>(source);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(source.PropertyId, result.PropertyId);
            Assert.Equal(source.ImageBlobUrl, result.ImageBlobUrl);
        }
    }
}
