﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using MRI.OTA.Integration.Http;
using MRI.OTA.Integration.Models;
using MRI.OTA.Integration.Services;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;

namespace MRI.OTA.Tests.Integration.Services
{
    public class ProxyServiceTests
    {
        private readonly Mock<IHttpClientFactoryManager> _httpClientFactoryManagerMock;
        private readonly Mock<ILogger<ProxyService>> _loggerMock;
        private readonly ProxyService _proxyService;
        private readonly Mock<HttpMessageHandler> _httpMessageHandlerMock;

        public ProxyServiceTests()
        {
            _httpClientFactoryManagerMock = new Mock<IHttpClientFactoryManager>();
            _loggerMock = new Mock<ILogger<ProxyService>>();
            _httpMessageHandlerMock = new Mock<HttpMessageHandler>(MockBehavior.Strict);

            var httpClient = new HttpClient(_httpMessageHandlerMock.Object);
            _httpClientFactoryManagerMock.Setup(f => f.GetHttpClient()).Returns(httpClient);

            _proxyService = new ProxyService(_httpClientFactoryManagerMock.Object, _loggerMock.Object);
        }

        [Fact]
        public async Task ForwardRequestAsync_ShouldReturnBadRequest_WhenFullUrlIsMissing()
        {
            // Arrange
            var request = new ProxyRequestModel { FullUrl = "", Method = "GET", RequestId = "abc123" };

            // Act
            var result = await _proxyService.ForwardRequestAsync(request, "Bearer token");

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("Invalid request: FullUrl is required", badRequestResult.Value);
        }

        [Fact]
        public async Task ForwardRequestAsync_ShouldReturnBadRequest_WhenFullUrlIsInvalid()
        {
            // Arrange
            var request = new ProxyRequestModel { FullUrl = "invalid-url", Method = "GET", RequestId = "abc123" };

            // Act
            var result = await _proxyService.ForwardRequestAsync(request, "Bearer token");

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("Invalid full URL", badRequestResult.Value);
        }

        [Fact]
        public async Task ForwardRequestAsync_ShouldReturnOk_WhenRequestSucceeds()
        {
            var request = new ProxyRequestModel { FullUrl = "https://api.example.com/data", Method = "GET", RequestId = "abc123" };
            var responseBody = JsonSerializer.Serialize(new { success = true });

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent(responseBody, Encoding.UTF8, "application/json")
            };

            _httpMessageHandlerMock
                .Protected()
                .Setup<Task<HttpResponseMessage>>("SendAsync", ItExpr.IsAny<HttpRequestMessage>(), ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(responseMessage);

            // Act
            var result = await _proxyService.ForwardRequestAsync(request, "Bearer token");

            // Assert
            var okResult = Assert.IsType<ObjectResult>(result);
            var jsonResponse = okResult.Value as JsonDocument;
            Assert.NotNull(jsonResponse);
            Assert.True(jsonResponse.RootElement.GetProperty("success").GetBoolean());
        }

        [Fact]
        public async Task ForwardRequestAsync_ShouldReturnInternalServerError_WhenHttpRequestFails()
        {
            // Arrange
            var request = new ProxyRequestModel { FullUrl = "https://api.example.com/data", Method = "GET", RequestId = "abc123" };

            _httpMessageHandlerMock
           .Protected()
           .Setup<Task<HttpResponseMessage>>(
               "SendAsync",
               ItExpr.IsAny<HttpRequestMessage>(),
               ItExpr.IsAny<CancellationToken>()
           )
           .ThrowsAsync(new HttpRequestException("Network failure"));


            // Act
            var result = await _proxyService.ForwardRequestAsync(request, "Bearer token");

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, objectResult.StatusCode);
        }

        [Fact]
        public async Task ForwardRequestAsync_ShouldReturnCorrectStatusCode_WhenHttpClientReturnsError()
        {
            // Arrange
            var request = new ProxyRequestModel { FullUrl = "https://api.example.com/data", Method = "GET", RequestId = "abc123" };

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.NotFound,
                Content = new StringContent("{ \"error\": \"Not Found\" }", Encoding.UTF8, "application/json")
            };

            _httpMessageHandlerMock
    .Protected()
    .Setup<Task<HttpResponseMessage>>(
        "SendAsync",
        ItExpr.IsAny<HttpRequestMessage>(),
        ItExpr.IsAny<CancellationToken>()
    )
    .ReturnsAsync(responseMessage);


            // Act
            var result = await _proxyService.ForwardRequestAsync(request, "Bearer token");

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(404, objectResult.StatusCode);
            var errorResponse = objectResult.Value as dynamic;
        }

        [Fact]
        public async Task ForwardRequestAsync_ShouldIncludeBearerTokenInRequest()
        {
            // Arrange
            var request = new ProxyRequestModel { FullUrl = "https://api.example.com/data", Method = "GET", RequestId = "abc123" };
            var expectedToken = "my-secure-token";

            var responseMessage = new HttpResponseMessage
            {
                StatusCode = HttpStatusCode.OK,
                Content = new StringContent("{}", Encoding.UTF8, "application/json")
            };
            _httpMessageHandlerMock
     .Protected()
     .Setup<Task<HttpResponseMessage>>(
         "SendAsync",
         ItExpr.Is<HttpRequestMessage>(req =>
             req.Headers.Authorization != null && // Ensure Authorization is not null
             req.Headers.Authorization.Scheme == "Bearer" &&
             req.Headers.Authorization.Parameter == expectedToken),
         ItExpr.IsAny<CancellationToken>()
     )
     .ReturnsAsync(responseMessage);

            // Act
            var result = await _proxyService.ForwardRequestAsync(request, $"Bearer {expectedToken}");

            // Assert
            Assert.IsType<ObjectResult>(result);

            // Verify that SendAsync was called exactly once
            _httpMessageHandlerMock.Protected()
                .Verify(
                    "SendAsync",
                    Times.Once(),
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>()
                );
        }

    }
}
