﻿using MRI.OTA.Application.Models;

namespace MRI.OTA.Application.Interfaces
{
    public interface IImageStorageService
    {
       public Task<List<(int PropertyId, string ImageBlobUrl)>> UploadImages(ImageModel images);

       public  Task<int> DeletePropertyImage(int propertyImagesId);

       public Task<int> UpdateDefaultImage(UpdateDefaultImageModel defaultImageModel);

        public Task<int> DeletePropertyImagesByUserId(int userId);
    }
}
