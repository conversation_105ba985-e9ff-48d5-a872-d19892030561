using Microsoft.ApplicationInsights.Channel;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using System.Security.Claims;
using UAParser;

namespace MRI.OTA.Infrastructure.Telemetry
{
    /// <summary>
    /// Custom telemetry initializer that enriches Application Insights telemetry with
    /// additional information from the HTTP context, user claims, and device details.
    /// </summary>
    public class CustomTelemetryInitializer(IHttpContextAccessor httpContextAccessor) : ITelemetryInitializer
    {
        // Header constants
        private const string CorrelationIdHeader = "X-Correlation-ID";
        private const string ForwardedForHeader = "X-Forwarded-For";
        private const string UserAgentHeader = "User-Agent";

        // Claim type constants
        private const string SubClaimType = "sub";
        private const string NameIdentifierClaimType = "http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier";
        private const string EmailsClaimType = "emails";
        private const string NameClaimType = "name";

        // Property name constants
        private const string UserEmailProperty = "UserEmail";
        private const string UserNameProperty = "UserName";
        private const string DeviceFamilyProperty = "DeviceFamily";
        private const string DeviceBrandProperty = "DeviceBrand";
        private const string DeviceModelProperty = "DeviceModel";
        private const string BrowserVersionProperty = "BrowserVersion";
        private const string OSVersionProperty = "OSVersion";
        private const string ClientIPProperty = "ClientIP";
        private const string HostProperty = "Host";
        private const string ProtocolProperty = "Protocol";
        private const string MethodProperty = "Method";
        private const string PathProperty = "Path";
        private const string QueryStringProperty = "QueryString";

        private readonly IHttpContextAccessor _httpContextAccessor = httpContextAccessor ??
            throw new ArgumentNullException(nameof(httpContextAccessor));
        private readonly Parser _uaParser = Parser.GetDefault();

        /// <summary>
        /// Initializes the specified telemetry with additional information.
        /// </summary>
        /// <param name="telemetry">The telemetry to initialize.</param>
        public void Initialize(ITelemetry telemetry)
        {
            if (telemetry == null) return;

            var httpContext = _httpContextAccessor.HttpContext;
            if (httpContext == null) return;

            AddCorrelationId(telemetry, httpContext);
            AddUserDetails(telemetry, httpContext.User);
            AddDeviceDetails(telemetry, httpContext);
            AddRequestDetails(telemetry, httpContext);
        }

        /// <summary>
        /// Adds correlation ID from request headers to telemetry.
        /// </summary>
        private static void AddCorrelationId(ITelemetry telemetry, HttpContext httpContext)
        {
            var correlationId = httpContext.Request.Headers[CorrelationIdHeader].ToString();
            if (!string.IsNullOrEmpty(correlationId))
            {
                telemetry.Context.Operation.Id = correlationId;
            }
        }

        /// <summary>
        /// Adds user details from claims to telemetry.
        /// </summary>
        private static void AddUserDetails(ITelemetry telemetry, ClaimsPrincipal? user)
        {
            if (user?.Identity?.IsAuthenticated != true) return;

            // Set user ID from claims
            telemetry.Context.User.Id = GetClaimValue(user, SubClaimType) ??
                GetClaimValue(user, NameIdentifierClaimType);

            // Set authenticated user ID from email claim
            var email = GetClaimValue(user, EmailsClaimType);
            telemetry.Context.User.AuthenticatedUserId = email;

            // Add additional user properties if supported
            if (telemetry is ISupportProperties userTelemetryWithProperties)
            {
                userTelemetryWithProperties.Properties[UserEmailProperty] = email ?? string.Empty;
                userTelemetryWithProperties.Properties[UserNameProperty] = GetClaimValue(user, NameClaimType) ?? string.Empty;
            }
        }

        /// <summary>
        /// Adds device details from user agent to telemetry.
        /// </summary>
        private void AddDeviceDetails(ITelemetry telemetry, HttpContext httpContext)
        {
            var userAgent = httpContext.Request.Headers[UserAgentHeader].ToString();
            if (string.IsNullOrEmpty(userAgent)) return;

            try
            {
                var clientInfo = _uaParser.Parse(userAgent);

                // Set OS information
                telemetry.Context.Device.OperatingSystem = $"{clientInfo.OS.Family} {clientInfo.OS.Major}";

                // Add detailed device properties if supported
                if (telemetry is ISupportProperties deviceTelemetryWithProperties)
                {
                    deviceTelemetryWithProperties.Properties[DeviceFamilyProperty] = clientInfo.Device.Family;
                    deviceTelemetryWithProperties.Properties[DeviceBrandProperty] = clientInfo.Device.Brand ?? string.Empty;
                    deviceTelemetryWithProperties.Properties[DeviceModelProperty] = clientInfo.Device.Model ?? string.Empty;
                    deviceTelemetryWithProperties.Properties[BrowserVersionProperty] = $"{clientInfo.UA.Major}.{clientInfo.UA.Minor}";
                    deviceTelemetryWithProperties.Properties[OSVersionProperty] = $"{clientInfo.OS.Major}.{clientInfo.OS.Minor}";
                }
            }
            catch
            {
                // Silently continue if user agent parsing fails
            }
        }

        /// <summary>
        /// Adds HTTP request details to telemetry.
        /// </summary>
        private static void AddRequestDetails(ITelemetry telemetry, HttpContext httpContext)
        {
            if (telemetry is not ISupportProperties telemetryWithProperties) return;

            telemetryWithProperties.Properties[ClientIPProperty] = GetClientIpAddress(httpContext);
            telemetryWithProperties.Properties[HostProperty] = httpContext.Request.Host.ToString();
            telemetryWithProperties.Properties[ProtocolProperty] = httpContext.Request.Protocol;
            telemetryWithProperties.Properties[MethodProperty] = httpContext.Request.Method;
            telemetryWithProperties.Properties[PathProperty] = httpContext.Request.Path.ToString();
            telemetryWithProperties.Properties[QueryStringProperty] = httpContext.Request.QueryString.ToString();
        }

        /// <summary>
        /// Gets the value of a claim from the user's claims.
        /// </summary>
        /// <param name="user">The claims principal.</param>
        /// <param name="claimType">The claim type.</param>
        /// <returns>The claim value or null if not found.</returns>
        private static string? GetClaimValue(ClaimsPrincipal user, string claimType)
        {
            return user.Claims.FirstOrDefault(c => c.Type == claimType)?.Value;
        }

        /// <summary>
        /// Gets the client IP address from the HTTP context.
        /// </summary>
        /// <param name="context">The HTTP context.</param>
        /// <returns>The client IP address.</returns>
        private static string GetClientIpAddress(HttpContext context)
        {
            var forwardedFor = context.Request.Headers[ForwardedForHeader].ToString();
            if (!string.IsNullOrEmpty(forwardedFor))
            {
                return forwardedFor.Split(',')[0].Trim();
            }

            return context.Connection.RemoteIpAddress?.ToString() ?? "unknown";
        }
    }
}