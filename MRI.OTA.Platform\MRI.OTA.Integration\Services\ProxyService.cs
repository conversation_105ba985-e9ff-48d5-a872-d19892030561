using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MRI.OTA.Integration.Http;
using MRI.OTA.Integration.Models;
using System;
using System.Net.Http.Headers;
using System.Net.Http;
using System.Text.Json;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.Integration.Services
{
    public class ProxyService : IProxyService
    {
        private readonly IHttpClientFactoryManager _httpClientFactoryManager;
        private readonly ILogger<ProxyService> _logger;

        public ProxyService(IHttpClientFactoryManager httpClientFactoryManager, ILogger<ProxyService> logger)
        {
            _httpClientFactoryManager = httpClientFactoryManager;
            _logger = logger;
        }

        public async Task<IActionResult> ForwardRequestAsync(ProxyRequestModel request, string authToken, string? tokenName = null)
        {
            if (string.IsNullOrEmpty(request.FullUrl))
            {
                return new BadRequestObjectResult("Invalid request: FullUrl is required");
            }

            if (!Uri.TryCreate(request.FullUrl, UriKind.Absolute, out var uri))
            {
                return new BadRequestObjectResult("Invalid full URL");
            }

            _logger.LogInformation("Request ID: {RequestId} - Fetching HttpClient for domain: {Domain}", request.RequestId, uri.Host);
            var httpClient = _httpClientFactoryManager.GetHttpClient();
            httpClient.BaseAddress = uri;
            var token = authToken.Replace(string.IsNullOrEmpty(tokenName) ? "Bearer " : tokenName, "");

            var requestMessage = new HttpRequestMessage(new HttpMethod(request.Method), request.FullUrl);
            if (tokenName == "AccessKey") { httpClient.DefaultRequestHeaders.Add("AccessKey", token); }
            else
            {
                requestMessage = new HttpRequestMessage(new HttpMethod(request.Method), request.FullUrl)
                {
                    Headers = { Authorization = new AuthenticationHeaderValue(string.IsNullOrEmpty(tokenName) ? "Bearer" : tokenName, token) }
                };
            }

            // Add custom headers if provided
            if (request.Headers != null)
            {
                foreach (var header in request.Headers)
                {
                    requestMessage.Headers.TryAddWithoutValidation(header.Key, header.Value);
                }
            }

            if (request.Body != null)
            {
                requestMessage.Content = new StringContent(JsonSerializer.Serialize(request.Body), Encoding.UTF8, "application/json");
            }

            try
            {
                _logger.LogInformation("Request ID: {RequestId} - Sending request to {Url} with method {Method}", request.RequestId, request.FullUrl, request.Method);
                
                // Log request details for debugging
                if (request.Body != null)
                {
                    _logger.LogDebug("Request ID: {RequestId} - Request body: {Body}", request.RequestId, JsonSerializer.Serialize(request.Body));
                }
                
                var response = await httpClient.SendAsync(requestMessage);
                
                // Don't throw exception, capture the response regardless of status code
                var responseBody = await response.Content.ReadAsStringAsync();
                _logger.LogInformation("Request ID: {RequestId} - Received response with status code {StatusCode} from {Url}", 
                    request.RequestId, (int)response.StatusCode, request.FullUrl);
                _logger.LogDebug("Request ID: {RequestId} - Response body: {Body}", request.RequestId, responseBody);

                // Try to parse JSON if possible
                if (string.IsNullOrWhiteSpace(responseBody))
                {
                    _logger.LogWarning("Request ID: {RequestId} - Response body is empty for {Url}", request.RequestId, request.FullUrl);
                    return new ObjectResult(new { statusCode = (int)response.StatusCode, content = "No content" })
                    {
                        StatusCode = (int)response.StatusCode
                    };
                }
                try {
                    var json = JsonDocument.Parse(responseBody);
                    return new ObjectResult(json) { StatusCode = (int)response.StatusCode };
                }
                catch (JsonException) {
                    // If JSON parsing fails, return raw response
                    return new ObjectResult(new { statusCode = (int)response.StatusCode, content = responseBody }) 
                        { StatusCode = (int)response.StatusCode };
                }
            }
            catch (HttpRequestException ex)
            {
                _logger.LogError("Request ID: {RequestId} - Failed to fetch data from {Url}: {Error}", request.RequestId, request.FullUrl, ex.Message);
                return new ObjectResult(new { error = "Failed to fetch mini-app data", details = ex.Message }) { StatusCode = 500 };
            }
        }
    }
}

