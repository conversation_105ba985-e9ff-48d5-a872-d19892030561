﻿using System.Net.Mime;
using System.Security;
using Asp.Versioning;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.Invitation.v1
{
    //[AllowAnonymous]
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/invitations")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class InvitationController : ControllerBase
    {
        private readonly IInvitationService _invitationService;
        private readonly TelemetryClient _telemetryClient;

        /// <summary>
        /// Constructor for invitation controller
        /// </summary>
        /// <param name="invitationService"></param>
        /// <param name="telemetryClient"></param>
        public InvitationController(IInvitationService invitationService, TelemetryClient telemetryClient)
        {
            _invitationService = invitationService;
            _telemetryClient = telemetryClient;
        }

        /// <summary>
        /// Invite user
        /// </summary>
        /// <param name="invitationRequest"></param>
        /// <returns></returns>
        [HttpPost("invite-user")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "Invitation sent successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> SendInvitation(InvitationRequestModel invitationRequest)
        {
            // Create operation ID for tracking this request in AppInsights
            string operationId = Guid.NewGuid().ToString();
            
            // Create a new RequestTelemetry instead of using StartOperation
            var requestTelemetry = new RequestTelemetry
            {
                Name = "SendInvitation",
                Id = operationId
            };
            
            try
            {
                // Add custom properties to the telemetry
                _telemetryClient.TrackTrace("Starting SendInvitation process", SeverityLevel.Information,
                    new Dictionary<string, string>
                    {
                        { "InvitationEmail", invitationRequest.UserEmail ?? "not provided" },
                        { "OperationId", operationId }
                    });
                
                var result = await _invitationService.SendInvitationAsync(invitationRequest);
                
                if (result)
                {
                    _telemetryClient.TrackTrace("SendInvitation completed successfully", SeverityLevel.Information,
                        new Dictionary<string, string> { { "OperationId", operationId } });
                    
                    // Track successful request
                    requestTelemetry.Success = true;
                    requestTelemetry.ResponseCode = StatusCodes.Status200OK.ToString();
                    _telemetryClient.TrackRequest(requestTelemetry);
                    
                    return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Invitation sent successfully", null!));
                }
                else
                {
                    _telemetryClient.TrackTrace("SendInvitation failed - invitation not sent", SeverityLevel.Warning,
                        new Dictionary<string, string> { { "OperationId", operationId } });
                    
                    // Track failed request
                    requestTelemetry.Success = false;
                    requestTelemetry.ResponseCode = StatusCodes.Status400BadRequest.ToString();
                    _telemetryClient.TrackRequest(requestTelemetry);
                    
                    return BadRequest(new ApiResponse<object>(false, "Invitation not sent", null!, StatusCodes.Status400BadRequest, new List<string> { "The invitation could not be sent." }));
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _telemetryClient.TrackException(ex, new Dictionary<string, string>
                {
                    { "ExceptionType", "UnauthorizedAccessException" },
                    { "OperationId", operationId }
                });
                
                // Track exception request
                requestTelemetry.Success = false;
                requestTelemetry.ResponseCode = StatusCodes.Status401Unauthorized.ToString();
                _telemetryClient.TrackRequest(requestTelemetry);
                
                return Unauthorized(new ApiResponse<object>(false, "Unauthorized access", null!, StatusCodes.Status401Unauthorized, new List<string> { ex.Message }));
            }
            catch (Exception ex)
            {
                _telemetryClient.TrackException(ex, new Dictionary<string, string>
                {
                    { "ExceptionType", ex.GetType().Name },
                    { "OperationId", operationId }
                });
                
                // Track exception request
                requestTelemetry.Success = false;
                requestTelemetry.ResponseCode = StatusCodes.Status500InternalServerError.ToString();
                _telemetryClient.TrackRequest(requestTelemetry);
                
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ApiResponse<object>(false, "Internal Server Error", null!, StatusCodes.Status500InternalServerError, new List<string> { "An error occurred while processing your request." }));
            }
        }

        /// <summary>
        /// Accept invitation
        /// </summary>
        /// <param name="acceptInvitationRequest"></param>
        /// <returns></returns>
        [HttpPost("accept-invitation")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "Invitation accepted successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> AcceptInvitation(AcceptInvitationModel acceptInvitationRequest)
        {
            // Create operation ID for tracking this request in AppInsights
            string operationId = Guid.NewGuid().ToString();
            
            // Create a new RequestTelemetry
            var requestTelemetry = new RequestTelemetry
            {
                Name = "AcceptInvitation",
                Id = operationId
            };
            
            try
            {
                // Add custom properties to the telemetry
                _telemetryClient.TrackTrace("Starting AcceptInvitation process", SeverityLevel.Information,
                    new Dictionary<string, string>
                    {
                        { "InvitationToken", acceptInvitationRequest.UserEmail ?? "not provided" },
                        { "OperationId", operationId }
                    });
                
                var result = await _invitationService.AcceptInvitationAsync(acceptInvitationRequest);
                
                if (result.Item1 > 0)
                {
                    _telemetryClient.TrackTrace("AcceptInvitation completed successfully", SeverityLevel.Information,
                        new Dictionary<string, string> { { "OperationId", operationId } });
                    
                    // Track successful request
                    requestTelemetry.Success = true;
                    requestTelemetry.ResponseCode = StatusCodes.Status200OK.ToString();
                    _telemetryClient.TrackRequest(requestTelemetry);
                    
                    return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Invitation accepted successfully", result.Item2));
                }
                else if (result.Item1 == Constants.InvitationLinkExpires)
                {
                    _telemetryClient.TrackTrace("AcceptInvitation failed - invitation link expired", SeverityLevel.Warning,
                        new Dictionary<string, string> { { "OperationId", operationId } });
                    
                    // Track failed request
                    requestTelemetry.Success = false;
                    requestTelemetry.ResponseCode = StatusCodes.Status400BadRequest.ToString();
                    _telemetryClient.TrackRequest(requestTelemetry);
                    
                    return BadRequest(new ApiResponse<object>(false, "Invitation link expired.", result.Item2, StatusCodes.Status400BadRequest, new List<string> { "Invitation link expired." }));
                }
                else
                {
                    _telemetryClient.TrackTrace("AcceptInvitation failed - invitation not accepted", SeverityLevel.Warning,
                        new Dictionary<string, string> { { "OperationId", operationId } });
                    
                    // Track failed request
                    requestTelemetry.Success = false;
                    requestTelemetry.ResponseCode = StatusCodes.Status400BadRequest.ToString();
                    _telemetryClient.TrackRequest(requestTelemetry);
                    
                    return BadRequest(new ApiResponse<object>(false, "Invitation not accepted", result.Item2, StatusCodes.Status400BadRequest, new List<string> { "The invitation could not be accepted." }));
                }
            }
            catch (UnauthorizedAccessException ex)
            {
                _telemetryClient.TrackException(ex, new Dictionary<string, string>
                {
                    { "ExceptionType", "UnauthorizedAccessException" },
                    { "OperationId", operationId }
                });
                
                // Track exception request
                requestTelemetry.Success = false;
                requestTelemetry.ResponseCode = StatusCodes.Status401Unauthorized.ToString();
                _telemetryClient.TrackRequest(requestTelemetry);
                
                return Unauthorized(new ApiResponse<object>(false, "Unauthorized access", null!, StatusCodes.Status401Unauthorized, new List<string> { ex.Message }));
            }
            catch (Exception ex)
            {
                _telemetryClient.TrackException(ex, new Dictionary<string, string>
                {
                    { "ExceptionType", ex.GetType().Name },
                    { "OperationId", operationId }
                });
                
                // Track exception request
                requestTelemetry.Success = false;
                requestTelemetry.ResponseCode = StatusCodes.Status500InternalServerError.ToString();
                _telemetryClient.TrackRequest(requestTelemetry);
                
                return StatusCode(StatusCodes.Status500InternalServerError, 
                    new ApiResponse<object>(false, "Internal Server Error", null!, StatusCodes.Status500InternalServerError, new List<string> { "An error occurred while processing your request." }));
            }
        }
    }
}
