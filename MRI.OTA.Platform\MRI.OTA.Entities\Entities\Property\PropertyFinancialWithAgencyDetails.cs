using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    /// <summary>
    /// Entity for Property Financial Information with Agency Details
    /// </summary>
    public class PropertyFinancialWithAgencyDetails
    {
        [ExcludeColumn]
        public int PropertyFinancialInformationId { get; set; }
        [ExcludeColumn]
        public int PropertyId { get; set; }
        public string? TenancyName { get; set; }
        public DateTime? LeaseStart { get; set; }
        public DateTime? LeaseEnd { get; set; }
        public DateTime? VacateDate { get; set; }
        public decimal? Rent { get; set; }
        public decimal? IncreaseRent { get; set; }
        public DateTime? IncreaseDate { get; set; }
        public DateTime? OptionsDate { get; set; }
        public string? OptionsDetail { get; set; }
        public decimal? Arrears { get; set; }
        public DateTime? PayToDate { get; set; }
        public decimal? AmountToVacate { get; set; }
        public decimal? OutstandingInvoices { get; set; }
        public decimal? InvoiceFeesArrears { get; set; }
        public decimal? RentCharge { get; set; }
        public decimal? WeeklyRent { get; set; }
        public DateTime? LastPaid { get; set; }
        public decimal? HeldFunds { get; set; }

        // Source IDs
        public string? SRCAgencyId { get; set; }
        public string? SRCManagementId { get; set; }
        public string? SRCPropertyId { get; set; }

        // AgencyDetails fields - AgencyName comes from BusinessRegisteredName
        public string? AgencyName { get; set; } // Mapped from AD.BusinessRegisteredName
        public string? BusinessRegisteredName { get; set; }
        public string? BusinessName { get; set; }

        // Additional Financial Details
        public decimal? OwnershipTotalAvailableBalance { get; set; }
        public decimal? PropertyOutstandingFees { get; set; }
        public decimal? PropertyOutstandingInvoices { get; set; }
        public decimal? PropertyOverdueInvoices { get; set; }
        public decimal? LastPaymentAmount { get; set; }
        public string? Currency { get; set; }

        [ExcludeColumn]
        public string? CurrencySymbol { get; set; }
        
        public DateTime? LastStatementDate { get; set; }
    }
} 