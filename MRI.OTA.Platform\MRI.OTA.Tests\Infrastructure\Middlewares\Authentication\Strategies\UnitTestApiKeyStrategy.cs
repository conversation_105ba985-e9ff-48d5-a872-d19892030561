using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies;
using System.IO;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares.Authentication.Strategies
{
    public class UnitTestApiKeyStrategy
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly Mock<IResponseGenerator> _responseGeneratorMock;
        private readonly ApiKeyStrategy _strategy;

        public UnitTestApiKeyStrategy()
        {
            _nextMock = new Mock<RequestDelegate>();
            _loggerMock = new Mock<ILogger>();
            _responseGeneratorMock = new Mock<IResponseGenerator>();

            _strategy = new ApiKeyStrategy(
                _nextMock.Object,
                _loggerMock.Object,
                _responseGeneratorMock.Object);
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenNextIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ApiKeyStrategy(
                null!,
                _loggerMock.Object,
                _responseGeneratorMock.Object));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenLoggerIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ApiKeyStrategy(
                _nextMock.Object,
                null!,
                _responseGeneratorMock.Object));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenResponseGeneratorIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new ApiKeyStrategy(
                _nextMock.Object,
                _loggerMock.Object,
                null!));
        }

        [Fact]
        public void CanHandle_ReturnsTrue_WhenAccessKeyAndSecretPresent()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessKeyMissing()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessSecret"] = "test-secret";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessSecretMissing()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessKeyEmpty()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = string.Empty;
            context.Request.Headers["AccessSecret"] = "test-secret";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAccessSecretEmpty()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = string.Empty;

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenBothHeadersMissing()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task AuthenticateAsync_CallsNext()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledAndAuthenticated_WhenSuccessful()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";
            context.Response.StatusCode = StatusCodes.Status200OK;

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.True(authenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledButNotAuthenticated_WhenUnauthorized()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.False(authenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_WritesUnauthorizedResponse_WhenStatusIs401()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _responseGeneratorMock.Verify(r => r.WriteUnauthorizedResponse(
                context,
                It.IsAny<string>(),
                StatusCodes.Status401Unauthorized), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_DoesNotWriteResponse_WhenStatusIsNot401()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";
            context.Response.StatusCode = StatusCodes.Status200OK;

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _responseGeneratorMock.Verify(r => r.WriteUnauthorizedResponse(
                It.IsAny<HttpContext>(),
                It.IsAny<string>(),
                It.IsAny<int>()), Times.Never);
        }

        [Fact]
        public async Task AuthenticateAsync_LogsInformation()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _loggerMock.Verify(
                x => x.Log(
                    LogLevel.Information,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Attempting API Key authentication")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Theory]
        [InlineData(StatusCodes.Status200OK, true)]
        [InlineData(StatusCodes.Status201Created, true)]
        [InlineData(StatusCodes.Status204NoContent, true)]
        [InlineData(StatusCodes.Status400BadRequest, true)]
        [InlineData(StatusCodes.Status401Unauthorized, false)]
        [InlineData(StatusCodes.Status403Forbidden, true)]
        [InlineData(StatusCodes.Status404NotFound, true)]
        [InlineData(StatusCodes.Status500InternalServerError, true)]
        public async Task AuthenticateAsync_ReturnsCorrectAuthenticationStatus_BasedOnStatusCode(int statusCode, bool expectedAuthenticated)
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";
            context.Response.StatusCode = statusCode;

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.Equal(expectedAuthenticated, authenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_HandlesNextMiddlewareException()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";

            _nextMock.Setup(n => n(It.IsAny<HttpContext>()))
                .ThrowsAsync(new InvalidOperationException("Next middleware error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _strategy.AuthenticateAsync(context));
        }

        [Fact]
        public async Task AuthenticateAsync_HandlesResponseGeneratorException()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["AccessKey"] = "test-key";
            context.Request.Headers["AccessSecret"] = "test-secret";
            context.Response.StatusCode = StatusCodes.Status401Unauthorized;

            _responseGeneratorMock.Setup(r => r.WriteUnauthorizedResponse(
                It.IsAny<HttpContext>(),
                It.IsAny<string>(),
                It.IsAny<int>()))
                .ThrowsAsync(new InvalidOperationException("Response generator error"));

            // Act & Assert
            await Assert.ThrowsAsync<InvalidOperationException>(() => _strategy.AuthenticateAsync(context));
        }

        [Fact]
        public void CanHandle_IsCaseInsensitive_ForHeaderNames()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["accesskey"] = "test-key";
            context.Request.Headers["accesssecret"] = "test-secret";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            // Note: HTTP headers are case-insensitive by default in ASP.NET Core
            Assert.True(result);
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.Response.Body = new MemoryStream();
            return context;
        }
    }
}
