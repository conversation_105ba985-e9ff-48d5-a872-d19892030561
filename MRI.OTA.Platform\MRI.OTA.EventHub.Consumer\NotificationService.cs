﻿using System.Net.Http.Json;
using Azure.Messaging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Newtonsoft.Json;

namespace MRI.OTA.EventHub.Consumer
{
    public class NotificationService : INotificationService
    {
        private readonly ILogger<NotificationService> _logger;
        private readonly IHttpClientFactory _httpClientFactory;
        private readonly IConfiguration _configuration;
        public NotificationService(ILogger<NotificationService> logger, IHttpClientFactory httpClientFactory, IConfiguration configuration)
        {
            _logger = logger;
            _httpClientFactory = httpClientFactory;
            _configuration = configuration;
        }
        public async Task<bool> SendPushNotificationAsync(CloudEvent cloudEvent)
        {
            try
            {
                var otaBaseUrl = _configuration.GetSection("Application:OtaBaseUrl").Value;
                if (string.IsNullOrEmpty(otaBaseUrl))
                {
                    throw new InvalidOperationException("OtaBaseUrl is not configured.");
                }

                var client = _httpClientFactory.CreateClient("EventConsumer");
                client.BaseAddress = new Uri(otaBaseUrl);

                if (cloudEvent.Data == null)
                {
                    throw new InvalidOperationException("CloudEvent data is null.");
                }

                EventData? eventData = JsonConvert.DeserializeObject<EventData>(cloudEvent.Data.ToString() ?? string.Empty);
                if (eventData == null)
                {
                    throw new InvalidOperationException("Failed to deserialize CloudEvent data.");
                }
                NotificationRequestModel notificationRequestModel = new NotificationRequestModel();
                notificationRequestModel.Title = cloudEvent.Subject ?? string.Empty;
                notificationRequestModel.Body = System.DateTime.Now + cloudEvent.Type;
                notificationRequestModel.UserEmail = eventData.UserId ?? string.Empty;

                var response = await client.PostAsJsonAsync(APIConstants.PushNotificationURL, notificationRequestModel);
                

                NotificationRequestModel notificationRequestBroadcastModel = new NotificationRequestModel();
                notificationRequestBroadcastModel.Title = "BroadCast " + cloudEvent.Subject ?? string.Empty;
                notificationRequestBroadcastModel.Body = System.DateTime.Now + " BroadCast " + cloudEvent.Type;
                notificationRequestBroadcastModel.UserEmail = eventData.UserId ?? string.Empty;
                var responseAll = await client.PostAsJsonAsync(APIConstants.BroadcastNotificationURL, notificationRequestBroadcastModel);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Push notification sent successfully.");
                    return true;
                }
                else
                {
                    _logger.LogError($"Failed to send push notification. Status Code: {responseAll.StatusCode}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurred while sending push notification: {ex.Message}");
                return false;
            }
        }
    }
}
