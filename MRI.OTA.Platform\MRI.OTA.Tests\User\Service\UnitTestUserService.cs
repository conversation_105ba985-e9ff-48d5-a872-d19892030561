﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Application.Models;
using MRI.OTA.Application.Services;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using System.Net;
using System.Reflection;
using System.Security.Claims;

namespace MRI.OTA.Tests.Users.Services
{
    public class UnitTestUserService
    {
        private readonly Mock<IUserRepository> _userRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger<UserService>> _loggerMock;
        private readonly Mock<IJwtTokenValidator> _jwtTokenValidatorMock;
        private readonly Mock<IConfiguration> _connfigurationMock;
        private readonly Mock<IAzureB2CTokenService> _azureB2cTokenMock;
        private readonly UserService _userService;
        private readonly Mock<IImageStorageService> _imageStorageServiceMock;
        private readonly Mock<IPropertTreeService> _propertyTreeServiceMock;
        private readonly TaskContext _taskContext;

        public UnitTestUserService()
        {
            _userRepositoryMock = new Mock<IUserRepository>();
            _mapperMock = new Mock<IMapper>();
            _loggerMock = new Mock<ILogger<UserService>>();
            _jwtTokenValidatorMock = new Mock<IJwtTokenValidator>();
            _azureB2cTokenMock = new Mock<IAzureB2CTokenService>();
            _connfigurationMock = new Mock<IConfiguration>();
            _imageStorageServiceMock = new Mock<IImageStorageService>();
            _propertyTreeServiceMock = new Mock<IPropertTreeService>();
            _taskContext = new TaskContext
            {
                UserId = 1,
                Email = "<EMAIL>",
                AccessToken = "test-token",
                AccessKey = "test-key"
            };
            _userService = new UserService(_loggerMock.Object, _userRepositoryMock.Object, _mapperMock.Object, _azureB2cTokenMock.Object, _jwtTokenValidatorMock.Object, _imageStorageServiceMock.Object, _propertyTreeServiceMock.Object, _taskContext);
        }

        #region GetAllUsers Tests

        [Fact]
        public async Task GetAllUsers_ShouldReturnListOfUserModels()
        {
            // Arrange
            var users = new List<MRI.OTA.Core.Entities.Users>
            {
                new MRI.OTA.Core.Entities.Users { UserId = 1, UserEmail = "<EMAIL>", DisplayName = "Test User 1", ProviderTypeId = 1, ProviderId = "1", IsActive = true },
                new MRI.OTA.Core.Entities.Users { UserId = 2, UserEmail = "<EMAIL>", DisplayName = "Test User 2", ProviderTypeId = 2, ProviderId = "2", IsActive = true }
            };
            var userModels = new List<UserModel>
            {
                new UserModel { UserId = 1, UserEmail = "<EMAIL>", DisplayName = "Test User 1", ProviderTypeId = 1, ProviderId = "1", IsActive = true },
                new UserModel { UserId = 2, UserEmail = "<EMAIL>", DisplayName = "Test User 2", ProviderTypeId = 2, ProviderId = "2", IsActive = true }
            };

            _userRepositoryMock.Setup(repo => repo.GetAllUsers()).ReturnsAsync(users);
            _mapperMock.Setup(mapper => mapper.Map<List<UserModel>>(users)).Returns(userModels);

            // Act
            var result = await _userService.GetAllUsers();

            // Assert
            Assert.NotNull(result);
            Assert.Equal(2, result.Count);
            Assert.Equal("<EMAIL>", result[0].UserEmail);
            Assert.Equal("<EMAIL>", result[1].UserEmail);
            _userRepositoryMock.Verify(repo => repo.GetAllUsers(), Times.Once);
            _mapperMock.Verify(mapper => mapper.Map<List<UserModel>>(users), Times.Once);
        }

        [Fact]
        public async Task GetAllUsers_ShouldReturnEmptyList_WhenNoUsersExist()
        {
            // Arrange
            var users = new List<MRI.OTA.Core.Entities.Users>();
            var userModels = new List<UserModel>();

            _userRepositoryMock.Setup(repo => repo.GetAllUsers()).ReturnsAsync(users);
            _mapperMock.Setup(mapper => mapper.Map<List<UserModel>>(users)).Returns(userModels);

            // Act
            var result = await _userService.GetAllUsers();

            // Assert
            Assert.NotNull(result);
            Assert.Empty(result);
            _userRepositoryMock.Verify(repo => repo.GetAllUsers(), Times.Once);
            _mapperMock.Verify(mapper => mapper.Map<List<UserModel>>(users), Times.Once);
        }

        #endregion

        #region CreateUser Tests

        [Fact]
        public async Task CreateUser_ShouldReturnSuccess_WhenUserIsCreated()
        {
            // Arrange
            var addUserModel = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Test User", ProviderTypeId = 1, ProviderId = "1", IsActive = true };
            var user = new MRI.OTA.Core.Entities.Users { UserId = 1, UserEmail = "<EMAIL>", DisplayName = "Test User", ProviderTypeId = 1, ProviderId = "1", IsActive = true };

            _mapperMock.Setup(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(addUserModel)).Returns(user);
            _userRepositoryMock.Setup(repo => repo.AddAsync(user)).ReturnsAsync(1);

            // Act
            var result = await _userService.CreateUser(addUserModel);

            // Assert
            Assert.Equal(Constants.Success, result);
            _mapperMock.Verify(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(addUserModel), Times.Once);
            _userRepositoryMock.Verify(repo => repo.AddAsync(user), Times.Once);
        }

        [Fact]
        public async Task CreateUser_ShouldReturnError_WhenUserIsNotCreated()
        {
            // Arrange
            var addUserModel = new AddUserModel { UserEmail = "<EMAIL>", DisplayName = "Test User", ProviderTypeId = 1, ProviderId = "1", IsActive = true };
            var user = new MRI.OTA.Core.Entities.Users { UserId = 1, UserEmail = "<EMAIL>", DisplayName = "Test User", ProviderTypeId = 1, ProviderId = "1", IsActive = true };

            _mapperMock.Setup(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(addUserModel)).Returns(user);
            _userRepositoryMock.Setup(repo => repo.AddAsync(user)).ReturnsAsync(0);

            // Act
            var result = await _userService.CreateUser(addUserModel);

            // Assert
            Assert.Equal(Constants.Error, result);
            _mapperMock.Verify(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(addUserModel), Times.Once);
            _userRepositoryMock.Verify(repo => repo.AddAsync(user), Times.Once);
        }

        [Fact]
        public async Task CreateUser_ShouldReturnError_WhenUserModelIsNull()
        {
            // Arrange
            AddUserModel addUserModel = null;

            // Act
            var result = await _userService.CreateUser(addUserModel);

            // Assert
            Assert.Equal(Constants.Error, result);
        }

        #endregion

        #region CreateUserProfile Tests

        [Fact]
        public async Task CreateUserProfile_ShouldCreateNewUser_WhenUserDoesNotExist()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "valid-token" };
            var claims = new List<Claim>
            {
                new Claim("sub", "123"),
                new Claim("idp", "google.com"),
                new Claim("given_name", "Test User"),
                new Claim("emails", "<EMAIL>"),
                new Claim("idp_access_token", "access-token")
            };

            var userProfile = new ViewUserProfileModel
            {
                ProviderId = "123",
                ProviderTypeId = 2, // Google
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IdpAccessToken = "access-token"
            };

            var userEntity = new MRI.OTA.Core.Entities.Users
            {
                ProviderId = "123",
                ProviderTypeId = 2,
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IsActive = true
            };

            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ReturnsAsync(claims);
            _userRepositoryMock.Setup(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>())).ReturnsAsync((ViewUserProfileModel)null);
            _mapperMock.Setup(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(It.IsAny<ViewUserProfileModel>())).Returns(userEntity);
            _userRepositoryMock.Setup(repo => repo.AddAsync(userEntity)).ReturnsAsync(1);

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.UserId);
            Assert.Equal("<EMAIL>", result.UserEmail);
            Assert.Equal("Google", result.ProviderName);
            Assert.True(result.IsActive);
            _jwtTokenValidatorMock.Verify(validator => validator.GetClaims(idTokenModel.IdToken), Times.Once);
            _userRepositoryMock.Verify(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>()), Times.Once);
            _mapperMock.Verify(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(It.IsAny<ViewUserProfileModel>()), Times.Once);
            _userRepositoryMock.Verify(repo => repo.AddAsync(userEntity), Times.Once);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldUpdateExistingUser_WhenEmailChanged()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "valid-token" };
            var claims = new List<Claim>
            {
                new Claim("sub", "123"),
                new Claim("idp", "google.com"),
                new Claim("given_name", "Test User"),
                new Claim("emails", "<EMAIL>"),
                new Claim("idp_access_token", "access-token")
            };

            var userProfile = new ViewUserProfileModel
            {
                ProviderId = "123",
                ProviderTypeId = 2, // Google
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IdpAccessToken = "access-token"
            };

            var existingUserProfile = new ViewUserProfileModel
            {
                UserId = 1,
                ProviderId = "123",
                ProviderTypeId = 2,
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IsActive = true
            };

            var userEntity = new MRI.OTA.Core.Entities.Users
            {
                UserId = 1,
                ProviderId = "123",
                ProviderTypeId = 2,
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IsActive = true
            };

            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ReturnsAsync(claims);
            _userRepositoryMock.Setup(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>())).ReturnsAsync(existingUserProfile);
            _mapperMock.Setup(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(It.IsAny<ViewUserProfileModel>())).Returns(userEntity);
            _userRepositoryMock.Setup(repo => repo.UpdateUserEmail(userEntity)).ReturnsAsync(1);

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.UserId);
            Assert.Equal("<EMAIL>", result.UserEmail);
            _jwtTokenValidatorMock.Verify(validator => validator.GetClaims(idTokenModel.IdToken), Times.Once);
            _userRepositoryMock.Verify(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>()), Times.Once);
            _mapperMock.Verify(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(It.IsAny<ViewUserProfileModel>()), Times.Once);
            _userRepositoryMock.Verify(repo => repo.UpdateUserEmail(userEntity), Times.Once);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldUpdateExistingUser_WhenProviderTypeChanged()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "valid-token" };
            var claims = new List<Claim>
            {
                new Claim("sub", "123"),
                new Claim("idp", "facebook.com"),
                new Claim("given_name", "Test User"),
                new Claim("emails", "<EMAIL>"),
                new Claim("idp_access_token", "access-token")
            };

            var userProfile = new ViewUserProfileModel
            {
                ProviderId = "123",
                ProviderTypeId = 3, // Facebook
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IdpAccessToken = "access-token"
            };

            var existingUserProfile = new ViewUserProfileModel
            {
                UserId = 1,
                ProviderId = "123",
                ProviderTypeId = 2, // Google
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IsActive = true
            };

            var userEntity = new MRI.OTA.Core.Entities.Users
            {
                UserId = 1,
                ProviderId = "123",
                ProviderTypeId = 3,
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IsActive = true
            };

            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ReturnsAsync(claims);
            _userRepositoryMock.Setup(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>())).ReturnsAsync(existingUserProfile);
            _mapperMock.Setup(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(It.IsAny<ViewUserProfileModel>())).Returns(userEntity);
            _userRepositoryMock.Setup(repo => repo.UpdateUserProviderType(userEntity)).ReturnsAsync(1);

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.UserId);
            Assert.Equal(3, result.ProviderTypeId);
            _jwtTokenValidatorMock.Verify(validator => validator.GetClaims(idTokenModel.IdToken), Times.Once);
            _userRepositoryMock.Verify(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>()), Times.Once);
            _mapperMock.Verify(mapper => mapper.Map<MRI.OTA.Core.Entities.Users>(It.IsAny<ViewUserProfileModel>()), Times.Once);
            _userRepositoryMock.Verify(repo => repo.UpdateUserProviderType(userEntity), Times.Once);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldReturnExistingUser_WhenNoChangesNeeded()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "valid-token" };
            var claims = new List<Claim>
            {
                new Claim("sub", "123"),
                new Claim("idp", "google.com"),
                new Claim("given_name", "Test User"),
                new Claim("emails", "<EMAIL>"),
                new Claim("idp_access_token", "access-token")
            };

            var existingUserProfile = new ViewUserProfileModel
            {
                UserId = 1,
                ProviderId = "123",
                ProviderTypeId = 2, // Google
                DisplayName = "Test User",
                UserEmail = "<EMAIL>",
                IsActive = true
            };

            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ReturnsAsync(claims);
            _userRepositoryMock.Setup(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>())).ReturnsAsync(existingUserProfile);

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(1, result.UserId);
            Assert.Equal("<EMAIL>", result.UserEmail);
            Assert.Equal("access-token", result.IdpAccessToken);
            _jwtTokenValidatorMock.Verify(validator => validator.GetClaims(idTokenModel.IdToken), Times.Once);
            _userRepositoryMock.Verify(repo => repo.GetUserDetails(It.IsAny<ViewUserProfileModel>()), Times.Once);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldReturnEmptyUser_WhenNoClaims()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "invalid-token" };
            var claims = new List<Claim>();

            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ReturnsAsync(claims);

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(0, result.UserId);
            Assert.Null(result.UserEmail);
            _jwtTokenValidatorMock.Verify(validator => validator.GetClaims(idTokenModel.IdToken), Times.Once);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldReturnUser_WhenEmailNotFound()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "token-without-email" };
            var claims = new List<Claim>
            {
                new Claim("sub", "123"),
                new Claim("idp", "google.com"),
                new Claim("given_name", "Test User")
                // No email claim
            };

            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ReturnsAsync(claims);

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(0, result.UserId);
            Assert.Null(result.UserEmail);
            Assert.Equal("123", result.ProviderId);
            _jwtTokenValidatorMock.Verify(validator => validator.GetClaims(idTokenModel.IdToken), Times.Once);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldReturnNull_WhenIdTokenIsNull()
        {
            // Arrange
            IdTokenModel idTokenModel = null;

            //Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldReturnNull_WhenIdTokenValueIsEmpty()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "" };

            //Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldReturnNull_WhenExceptionOccurs()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "valid-token" };
            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task CreateUserProfile_ShouldReturnNull_WhenProviderIdIsNull()
        {
            // Arrange
            var idTokenModel = new IdTokenModel { IdToken = "valid-token" };
            var claims = new List<Claim>
            {
                // No "sub" claim, so ProviderId will be null
                new Claim("idp", "google.com"),
                new Claim("given_name", "Test User"),
                new Claim("emails", "<EMAIL>"),
                new Claim("idp_access_token", "access-token")
            };

            _jwtTokenValidatorMock.Setup(validator => validator.GetClaims(idTokenModel.IdToken)).ReturnsAsync(claims);

            // Act
            var result = await _userService.CreateUserProfile(idTokenModel);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task UpdateUserTermsAndCondition_ShouldReturnResult_WhenRepositorySucceeds()
        {
            // Arrange
            var model = new TermsConditionModel { UserId = 1, TermsAndConditions = true };
            var userEntity = new Core.Entities.Users { UserId = 1, TermsAndConditions = true };
            _mapperMock.Setup(m => m.Map<Core.Entities.Users>(model)).Returns(userEntity);
            _userRepositoryMock.Setup(r => r.UpdateUserTermsAndCondition(userEntity)).ReturnsAsync(1);

            // Act
            var result = await _userService.UpdateUserTermsAndCondition(model);

            // Assert
            Assert.Equal(1, result);
            _mapperMock.Verify(m => m.Map<Core.Entities.Users>(model), Times.Once);
            _userRepositoryMock.Verify(r => r.UpdateUserTermsAndCondition(userEntity), Times.Once);
        }

        [Fact]
        public async Task UpdateUserTermsAndCondition_ShouldReturnMinusOne_AndLogError_WhenExceptionThrown()
        {
            // Arrange
            var model = new TermsConditionModel { UserId = 1, TermsAndConditions = true };
            _mapperMock.Setup(m => m.Map<Core.Entities.Users>(model)).Throws(new Exception("Mapping failed"));

            // Act
            var result = await _userService.UpdateUserTermsAndCondition(model);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task UpdateUserProfileSettings_ShouldReturnResult_WhenRepositorySucceeds()
        {
            // Arrange
            var model = new UserProfileSettingsModel { UserId = 1 };
            var userEntity = new Core.Entities.Users { UserId = 1 };
            _mapperMock.Setup(m => m.Map<Core.Entities.Users>(model)).Returns(userEntity);
            _userRepositoryMock.Setup(r => r.UpdateUserProfileSettings(userEntity)).ReturnsAsync(1);

            // Act
            var result = await _userService.UpdateUserProfileSettings(model);

            // Assert
            Assert.Equal(1, result);
            _mapperMock.Verify(m => m.Map<Core.Entities.Users>(model), Times.Once);
            _userRepositoryMock.Verify(r => r.UpdateUserProfileSettings(userEntity), Times.Once);
        }

        [Fact]
        public async Task UpdateUserProfileSettings_ShouldReturnMinusOne_AndLogError_WhenExceptionThrown()
        {
            // Arrange
            var model = new UserProfileSettingsModel { UserId = 1 };
            _mapperMock.Setup(m => m.Map<Core.Entities.Users>(model)).Throws(new Exception("Mapping failed"));

            // Act
            var result = await _userService.UpdateUserProfileSettings(model);

            // AssertAssert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task DeleteUserFromB2C_ShouldThrowException_WhenResponseIsNotSuccess()
        {
            // Arrange
            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.BadRequest,
                    Content = new StringContent("error details")
                });

            var httpClient = new HttpClient(handlerMock.Object);
            var tokenServiceMock = new Mock<IAzureB2CTokenService>();
            tokenServiceMock.Setup(s => s.GetAccessTokenAsync()).ReturnsAsync("fake-token");

            var userService = new UserService(
                Mock.Of<ILogger<UserService>>(),
                Mock.Of<IUserRepository>(),
                Mock.Of<AutoMapper.IMapper>(),
                tokenServiceMock.Object,
                Mock.Of<IJwtTokenValidator>(),
                Mock.Of<IImageStorageService>(),
                Mock.Of<IPropertTreeService>(),
                new TaskContext()
            );

            // Use reflection to inject the custom HttpClient if needed (not shown here, as the method uses new HttpClient())

            // Act & Assert
            var method = userService.GetType()
                .GetMethod("DeleteUserFromB2C", BindingFlags.NonPublic | BindingFlags.Instance);

            var ex = await Assert.ThrowsAsync<Exception>(async () =>
            {
                var task = (Task)method.Invoke(userService, new object[] { "userId123" });
                await task;
            });
            Assert.Contains("Failed to delete user", ex.Message);
        }

        [Fact]
        public async Task AddUpdateUserDevice_ShouldReturnResult_WhenRepositorySucceeds()
        {
            // Arrange
            var deviceModel = new UserDeviceDetail { DeviceId = "dev1", DeviceToken = "token", DeviceType = 1, UserId = 1 };
            _userRepositoryMock.Setup(r => r.AddUpdateUserDevice(deviceModel, _taskContext.UserId)).ReturnsAsync(1);

            // Act
            var result = await _userService.AddUpdateUserDevice(deviceModel);

            // Assert
            Assert.Equal(1, result);
            _userRepositoryMock.Verify(r => r.AddUpdateUserDevice(deviceModel, _taskContext.UserId), Times.Once);
        }

        [Fact]
        public async Task AddUpdateUserDevice_ShouldReturnMinusOne_AndLogError_WhenExceptionThrown()
        {
            // Arrange
            var deviceModel = new UserDeviceDetail { DeviceId = "dev1", DeviceToken = "token", DeviceType = 1, UserId = 1 };
            _userRepositoryMock.Setup(r => r.AddUpdateUserDevice(deviceModel, _taskContext.UserId)).Throws(new Exception("DB error"));

            // Act
            var result = await _userService.AddUpdateUserDevice(deviceModel);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task DeleteUserDeviceInfo_ShouldReturnResult_WhenRepositorySucceeds()
        {
            // Arrange
            var deviceId = "dev1";
            _userRepositoryMock.Setup(r => r.DeleteUserDeviceInfo(_taskContext.UserId, deviceId)).ReturnsAsync(true);

            // Act
            var result = await _userService.DeleteUserDeviceInfo(deviceId);

            // Assert
            Assert.True(result);
            _userRepositoryMock.Verify(r => r.DeleteUserDeviceInfo(_taskContext.UserId, deviceId), Times.Once);
        }

        [Fact]
        public async Task DeleteUserDeviceInfo_ShouldReturnFalse_AndLogError_WhenExceptionThrown()
        {
            // Arrange
            var deviceId = "dev1";
            _userRepositoryMock.Setup(r => r.DeleteUserDeviceInfo(_taskContext.UserId, deviceId)).Throws(new Exception("DB error"));

            // Act
            var result = await _userService.DeleteUserDeviceInfo(deviceId);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task DeleteAccount_ShouldReturnTrue_WhenAllStepsSucceed()
        {
            // Arrange
            int userId = 1;
            string providerId = "provider123";
            _propertyTreeServiceMock.Setup(s => s.RemoveUserAccount(userId, providerId)).Returns(Task.FromResult(true));
            _imageStorageServiceMock.Setup(s => s.DeletePropertyImagesByUserId(userId)).ReturnsAsync(1);
            _userRepositoryMock.Setup(r => r.DeleteAccount(userId, providerId)).ReturnsAsync(true);

            // Act
            var result = await _userService.DeleteAccount(userId, providerId);

            // Assert
            Assert.True(result);
            _propertyTreeServiceMock.Verify(s => s.RemoveUserAccount(userId, providerId), Times.Once);
            _imageStorageServiceMock.Verify(s => s.DeletePropertyImagesByUserId(userId), Times.Once);
            _userRepositoryMock.Verify(r => r.DeleteAccount(userId, providerId), Times.Once);
        }

        [Fact]
        public async Task DeleteAccount_ShouldLogErrorAndContinue_WhenProviderIdIsNullOrEmpty()
        {
            // Arrange
            int userId = 1;
            string providerId = null;
            _propertyTreeServiceMock.Setup(s => s.RemoveUserAccount(userId, providerId)).Returns(Task.FromResult(true)); _imageStorageServiceMock.Setup(s => s.DeletePropertyImagesByUserId(userId)).ReturnsAsync(1);
            _userRepositoryMock.Setup(r => r.DeleteAccount(userId, providerId)).ReturnsAsync(true);

            // Act
            var result = await _userService.DeleteAccount(userId, providerId);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public async Task DeleteAccount_ShouldReturnFalse_WhenExceptionIsThrown()
        {
            // Arrange
            int userId = 1;
            string providerId = "provider123";
            _propertyTreeServiceMock.Setup(s => s.RemoveUserAccount(userId, providerId)).Throws(new Exception("Service error"));

            // Act
            var result = await _userService.DeleteAccount(userId, providerId);

            // Assert
            Assert.False(result);
        }
        #endregion
    }
}
