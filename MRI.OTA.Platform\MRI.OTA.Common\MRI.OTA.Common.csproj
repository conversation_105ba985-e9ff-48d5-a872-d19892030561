﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

	<ItemGroup>
		<!-- Core ASP.NET and Dependency Injection -->
		<PackageReference Include="AutoMapper" />
		<PackageReference Include="Dapper" />
		<PackageReference Include="Microsoft.Data.SqlClient" />
		<PackageReference Include="Serilog" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" />
		<PackageReference Include="Serilog.Extensions.Hosting" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" />
	</ItemGroup>
</Project>
