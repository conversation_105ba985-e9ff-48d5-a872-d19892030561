﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class DocumentDetail
    {
        [ExcludeColumn]
        public string DocumentDetailId { get; set; }
        public string SRCManagementId { get; set; }
        public string SRCTenancyId { get; set; }
        public string SRCDocumentId { get; set; }
        public string DocumentName { get; set; }
        public string DocumentLink { get; set; }
        public string DocumentType { get; set; }
        public string MetaType { get; set; }
        public string MetaNumber { get; set; }
        public DateTime? MetaDate { get; set; }
        public string MetaStatus { get; set; }
        public decimal? MetaAmount { get; set; }
        public decimal? MetaOwing { get; set; }
        public string? MetaCurrency { get; set; }
        public string? MetaPeriod { get; set; }
        public DateTime SharedDate { get; set; }
        public DateTime LastUpdatedDate { get; set; }
        [ExcludeColumn]
        public bool isRemove { get; set; }
    }
}
