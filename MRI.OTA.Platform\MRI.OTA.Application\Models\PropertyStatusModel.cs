﻿
namespace MRI.OTA.Application.Models
{
    public class PropertyStatusModel
    {
        /// <summary>
        /// User id
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// PropertyId
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }
    }
}
