﻿using System.Text;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.DBCore.Repositories
{
    public class InvitationRepository : BaseRepository<UserInvites, int>, IInvitationRepository
    {
        private readonly ILogger<InvitationRepository> _logger;
        protected readonly IDapperWrapper _dapperWrapper;

        /// <summary>
        /// Contructor for invite repository
        /// </summary>
        /// <param name="dbConnection"></param>
        public InvitationRepository(IDbConnectionFactory dbConnection, ILogger<InvitationRepository> logger, IDapperWrapper dapperWrapper)
            : base(dbConnection, logger, dapperWrapper)
        {
            _logger = logger;
            _dapperWrapper = dapperWrapper;
        }

        /// <summary>
        /// Get property by Id
        /// </summary>
        /// <param name="userId"></param>
        /// <param name="propertyId"></param>
        /// <returns></returns>
        public async Task<ViewUserInvites> GetInvitationDetailsById(string? inviteCode)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT UI.UserEmail,UI.Name,UI.IsActive,UI.DataSourceId,UI.PortfolioId,UI.AgencyId,UI.AgencyName,UI.AgencyLogo,UI.AgencyColour");
            query.Append($" FROM {Constants.UserInvitesTableName} UI ");
            query.Append($" WHERE InviteCode = @InviteCode AND IsActive = 1");

            var parameters = new { InviteCode = inviteCode};
            return await GetByIdAsync<ViewUserInvites>(query.ToString(), parameters);
        }

        public async Task<int> UpdateUserInvites(string? inviteCode, string? providerId, string? loginEmail, bool isActive)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"UPDATE {Constants.UserInvitesTableName} SET ProviderId = @ProviderId, LoginEmail = @LoginEmail, IsActive = @IsActive ");
            query.Append($" WHERE InviteCode = @InviteCode");
            var parameters = new { InviteCode = inviteCode, ProviderId = providerId, LoginEmail = loginEmail, IsActive = isActive };
            return await UpdateAsync(query.ToString(), parameters);
        }
        /// <summary>
        /// Get invitation by PortfolioId and UserEmail
        /// </summary>
        /// <param name="portfolioId">The portfolio ID</param>
        /// <param name="userEmail">The user's email</param>
        /// <returns>The invitation if found, null otherwise</returns>
        public async Task<UserInvites> GetInvitationByPortfolioIdAndEmail(string portfolioId, string userEmail)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT * FROM {Constants.UserInvitesTableName} ");
            query.Append($"WHERE PortfolioId = @PortfolioId AND UserEmail = @UserEmail AND IsActive = 1");

            var parameters = new { PortfolioId = portfolioId, UserEmail = userEmail };
            return await GetByIdAsync<UserInvites>(query.ToString(), parameters);
        }
    }
}
