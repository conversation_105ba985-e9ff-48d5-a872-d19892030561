﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class ComplianceDetail
    {
        [ExcludeColumn]
        public int ComplianceDetailId { get; set; }
        public string SRCManagementId { get; set; }
        public string SRCPropertyId { get; set; }
        public int PropertyId { get; set; }
        public string SRCComplianceId { get; set; }
        public string ComplianceName { get; set; }
        public string Status { get; set; }
        public DateTime ExpiryDate { get; set; }
        public string ServicedBy { get; set; }
    }
}
