using Microsoft.Data.SqlClient;
using Microsoft.Extensions.Logging;

public class SqlRetryHandler
{
    private readonly ILogger _logger;
    private readonly int _maxRetries;
    private readonly TimeSpan _retryInterval;

    public SqlRetryHandler(ILogger logger, int maxRetries = 3, int retryIntervalSeconds = 1)
    {
        _logger = logger;
        _maxRetries = maxRetries;
        _retryInterval = TimeSpan.FromSeconds(retryIntervalSeconds);
    }

    public async Task<T> ExecuteWithRetryAsync<T>(Func<Task<T>> operation)
    {
        for (int i = 0; i <= _maxRetries; i++)
        {
            try
            {
                return await operation();
            }
            catch (Exception ex) when (
                ex is SqlException sqlEx && 
                (sqlEx.Number == -2 || // Timeout
                 sqlEx.Number == -1 || // Connection failed
                 sqlEx.Number == 1205 || // Deadlock
                 sqlEx.Number == 1204)) // Lock timeout
            {
                if (i == _maxRetries)
                {
                    _logger.LogError(ex, "Max retries reached for database operation");
                    throw;
                }

                _logger.LogWarning(ex, "Retry attempt {Attempt} of {MaxRetries}", i + 1, _maxRetries);
                await Task.Delay(_retryInterval);
            }
        }

        throw new Exception("Unexpected error in retry logic");
    }
}