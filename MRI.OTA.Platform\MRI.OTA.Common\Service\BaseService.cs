﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Interfaces;
using Newtonsoft.Json;

/// <summary>
/// Base service
/// </summary>
/// <typeparam name="TEntity"></typeparam>
/// <typeparam name="TModel"></typeparam>
/// <typeparam name="TKey"></typeparam>
public class BaseService<TEntity, TModel, TKey> : IBaseService<TEntity, TModel, TKey> where TEntity : class
{
    protected readonly IBaseRepository<TEntity, TKey> _repository;
    private readonly IMapper _mapper;
    private readonly ILogger<BaseService<TEntity, TModel, TKey>> _logger;
    /// <summary>
    /// Class for baseService
    /// </summary>
    /// <param name="repository"></param>
    /// <param name="mapper"></param>
    protected BaseService(ILogger<BaseService<TEntity, TModel, TK<PERSON>>> logger, IBaseRepository<TEntity, TK<PERSON>> repository, IMapper mapper)
    {
        _logger = logger;
        _repository = repository;
        _mapper = mapper;
    }
    /// <summary>
    /// Get all
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<TModel>> GetAllAsync()
    {
        _logger.LogInformation($"Executing Base Service Method: {nameof(GetAllAsync)}");

        try
        {
            var entities = await _repository.GetAllAsync().ConfigureAwait(false);
            return _mapper.Map<List<TModel>>(entities);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Service Exception: {nameof(GetAllAsync)} | {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Get table data
    /// </summary>
    /// <returns></returns>
    public async Task<IEnumerable<TEntity>> GetDataByTableName(string tablename)
    {
        _logger.LogInformation($"Executing Base Service Method: {nameof(GetDataByTableName)}");

        try
        {
            var entities = await _repository.GetDataByTableName<TEntity>(tablename).ConfigureAwait(false);
            return entities;
        }
        catch (Exception ex)
        {
            _logger.LogError($"Service Exception: {nameof(GetDataByTableName)} | {ex.Message}");
            throw;
        }
    }

    /// <summary>
    /// Get by Id
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<TModel> GetByIdAsync(TKey id, string idColumnName)
    {
        _logger.LogInformation($"Executing Base Service Method: {nameof(GetByIdAsync)}");
        _logger.LogDebug($"Service Parameters: {id}");

        try
        {
            var entity = await _repository.GetByIdAsync(id, idColumnName).ConfigureAwait(false);
            return _mapper.Map<TModel>(entity);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Service Exception: {nameof(GetByIdAsync)} | {ex.Message}");
            _logger.LogError($"Service Parameters: {id}");
            throw;
        }
    }

    /// <summary>
    /// Add Entity
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<int> AddAsync(TModel model)
    {
        _logger.LogInformation($"Executing Base Service Method: {nameof(AddAsync)}");
        _logger.LogDebug($"Service Parameters: {JsonConvert.SerializeObject(model)}");

        try
        {
            return await _repository.AddAsync(_mapper.Map<TEntity>(model)).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Service Exception: {nameof(AddAsync)} | {ex.Message}");
            _logger.LogError($"Service Parameters: {JsonConvert.SerializeObject(model)}");
            throw;
        }
    }

    /// <summary>
    /// Update Entity
    /// </summary>
    /// <param name="model"></param>
    /// <returns></returns>
    public async Task<int> UpdateAsync(TKey id,string idColumnName, TModel model)
    {
        _logger.LogInformation($"Executing Base Service Method: {nameof(UpdateAsync)}");
        _logger.LogDebug($"Service Parameters: {id} | {JsonConvert.SerializeObject(model)}");

        try
        {
            return await _repository.UpdateAsync(id, idColumnName ,_mapper.Map<TEntity>(model)).ConfigureAwait(false);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Service Exception: {nameof(UpdateAsync)} | {ex.Message}");
            _logger.LogError($"Service Parameters: {id} | {JsonConvert.SerializeObject(model)}");
            throw;
        }
    }

    /// <summary>
    /// Delete Entity
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    public async Task<int> DeleteAsync(TKey id, string idColumnName)
    {
        _logger.LogInformation($"Executing Base Service Method: {nameof(DeleteAsync)}");
        _logger.LogDebug($"Service Parameters: {id}");

        try
        {
            return await _repository.DeleteAsync(id, idColumnName);
        }
        catch (Exception ex)
        {
            _logger.LogError($"Service Exception: {nameof(DeleteAsync)} | {ex.Message}");
            _logger.LogError($"Service Parameters: {id}");
            throw;
        }
    }
}

