﻿using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.EventHub.v1
{
    [AllowAnonymous]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/eventhubs")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class EventHubController : ControllerBase
    {
        private readonly IEventConsumerService _eventConsumerService;

        /// <summary>
        /// Constructor for event hub controller
        /// </summary>
        /// <param name="propertyService"></param>
        public EventHubController(IEventConsumerService eventConsumerService)
        {
            _eventConsumerService = eventConsumerService;
        }


        /// <summary>
        /// Add new event for consumer
        /// </summary>
        /// <param name="user"></param>
        /// <returns></returns>
        [HttpPost]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "event data added successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> AddConsumerEvent(EventConsumerModel eventConsumer)
        {
            var result = await _eventConsumerService.AddAsync(eventConsumer);
            if (result > 0)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item added successfully", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "Item not added", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be added." }));
            }
        }
    }
}
