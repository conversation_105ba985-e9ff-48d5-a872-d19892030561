﻿using MRI.OTA.Application.Validators;

namespace MRI.OTA.UnitTestCases.Validators
{
    public class DeleteAccountParametersValidatorTest
    {
        private readonly DeleteAccountParametersValidator _validator = new DeleteAccountParametersValidator();


        [Fact]
        public void Should_Have_Error_When_UserId_Is_Zero()
        {
            var model = new DeleteAccountParameters { UserId = 0, ProviderId = "provider" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "UserId" &&
                e.ErrorMessage == "User ID must be greater than 0"
            );
        }

        [Fact]
        public void DeleteAccountParametersValidator_Should_Have_Error_When_ProviderId_Is_Empty()
        {
            var model = new DeleteAccountParameters { UserId = 1, ProviderId = "" };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "ProviderId" &&
                e.ErrorMessage == "Provider ID is required"
            );
        }

        [Fact]
        public void DeleteAccountParametersValidator_Should_Have_Error_When_ProviderId_Too_Long()
        {
            var model = new DeleteAccountParameters { UserId = 1, ProviderId = new string('a', 256) };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "ProviderId" &&
                e.ErrorMessage == "Provider ID cannot exceed 255 characters"
            );
        }
    }
}
