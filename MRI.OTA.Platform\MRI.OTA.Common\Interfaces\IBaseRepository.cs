﻿using System.Data;

namespace MRI.OTA.Common.Interfaces
{
    /// <summary>
    /// Interface for base repository
    /// </summary>
    /// <typeparam name="TEntity"></typeparam>
    /// <typeparam name="TK<PERSON>"></typeparam>
    public interface IBaseRepository<TEntity, TKey> where TEntity : class
    {
        /// <summary>
        /// Get all
        /// </summary>
        /// <returns></returns>
        Task<IEnumerable<TEntity>> GetAllAsync();

        /// <summary>
        /// Get all
        /// </summary>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        Task<List<T>> GetAllAsync<T>(string query, object? parameters = null);

        /// <summary>
        /// Get by Id
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<TEntity> GetByIdAsync(TKey id, string idColumnName);

        /// <summary>
        /// Get by Id
        /// </summary>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        Task<T> GetByIdAsync<T>(string query, object? parameters = null);

        /// <summary>
        /// Get by id with transaction support
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <param name="transaction"></param>
        /// <returns></returns>
        Task<T> GetByIdAsync<T>(string query, object? parameters = null, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null);

        /// <summary>
        /// Get data by table name
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="tableName"></param>
        /// <returns></returns>
        Task<List<T>> GetDataByTableName<T>(string tableName);
        /// <summary>
        /// Add Entity
        /// </summary>
        /// <param name="entity"></param>
        /// <returns></returns>
        Task<int> AddAsync(TEntity entity);

        /// <summary>
        /// Add Entity using dictionary of columnname and values
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="keyValuePairs"></param>
        /// <param name="transaction"></param>
        /// <returns></returns>
        public Task<int> AddAsync(string tableName, string idColumnname , Dictionary<string, object> keyValuePairs, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null);
        /// <summary>
        /// Update Entity
        /// </summary>
        /// <param name="entity"></param>
        /// <param name="id"></param>
        /// <param name="idColumnName"></param>
        /// <returns></returns>
        Task<int> UpdateAsync(TKey id, string idColumnName, TEntity entity);

        /// <summary>
        /// Update Entity
        /// </summary>
        /// <param name="query"></param>
        /// <param name="transaction"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public Task<int> UpdateAsync(string query, object? parameters = null, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null);

        /// <summary>
        /// Update Entity
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="idColumnName"></param>
        /// <param name="idValue"></param>
        /// <param name="keyValuePairs"></param>
        /// <param name="transaction"></param>
        /// <returns></returns>
        public Task<int> UpdateAsync(string tableName, string idColumnName , object idValue, Dictionary<string, object> keyValuePairs, IDbTransaction? transaction = null);

        /// <summary>
        /// Get by Id list
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public Task<List<T>> GetByIdListAsync<T>(string query, object? parameters = null);

        /// <summary>
        /// Delete Entity
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        Task<int> DeleteAsync(TKey id, string idColumnName);

        /// <summary>
        /// Execute a query and return the first result, or default if no results
        /// </summary>
        /// <typeparam name="T">The type to map the result to</typeparam>
        /// <param name="query">The SQL query to execute</param>
        /// <param name="parameters">The parameters for the query</param>
        /// <returns>The first result or default value</returns>
        Task<T> QueryFirstOrDefaultAsync<T>(string query, object? parameters = null);

        /// <summary>
        /// Execute a query and return multiple results
        /// </summary>
        /// <typeparam name="T">The type to map the results to</typeparam>
        /// <param name="query">The SQL query to execute</param>
        /// <param name="parameters">The parameters for the query</param>
        /// <returns>An enumerable of results</returns>
        Task<IEnumerable<T>> QueryAsync<T>(string query, object? parameters = null);

        /// <summary>
        /// Execute a query and return multiple results with transaction support
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="query"></param>
        /// <param name="parameters"></param>
        /// <param name="transaction"></param>
        /// <returns></returns>
        Task<IEnumerable<T>> QueryAsync<T>(string query, object? parameters = null, IDbTransaction? transaction = null, IDbConnection? dbConnectionTran = null);

        /// <summary>
        /// Convert to dictionary
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="obj"></param>
        /// <returns></returns>
        public Dictionary<string, object> ConvertToDictionary<T>(T obj);

        /// <summary>
        /// Generate update statement
        /// </summary>
        /// <param name="tableName"></param>
        /// <param name="values"></param>
        /// <param name="whereClause"></param>
        /// <returns></returns>
        public Task<string> GenerateUpdateStatement(string tableName, Dictionary<string, object> values, string whereClause);
    }
}
