using Microsoft.AspNetCore.Http;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces
{
    /// <summary>
    /// Interface for generating HTTP responses
    /// </summary>
    public interface IResponseGenerator
    {
        /// <summary>
        /// Writes an unauthorized response to the HTTP context
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="message">The error message</param>
        /// <param name="statusCode">The HTTP status code</param>
        /// <returns>A task representing the asynchronous operation</returns>
        Task WriteUnauthorizedResponse(HttpContext context, string message, int statusCode);
    }
}
