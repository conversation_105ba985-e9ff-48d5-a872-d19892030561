﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MRI.OTA.Common.Models;
using Newtonsoft.Json;

namespace MRI.OTA.Application.Models
{
    public class DocumentDetailResponse
    {
        public string DocumentId { get; set; }
        public string DocumentName { get; set; }
        public string DocumentLink { get; set; }
        public string DocumentType { get; set; }
        public DateTime DateShared { get; set; }
        public DateTime LastUpdated { get; set; }
        public MetaResponse Metadata { get; set; }
        public List<DocumentListAssociatedResponse> Added { get; set; }
        public List<DocumentListAssociatedResponse> Removed { get; set; }
    }
    public class DocumentListAssociatedResponse
    {
        public string[] ManagementIds { get; set; }
        public string[] TenancyIds { get; set; }
    }
    public class MetaResponse
    {
        public string? ReceiptType { get; set; }
        public string? InvoiceStatus { get; set; }
        public decimal? InvoiceOwing { get; set; }

        #region Store data in MetaDate Column
        public DateTime? DueDate { get; set; }
        public DateTime? ReceiptDate { get; set; }
        public DateTime? IEGenerationDate { get; set; }
        #endregion

        #region Store data in MetaNumber Column
        public string? InvoiceNumber { get; set; }
        public string? ReceiptNumber { get; set; }
        #endregion

        #region Store data in MetaAmount Column
        public decimal? InvoiceAmount { get; set; }
        public decimal? ReceiptAmount { get; set; }
        #endregion
        
        #region Store data in MetaCurrency Column
        public string? Currency { get; set; }
        public string? ReceiptCurrency { get; set; }
        #endregion

        #region Store data in MetaPeriod Column
        public string? IEDateRange { get; set; }
        public string? StatementPeriod { get; set; }
        #endregion
    }
}
