﻿
namespace MRI.OTA.Application.Models
{
    public class PropertyOwnershipDetailsModel
    {
        /// <summary>
        /// PropertyOwnershipDetailsId
        /// </summary>
        public int PropertyOwnershipDetailsId { get; set; }
        /// <summary>
        /// OwnershipPercentage
        /// </summary>
        public decimal OwnershipPercentage { get; set; } = 100; // Default value
        /// <summary>
        /// OwnershipStartDate
        /// </summary>
        public DateTime? OwnershipStartDate { get; set; }

        /// <summary>
        /// OwnershipEndDate
        /// </summary>
        public DateTime? OwnershipEndDate { get; set; }
        /// <summary>
        /// PurchasePrice
        /// </summary>
        public decimal? PurchasePrice { get; set; }
        /// <summary>
        /// RentAmount
        /// </summary>
        public decimal? RentAmount { get; set; }
        /// <summary>
        /// LeaseStartDate
        /// </summary>
        public DateTime? LeaseStartDate { get; set; }
        /// <summary>
        /// LeaseEndDate
        /// </summary>
        public DateTime? LeaseEndDate { get; set; }
        /// <summary>
        /// SecurityDeposit
        /// </summary>
        public decimal? SecurityDeposit { get; set; }
       
    }
}
