﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.UnitTestCases.User.Mapper
{
    public class AddUsersMappingProfileTests
    {
        private readonly IMapper _mapper;

        public AddUsersMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<AddUsersMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_Users_To_AddUserModel()
        {
            // Arrange  
            var user = new Users
            {
                UserId = 1,
                DisplayName = "John Doe",
                UserEmail = "<EMAIL>"
            };

            // Act  
            var result = _mapper.Map<AddUserModel>(user);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(user.DisplayName, result.DisplayName);
            Assert.Equal(user.UserEmail, result.UserEmail);
        }

        [Fact]
        public void Should_Map_AddUserModel_To_Users()
        {
            // Arrange  
            var addUserModel = new AddUserModel
            {
                UserId = 1,
                DisplayName = "Jane Doe",
                UserEmail = "<EMAIL>"
            };

            // Act  
            var result = _mapper.Map<Users>(addUserModel);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(addUserModel.UserId, result.UserId);
            Assert.Equal(addUserModel.DisplayName, result.DisplayName);
            Assert.Equal(addUserModel.UserEmail, result.UserEmail);
        }
    }
}
