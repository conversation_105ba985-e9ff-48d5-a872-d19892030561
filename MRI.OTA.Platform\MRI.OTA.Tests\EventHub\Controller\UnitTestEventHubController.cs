﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using MRI.OTA.API.Controllers.EventHub.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Common.Models;

namespace MRI.OTA.UnitTestCases.EventHub.Controller
{
    public class UnitTestEventHubController
    {
        private readonly Mock<IEventConsumerService> _mockService;
        private readonly EventHubController _controller;

        public UnitTestEventHubController()
        {
            _mockService = new Mock<IEventConsumerService>();
            _controller = new EventHubController(_mockService.Object);
        }

        [Fact]
        public async Task AddConsumerEvent_ReturnsOk_WhenItemAdded()
        {
            // Arrange
            var eventConsumer = new EventConsumerModel { EventConsumerId = "1" };
            _mockService.Setup(s => s.AddAsync(eventConsumer)).ReturnsAsync(1);

            // Act
            var result = await _controller.AddConsumerEvent(eventConsumer);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var response = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(response.Success);
            Assert.Equal(StatusCodes.Status200OK, response.StatusCode);
            Assert.Equal("Item added successfully", response.Message);
        }

        [Fact]
        public async Task AddConsumerEvent_ReturnsBadRequest_WhenItemNotAdded()
        {
            // Arrange
            var eventConsumer = new EventConsumerModel { EventConsumerId = "2" };
            _mockService.Setup(s => s.AddAsync(eventConsumer)).ReturnsAsync(0);

            // Act
            var result = await _controller.AddConsumerEvent(eventConsumer);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var response = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(response.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, response.StatusCode);
            Assert.Equal("Item not added", response.Message);
            Assert.Contains("The item could not be added.", response.Errors);
        }
    }
}