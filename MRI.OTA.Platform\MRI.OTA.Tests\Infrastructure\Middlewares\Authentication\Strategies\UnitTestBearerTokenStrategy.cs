using Microsoft.ApplicationInsights.Extensibility;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies;
using System.IO;
using System.Security.Claims;
using Xunit;

namespace MRI.OTA.UnitTestCases.Infrastructure.Middlewares.Authentication.Strategies
{
    public class UnitTestBearerTokenStrategy
    {
        private readonly Mock<RequestDelegate> _nextMock;
        private readonly Mock<IJwtTokenValidator> _jwtTokenValidatorMock;
        private readonly Mock<ILogger> _loggerMock;
        private readonly Mock<IResponseGenerator> _responseGeneratorMock;
        private readonly Mock<AuthenticationLogger> _authLoggerMock;
        private readonly string[] _clientCredentialEndpoints;
        private readonly BearerTokenStrategy _strategy;

        public UnitTestBearerTokenStrategy()
        {
            _nextMock = new Mock<RequestDelegate>();
            _jwtTokenValidatorMock = new Mock<IJwtTokenValidator>();
            _loggerMock = new Mock<ILogger>();
            _responseGeneratorMock = new Mock<IResponseGenerator>();
            var telemetryConfiguration = TelemetryConfiguration.CreateDefault();
            _authLoggerMock = new Mock<AuthenticationLogger>(_loggerMock.Object, new Microsoft.ApplicationInsights.TelemetryClient(telemetryConfiguration));
            _clientCredentialEndpoints = new[] { "/api/v1/invitations/invite-user", "/api/v1/integrations", "/api/v1/openid-configuration" };

            _strategy = new BearerTokenStrategy(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _responseGeneratorMock.Object,
                _authLoggerMock.Object,
                _clientCredentialEndpoints);
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenNextIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BearerTokenStrategy(
                null!,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _responseGeneratorMock.Object,
                _authLoggerMock.Object,
                _clientCredentialEndpoints));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenJwtTokenValidatorIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BearerTokenStrategy(
                _nextMock.Object,
                null!,
                _loggerMock.Object,
                _responseGeneratorMock.Object,
                _authLoggerMock.Object,
                _clientCredentialEndpoints));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenLoggerIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BearerTokenStrategy(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                null!,
                _responseGeneratorMock.Object,
                _authLoggerMock.Object,
                _clientCredentialEndpoints));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenResponseGeneratorIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BearerTokenStrategy(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                null!,
                _authLoggerMock.Object,
                _clientCredentialEndpoints));
        }

        [Fact]
        public void Constructor_ThrowsArgumentNullException_WhenAuthLoggerIsNull()
        {
            // Arrange & Act & Assert
            Assert.Throws<ArgumentNullException>(() => new BearerTokenStrategy(
                _nextMock.Object,
                _jwtTokenValidatorMock.Object,
                _loggerMock.Object,
                _responseGeneratorMock.Object,
                null!,
                _clientCredentialEndpoints));
        }

        [Fact]
        public void CanHandle_ReturnsTrue_WhenBearerTokenPresent()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer test-token";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.True(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAuthorizationHeaderMissing()
        {
            // Arrange
            var context = CreateHttpContext();

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenAuthorizationHeaderEmpty()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = string.Empty;

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsFalse_WhenNotBearerToken()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Basic dGVzdDp0ZXN0";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public void CanHandle_ReturnsTrue_WhenBearerTokenEmpty()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer ";

            // Act
            var result = _strategy.CanHandle(context);

            // Assert
            Assert.True(result); // CanHandle only checks for "Bearer " prefix, not token validity
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledAndNotAuthenticated_WhenTokenTooLong()
        {
            // Arrange
            var context = CreateHttpContext();
            var longToken = new string('a', 5000); // Exceeds MaxTokenLength
            context.Request.Headers["Authorization"] = $"Bearer {longToken}";

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.False(authenticated);
            _responseGeneratorMock.Verify(r => r.WriteUnauthorizedResponse(
                context, "Invalid token format", StatusCodes.Status401Unauthorized), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledAndNotAuthenticated_WhenTokenEmpty()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer ";

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.False(authenticated);
            _responseGeneratorMock.Verify(r => r.WriteUnauthorizedResponse(
                context, "Invalid token format", StatusCodes.Status401Unauthorized), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_ValidatesTokenWithCorrectParameters_ForPKCE()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/api/v1/users"; // Not a client credential endpoint
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            var principal = new ClaimsPrincipal();
            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ReturnsAsync(principal);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _jwtTokenValidatorMock.Verify(v => v.ValidateToken("valid-token", false), Times.Once);
        }

        [Theory]
        [InlineData("/api/v1/invitations/invite-user")]
        [InlineData("/api/v1/integrations")]
        [InlineData("/api/v1/openid-configuration")]
        public async Task AuthenticateAsync_ValidatesTokenWithCorrectParameters_ForClientCredentials(string path)
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = path;
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            var principal = new ClaimsPrincipal();
            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", true))
                .ReturnsAsync(principal);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _jwtTokenValidatorMock.Verify(v => v.ValidateToken("valid-token", true), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_SetsUserPrincipal_WhenTokenValid()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            var claims = new[] { new Claim("sub", "user123") };
            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims));

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ReturnsAsync(principal);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.Equal(principal, context.User);
        }

        [Fact]
        public async Task AuthenticateAsync_SetsUserPrincipal_ForPKCE()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            var claims = new[] { new Claim("sub", "user123") };
            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims));

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ReturnsAsync(principal);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.Equal(principal, context.User);
        }

        [Fact]
        public async Task AuthenticateAsync_SetsUserPrincipal_ForClientCredentials()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Path = "/api/v1/openid-configuration";
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            var claims = new[] { new Claim("client_id", "client123") };
            var principal = new ClaimsPrincipal(new ClaimsIdentity(claims));

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", true))
                .ReturnsAsync(principal);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.NotNull(context.User);
            Assert.True(context.User.Identity!.IsAuthenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_CallsNext_WhenTokenValid()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            var principal = new ClaimsPrincipal();
            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ReturnsAsync(principal);

            // Act
            await _strategy.AuthenticateAsync(context);

            // Assert
            _nextMock.Verify(n => n(context), Times.Once);
        }

        [Fact]
        public async Task AuthenticateAsync_ReturnsHandledAndAuthenticated_WhenTokenValid()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer valid-token";

            var principal = new ClaimsPrincipal();
            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("valid-token", false))
                .ReturnsAsync(principal);

            // Act
            var (handled, authenticated) = await _strategy.AuthenticateAsync(context);

            // Assert
            Assert.True(handled);
            Assert.True(authenticated);
        }

        [Fact]
        public async Task AuthenticateAsync_HandlesTokenValidationException()
        {
            // Arrange
            var context = CreateHttpContext();
            context.Request.Headers["Authorization"] = "Bearer invalid-token";

            _jwtTokenValidatorMock.Setup(v => v.ValidateToken("invalid-token", false))
                .ThrowsAsync(new Microsoft.IdentityModel.Tokens.SecurityTokenException("Invalid token"));

            // Act & Assert
            await Assert.ThrowsAsync<Microsoft.IdentityModel.Tokens.SecurityTokenException>(() =>
                _strategy.AuthenticateAsync(context));
        }

        private static HttpContext CreateHttpContext()
        {
            var context = new DefaultHttpContext();
            context.Request.Method = "GET";
            context.Request.Path = "/test";
            context.Request.Scheme = "https";
            context.Request.Host = new HostString("localhost");
            context.Response.Body = new MemoryStream();
            return context;
        }
    }
}
