﻿using Microsoft.Extensions.DependencyInjection;
using MRI.OTA.Integration.Http;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.Tests.Integration.Http
{
    public class HttpClientPoolingIntegrationTests
    {
        private readonly ServiceProvider _serviceProvider;

        public HttpClientPoolingIntegrationTests()
        {
            var services = new ServiceCollection();

            // Register HttpClientFactory with pooling behavior - Ensure pooling
            services.AddHttpClient("PooledHttpClient")
                .SetHandlerLifetime(TimeSpan.FromMinutes(5));

            services.AddSingleton<IHttpClientFactoryManager, HttpClientFactoryManager>();

            _serviceProvider = services.BuildServiceProvider();
        }

        [Fact]
        public void GetHttpClient_ShouldReuseHttpMessageHandler()
        {
            // Arrange
            var httpClientFactoryManager = _serviceProvider.GetRequiredService<IHttpClientFactoryManager>();

            // Act: Get two HttpClient instances
            var client1 = httpClientFactoryManager.GetHttpClient();
            var client2 = httpClientFactoryManager.GetHttpClient();

            // Assert: Both should have different HttpClient instances but the same HttpMessageHandler
            Assert.NotSame(client1, client2); // Different HttpClient instances

            // Extract the inner handler (reflection to get private fields)
            var handler1 = GetInnerHandler(client1);
            var handler2 = GetInnerHandler(client2);

            // Validate that the handlers are the same, proving connection pooling
            Assert.Same(handler1, handler2);
        }

        private static HttpMessageHandler? GetInnerHandler(HttpClient client)
        {
            var fieldInfo = typeof(HttpMessageInvoker)
                .GetField("_handler", BindingFlags.NonPublic | BindingFlags.Instance);
            return fieldInfo?.GetValue(client) as HttpMessageHandler;
        }
    }
}
