﻿namespace MRI.OTA.Application.Models
{
    public class PropertyManagerInformationModel
    {
        public int PropertyId { get; set; }
        public string? ManagementType { get; set; } 
        public string? AgencyName { get; set; } 
        public string? PropertyManagerName { get; set; } 
        public string? PropertyManagerMobile { get; set; } 
        public string? PropertyManagerPhone { get; set; } 
        public string? PropertyManagerEmail { get; set; } 
        public DateTime? AuthorityStartDate { get; set; } 
        public DateTime? AuthorityEndDate { get; set; } 
        public string? Ownership { get; set; } 
        public decimal? ExpenditureLimit { get; set; } 
        public string? ExpenditureNotes { get; set; }
    }
}
