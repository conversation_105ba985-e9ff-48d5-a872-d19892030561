using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Interfaces;
using System.Data;

namespace MRI.OTA.Core.Repositories
{
    /// <summary>
    /// User repository
    /// </summary>
    public class DataSourceRepository : BaseRepository<DataSource, int>, IDataSourceRepository
    {
        /// <summary>
        /// Contructor for user repository
        /// </summary>
        /// <param name="dbConnection"></param>
        public DataSourceRepository(IDbConnectionFactory dbConnection, ILogger<DataSourceRepository> logger, IDapperWrapper dapperWrapper) 
            : base(dbConnection, logger, dapperWrapper)
        {
        }

        public Task<DataSource> GetUserDataSource(string accessKey, string accessSecret)
        {
            string query = "SELECT * FROM DataSource WHERE AccessKey = @AccessKey AND AccessSecret = @AccessSecret";
            var parameters = new { AccessKey = accessKey, AccessSecret = accessSecret };
            return GetByIdAsync<DataSource>(query, parameters);
        }

        public Task<DataSource> GetUserDataSource(string accessKey)
        {
            string query = "SELECT * FROM DataSource WHERE AccessKey = @AccessKey";
            var parameters = new { AccessKey = accessKey };
            return GetByIdAsync<DataSource>(query, parameters);
        }

        public async Task<int> UpdateDataSource(string accessKey, string accessSecret, string manifestJson)
        {
            var query = "UPDATE DataSource SET ManifestJson = @ManifestJson WHERE AccessKey = @AccessKey AND AccessSecret = @AccessSecret";
            var parameters = new { ManifestJson = manifestJson, AccessKey = accessKey, AccessSecret = accessSecret };
            var result = await this.UpdateAsync(query, parameters);
            return result;
        }

        public Task<DataSource> GetAccessToken(string accessToken)
        {
            string query = "SELECT * FROM DataSource WHERE AccessSecret = @AccessToken";
            var parameters = new { AccessToken = accessToken };
            return GetByIdAsync<DataSource>(query, parameters);
        }

        public async Task<List<DataSource>> GetAllDataSource()
        {
            string query = "SELECT DataSourceId,Name, ManifestJson FROM DataSource";
            var result = await QueryAsync<DataSource>(query.ToString(), null);
            return result.ToList();
        }
    }
}
