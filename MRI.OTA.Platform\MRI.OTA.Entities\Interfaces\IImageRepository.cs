﻿using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.DBCore.Interfaces
{
    public interface IImageRepository : IBaseRepository<PropertyImages, int>
    {
        public Task<int> AddPropertyImages(List<PropertyImages> imagesList);

        public Task<PropertyImages> GetPropertyImage(int propertyImagesId);

        public Task<int> DeleteImage(int propertyImagesId);

        public Task<bool> UpdateDefaultImage(int propertyId, string defaultImageUri);

        public Task<bool> UpdateDefaultImage(int propertyId, int propertyImagesId, int userId);

        public Task<ViewPropertyImages> GetPropertyDefaultImage(int propertyImagesId, int defaultImageId);

        public Task<List<PropertyImages>> GetPropertyImagesByUserId(int userId);
    }
}
