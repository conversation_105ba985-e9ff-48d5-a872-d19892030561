using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.DBCore.Interfaces
{
    /// <summary>
    /// Interface for userRepository
    /// </summary>
    public interface IDataSourceRepository : IBaseRepository<DataSource, int>
    {
        /// <summary>
        /// Get user data source
        /// </summary>
        /// <returns></returns>
        Task<DataSource> GetUserDataSource(string accessKey, string accessSecret);

        Task<int> UpdateDataSource(string accessKey, string accessSecret, string manifestJson);

        Task<DataSource> GetUserDataSource(string accessKey);

        Task<DataSource> GetAccessToken(string accessToken);

        public Task<List<DataSource>> GetAllDataSource();
    }
}
