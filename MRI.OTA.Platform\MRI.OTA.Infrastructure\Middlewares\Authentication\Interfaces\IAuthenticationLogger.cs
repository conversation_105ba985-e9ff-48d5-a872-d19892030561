using Microsoft.AspNetCore.Http;
using System.Security.Claims;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces
{
    /// <summary>
    /// Interface for logging authentication events
    /// </summary>
    public interface IAuthenticationLogger
    {
        /// <summary>
        /// Logs a successful user authentication
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="principal">The claims principal</param>
        void LogSuccessfulUserAuthentication(HttpContext context, ClaimsPrincipal principal);

        /// <summary>
        /// Logs a successful client credentials authentication
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="principal">The claims principal</param>
        void LogSuccessfulClientCredentialsAuthentication(HttpContext context, ClaimsPrincipal principal);

        /// <summary>
        /// Gets or generates a correlation ID for the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>The correlation ID</returns>
        string GetOrGenerateCorrelationId(HttpContext context);
    }
}
