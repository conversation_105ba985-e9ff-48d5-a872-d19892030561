﻿using Azure.Messaging;
using Microsoft.Extensions.Options;
using MRI.Integration.Consumer.SDK;
using MRI.Integration.Consumer.SDK.Models;
using MRI.Integration.Consumer.SDK.Protocols.V1.Models;
using MRI.Integration.SDK.Shared.Enums;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using System.Net.Http;
using System.Net.Http.Json;
using System.Text.Json;

namespace MRI.OTA.EventHub.Consumer
{
    /// <summary>
    /// This class is responsible for consuming events from the Event Hub.
    /// </summary>
    public class ConsumerService : IConsumerService
    {
        private readonly ILogger<ConsumerService> _logger;
        private readonly ConsumerHelper _consumerHelper;
        private readonly IOptionsMonitor<ConsumerConfig> _consumerConfig;
        private readonly IConsumerEventService _consumerEventService;
        private readonly INotificationService _notificationService;

        /// <summary>
        /// This constructor is used to inject the dependencies required for the consumer service.
        /// </summary>
        /// <param name="consumerConfig"></param>
        /// <param name="consumerHelper"></param>
        /// <param name="logger"></param>
        public ConsumerService(IOptionsMonitor<ConsumerConfig> consumerConfig, ConsumerHelper consumerHelper, ILogger<ConsumerService> logger, IConsumerEventService consumerEventService, INotificationService notificationService)
        {
            _consumerConfig = consumerConfig;
            _consumerHelper = consumerHelper;
            _logger = logger;
            _consumerEventService = consumerEventService;
            _notificationService = notificationService;
        }

        public async Task ConnectAndHandle(CancellationToken cancellationToken)
        {
            // following code required to listen for events/commands
            try
            {
                var token = CancellationToken.None;

                // initialise a cancellation token to enable graceful shutdown of the consumer from the consumer application after a period of time
                // if this is not required, an empty cancellation token can be used
                if (_consumerConfig.CurrentValue.CancellationTokenEnabled)
                {
                    var cancellationTokenSource = new CancellationTokenSource();
                    cancellationTokenSource.CancelAfter(TimeSpan.FromSeconds(_consumerConfig.CurrentValue.CancellationTokenTimeSpanInSeconds));

                    token = cancellationTokenSource.Token;
                }

                _logger.LogInformation("Consumer is connecting to listen for events...");

                // delegate methods specified to handle the incoming events, commands, faults and timeouts. Overload provided with option to auto acknowledge events which should only be used 
                // when the consumer does not care if the events are processed or not
                await _consumerHelper.ConnectAndHandle(
                    cancellationToken,
                    async (cloudEvent) => await ProcessEvent(cloudEvent),
                    null,
                    async (fault) => await HandleFault(fault, cancellationToken),
                    async (timeouts) => await HandleTimeout(timeouts, cancellationToken),
                    async (connectionStatus) => await DisplayConnectionStatus(connectionStatus));

                _logger.LogInformation("Consumer connection established successfully.");
            }
            catch (Exception e)
            {
                _logger.LogError(e, "Error occurred while connecting and handling events.");
                Console.WriteLine(e.Message);
            }
        }

        // delegate method called when an event is received
        // use this delegate to process an incoming event and send back an appropriate action (Acknowledge, Abandon, Deadletter, Defer)
        private async Task ProcessEvent(CloudEvent cloudEvent)
        {
            var serializedEvent = JsonSerializer.Serialize(cloudEvent, new JsonSerializerOptions());
            try
            {
                using (_logger.BeginScope("EventId: {CloudEventId}", cloudEvent.Id))
                using (_logger.BeginScope("EventSource: {CloudEventIdSource}", cloudEvent.Source))
                using (_logger.BeginScope("EventType: {CloudEventType}", cloudEvent.Type))
                using (_logger.BeginScope("EventSubject: {CloudeventSubject}", cloudEvent.Subject))
                {
                    _logger.LogInformation("Processing Event hub cloud event: {EventId}. Event: {event}", cloudEvent.Id, serializedEvent);

                    var result = await _consumerEventService.AddConsumerEvent(cloudEvent);

                    var sendNotification = await _notificationService.SendPushNotificationAsync(cloudEvent);

                    await _consumerHelper.Acknowledge(cloudEvent.Id, cloudEvent.Source);
                    _logger.LogInformation("Acknowledged Event {Id}", cloudEvent.Id);

                    _logger.LogInformation("Completed processing of Event ID: {Id}", cloudEvent.Id);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("Exception occurred while processing event, abandoning event. Id: {id}. Event: {event}. Exception: {exception} ",
                       cloudEvent.Id, serializedEvent, ex);

                await _consumerHelper.Abandon(cloudEvent.Id, cloudEvent.Source);
            }
        }

        /// <summary>
        /// This method is called when a dead lettered event is acknowledged. This is used to remove the event from the dead letter queue.
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="eventSource"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task AcknowledgeDeadLetteredEvent(Guid eventId, string eventSource)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// This method is called when a fault is received from the consumer API. This is used to log the fault and take appropriate action.
        /// </summary>
        /// <param name="fault"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        public Task HandleFault(Fault fault, CancellationToken ct)
        {
            _logger.LogError("""
                                   ---FAULT RECEIVED---
                                   
                                       Time Received: {time}
                                       Reason:        {reason}
                                       Description:   {description}
                                       Severity:      {severity}

                                   ---END OF RECEIVED FAULT---
                                   """,
                DateTime.UtcNow,
                fault.Reason,
                fault.Description,
                fault.Severity);

            return Task.CompletedTask;
        }

        /// <summary>
        /// This method is called when a timeout is received from the consumer API. This is used to log the timeout and take appropriate action.
        /// </summary>
        /// <param name="timeout"></param>
        /// <param name="ct"></param>
        /// <returns></returns>
        public Task HandleTimeout(EventDetails timeout, CancellationToken ct)
        {
            _logger.LogError("""
                             ---ACK TIMEOUT---
                             
                                 Time Received: {time}
                                 Id:            {id}
                                 Source:        {src}

                             ---END OF ACK TIMEOUT---
                             """,
                DateTime.UtcNow,
                timeout.Id,
                timeout.Source);

            return Task.CompletedTask;
        }

        // delegate method called when a acknowledgement timeout is received from the consumer API
        // use this delegate to process a timeout if required.
        public Task DisplayConnectionStatus(ConnectionDetails connectionDetails)
        {
            _logger.LogInformation("""
                                   ---CONNECTION STATUS---
                                   
                                       Connection Status: {connectionDetails.CurrentStatus}
                                       Description: {connectionDetails.Description}
                                       Is Connection Error: {connectionDetails.IsError}
                                       Error Description: {connectionDetails.ErrorDescription}
                                   
                                   ---END OF CONNECTION STATUS---
                                   """,
                connectionDetails.CurrentStatus,
                connectionDetails.Description,
                connectionDetails.IsError,
                connectionDetails.ErrorDescription);

            return Task.CompletedTask;
        }

        /// <summary>
        /// This method is called when a dead lettered event is resubmitted. This is used to resubmit the event to the consumer API.
        /// </summary>
        /// <param name="startDate"></param>
        /// <param name="endDate"></param>
        /// <param name="eventType"></param>
        /// <param name="tenant"></param>
        /// <param name="tenantEnvironment"></param>
        /// <param name="sortBy"></param>
        /// <param name="pageNumber"></param>
        /// <param name="itemsPerPage"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task GetDeadLetteredEvents(string startDate, string endDate, string eventType, string tenant, string tenantEnvironment, DeadLetterSortOptions sortBy, int pageNumber, int itemsPerPage)
        {
            throw new NotImplementedException();
        }

        /// <summary>
        /// This method is called when a dead lettered event is resubmitted. This is used to resubmit the event to the consumer API.
        /// </summary>
        /// <param name="eventId"></param>
        /// <param name="eventSource"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public Task ResubmitDeadLetteredEvent(Guid eventId, string eventSource)
        {
            throw new NotImplementedException();
        }
    }
}
