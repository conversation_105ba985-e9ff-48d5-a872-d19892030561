namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// Application model for Compliance Detail
    /// </summary>
    public class ComplianceDetailModel
    {
        /// <summary>
        /// Management ID
        /// </summary>
        public string ManagementId { get; set; }

        /// <summary>
        /// Property ID
        /// </summary>
        public string PropertyId { get; set; }

        /// <summary>
        /// Compliance ID
        /// </summary>
        public string ComplianceId { get; set; }

        /// <summary>
        /// Compliance Name
        /// </summary>
        public string ComplianceName { get; set; }

        /// <summary>
        /// Expiry Date
        /// </summary>
        public DateTime ExpiryDate { get; set; }

        /// <summary>
        /// Serviced By
        /// </summary>
        public string ServicedBy { get; set; }

        /// <summary>
        /// Days until expiry (calculated field)
        /// </summary>
        public int DaysUntilExpiry => (ExpiryDate - DateTime.Now).Days;

        /// <summary>
        /// Is expired (calculated field)
        /// </summary>
        public bool IsExpired => ExpiryDate < DateTime.Now;
        public string Status { get; set; }
    }
} 