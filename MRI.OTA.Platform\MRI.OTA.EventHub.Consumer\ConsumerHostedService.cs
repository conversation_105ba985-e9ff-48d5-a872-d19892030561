namespace MRI.OTA.EventHub.Consumer
{
    internal sealed class ConsumerHostedService : BackgroundService
    {
        private readonly IServiceScopeFactory _serviceScopeFactory;

        public ConsumerHostedService(IServiceScopeFactory serviceScopeFactory)
        {
            _serviceScopeFactory = serviceScopeFactory;
        }

        protected override async Task ExecuteAsync(CancellationToken stoppingToken)
        {
            using var scope = _serviceScopeFactory.CreateScope();
            var consumerService = scope.ServiceProvider.GetRequiredService<IConsumerService>();
            await consumerService.ConnectAndHandle(stoppingToken);
        }
    }
}
