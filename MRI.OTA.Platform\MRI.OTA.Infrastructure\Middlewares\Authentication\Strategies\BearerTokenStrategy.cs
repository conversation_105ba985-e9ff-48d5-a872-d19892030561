using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;
using System.Security.Claims;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies
{
    /// <summary>
    /// Strategy for handling Bearer token authentication
    /// </summary>
    public class BearerTokenStrategy : IAuthenticationStrategy
    {
        private readonly RequestDelegate _next;
        private readonly IJwtTokenValidator _jwtTokenValidator;
        private readonly ILogger _logger;
        private readonly IResponseGenerator _responseGenerator;
        private readonly AuthenticationLogger _authLogger;
        private readonly string[] _clientCredentialEndpoints;
        private const int MaxTokenLength = 4096; // Prevent token-based DOS attacks

        /// <summary>
        /// Constructor for BearerTokenStrategy
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="jwtTokenValidator">The JWT token validator</param>
        /// <param name="logger">The logger</param>
        /// <param name="responseGenerator">The response generator</param>
        /// <param name="authLogger">The authentication logger</param>
        /// <param name="clientCredentialEndpoints">The client credential endpoints</param>
        public BearerTokenStrategy(
            RequestDelegate next,
            IJwtTokenValidator jwtTokenValidator,
            ILogger logger,
            IResponseGenerator responseGenerator,
            AuthenticationLogger authLogger,
            string[] clientCredentialEndpoints)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _jwtTokenValidator = jwtTokenValidator ?? throw new ArgumentNullException(nameof(jwtTokenValidator));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _responseGenerator = responseGenerator ?? throw new ArgumentNullException(nameof(responseGenerator));
            _authLogger = authLogger ?? throw new ArgumentNullException(nameof(authLogger));
            _clientCredentialEndpoints = clientCredentialEndpoints ?? throw new ArgumentNullException(nameof(clientCredentialEndpoints));
        }

        /// <summary>
        /// Determines if this strategy can handle the current request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>True if this strategy can handle the request, false otherwise</returns>
        public bool CanHandle(HttpContext context)
        {
            var authHeader = context.Request.Headers["Authorization"].ToString();
            return !string.IsNullOrEmpty(authHeader) && authHeader.StartsWith("Bearer ", StringComparison.OrdinalIgnoreCase);
        }

        /// <summary>
        /// Authenticates the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>A tuple indicating if the request was handled and authenticated</returns>
        public async Task<(bool Handled, bool Authenticated)> AuthenticateAsync(HttpContext context)
        {
            var authHeader = context.Request.Headers["Authorization"].ToString();
            var accessKey = context.Request.Headers["AccessKey"].ToString();
            var token = authHeader["Bearer ".Length..].Trim();

            if (string.IsNullOrEmpty(token) || token.Length > MaxTokenLength)
            {
                await _responseGenerator.WriteUnauthorizedResponse(context, "Invalid token format", StatusCodes.Status401Unauthorized);
                return (true, false);
            }

            try
            {
                var isClientCredentialsEndpoint = IsClientCredentialsEndpoint(context.Request.Path);
                var principal = await _jwtTokenValidator.ValidateToken(token, isClientCredentialsEndpoint);

                if (principal != null)
                {
                    context.User = principal;

                    if (isClientCredentialsEndpoint)
                    {
                        var identity = new ClaimsIdentity(new Claim[] {
                        new Claim(ClaimTypes.Name, accessKey),
                    }, "AccessKey");

                        context.User = new ClaimsPrincipal(identity);
                        _authLogger.LogSuccessfulClientCredentialsAuthentication(context, principal);
                    }
                    else
                    {
                        _authLogger.LogSuccessfulUserAuthentication(context, principal);
                    }

                    await _next(context);
                    return (true, true);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token validation failed");
                throw;
            }

            return (false, false);
        }

        /// <summary>
        /// Checks if the endpoint is a client credentials endpoint
        /// </summary>
        /// <param name="path">The path</param>
        /// <returns>True if the endpoint is a client credentials endpoint, false otherwise</returns>
        private bool IsClientCredentialsEndpoint(PathString path)
        {
            return _clientCredentialEndpoints.Any(endpoint =>
                path.StartsWithSegments(endpoint, StringComparison.OrdinalIgnoreCase));
        }
    }
}
