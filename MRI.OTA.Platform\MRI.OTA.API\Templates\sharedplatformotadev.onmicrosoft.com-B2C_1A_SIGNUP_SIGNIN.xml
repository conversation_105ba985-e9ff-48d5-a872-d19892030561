<TrustFrameworkPolicy xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns="http://schemas.microsoft.com/online/cpim/schemas/2013/06" PolicySchemaVersion="0.3.0.0" TenantId="sharedplatformotadev.onmicrosoft.com" TenantObjectId="218731ff-b7f8-457f-ae11-b1b13bfc738a" PolicyId="B2C_1A_signup_signin" PublicPolicyUri="http://sharedplatformotadev.onmicrosoft.com/B2C_1A_signup_signin">
  <BasePolicy>
    <TenantId>sharedplatformotadev.onmicrosoft.com</TenantId>
    <PolicyId>B2C_1A_TrustFrameworkExtensions</PolicyId>
  </BasePolicy>
  <BuildingBlocks>
    <ClaimsSchema>
      <ClaimType Id="newPassword">
        <DataType>string</DataType>
        <PredicateValidationReference Id="StrongPassword" />
      </ClaimType>
      <ClaimType Id="reenterPassword">
        <DataType>string</DataType>
        <PredicateValidationReference Id="StrongPassword" />
      </ClaimType>
      <ClaimType Id="identityProviderAccessToken">
        <DisplayName>Identity Provider Access Token</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>The access token returned by the identity provider.</UserHelpText>
      </ClaimType>
      <ClaimType Id="identityProvider">
        <DisplayName>Identity Provider</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Name of the identity provider</UserHelpText>
      </ClaimType>
      <ClaimType Id="newUser">
        <DisplayName>User is new</DisplayName>
        <DataType>boolean</DataType>
      </ClaimType>
      <ClaimType Id="objectId">
        <DisplayName>User's Object ID</DisplayName>
        <DataType>string</DataType>
        <DefaultPartnerClaimTypes>
          <Protocol Name="OAuth2" PartnerClaimType="oid" />
          <Protocol Name="OpenIdConnect" PartnerClaimType="oid" />
        </DefaultPartnerClaimTypes>
        <UserHelpText>Object identifier (ID) of the user object in Azure AD.</UserHelpText>
      </ClaimType>
      <ClaimType Id="emails">
        <DisplayName>Email Addresses</DisplayName>
        <DataType>stringCollection</DataType>
        <UserHelpText>Email addresses of the user.</UserHelpText>
      </ClaimType>
      <ClaimType Id="givenName">
        <DisplayName>Given Name</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Your given name (first name).</UserHelpText>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="city">
        <DisplayName>City</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Your city.</UserHelpText>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="email">
        <DisplayName>Email Address</DisplayName>
        <DataType>string</DataType>
        <DefaultPartnerClaimTypes>
          <Protocol Name="OAuth2" PartnerClaimType="email" />
          <Protocol Name="OpenIdConnect" PartnerClaimType="email" />
        </DefaultPartnerClaimTypes>
        <UserHelpText>Email address that can be used to contact you.</UserHelpText>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="trustFrameworkPolicy">
        <DisplayName>Trust framework policy name</DisplayName>
        <DataType>string</DataType>
      </ClaimType>
      <ClaimType Id="authenticationSource">
        <DisplayName>Authentication Source</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Specifies whether the user was authenticated at a local account or social identity provider.</UserHelpText>
      </ClaimType>
      <ClaimType Id="surname">
        <DisplayName>Surname</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Your surname (family name or last name).</UserHelpText>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="userPrincipalName">
        <DisplayName>UserPrincipalName</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Your user name as stored in the Azure Active Directory.</UserHelpText>
      </ClaimType>
      <ClaimType Id="displayName">
        <DisplayName>Display Name</DisplayName>
        <DataType>string</DataType>
        <UserHelpText>Your display name.</UserHelpText>
        <UserInputType>TextBox</UserInputType>
      </ClaimType>
      <ClaimType Id="otherMails">
        <DisplayName>Other Email Addresses</DisplayName>
        <DataType>stringCollection</DataType>
        <UserHelpText>Other email addresses associated with the user account.</UserHelpText>
      </ClaimType>
    </ClaimsSchema>
    <ContentDefinitions>
      <ContentDefinition Id="api.error">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:globalexception:1.2.6</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.blockminor">
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:1.2.0</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.signinandsignupwithpassword1.1">
        <LoadUri>https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/basic_new.html</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:unifiedssp:2.1.23</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.phoneSignUp">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.phoneInput">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.expiredpassword">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted2.1">
        <LoadUri>https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/basic_new.html</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.blockpagesignup">
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.blockpage">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.emailSignIn">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.phoneSignUpCollectEmailAddress">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.idpselections.signup1.1">
        <LoadUri>~/tenant/default/idpSelector.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:providerselection:1.2.6</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.localaccountlookup2.1">
        <LoadUri>https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/forgetPassword.html</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.localaccountpasswordchange2.1">
        <LoadUri>https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/changePassword.html</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.emailverify">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.progressiveprofile">
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:1.2.0</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.localaccountsignup2.1">
        <LoadUri>https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/basic_new.html</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.changePhoneNumberVerifyEmailAddress">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.newPhoneNumber">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.phonesuccess">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.phoneSignIn">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.phoneSignInCollectEmailAddress">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
      <ContentDefinition Id="api.phonefactor1.1">
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:multifactor:1.2.21</DataUri>
      </ContentDefinition>
      <ContentDefinition Id="api.selfasserted.totp">
        <LoadUri>~/tenant/default/selfAsserted.cshtml</LoadUri>
        <RecoveryUri>~/tenant/default/exception.cshtml</RecoveryUri>
        <DataUri>urn:com:microsoft:aad:b2c:elements:contract:selfasserted:2.1.35</DataUri>
        <Metadata>
          <Item Key="TemplateId">defaultTemplate</Item>
        </Metadata>
      </ContentDefinition>
    </ContentDefinitions>
  </BuildingBlocks>
  <ClaimsProviders>
    <ClaimsProvider>
      <DisplayName>AAD SSPR</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="AadSspr-SendCode">
          <DisplayName>AAD SSPR Send Code</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.AadSsprProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <EnabledForUserJourneys>Always</EnabledForUserJourneys>
        </TechnicalProfile>
        <TechnicalProfile Id="AadSspr-VerifyCode">
          <DisplayName>AAD SSPR Verify Code</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.AadSsprProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <EnabledForUserJourneys>Always</EnabledForUserJourneys>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Apple</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="Apple-Managed-OIDC">
          <DisplayName>Apple Managed OIDC</DisplayName>
          <Protocol Name="OpenIdConnect" />
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="identityProviderAccessToken" PartnerClaimType="{oauth2:access_token}" />
          </OutputClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="AppleManagedExchange">
          <DisplayName>Apple ID</DisplayName>
          <Protocol Name="OpenIdConnect" />
          <Metadata>
            <Item Key="ProviderName">apple</Item>
            <Item Key="authorization_endpoint">https://appleid.apple.com/auth/authorize</Item>
            <Item Key="AccessTokenEndpoint">https://appleid.apple.com/auth/token</Item>
            <Item Key="JWKS">https://appleid.apple.com/auth/keys</Item>
            <Item Key="issuer">https://appleid.apple.com</Item>
            <Item Key="scope">name email</Item>
            <Item Key="HttpBinding">POST</Item>
            <Item Key="response_types">code</Item>
            <Item Key="response_mode">form_post</Item>
            <Item Key="UsePolicyInRedirectUri">false</Item>
            <Item Key="client_id">YOUR_APPLE_SERVICE_ID</Item>
          </Metadata>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="identityProvider" DefaultValue="apple.com" />
            <OutputClaim ClaimTypeReferenceId="identityProviderAccessToken" PartnerClaimType="{oauth2:access_token}" />
            <OutputClaim ClaimTypeReferenceId="email" PartnerClaimType="email" />
            <OutputClaim ClaimTypeReferenceId="givenName" PartnerClaimType="given_name" />
            <OutputClaim ClaimTypeReferenceId="surname" PartnerClaimType="family_name" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="CreateRandomUPNUserName" />
            <OutputClaimsTransformation ReferenceId="CreateUserPrincipalName" />
            <OutputClaimsTransformation ReferenceId="CreateAlternativeSecurityId" />
            <OutputClaimsTransformation ReferenceId="CreateSubjectClaimFromAlternativeSecurityId" />
          </OutputClaimsTransformations>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-SocialLogin" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Azure Active Directory</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="AAD-ReadCommon">
          <DisplayName>AAD Read Common</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.AzureActiveDirectoryProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="givenName" />
            <OutputClaim ClaimTypeReferenceId="city" />
          </OutputClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="AAD-WriteCommon">
          <DisplayName>AAD Write Common</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.AzureActiveDirectoryProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <PersistedClaims>
            <PersistedClaim ClaimTypeReferenceId="givenName" />
          </PersistedClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="AAD-UserReadUsingEmailAddress">
          <DisplayName>AAD User Read Using Email Address</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.AzureActiveDirectoryProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="Operation">Read</Item>
            <Item Key="RaiseErrorIfClaimsPrincipalDoesNotExist">true</Item>
            <Item Key="UserMessageIfClaimsPrincipalDoesNotExist">An account could not be found for the provided user ID.</Item>
          </Metadata>
          <IncludeInSso>false</IncludeInSso>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" PartnerClaimType="signInNames.emailAddress" Required="true" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="localAccountAuthentication" />
            <OutputClaim ClaimTypeReferenceId="userPrincipalName" />
            <OutputClaim ClaimTypeReferenceId="displayName" />
            <OutputClaim ClaimTypeReferenceId="otherMails" />
            <OutputClaim ClaimTypeReferenceId="givenName" />
            <OutputClaim ClaimTypeReferenceId="surname" />
          </OutputClaims>
          <IncludeTechnicalProfile ReferenceId="AAD-Common" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Evaluate Block User For GDPR</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="SetFeatureDefaultValue">
          <DisplayName>Set Feature Default Values</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.ClaimsTransformationProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="isConditionalAccessOn" DefaultValue="false" />
            <OutputClaim ClaimTypeReferenceId="sendEmailforMfaRestfulEnabled" DefaultValue="false" />
            <OutputClaim ClaimTypeReferenceId="mfaEnabledV3" DefaultValue="false" />
            <OutputClaim ClaimTypeReferenceId="mfaEnroll" DefaultValue="false" />
            <OutputClaim ClaimTypeReferenceId="needToPerformMfa" DefaultValue="false" />
            <OutputClaim ClaimTypeReferenceId="collectEmailOnSignUp" DefaultValue="false" />
            <OutputClaim ClaimTypeReferenceId="collectEmailOnSignIn" DefaultValue="false" />
          </OutputClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Facebook</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="Facebook-OAUTH">
          <DisplayName>Facebook OAuth</DisplayName>
          <Protocol Name="OAuth2" />
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="identityProviderAccessToken" PartnerClaimType="{oauth2:access_token}" />
          </OutputClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="FacebookExchange">
          <DisplayName>Facebook</DisplayName>
          <Protocol Name="OAuth2" />
          <Metadata>
            <Item Key="ProviderName">facebook</Item>
            <Item Key="authorization_endpoint">https://www.facebook.com/dialog/oauth</Item>
            <Item Key="AccessTokenEndpoint">https://graph.facebook.com/oauth/access_token</Item>
            <Item Key="ClaimsEndpoint">https://graph.facebook.com/me?fields=id,first_name,last_name,name,email</Item>
            <Item Key="scope">email</Item>
            <Item Key="HttpBinding">POST</Item>
            <Item Key="response_types">code</Item>
            <Item Key="UsePolicyInRedirectUri">false</Item>
            <Item Key="client_id">YOUR_FACEBOOK_APP_ID</Item>
          </Metadata>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="identityProvider" DefaultValue="facebook.com" />
            <OutputClaim ClaimTypeReferenceId="identityProviderAccessToken" PartnerClaimType="{oauth2:access_token}" />
            <OutputClaim ClaimTypeReferenceId="email" PartnerClaimType="email" />
            <OutputClaim ClaimTypeReferenceId="givenName" PartnerClaimType="first_name" />
            <OutputClaim ClaimTypeReferenceId="surname" PartnerClaimType="last_name" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="CreateRandomUPNUserName" />
            <OutputClaimsTransformation ReferenceId="CreateUserPrincipalName" />
            <OutputClaimsTransformation ReferenceId="CreateAlternativeSecurityId" />
            <OutputClaimsTransformation ReferenceId="CreateSubjectClaimFromAlternativeSecurityId" />
          </OutputClaimsTransformations>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-SocialLogin" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Google</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="Google-OAUTH">
          <DisplayName>Google OAuth</DisplayName>
          <Protocol Name="OAuth2" />
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="identityProviderAccessToken" PartnerClaimType="{oauth2:access_token}" />
          </OutputClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="GoogleExchange">
          <DisplayName>Google</DisplayName>
          <Protocol Name="OAuth2" />
          <Metadata>
            <Item Key="ProviderName">google</Item>
            <Item Key="authorization_endpoint">https://accounts.google.com/o/oauth2/auth</Item>
            <Item Key="AccessTokenEndpoint">https://accounts.google.com/o/oauth2/token</Item>
            <Item Key="ClaimsEndpoint">https://www.googleapis.com/oauth2/v1/userinfo</Item>
            <Item Key="scope">email profile</Item>
            <Item Key="HttpBinding">POST</Item>
            <Item Key="response_types">code</Item>
            <Item Key="UsePolicyInRedirectUri">false</Item>
            <Item Key="client_id">YOUR_GOOGLE_CLIENT_ID</Item>
          </Metadata>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="identityProvider" DefaultValue="google.com" />
            <OutputClaim ClaimTypeReferenceId="identityProviderAccessToken" PartnerClaimType="{oauth2:access_token}" />
            <OutputClaim ClaimTypeReferenceId="email" PartnerClaimType="email" />
            <OutputClaim ClaimTypeReferenceId="givenName" PartnerClaimType="given_name" />
            <OutputClaim ClaimTypeReferenceId="surname" PartnerClaimType="family_name" />
          </OutputClaims>
          <OutputClaimsTransformations>
            <OutputClaimsTransformation ReferenceId="CreateRandomUPNUserName" />
            <OutputClaimsTransformation ReferenceId="CreateUserPrincipalName" />
            <OutputClaimsTransformation ReferenceId="CreateAlternativeSecurityId" />
            <OutputClaimsTransformation ReferenceId="CreateSubjectClaimFromAlternativeSecurityId" />
          </OutputClaimsTransformations>
          <UseTechnicalProfileForSessionManagement ReferenceId="SM-SocialLogin" />
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>One time password technical profiles</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="GenerateOtpEmailCustomizationApiConnector">
          <DisplayName>Generate OTP Email Customization</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.OneTimePasswordProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <EnabledForUserJourneys>Never</EnabledForUserJourneys>
        </TechnicalProfile>
        <TechnicalProfile Id="VerifyOtpEmailCustomizationApiConnector">
          <DisplayName>Verify OTP Email Customization</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.OneTimePasswordProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <EnabledForUserJourneys>Never</EnabledForUserJourneys>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>PhoneFactor</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="EmailFactor-Common">
          <DisplayName>Email Factor Common</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.PhoneFactorProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <EnabledForUserJourneys>Always</EnabledForUserJourneys>
        </TechnicalProfile>
        <TechnicalProfile Id="PhoneFactor-Common">
          <DisplayName>Phone Factor Common</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.PhoneFactorProtocolProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <EnabledForUserJourneys>OnClaimsExistence</EnabledForUserJourneys>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Self Asserted</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="SelfAsserted-Input">
          <DisplayName>Self Asserted Input</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="givenName" />
            <InputClaim ClaimTypeReferenceId="email" />
          </InputClaims>
          <DisplayClaims>
            <DisplayClaim ClaimTypeReferenceId="givenName" Required="true" />
            <DisplayClaim DisplayControlReferenceId="emailVerificationControl" />
          </DisplayClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="givenName" Required="true" />
            <OutputClaim ClaimTypeReferenceId="email" Required="true" />
          </OutputClaims>
        </TechnicalProfile>
        <TechnicalProfile Id="LocalAccountSigninEmailExchange">
          <DisplayName>Local Account Signin Email Exchange</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="SignUpTarget">SignUpWithLogonEmailExchange</Item>
            <Item Key="setting.operatingMode">Email</Item>
            <Item Key="ContentDefinitionReferenceId">api.signinandsignupwithpassword1.1</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="email" Required="true" />
            <OutputClaim ClaimTypeReferenceId="objectId" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="localAccountAuthentication" />
          </OutputClaims>
          <ValidationTechnicalProfiles>
            <ValidationTechnicalProfile ReferenceId="AAD-UserReadUsingEmailAddress" />
          </ValidationTechnicalProfiles>
        </TechnicalProfile>
        <TechnicalProfile Id="SignUpWithLogonEmailExchange">
          <DisplayName>Email signup</DisplayName>
          <Protocol Name="Proprietary" Handler="Web.TPEngine.Providers.SelfAssertedAttributeProvider, Web.TPEngine, Version=*******, Culture=neutral, PublicKeyToken=null" />
          <Metadata>
            <Item Key="ContentDefinitionReferenceId">api.localaccountsignup2.1</Item>
            <Item Key="language.button_continue">Create</Item>
          </Metadata>
          <InputClaims>
            <InputClaim ClaimTypeReferenceId="email" />
          </InputClaims>
          <OutputClaims>
            <OutputClaim ClaimTypeReferenceId="email" Required="true" />
            <OutputClaim ClaimTypeReferenceId="newPassword" Required="true" />
            <OutputClaim ClaimTypeReferenceId="reenterPassword" Required="true" />
            <OutputClaim ClaimTypeReferenceId="authenticationSource" DefaultValue="localAccountAuthentication" />
            <OutputClaim ClaimTypeReferenceId="newUser" DefaultValue="true" />
            <OutputClaim ClaimTypeReferenceId="givenName" />
            <OutputClaim ClaimTypeReferenceId="surname" />
          </OutputClaims>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
    <ClaimsProvider>
      <DisplayName>Token Issuer</DisplayName>
      <TechnicalProfiles>
        <TechnicalProfile Id="JwtIssuer">
          <Protocol Name="OpenIdConnect" />
          <Metadata>
            <Item Key="token_lifetime_secs">300</Item>
            <Item Key="id_token_lifetime_secs">300</Item>
            <Item Key="refresh_token_lifetime_secs">86400</Item>
            <Item Key="rolling_refresh_token_lifetime_secs">7776000</Item>
            <Item Key="IssuanceClaimPattern">AuthorityAndTenantGuid</Item>
            <Item Key="AuthenticationContextReferenceClaimPattern">None</Item>
          </Metadata>
        </TechnicalProfile>
      </TechnicalProfiles>
    </ClaimsProvider>
  </ClaimsProviders>
  <UserJourneys>
    <UserJourney Id="B2CSignUpOrSignInWithPassword_V3" DefaultCpimIssuerTechnicalProfileReferenceId="JwtIssuer">
      <OrchestrationSteps>
        <OrchestrationStep Order="1" Type="ClaimsProviderSelection" ContentDefinitionReferenceId="api.signinandsignupwithpassword1.1">
          <ClaimsProviderSelections>
            <ClaimsProviderSelection TargetClaimsExchangeId="GoogleExchange" />
            <ClaimsProviderSelection TargetClaimsExchangeId="FacebookExchange" />
            <ClaimsProviderSelection TargetClaimsExchangeId="AppleManagedExchange" />
            <ClaimsProviderSelection TargetClaimsExchangeId="LocalAccountSigninEmailExchange" />
          </ClaimsProviderSelections>
        </OrchestrationStep>
        <OrchestrationStep Order="2" Type="ClaimsExchange">
          <ClaimsExchanges>
            <ClaimsExchange Id="GoogleExchange" TechnicalProfileReferenceId="GoogleExchange" />
            <ClaimsExchange Id="FacebookExchange" TechnicalProfileReferenceId="FacebookExchange" />
            <ClaimsExchange Id="AppleManagedExchange" TechnicalProfileReferenceId="AppleManagedExchange" />
            <ClaimsExchange Id="LocalAccountSigninEmailExchange" TechnicalProfileReferenceId="LocalAccountSigninEmailExchange" />
          </ClaimsExchanges>
        </OrchestrationStep>
        <OrchestrationStep Order="3" Type="SendClaims" CpimIssuerTechnicalProfileReferenceId="JwtIssuer" />
      </OrchestrationSteps>
    </UserJourney>
  </UserJourneys>
  <RelyingParty>
    <DefaultUserJourney ReferenceId="B2CSignUpOrSignInWithPassword_V3" />
    <UserJourneyBehaviors>
      <SessionExpiryType>Rolling</SessionExpiryType>
      <SessionExpiryInSeconds>86400</SessionExpiryInSeconds>
    </UserJourneyBehaviors>
    <TechnicalProfile Id="PolicyProfile">
      <DisplayName>PolicyProfile</DisplayName>
      <Protocol Name="OpenIdConnect" />
      <OutputClaims>
        <OutputClaim ClaimTypeReferenceId="identityProvider" />
        <OutputClaim ClaimTypeReferenceId="identityProviderAccessToken" />
        <OutputClaim ClaimTypeReferenceId="objectId" />
        <OutputClaim ClaimTypeReferenceId="objectId" PartnerClaimType="sub" />
        <OutputClaim ClaimTypeReferenceId="trustFrameworkPolicy" Required="true" DefaultValue="{policy}" />
      </OutputClaims>
      <SubjectNamingInfo ClaimType="sub" />
    </TechnicalProfile>
  </RelyingParty>
</TrustFrameworkPolicy>