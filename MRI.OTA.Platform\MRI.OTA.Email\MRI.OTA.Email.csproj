﻿<Project Sdk="Microsoft.NET.Sdk.Worker">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
	<DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
  </PropertyGroup>

	<ItemGroup>
		<PackageReference Include="Azure.Communication.Email" />
		<PackageReference Include="Microsoft.Extensions.Configuration" />
		<PackageReference Include="Microsoft.Extensions.Configuration.Json" />
		<PackageReference Include="Microsoft.Extensions.Hosting" />
		<PackageReference Include="SendGrid" />
		<PackageReference Include="Serilog" />
		<PackageReference Include="Serilog.AspNetCore" />
		<PackageReference Include="Serilog.Extensions.Hosting" />
		<PackageReference Include="Serilog.Extensions.Logging" />
		<PackageReference Include="Serilog.Settings.Configuration" />
		<PackageReference Include="Serilog.Sinks.Async" />
		<PackageReference Include="Serilog.Sinks.Console" />
		<PackageReference Include="Newtonsoft.Json" />
	</ItemGroup>
</Project>
