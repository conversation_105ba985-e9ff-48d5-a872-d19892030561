using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Email;
using Xunit;

namespace MRI.OTA.UnitTestCases.Email
{
    public class UnitTestEmailServiceFactory
    {
        [Fact]
        public void Constructor_WithValidParameters_InitializesCorrectly()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<EmailServiceFactory>>();
            var configurationMock = new Mock<IConfiguration>();
            var loggerFactoryMock = new Mock<ILoggerFactory>();

            // Act
            var factory = new EmailServiceFactory(loggerMock.Object, configurationMock.Object, loggerFactoryMock.Object);

            // Assert
            Assert.NotNull(factory);
        }

        [Fact]
        public void EmailServiceFactory_ImplementsIEmailServiceFactory()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<EmailServiceFactory>>();
            var configurationMock = new Mock<IConfiguration>();
            var loggerFactoryMock = new Mock<ILoggerFactory>();

            // Act
            var factory = new EmailServiceFactory(loggerMock.Object, configurationMock.Object, loggerFactoryMock.Object);

            // Assert
            Assert.IsAssignableFrom<IEmailServiceFactory>(factory);
        }

        [Theory]
        [InlineData(1, 1)]
        [InlineData(0, 0)]
        [InlineData(-1, -1)]
        [InlineData(int.MaxValue, int.MaxValue)]
        public void GetEmailService_WithVariousAppAndProviderIds_ReturnsServiceOrNull(int appId, int providerId)
        {
            // Arrange
            var loggerMock = new Mock<ILogger<EmailServiceFactory>>();
            var configurationMock = new Mock<IConfiguration>();
            var loggerFactoryMock = new Mock<ILoggerFactory>();
            var factory = new EmailServiceFactory(loggerMock.Object, configurationMock.Object, loggerFactoryMock.Object);

            // Act
            var result = factory.GetEmailService(appId, providerId);

            // Assert
            // Result can be null or an email service depending on configuration
            Assert.True(result == null || result is IEmailService);
        }


    }
}
