﻿namespace MRI.OTA.Common.Models
{
    public class InvitationRequestModel
    {
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// AgencyId
        /// </summary>
        public string? AgencyId { get; set; }

        /// <summary>
        /// AgencyName
        /// </summary>
        public string? AgencyName { get; set; }

        /// <summary>
        /// AgencyLogo
        /// </summary>
        public string? AgencyLogo { get; set; }

        /// <summary>
        /// AgencyColour
        /// </summary>
        public string? AgencyColour { get; set; }

        /// <summary>
        /// PortfolioId
        /// </summary>
        public string? PortfolioId { get; set; }

        /// <summary>
        /// MigrateUser
        /// </summary>
        public bool MigrateUser { get; set; }
    }
}
