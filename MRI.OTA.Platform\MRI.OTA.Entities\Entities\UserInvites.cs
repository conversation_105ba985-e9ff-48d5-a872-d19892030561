﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities
{
    public class UserInvites
    {
        /// <summary>
        /// UserInvitesId
        /// </summary>
        [ExcludeColumn]
        public int UserInvitesId { get; set; }
        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }

        /// <summary>
        /// Name
        /// </summary>
        public string? Name { get; set; }

        /// <summary>
        /// InviteCode
        /// </summary>
        public string? InviteCode { get; set; }

        /// <summary>
        /// InviteLink
        /// </summary>
        public string? InviteLink { get; set; }
        /// <summary>
        /// IsActive
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// AgencyId
        /// </summary>
        public string? AgencyId { get; set; }

        /// <summary>
        /// AgencyName
        /// </summary>
        public string? AgencyName { get; set; }

        /// <summary>
        /// AgencyLogo
        /// </summary>
        public string? AgencyLogo { get; set; }

        /// <summary>
        /// AgencyColour
        /// </summary>
        public string? AgencyColour { get; set; }

        /// <summary>
        /// PortfolioId
        /// </summary>
        public string? PortfolioId { get; set; }

        /// <summary>
        /// datasourceId
        /// </summary>
        public int? DataSourceId { get; set; }

        /// <summary>
        /// MigrateUser
        /// </summary>
        public bool MigrateUser { get; set; }
    }
}
