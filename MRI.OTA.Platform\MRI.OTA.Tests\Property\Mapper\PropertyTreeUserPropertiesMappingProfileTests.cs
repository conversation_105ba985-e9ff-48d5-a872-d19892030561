﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.Property.Mapper
{
    public class PropertyTreeUserPropertiesMappingProfileTests
    {
        private readonly IMapper _mapper;

        public PropertyTreeUserPropertiesMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<PropertyTreeUserPropertiesMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_UserProperties_To_PropertyTreeInviteResponse()
        {
            // Arrange  
            var userProperties = new UserProperties
            {
                PropertyName = "Test Property",
                Suburb = "Test Suburb",
                PropertyRelationshipId = 1
            };



            // Act  
            var result = _mapper.Map<PropertyTreeInviteResponse>(userProperties);

            // Assert  
            Assert.Equal("Test Property", result.PropertyName);
            Assert.Equal("Test Suburb", result.PTSuburb);
            Assert.Equal("Owner", result.UserRelationshipToProperty);
        }

        [Fact]
        public void Should_Map_PropertyTreeInviteResponse_To_UserProperties()
        {
            // Arrange  
            var propertyTreeInviteResponse = new PropertyTreeInviteResponse
            {
                PropertyName = "Test Property",
                PTSuburb = "Test Suburb",
                UserRelationshipToProperty = "Owner"
            };

            var PropertyRelationShipDic = GetPropertyRelationshipDictionary();

            // Act  
            var result = _mapper.Map<UserProperties>(propertyTreeInviteResponse);

            // Assert  
            Assert.Equal("Test Property", result.PropertyName);
            Assert.Equal("Test Suburb", result.Suburb);
            Assert.Equal(1, result.PropertyRelationshipId);
        }

        [Fact]
        public void Should_Map_UserRelationshipToProperty_To_Other_When_Not_Found()
        {
            // Arrange  
            var userProperties = new UserProperties
            {
                PropertyName = "Test Property",
                Suburb = "Test Suburb",
                PropertyRelationshipId = 99
            };

            var PropertyRelationShipDic = GetPropertyRelationshipDictionary();

            // Act  
            var result = _mapper.Map<PropertyTreeInviteResponse>(userProperties);

            // Assert  
            Assert.Equal("Other", result.UserRelationshipToProperty);
        }

        private Dictionary<string, int> GetPropertyRelationshipDictionary()
        {
            return new Dictionary<string, int>
                   {
                       { "Owner", 1 },
                       { "Tenant", 2 }
                   };
        }
    }
}
