﻿using Asp.Versioning;
using System.Net.Mime;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using Swashbuckle.AspNetCore.Annotations;
using Microsoft.AspNetCore.Authorization;


namespace MRI.OTA.API.Controllers.DataSource.v1
{
    [Authorize(Policy = "ApiKeyPolicy")]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/data-source")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class DataSourceController : ControllerBase
    {
        private readonly IDataSourceService _dataSourceService;

        /// <summary>
        /// Constructor for dataSource controller
        /// </summary>
        /// <param name="dataSourceService"></param>
        public DataSourceController(IDataSourceService dataSourceService)
        {
            _dataSourceService = dataSourceService;
        }

        /// <summary>
        /// Get all data source
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        [HttpGet]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(DataSourceModel), description: "dataSource data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<DataSourceModel>> GetUserDataSource()
        {
            var accessKey = Request.Headers["AccessKey"].ToString();
            var accessSecret = Request.Headers["AccessSecret"].ToString();

            if (string.IsNullOrEmpty(accessKey) || string.IsNullOrEmpty(accessSecret))
            {
                return BadRequest(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status400BadRequest, new List<string> { MessagesConstants.ItemNotFound }));
            }

            var users = await _dataSourceService.GetUserDataSource(accessKey, accessSecret);

            if (users == null)
            {
                return NotFound(new ApiResponse<object>(false, MessagesConstants.ItemNotFound, data: null!, StatusCodes.Status404NotFound, new List<string> { MessagesConstants.ItemSpecifiedIdNotExists }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", users));
        }

        /// <summary>
        /// Update new dataSource
        /// </summary>
        /// <param name="dataSource"></param>
        /// <returns></returns>
        [HttpPut]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, description: "DataSource updated successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult> UpdateDataSource(DataSourceUpdateModel dataSource)
        {
            var accessKey = Request.Headers["AccessKey"].ToString();
            var accessSecret = Request.Headers["AccessSecret"].ToString();         
            
            var result = await _dataSourceService.UpdateDataSource(accessKey, accessSecret, dataSource);

            if (result == Constants.Success)
            {
                return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item updated successfully.", null!));
            }
            else
            {
                return BadRequest(new ApiResponse<object>(false, "Item not updated", null!, StatusCodes.Status400BadRequest, new List<string> { "The item could not be updated." }));
            }
        }
    }
}
