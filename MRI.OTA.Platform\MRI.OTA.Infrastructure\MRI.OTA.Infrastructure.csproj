﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\MRI.OTA.Common\MRI.OTA.Common.csproj" />
    <ProjectReference Include="..\MRI.OTA.Entities\MRI.OTA.DBCore.csproj" />
  </ItemGroup>
	<ItemGroup>
		<!-- Core ASP.NET and Dependency Injection -->
		<PackageReference Include="Microsoft.Extensions.DependencyInjection" />
		<PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" />
		<PackageReference Include="AutoMapper" />
		<PackageReference Include="Serilog" />
		<PackageReference Include="Microsoft.AspNetCore.Mvc.Core" />
		<PackageReference Include="Serilog.Extensions.Hosting" />
		<PackageReference Include="Serilog.Sinks.ApplicationInsights" />
		<PackageReference Include="UAParser" />
		<PackageReference Include="Microsoft.Extensions.Caching.StackExchangeRedis" />

	</ItemGroup>
</Project>
