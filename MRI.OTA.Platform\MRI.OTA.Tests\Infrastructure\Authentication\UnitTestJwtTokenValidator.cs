using System;
using System.Collections.Generic;
using System.IdentityModel.Tokens.Jwt;
using System.Runtime.Serialization;
using System.Security.Claims;
using System.Threading.Tasks;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using Moq;
using MRI.OTA.Core.Entities;
using MRI.OTA.Core.Repositories;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Infrastructure.Authentication;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Caching.Interfaces;
using Xunit;
using DataSourceEntity = MRI.OTA.Core.Entities.DataSource;

namespace MRI.OTA.UnitTestCases.Infrastructure.Authentication
{
    /// <summary>
    /// Unit tests for JwtTokenValidator class.
    /// Note: These tests focus on the core functionality that can be tested without complex HTTP mocking.
    /// The JwtTokenValidator class has dependencies on external HTTP calls for OpenID configuration,
    /// which makes comprehensive unit testing challenging without significant infrastructure setup.
    /// </summary>
    public class UnitTestJwtTokenValidator
    {
        [Fact]
        public void JwtTokenValidator_Interface_HasCorrectMethods()
        {
            // Arrange & Act
            var interfaceType = typeof(IJwtTokenValidator);

            // Assert
            Assert.NotNull(interfaceType.GetMethod("ValidateToken"));
            Assert.NotNull(interfaceType.GetMethod("GetClaims"));
            Assert.NotNull(interfaceType.GetMethod("ValidateAccessToken"));
        }

        [Fact]
        public void JwtTokenValidator_ImplementsInterface()
        {
            // Arrange & Act
            var implementationType = typeof(JwtTokenValidator);
            var interfaceType = typeof(IJwtTokenValidator);

            // Assert
            Assert.True(interfaceType.IsAssignableFrom(implementationType));
        }

        [Fact]
        public void GetClaims_Method_HasCorrectSignature()
        {
            // Arrange & Act
            var method = typeof(IJwtTokenValidator).GetMethod("GetClaims");

            // Assert
            Assert.NotNull(method);
            Assert.Equal(typeof(Task<List<Claim>>), method.ReturnType);
            var parameters = method.GetParameters();
            Assert.Single(parameters);
            Assert.Equal("token", parameters[0].Name);
            Assert.Equal(typeof(string), parameters[0].ParameterType);
        }

        [Fact]
        public void ValidateToken_Method_HasCorrectSignature()
        {
            // Arrange & Act
            var method = typeof(IJwtTokenValidator).GetMethod("ValidateToken");

            // Assert
            Assert.NotNull(method);
            Assert.Equal(typeof(Task<ClaimsPrincipal>), method.ReturnType);
            var parameters = method.GetParameters();
            Assert.Equal(2, parameters.Length);
            Assert.Equal("token", parameters[0].Name);
            Assert.Equal(typeof(string), parameters[0].ParameterType);
            Assert.Equal("isClientCredentials", parameters[1].Name);
            Assert.Equal(typeof(bool), parameters[1].ParameterType);
        }

        [Fact]
        public void ValidateAccessToken_Method_HasCorrectSignature()
        {
            // Arrange & Act
            var method = typeof(IJwtTokenValidator).GetMethod("ValidateAccessToken");

            // Assert
            Assert.NotNull(method);
            Assert.Equal(typeof(Task<bool>), method.ReturnType);
            var parameters = method.GetParameters();
            Assert.Equal(2, parameters.Length);
            Assert.Equal("accessKey", parameters[0].Name);
            Assert.Equal(typeof(string), parameters[0].ParameterType);
            Assert.Equal("accessToken", parameters[1].Name);
            Assert.Equal(typeof(string), parameters[1].ParameterType);
        }

        [Fact]
        public void JwtTokenValidator_Class_HasCorrectConstructor()
        {
            // Arrange & Act
            var constructors = typeof(JwtTokenValidator).GetConstructors();

            // Assert
            Assert.Single(constructors);
            var constructor = constructors[0];
            var parameters = constructor.GetParameters();
            Assert.Equal(4, parameters.Length);
            Assert.Equal(typeof(IConfiguration), parameters[0].ParameterType);
            Assert.Equal(typeof(ILogger<JwtTokenValidator>), parameters[1].ParameterType);
            Assert.Equal(typeof(IServiceProvider), parameters[2].ParameterType);
            Assert.Equal(typeof(ICacheService), parameters[3].ParameterType);
        }

        [Fact]
        public void JwtSecurityTokenHandler_CanCreateValidToken()
        {
            // Arrange
            var tokenHandler = new JwtSecurityTokenHandler();
            var claims = new List<Claim>
            {
                new Claim("sub", "user123"),
                new Claim("email", "<EMAIL>")
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(1),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes("this-is-a-test-key-for-jwt-token-validation-purposes-only")),
                    SecurityAlgorithms.HmacSha256Signature)
            };

            // Act
            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            // Assert
            Assert.NotNull(token);
            Assert.NotEmpty(tokenString);
            Assert.Contains(".", tokenString); // JWT tokens contain dots
        }

        [Fact]
        public void JwtSecurityTokenHandler_CanReadTokenClaims()
        {
            // Arrange
            var tokenHandler = new JwtSecurityTokenHandler();
            var claims = new List<Claim>
            {
                new Claim("sub", "user123"),
                new Claim("email", "<EMAIL>"),
                new Claim("custom", "value")
            };

            var tokenDescriptor = new SecurityTokenDescriptor
            {
                Subject = new ClaimsIdentity(claims),
                Expires = DateTime.UtcNow.AddHours(1),
                SigningCredentials = new SigningCredentials(
                    new SymmetricSecurityKey(System.Text.Encoding.UTF8.GetBytes("this-is-a-test-key-for-jwt-token-validation-purposes-only")),
                    SecurityAlgorithms.HmacSha256Signature)
            };

            var token = tokenHandler.CreateToken(tokenDescriptor);
            var tokenString = tokenHandler.WriteToken(token);

            // Act
            var jwtToken = tokenHandler.ReadJwtToken(tokenString);

            // Assert
            Assert.NotNull(jwtToken);
            Assert.Contains(jwtToken.Claims, c => c.Type == "sub" && c.Value == "user123");
            Assert.Contains(jwtToken.Claims, c => c.Type == "email" && c.Value == "<EMAIL>");
            Assert.Contains(jwtToken.Claims, c => c.Type == "custom" && c.Value == "value");
        }

        [Fact]
        public void JwtSecurityTokenHandler_ThrowsOnInvalidToken()
        {
            // Arrange
            var tokenHandler = new JwtSecurityTokenHandler();
            var invalidToken = "invalid.token.format";

            // Act & Assert
            Assert.Throws<ArgumentException>(() => tokenHandler.ReadJwtToken(invalidToken));
        }

        [Fact]
        public void DataSource_Entity_HasCorrectProperties()
        {
            // Arrange & Act
            var dataSource = new DataSourceEntity();

            // Assert
            Assert.NotNull(dataSource);
            // Verify the entity has the expected property
            var property = typeof(DataSourceEntity).GetProperty("DataSourceId");
            Assert.NotNull(property);
            Assert.Equal(typeof(int), property.PropertyType);
        }

        [Fact]
        public void CacheKey_Format_IsCorrect()
        {
            // Arrange
            var accessKey = "test-key-123";
            var expectedFormat = $"access_key:{accessKey}";

            // Act
            var actualFormat = $"access_key:{accessKey}";

            // Assert
            Assert.Equal(expectedFormat, actualFormat);
        }

        [Fact]
        public void ClaimTypes_AreCorrect()
        {
            // Arrange & Act & Assert
            Assert.Equal("sub", JwtRegisteredClaimNames.Sub);
            Assert.Equal("email", JwtRegisteredClaimNames.Email);
            Assert.Equal("jti", JwtRegisteredClaimNames.Jti);
        }
    }
}
