using System.ComponentModel.DataAnnotations;

namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// View model for Property Manager Information API response
    /// </summary>
    public class PropertyManagerViewModel
    {
        /// <summary>
        /// Property Manager Information ID
        /// </summary>
        public int PropertyManagerInformationId { get; set; }

        /// <summary>
        /// Property ID
        /// </summary>
        public int PropertyId { get; set; }

        /// <summary>
        /// Source Agency ID
        /// </summary>
        public string? SRCAgencyId { get; set; }

        /// <summary>
        /// Source Property ID
        /// </summary>
        public string? SRCPropertyId { get; set; }

        /// <summary>
        /// Source Management ID
        /// </summary>
        public string? SRCManagementId { get; set; }

        /// <summary>
        /// Type of management (e.g., Self-managed, Professional, etc.)
        /// </summary>
        public string? ManagementType { get; set; }

        /// <summary>
        /// Name of the agency managing the property
        /// </summary>
        public string? AgencyName { get; set; }

        /// <summary>
        /// Property manager's full name
        /// </summary>
        public string? PropertyManagerName { get; set; }

        /// <summary>
        /// Property manager's mobile phone number
        /// </summary>
        [Phone]
        public string? PropertyManagerMobile { get; set; }

        /// <summary>
        /// Property manager's office phone number
        /// </summary>
        [Phone]
        public string? PropertyManagerPhone { get; set; }

        /// <summary>
        /// Property manager's email address
        /// </summary>
        [EmailAddress]
        public string? PropertyManagerEmail { get; set; }

        /// <summary>
        /// Date when management authority starts
        /// </summary>
        public DateTime? AuthorityStartDate { get; set; }

        /// <summary>
        /// Date when management authority ends
        /// </summary>
        public DateTime? AuthorityEndDate { get; set; }

        /// <summary>
        /// Ownership details or type
        /// </summary>
        public string? Ownership { get; set; }

        /// <summary>
        /// Maximum expenditure limit for the property manager
        /// </summary>
        public decimal? ExpenditureLimit { get; set; }

        /// <summary>
        /// Additional notes about expenditure limits or restrictions
        /// </summary>
        public string? ExpenditureNotes { get; set; }

        /// <summary>
        /// Business name from Agency Details
        /// </summary>
        public string? BusinessName { get; set; }

        public string? BusinessRegisteredName { get; set; }
        public string? ContactRole { get; set; }
    }
} 