﻿using MRI.OTA.Application.Models;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.Application.Interfaces
{
    /// <summary>
    /// Interface for user business operations
    /// </summary>
    public interface IUserService : IBaseService<Users, UserModel, int>
    {
        public Task<List<UserModel>> GetAllUsers();

        public Task<int> CreateUser(AddUserModel user);

        public Task<ViewUserProfileModel> CreateUserProfile(IdTokenModel idTokenModel);

        public Task<int> UpdateUserTermsAndCondition(TermsConditionModel updateTermsConditionModel);

        public Task<int> UpdateUserProfileSettings(UserProfileSettingsModel user);

        public Task<bool> DeleteAccount(int userId, string providerId);
        public Task<int> AddUpdateUserDevice(UserDeviceDetail deviceModel);
        public Task<bool> DeleteUserDeviceInfo(string deviceId);
    }
}
