﻿using AutoMapper;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.Application.Mappers
{
    public class InviteUserMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for invite user mapper
        /// </summary>
        public InviteUserMappingProfile() : base("InviteUserMappingProfile")
        { 
            CreateMap<UserInvites, InvitationRequestModel>()
            .ReverseMap();
        }
    }
}
