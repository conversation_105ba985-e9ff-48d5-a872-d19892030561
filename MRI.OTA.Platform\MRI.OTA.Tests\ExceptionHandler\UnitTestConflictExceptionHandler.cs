﻿using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.ExceptionHandler;
using MRI.OTA.Common.Models;

namespace MRI.OTA.UnitTestCases.ExceptionHandler
{
    public class UnitTestConflictExceptionHandler
    {
        [Fact]
        public async Task TryHandleAsync_WithConflictException_ReturnsTrueAndWritesResponse()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<ConflictExceptionHandler>>();
            var handler = new ConflictExceptionHandler(loggerMock.Object);

            var context = new DefaultHttpContext();
            var responseStream = new MemoryStream();
            context.Response.Body = responseStream;

            var exception = new ConflictException("Test conflict");

            // Act
            var result = await handler.TryHandleAsync(context, exception, CancellationToken.None);

            // Assert
            Assert.True(result);
            Assert.Equal(StatusCodes.Status409Conflict, context.Response.StatusCode);
            Assert.Equal("application/json; charset=utf-8", context.Response.ContentType);

            responseStream.Seek(0, SeekOrigin.Begin);
            var responseBody = await new StreamReader(responseStream).ReadToEndAsync();

            // The handler serializes the ApiResponse and then writes it as a string, so we need to deserialize twice
            var jsonString = JsonSerializer.Deserialize<string>(responseBody);
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(jsonString!);

            Assert.NotNull(apiResponse);
            Assert.False(apiResponse!.Success);
            Assert.Equal("Conflict.", apiResponse.Message);
            Assert.Equal(StatusCodes.Status409Conflict, apiResponse.StatusCode);
            Assert.Contains("Test conflict", apiResponse.Errors[0]);
        }

        [Fact]
        public async Task TryHandleAsync_WithNonConflictException_ReturnsFalse()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<ConflictExceptionHandler>>();
            var handler = new ConflictExceptionHandler(loggerMock.Object);

            var context = new DefaultHttpContext();
            var exception = new Exception("Some other exception");

            // Act
            var result = await handler.TryHandleAsync(context, exception, CancellationToken.None);

            // Assert
            Assert.False(result);
        }
    }
}
