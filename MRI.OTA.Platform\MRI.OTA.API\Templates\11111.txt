<!DOCTYPE html>
<html lang="en-US">
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <meta name="ROBOTS" content="NONE, NOARCHIVE">
    <meta name="GOOGLEBOT" content="NOARCHIVE">
    <title>Sign in</title>
    
    <style>
        /* CSS Variables */
        :root {
            --primary-color: rgb(0, 122, 198);
            --primary-hover: rgb(0, 102, 178);
            --secondary-color: #6C7278;
            --text-color: #607184;
            --text-dark: #111827;
            --background-color: #f7f7f7;
            --border-color: #d1d5db;
            --error-color: #a61e0c;
            --input-text-font-size: 16px;
        }

        /* Import Fonts */
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
        @import url('https://fonts.googleapis.com/css2?family=Roboto:wght@300;400;500;700&display=swap');

        /* Base Styles */
        * {
            box-sizing: border-box;
        }

        body, html {
            margin: 0;
            padding: 0;
            height: 100%;
            background-color: #f7f7f7;
            font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif;
        }

        .container {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: white;
            border-radius: 12px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
            width: 100%;
            max-width: 400px;
            padding: 40px 30px;
        }

        /* Logo */
        .logo {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 30px;
            font-size: 18px;
            font-weight: 900;
            color: #333;
            font-family: 'Inter', sans-serif;
        }

        .logo-icon {
            width: 20px;
            height: 20px;
            border-radius: 4px;
            background: var(--primary-color);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: bold;
        }

        /* Hide B2C default elements */
        .heading,
        .intro {
            display: none !important;
        }

        /* B2C Container Overrides */
        #api {
            background: transparent !important;
            border: none !important;
            box-shadow: none !important;
            padding: 0 !important;
            margin: 0 !important;
        }

        /* Social login section header */
        .social-login-header {
            margin-bottom: 20px;
            color: var(--text-color);
            font-size: 14px;
            font-weight: 400;
        }

        /* Social Buttons Styling */
        .claims-provider-list-buttons {
            margin-bottom: 20px;
        }

        .claims-provider-list-buttons .intro {
            display: none !important;
        }

        .accountButton {
            display: flex !important;
            align-items: center !important;
            justify-content: flex-start !important;
            width: 100% !important;
            height: 48px !important;
            border: 1px solid var(--border-color) !important;
            border-radius: 8px !important;
            background: white !important;
            color: var(--text-dark) !important;
            font-size: 16px !important;
            font-weight: 500 !important;
            cursor: pointer !important;
            transition: all 0.2s ease !important;
            margin: 0 0 12px 0 !important;
            padding: 0 16px !important;
            text-decoration: none !important;
            box-sizing: border-box !important;
        }

        .accountButton:hover {
            border-color: var(--primary-color) !important;
            box-shadow: 0 2px 8px rgba(0, 122, 198, 0.1) !important;
        }

        /* Add icons to social buttons */
        #GoogleExchange::before {
            content: "";
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%234285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/><path fill="%2334A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/><path fill="%23FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/><path fill="%23EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 12px;
        }

        #FacebookExchange::before {
            content: "";
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path fill="%231877F2" d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 12px;
        }

        #AppleManagedExchange::before,
        #AppleExchange::before {
            content: "";
            width: 20px;
            height: 20px;
            background-image: url('data:image/svg+xml;charset=utf-8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M18.71 19.5c-.83 1.24-1.71 2.45-3.05 2.47-1.34.03-1.77-.79-3.29-.79-1.53 0-2 .77-3.27.82-1.31.05-2.3-1.32-3.14-2.53C4.25 17 2.94 12.45 4.7 9.39c.87-1.52 2.43-2.48 4.12-2.51 1.28-.02 2.5.87 3.29.87.78 0 2.26-1.07 3.81-.91.65.03 2.47.26 3.64 1.98-.09.06-2.17 1.28-2.15 3.81.03 3.02 2.65 4.03 2.68 4.04-.03.07-.42 1.44-1.38 2.83M13 3.5c.73-.83 1.94-1.46 2.94-1.5.13 1.17-.34 2.35-1.04 3.19-.69.85-1.83 1.51-2.95 1.42-.15-1.15.41-2.35 1.05-3.11z"/></svg>');
            background-size: contain;
            background-repeat: no-repeat;
            margin-right: 12px;
        }

        /* Divider */
        .divider {
            display: flex !important;
            align-items: center !important;
            margin: 24px 0 !important;
            color: var(--secondary-color) !important;
            font-size: 14px !important;
        }

        .divider h2 {
            display: flex !important;
            align-items: center !important;
            margin: 0 !important;
            font-size: 14px !important;
            font-weight: 400 !important;
            width: 100% !important;
        }

        .divider h2:before,
        .divider h2:after {
            content: '' !important;
            flex: 1 !important;
            height: 1px !important;
            background: var(--border-color) !important;
        }

        .divider h2:before {
            margin-right: 16px !important;
        }

        .divider h2:after {
            margin-left: 16px !important;
        }

        /* Local Account Form */
        .localAccount {
            margin-top: 0;
        }

        .localAccount .intro {
            display: none !important;
        }

        /* Form styling */
        .entry {
            margin-bottom: 20px;
        }

        .entry-item {
            margin-bottom: 16px !important;
        }

        .entry-item label {
            display: block !important;
            margin-bottom: 6px !important;
            color: var(--text-color) !important;
            font-size: 14px !important;
            font-weight: 400 !important;
        }

        .entry-item input,
        input[type=email],
        input[type=password],
        input[type=text] {
            width: 100% !important;
            height: 48px !important;
            padding: 12px 16px !important;
            border: 1px solid var(--border-color) !important;
            border-radius: 8px !important;
            font-size: var(--input-text-font-size) !important;
            font-family: inherit !important;
            transition: border-color 0.2s ease !important;
            box-sizing: border-box !important;
            background-color: #f9fafb !important;
        }

        .entry-item input:focus,
        input:focus {
            outline: none !important;
            border-color: var(--primary-color) !important;
            background-color: white !important;
        }

        /* Button styling */
        .buttons button,
        #next {
            width: 100% !important;
            height: 48px !important;
            background: var(--primary-color) !important;
            color: white !important;
            border: none !important;
            border-radius: 8px !important;
            font-size: 16px !important;
            font-weight: 600 !important;
            cursor: pointer !important;
            transition: background-color 0.2s ease !important;
            margin: 24px 0 !important;
        }

        .buttons button:hover,
        #next:hover {
            background: var(--primary-hover) !important;
        }

        /* Forgot password link styling */
        .password-reset-link {
            text-align: right !important;
            margin-top: 8px !important;
        }

        .password-reset-link a {
            color: var(--primary-color) !important;
            text-decoration: none !important;
            font-size: 14px !important;
        }

        /* Links */
        #forgotPassword,
        #createAccount {
            color: var(--primary-color) !important;
            text-decoration: none !important;
            font-size: 14px !important;
        }

        .create {
            text-align: center !important;
            margin-top: 24px !important;
        }

        .create p {
            color: var(--secondary-color) !important;
            font-size: 14px !important;
            margin: 0 !important;
        }

        .create a {
            color: var(--primary-color) !important;
            text-decoration: none !important;
            font-weight: 500 !important;
        }

        /* Error messages */
        .error {
            color: var(--error-color) !important;
            font-size: 12px !important;
            margin-top: 4px !important;
        }

        /* Responsive */
        @media (max-width: 480px) {
            .container {
                padding: 15px;
            }
            
            .login-card {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-card">
            <div class="logo">
                <div class="logo-icon">S</div>
                Super App
            </div>
            
            <!-- B2C will inject the form content here -->
            <div id="api"></div>
        </div>
    </div>

    <script>
        // Wait for the page to load and then customize the UI
        document.addEventListener('DOMContentLoaded', function() {
            // Function to customize social buttons
            function customizeSocialButtons() {
                // Update Google button text
                const googleButton = document.getElementById('GoogleExchange');
                if (googleButton) {
                    googleButton.textContent = 'Continue with Google';
                }

                // Update Facebook button text
                const facebookButton = document.getElementById('FacebookExchange');
                if (facebookButton) {
                    facebookButton.textContent = 'Continue with Facebook';
                }

                // Update Apple button text
                const appleButton = document.getElementById('AppleManagedExchange') || document.getElementById('AppleExchange');
                if (appleButton) {
                    appleButton.textContent = 'Continue with Apple';
                }
            }

            // Function to add "Log in using social accounts" header
            function addSocialHeader() {
                const socialButtonsContainer = document.querySelector('.claims-provider-list-buttons');
                if (socialButtonsContainer && !document.querySelector('.social-login-header')) {
                    const header = document.createElement('div');
                    header.className = 'social-login-header';
                    header.textContent = 'Log in using social accounts';
                    socialButtonsContainer.insertBefore(header, socialButtonsContainer.firstChild);
                }
            }

            // Function to customize form labels
            function customizeFormLabels() {
                // Update email label
                const emailLabel = document.querySelector('label[for="email"]');
                if (emailLabel) {
                    emailLabel.textContent = 'Email Address';
                }

                // Update password label
                const passwordLabel = document.querySelector('label[for="password"]');
                if (passwordLabel) {
                    passwordLabel.textContent = 'Password';
                }

                // Update email input placeholder
                const emailInput = document.getElementById('email');
                if (emailInput) {
                    emailInput.placeholder = 'Email Address';
                }

                // Update password input placeholder
                const passwordInput = document.getElementById('password');
                if (passwordInput) {
                    passwordInput.placeholder = 'Password';
                }
            }

            // Function to customize the login button
            function customizeLoginButton() {
                const loginButton = document.getElementById('next');
                if (loginButton) {
                    loginButton.textContent = 'Log In';
                }
            }

            // Function to customize the "OR" divider
            function customizeDivider() {
                const dividerH2 = document.querySelector('.divider h2');
                if (dividerH2) {
                    dividerH2.textContent = 'OR';
                }
            }

            // Function to customize the sign up text
            function customizeSignUpText() {
                const createAccountSection = document.querySelector('.create');
                if (createAccountSection) {
                    const paragraph = createAccountSection.querySelector('p');
                    if (paragraph) {
                        paragraph.innerHTML = "Don't have an account? <a href='#' id='createAccount'>Sign up now</a>";
                    }
                }
            }

            // Run customizations
            customizeSocialButtons();
            addSocialHeader();
            customizeFormLabels();
            customizeLoginButton();
            customizeDivider();
            customizeSignUpText();

            // Use MutationObserver to handle dynamic content changes
            const observer = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        customizeSocialButtons();
                        addSocialHeader();
                        customizeFormLabels();
                        customizeLoginButton();
                        customizeDivider();
                        customizeSignUpText();
                    }
                });
            });

            // Start observing
            const apiContainer = document.getElementById('api');
            if (apiContainer) {
                observer.observe(apiContainer, {
                    childList: true,
                    subtree: true
                });
            }
        });
    </script>
</body>
</html>

