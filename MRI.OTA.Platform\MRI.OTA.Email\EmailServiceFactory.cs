﻿namespace MRI.OTA.Email
{
    public class EmailServiceFactory : IEmailServiceFactory
    {
        private readonly ILogger<EmailServiceFactory> _logger;
        private readonly IConfiguration _configuration;
        private readonly ILoggerFactory _loggerFactory; // Add ILoggerFactory dependency

        public EmailServiceFactory(ILogger<EmailServiceFactory> logger, IConfiguration configuration, ILoggerFactory loggerFactory)
        {
            _logger = logger;
            _configuration = configuration;
            _loggerFactory = loggerFactory; // Initialize ILoggerFactory
        }

        public IEmailService GetEmailService(int appId, int providerId)
        {
            try
            {
                string providerName = "azureCom"; // Replace with actual provider name from the database  

                switch (providerName)
                {
                    case "sendGrid":
                        return new SendGridEmailService(
                            _loggerFactory.CreateLogger<SendGridEmailService>(), // Use ILoggerFactory to create logger
                            _configuration
                        );
                    case "azureCom":
                        return new AzureCommunicationEmailService(
                            _loggerFactory.CreateLogger<AzureCommunicationEmailService>(), // Use ILoggerFactory to create logger
                            _configuration
                        );
                    default:
                        throw new Exception("Unknown provider.");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError($"Exception occurred while sending email: {ex.Message}");
                return null;
            }
        }
    }
}
