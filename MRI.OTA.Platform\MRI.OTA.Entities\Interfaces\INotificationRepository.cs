﻿using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.DBCore.Interfaces
{
    public interface INotificationRepository : IBaseRepository<NotificationMaster, int>
    {
        public Task<List<NotificationMaster>> GetNotificationDetailByCategory(string[] categories);
        public Task<List<NotificationUserList>> GetUsersForNotificationBySRCId(string[] srcIds, string dataTypeName);

        public Task<int> AddUserNotificationDetails(UserNotificationDetails userNotificationDetails);

        public Task<int> UpdateUserNotification(int userId);
       
    }
}
