﻿namespace MRI.OTA.Email
{
    public class EmailMessage : IEmailMessage
    {
        public string Subject { get; set; }
        public string HtmlContent { get; set; }
        public string PlainTextContent { get; set; }

        public IList<EmailAddress> ToAddresses { get; }
        public IList<EmailAddress> CcAddresses { get; }
        public IList<EmailAddress> BccAddresses { get; }
        public EmailAddress FromAddress { get; set; }
        public EmailAddress ReplyToAddress { get; set; }

        public IList<EmailAttachment> Attachments { get; }

        public EmailMessage()
        {
            ToAddresses = new List<EmailAddress>();
            CcAddresses = new List<EmailAddress>();
            BccAddresses = new List<EmailAddress>();
            Attachments = new List<EmailAttachment>();
        }
    }
}
