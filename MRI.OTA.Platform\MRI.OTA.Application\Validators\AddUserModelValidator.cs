using FluentValidation;
using MRI.OTA.Application.Models;

namespace MRI.OTA.Application.Validators
{
    /// <summary>
    /// Validator for AddUserModel
    /// </summary>
    public class AddUserModelValidator : AbstractValidator<AddUserModel>
    {
        public AddUserModelValidator()
        {
            RuleFor(x => x.UserEmail)
                .NotEmpty()
                .WithMessage("User email is required")
                .EmailAddress()
                .WithMessage("Please provide a valid email address")
                .MaximumLength(255)
                .WithMessage("Email cannot exceed 255 characters");

            RuleFor(x => x.DisplayName)
                .NotEmpty()
                .WithMessage("Display name is required")
                .MaximumLength(100)
                .WithMessage("Display name cannot exceed 100 characters")
                .Matches(@"^[a-zA-Z\s\-'\.]+$")
                .WithMessage("Display name can only contain letters, spaces, hyphens, apostrophes, and periods");

            RuleFor(x => x.ProviderId)
                .NotEmpty()
                .WithMessage("Provider ID is required")
                .MaximumLength(255)
                .WithMessage("Provider ID cannot exceed 255 characters");
        }
    }
} 