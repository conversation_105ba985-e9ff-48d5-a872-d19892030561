﻿namespace MRI.OTA.Application.Models
{
    public class ManagementResponse
    {
        public string AgencyId { get; set; }
        public string OwnershipId { get; set; }
        public string ManagementId { get; set; }
        public string PropertyId { get; set; }
        public string? OwnershipName { get; set; }
        public string? ManagementContactName { get; set; }
        public string? ManagementContactNumber { get; set; }
        public string? ManagementContactEmail { get; set; }
        public string? ManagementContactRole { get; set; }
        public DateTime? AuthorityStartDate { get; set; }
        public DateTime? AuthorityEndDate { get; set; }
    }
}
