﻿namespace MRI.OTA.Common.Models
{
    public class NotificationRequestModel
    {
        /// <summary>
        /// The title of the notification
        /// </summary>
        public string? Title { get; set; }

        /// <summary>
        /// The body content of the notification
        /// </summary>
        public string? Body { get; set; }
        public string? Path { get; set; }

        /// <summary>
        /// The recipient's device token
        /// </summary>
        public string? DeviceToken { get; set; }

        /// <summary>
        /// UserEmail
        /// </summary>
        public string? UserEmail { get; set; }
        public int? UserId { get; set; }
        public int? NotificationId { get; set; }

        /// <summary>
        /// Additional data to send with the notification
        /// </summary>
        public Dictionary<string, string>? Data { get; set; }
    }
}
