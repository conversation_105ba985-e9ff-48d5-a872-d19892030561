﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.UnitTestCases.Invitations.Mapper
{
    public class InviteUserMappingProfileTests
    {
        private readonly IMapper _mapper;

        public InviteUserMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<InviteUserMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_UserInvites_To_InvitationRequestModel()
        {
            // Arrange  
            var userInvites = new UserInvites
            {
                UserInvitesId = 1,
                UserEmail = "<EMAIL>",
                InviteCode = "ABC123"
            };

            // Act  
            var result = _mapper.Map<InvitationRequestModel>(userInvites);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(userInvites.UserEmail, result.UserEmail);
        }

        [Fact]
        public void Should_Map_InvitationRequestModel_To_UserInvites()
        {
            // Arrange  
            var invitationRequestModel = new InvitationRequestModel
            {
                UserEmail = "<EMAIL>",
            };

            // Act  
            var result = _mapper.Map<UserInvites>(invitationRequestModel);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(invitationRequestModel.UserEmail, result.UserEmail);
        }
    }
}
