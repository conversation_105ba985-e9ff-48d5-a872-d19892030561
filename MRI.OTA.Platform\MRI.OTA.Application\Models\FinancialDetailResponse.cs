﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.Application.Models
{
    public class FinancialDetailResponse
    {
        public string AgencyId { get; set; }
        public string UserId { get; set; }
        public string ManagementId { get; set; }
        public string PropertyId { get; set; }
        public decimal? OwnershipTotalAvailableBalance { get; set; }
        public decimal? PropertyOutstandingFees { get; set; }
        public decimal? PropertyOutstandingInvoices { get; set; }
        public decimal? PropertyOverdueInvoices { get; set; }
        public decimal? LastPaymentAmount { get; set; }
        public string? Currency { get; set; }
        public DateTime? LastStatementDate { get; set; }
    }
}
