using Microsoft.AspNetCore.Mvc;

namespace MRI.OTA.Application.Models.Integration
{
    /// <summary>
    /// Bundle of related HTTP responses from PropertyTree API calls
    /// </summary>
    public class PropertyTreeResponseBundle
    {

        /// <summary>
        /// Ownership data response
        /// </summary>
        public IActionResult OwnershipResponse { get; set; }

        /// <summary>
        /// Tenant tenancies response
        /// </summary>
        public IActionResult TenanciesTenantResponse { get; set; }

        /// <summary>
        /// Owner tenancies response
        /// </summary>
        public IActionResult TenanciesOwnerResponse { get; set; }

        /// <summary>
        /// Maintenance data response
        /// </summary>
        public IActionResult MaintenanceResponse { get; set; }
        public IActionResult InspectionResponse { get; set; }
        public IActionResult ComplianceResponse { get; set; }
        public IActionResult FinancialResponse { get; set; }
        public IActionResult DocumentResponse { get; set; }

        public PropertyTreeResponseBundle(
            IActionResult ownershipResponse,
            IActionResult tenanciesTenantResponse,
            IActionResult tenanciesOwnerResponse,
            IActionResult maintenanceResponse,
            IActionResult inspectionResponse,
            IActionResult complianceResponse,
            IActionResult financialResponse,
            IActionResult documentResponse)
        {
            OwnershipResponse = ownershipResponse;
            TenanciesTenantResponse = tenanciesTenantResponse;
            TenanciesOwnerResponse = tenanciesOwnerResponse;
            MaintenanceResponse = maintenanceResponse;
            InspectionResponse = inspectionResponse;
            ComplianceResponse = complianceResponse;
            FinancialResponse = financialResponse;
            DocumentResponse = documentResponse;
        }
    }
}
