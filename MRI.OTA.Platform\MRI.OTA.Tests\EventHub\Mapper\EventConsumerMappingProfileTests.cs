﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.UnitTestCases.EventHub.Mapper
{
    public class EventConsumerMappingProfileTests
    {
        private readonly IMapper _mapper;

        public EventConsumerMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<EventConsumerMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_EventConsumer_To_EventConsumerModel()
        {
            // Arrange  
            var eventConsumer = new EventConsumer
            {
                EventConsumerId = "1",
                EventType = "TestConsumer"
            };

            // Act  
            var result = _mapper.Map<EventConsumerModel>(eventConsumer);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(eventConsumer.EventConsumerId, result.EventConsumerId);
            Assert.Equal(eventConsumer.EventType, result.EventType);
        }

        [Fact]
        public void Should_Map_EventConsumerModel_To_EventConsumer()
        {
            // Arrange  
            var eventConsumerModel = new EventConsumerModel
            {
                EventConsumerId = "1",
                EventType = "TestConsumer"
            };

            // Act  
            var result = _mapper.Map<EventConsumer>(eventConsumerModel);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(eventConsumerModel.EventConsumerId, result.EventConsumerId);
            Assert.Equal(eventConsumerModel.EventType, result.EventType);
        }
    }
}
