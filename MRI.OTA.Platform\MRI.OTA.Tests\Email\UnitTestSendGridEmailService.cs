using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Email;
using SendGrid;
using System.Net;
using System.Text;
using Xunit;
using SendGridEmailAddress = SendGrid.Helpers.Mail.EmailAddress;

namespace MRI.OTA.UnitTestCases.Email
{
    public class UnitTestSendGridEmailService
    {
        private readonly Mock<ILogger<SendGridEmailService>> _mockLogger;
        private readonly IConfiguration _configuration;

        public UnitTestSendGridEmailService()
        {
            _mockLogger = new Mock<ILogger<SendGridEmailService>>();
            _configuration = CreateMockConfiguration();
        }

        private IConfiguration CreateMockConfiguration()
        {
            var configurationBuilder = new ConfigurationBuilder();
            configurationBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SendGridSettings:ApiKey"] = "SG.test-api-key",
                ["SendGridSettings:DefaultFromEmail"] = "<EMAIL>",
                ["SendGridSettings:DefaultFromName"] = "Test Sender"
            });

            return configurationBuilder.Build();
        }

        private EmailMessage CreateTestEmailMessage()
        {
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test plain text content",
                HtmlContent = "<p>Test HTML content</p>",
                FromAddress = new EmailAddress("<EMAIL>", "Test Sender")
            };

            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Test Recipient"));
            return message;
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_WithValidParameters_InitializesCorrectly()
        {
            // Arrange & Act
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);

            // Assert
            Assert.NotNull(service);
            Assert.IsAssignableFrom<IEmailService>(service);
        }

        [Fact]
        public void Constructor_WithNullLogger_AcceptsNullLogger()
        {
            // Arrange & Act
            var service = new SendGridEmailService(null!, _configuration);

            // Assert
            Assert.NotNull(service);
            Assert.IsAssignableFrom<IEmailService>(service);
        }

        [Fact]
        public void Constructor_WithNullConfiguration_ThrowsArgumentNullException()
        {
            // Arrange & Act & Assert
            // The constructor will fail when trying to access configuration.GetValue
            Assert.Throws<ArgumentNullException>(() => new SendGridEmailService(_mockLogger.Object, null!));
        }

        [Fact]
        public void Constructor_WithMissingApiKey_ThrowsArgumentNullException()
        {
            // Arrange
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SendGridSettings:DefaultFromEmail"] = "<EMAIL>",
                ["SendGridSettings:DefaultFromName"] = "Test Sender"
                // Missing ApiKey - will be null
            });
            var config = configBuilder.Build();

            // Act & Assert
            // SendGridClient constructor will throw ArgumentNullException for null API key
            Assert.Throws<ArgumentNullException>(() => new SendGridEmailService(_mockLogger.Object, config));
        }

        #endregion

        #region Interface Implementation Tests

        [Fact]
        public void SendGridEmailService_ImplementsIEmailService()
        {
            // Arrange & Act
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);

            // Assert
            Assert.IsAssignableFrom<IEmailService>(service);
        }

        [Fact]
        public void IEmailService_HasCorrectMethods()
        {
            // Arrange & Act
            var interfaceType = typeof(IEmailService);

            // Assert
            Assert.NotNull(interfaceType.GetMethod("SendEmailAsync"));
            var method = interfaceType.GetMethod("SendEmailAsync");
            Assert.Equal(typeof(Task<bool>), method!.ReturnType);
            var parameters = method.GetParameters();
            Assert.Single(parameters);
            Assert.Equal(typeof(EmailMessage), parameters[0].ParameterType);
        }

        #endregion

        #region SendEmailAsync Tests - Basic Scenarios

        [Fact]
        public async Task SendEmailAsync_WithNullMessage_ThrowsNullReferenceException()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);

            // Act & Assert
            await Assert.ThrowsAsync<NullReferenceException>(() => service.SendEmailAsync(null!));
        }

        [Fact]
        public async Task SendEmailAsync_WithValidMessage_ReturnsResult()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            // Act
            // This will likely fail due to invalid API key, but we're testing the service behavior
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region Email Message Construction Tests

        [Fact]
        public async Task SendEmailAsync_WithEmptyToAddresses_ReturnsResult()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content"
                // No ToAddresses added
            };

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithNullFromAddress_UsesDefaultFromConfiguration()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content",
                FromAddress = null! // Should use default from configuration
            };
            message.ToAddresses.Add(new EmailAddress("<EMAIL>"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithReplyToAddress_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.ReplyToAddress = new EmailAddress("<EMAIL>", "Reply To User");

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithCcAndBccAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.CcAddresses.Add(new EmailAddress("<EMAIL>", "CC Recipient"));
            message.BccAddresses.Add(new EmailAddress("<EMAIL>", "BCC Recipient"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithAttachments_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            var attachment = new EmailAttachment("test.txt", Encoding.UTF8.GetBytes("Test content"), "text/plain");
            message.Attachments.Add(attachment);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region Edge Case Tests

        [Fact]
        public async Task SendEmailAsync_WithEmptySubject_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.Subject = "";

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithNullSubject_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.Subject = null!;

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithNullPlainTextContent_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.PlainTextContent = null!;

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithNullHtmlContent_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.HtmlContent = null!;

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithEmptyHtmlContent_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.HtmlContent = "";

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region Configuration Tests

        [Fact]
        public async Task SendEmailAsync_WithMissingDefaultFromEmail_HandlesCorrectly()
        {
            // Arrange
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SendGridSettings:ApiKey"] = "SG.test-api-key",
                ["SendGridSettings:DefaultFromName"] = "Test Sender"
                // Missing DefaultFromEmail
            });
            var config = configBuilder.Build();

            var service = new SendGridEmailService(_mockLogger.Object, config);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content",
                FromAddress = null! // Should use default from configuration, but it's missing
            };
            message.ToAddresses.Add(new EmailAddress("<EMAIL>"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail
        }

        [Fact]
        public async Task SendEmailAsync_WithMissingDefaultFromName_HandlesCorrectly()
        {
            // Arrange
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SendGridSettings:ApiKey"] = "SG.test-api-key",
                ["SendGridSettings:DefaultFromEmail"] = "<EMAIL>"
                // Missing DefaultFromName
            });
            var config = configBuilder.Build();

            var service = new SendGridEmailService(_mockLogger.Object, config);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content",
                FromAddress = null! // Should use default from configuration
            };
            message.ToAddresses.Add(new EmailAddress("<EMAIL>"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region Multiple Recipients Tests

        [Fact]
        public async Task SendEmailAsync_WithMultipleToAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Test Recipient 2"));
            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Test Recipient 3"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithMultipleCcAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.CcAddresses.Add(new EmailAddress("<EMAIL>", "CC Recipient 1"));
            message.CcAddresses.Add(new EmailAddress("<EMAIL>", "CC Recipient 2"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithMultipleBccAddresses_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.BccAddresses.Add(new EmailAddress("<EMAIL>", "BCC Recipient 1"));
            message.BccAddresses.Add(new EmailAddress("<EMAIL>", "BCC Recipient 2"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region Attachment Tests

        [Fact]
        public async Task SendEmailAsync_WithMultipleAttachments_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            var attachment1 = new EmailAttachment("document.pdf", Encoding.UTF8.GetBytes("PDF content"), "application/pdf");
            var attachment2 = new EmailAttachment("image.jpg", Encoding.UTF8.GetBytes("Image content"), "image/jpeg");
            var attachment3 = new EmailAttachment("data.json", Encoding.UTF8.GetBytes("{\"test\": \"data\"}"), "application/json");

            message.Attachments.Add(attachment1);
            message.Attachments.Add(attachment2);
            message.Attachments.Add(attachment3);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithLargeAttachment_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            // Create a large attachment (1MB)
            var largeContent = new byte[1024 * 1024];
            for (int i = 0; i < largeContent.Length; i++)
            {
                largeContent[i] = (byte)(i % 256);
            }

            var attachment = new EmailAttachment("large_file.bin", largeContent, "application/octet-stream");
            message.Attachments.Add(attachment);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithEmptyAttachment_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            var attachment = new EmailAttachment("empty.txt", new byte[0], "text/plain");
            message.Attachments.Add(attachment);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        [Fact]
        public async Task SendEmailAsync_WithSpecialCharactersInAttachmentName_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            var attachment = new EmailAttachment("file with spaces & special chars.txt",
                Encoding.UTF8.GetBytes("Test content"), "text/plain");
            message.Attachments.Add(attachment);

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region Content Type Tests

        [Theory]
        [InlineData("Plain text only", null)]
        [InlineData(null, "<p>HTML only</p>")]
        [InlineData("Plain text content", "<p>HTML content</p>")]
        [InlineData("", "")]
        [InlineData("", "<p>HTML with empty plain text</p>")]
        [InlineData("Plain text with empty HTML", "")]
        public async Task SendEmailAsync_WithVariousContentTypes_HandlesCorrectly(string? plainText, string? htmlContent)
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();
            message.PlainTextContent = plainText!;
            message.HtmlContent = htmlContent!;

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region Email Address Format Tests

        [Theory]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        [InlineData("<EMAIL>")]
        public async Task SendEmailAsync_WithVariousEmailFormats_HandlesCorrectly(string emailAddress)
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = new EmailMessage
            {
                Subject = "Test Subject",
                PlainTextContent = "Test content",
                FromAddress = new EmailAddress("<EMAIL>")
            };
            message.ToAddresses.Add(new EmailAddress(emailAddress));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion

        #region SendGrid Specific Tests

        [Fact]
        public async Task SendEmailAsync_WithInvalidApiKey_ReturnsFalse()
        {
            // Arrange
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SendGridSettings:ApiKey"] = "invalid-api-key",
                ["SendGridSettings:DefaultFromEmail"] = "<EMAIL>",
                ["SendGridSettings:DefaultFromName"] = "Test Sender"
            });
            var config = configBuilder.Build();

            var service = new SendGridEmailService(_mockLogger.Object, config);
            var message = CreateTestEmailMessage();

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with invalid API key
        }

        [Fact]
        public void SendEmailAsync_WithNullApiKey_ThrowsArgumentNullExceptionInConstructor()
        {
            // Arrange
            var configBuilder = new ConfigurationBuilder();
            configBuilder.AddInMemoryCollection(new Dictionary<string, string?>
            {
                ["SendGridSettings:DefaultFromEmail"] = "<EMAIL>",
                ["SendGridSettings:DefaultFromName"] = "Test Sender"
                // No ApiKey
            });
            var config = configBuilder.Build();

            // Act & Assert
            // The service constructor should fail with null API key
            Assert.Throws<ArgumentNullException>(() => new SendGridEmailService(_mockLogger.Object, config));
        }

        #endregion

        #region Response Status Tests

        [Fact]
        public async Task SendEmailAsync_ReturnsStatusBasedOnResponse()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = CreateTestEmailMessage();

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            // The result should be based on response.IsSuccessStatusCode
            // With test API key, this should be false
            Assert.IsType<bool>(result);
        }

        #endregion

        #region Complex Scenario Tests

        [Fact]
        public async Task SendEmailAsync_WithComplexMessage_HandlesCorrectly()
        {
            // Arrange
            var service = new SendGridEmailService(_mockLogger.Object, _configuration);
            var message = new EmailMessage
            {
                Subject = "Complex Test Email with Special Characters: àáâãäåæçèéêë",
                PlainTextContent = "This is a complex email with:\n- Multiple lines\n- Special characters: àáâãäåæçèéêë\n- Numbers: 12345",
                HtmlContent = "<html><body><h1>Complex HTML Email</h1><p>With <strong>formatting</strong> and <em>styles</em></p><ul><li>Item 1</li><li>Item 2</li></ul></body></html>",
                FromAddress = new EmailAddress("<EMAIL>", "Complex Sender Name"),
                ReplyToAddress = new EmailAddress("<EMAIL>", "Reply Handler")
            };

            // Add multiple recipients
            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Recipient One"));
            message.ToAddresses.Add(new EmailAddress("<EMAIL>", "Recipient Two"));
            message.CcAddresses.Add(new EmailAddress("<EMAIL>", "CC Recipient"));
            message.BccAddresses.Add(new EmailAddress("<EMAIL>", "BCC Recipient"));

            // Add multiple attachments
            message.Attachments.Add(new EmailAttachment("text-file.txt", Encoding.UTF8.GetBytes("Text file content"), "text/plain"));
            message.Attachments.Add(new EmailAttachment("json-data.json", Encoding.UTF8.GetBytes("{\"key\": \"value\"}"), "application/json"));

            // Act
            var result = await service.SendEmailAsync(message);

            // Assert
            Assert.False(result); // Expected to fail with test API key
        }

        #endregion
    }
}
