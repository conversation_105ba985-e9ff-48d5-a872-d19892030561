﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Text.Json;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.ExceptionHandler;
using MRI.OTA.Common.Models;

namespace MRI.OTA.UnitTestCases.ExceptionHandler
{
    public class UnitTestNotFoundExceptionHandler
    {
        [Fact]
        public async Task TryHandleAsync_WithNotFoundException_ReturnsTrueAndWritesResponse()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<NotFoundExceptionHandler>>();
            var handler = new NotFoundExceptionHandler(loggerMock.Object);
            var context = new DefaultHttpContext();
            var exception = new NotFoundException("Resource not found");

            // Act
            var result = await handler.TryHandleAsync(context, exception, CancellationToken.None);

            // Assert
            Assert.True(result);
            Assert.Equal("application/json; charset=utf-8", context.Response.ContentType);
            Assert.Equal(StatusCodes.Status404NotFound, context.Response.StatusCode);
        }

        [Fact]
        public async Task TryHandleAsync_WithOtherException_ReturnsFalse()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<NotFoundExceptionHandler>>();
            var handler = new NotFoundExceptionHandler(loggerMock.Object);
            var context = new DefaultHttpContext();
            var exception = new Exception("Some other error");

            // Act
            var result = await handler.TryHandleAsync(context, exception, CancellationToken.None);

            // Assert
            Assert.False(result);
            Assert.Equal(200, context.Response.StatusCode); // Default status code

        }
    }
}
