﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class TenanciesPropertyManagerDetails
    {
        [ExcludeColumn]
        public int PropertyManagerInformationId { get; set; }
        public string SRCAgencyId { get; set; }
        public string SRCManagementId { get; set; }
        public string SRCPropertyId { get; set; }
        public string SRCOwnershipId { get; set; }
        public int PropertyId { get; set; }
        public string PropertyManagerName { get; set; }
        public string PropertyManagerMobile { get; set; }
        public string PropertyManagerEmail { get; set; }
        public string ContactRole { get; set; }
        public string? Ownership { get; set; } = null;
        public DateTime? AuthorityStartDate { get; set; } = null;
        public DateTime? AuthorityEndDate { get; set; } = null;
    }
}
