using System.Data;

namespace MRI.OTA.Common.Interfaces
{
    /// <summary>
    /// Wrapper interface for Dapper operations
    /// </summary>
    public interface IDapperWrapper
    {
        /// <summary>
        /// Executes a query and maps the result to a list of entities
        /// </summary>
        Task<IEnumerable<T>> QueryAsync<T>(IDbConnection connection, string sql, object? param = null, IDbTransaction? transaction = null, int? commandTimeout = null, CommandType? commandType = null);
        
        /// <summary>
        /// Executes a query and maps the result to a single entity or default
        /// </summary>
        Task<T> QuerySingleOrDefaultAsync<T>(IDbConnection connection, string sql, object? param = null, IDbTransaction? transaction = null, int? commandTimeout = null, CommandType? commandType = null);

        /// <summary>
        /// Executes a query and maps the result to a list of entities
        /// </summary>
        /// <returns></returns>
        Task<T> QueryFirstOrDefaultAsync<T>(IDbConnection connection, string sql, object? param = null, IDbTransaction? transaction = null, int? commandTimeout = null, CommandType? commandType = null);

        /// <summary>
        /// Executes a command and returns the number of rows affected
        /// </summary>
        Task<int> ExecuteAsync(IDbConnection connection, string sql, object? param = null, IDbTransaction? transaction = null, int? commandTimeout = null, CommandType? commandType = null);
        
        /// <summary>
        /// Executes a command and returns the first cell as a scalar value
        /// </summary>
        Task<T> ExecuteScalarAsync<T>(IDbConnection connection, string sql, object? param = null, IDbTransaction? transaction = null, int? commandTimeout = null, CommandType? commandType = null);
    }
}