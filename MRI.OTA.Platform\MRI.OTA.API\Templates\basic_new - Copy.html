<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Super App Login</title>
  <style>
    body {
      background: #23272f;
      min-height: 100vh;
      margin: 0;
      font-family: 'Segoe UI', Arial, sans-serif;
      display: flex;
      align-items: center;
      justify-content: center;
    }
    .login-card {
      background: #fff;
      border-radius: 24px;
      box-shadow: 0 8px 32px rgba(0,0,0,0.10);
      width: 100%;
      max-width: 370px;
      padding: 36px 24px 18px 24px;
      margin: 32px 0;
      display: flex;
      flex-direction: column;
      align-items: stretch;
    }
    .logo-row {
      display: flex;
      align-items: center;
      margin-bottom: 18px;
    }
    .logo-icon {
      width: 28px;
      height: 28px;
      background: #1976d2;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      color: #fff;
      margin-right: 10px;
    }
    .logo-text {
      font-size: 20px;
      font-weight: 600;
      color: #222;
    }
    .main-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin: 0 0 6px 0;
      color: #222;
    }
    .subtitle {
      color: #666;
      font-size: 15px;
      margin-bottom: 22px;
      font-weight: 400;
    }
    .social-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      width: 100%;
      padding: 13px 0;
      margin-bottom: 16px;
      border: 1.5px solid #e0e0e0;
      border-radius: 24px;
      background: #fff;
      font-size: 16px;
      font-weight: 500;
      color: #222;
      cursor: pointer;
      transition: border-color 0.2s, background 0.2s;
      outline: none;
      gap: 12px;
    }
    .social-btn:last-child { margin-bottom: 0; }
    .social-btn:hover { border-color: #bdbdbd; background: #f7fafd; }
    .social-icon {
      width: 22px;
      height: 22px;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
      border-radius: 50%;
      background: #fff;
    }
    .google { color: #ea4335; }
    .facebook { color: #1877f2; }
    .apple { color: #111; font-size: 20px; }
    .divider {
      display: flex;
      align-items: center;
      text-align: center;
      margin: 22px 0 18px 0;
      color: #aaa;
      font-size: 14px;
    }
    .divider::before, .divider::after {
      content: '';
      flex: 1;
      border-bottom: 1px solid #e0e0e0;
    }
    .divider:not(:empty)::before { margin-right: 10px; }
    .divider:not(:empty)::after { margin-left: 10px; }
    .form-label {
      font-size: 14px;
      color: #444;
      margin-bottom: 4px;
      font-weight: 500;
      display: block;
    }
    .form-input {
      width: 100%;
      padding: 13px 14px;
      border: 1.5px solid #e0e0e0;
      border-radius: 24px;
      font-size: 15px;
      margin-bottom: 16px;
      background: #fafbfc;
      transition: border-color 0.2s;
    }
    .form-input:focus { border-color: #1976d2; outline: none; }
    .forgot-link {
      display: block;
      text-align: right;
      color: #1976d2;
      font-size: 13px;
      text-decoration: none;
      margin-bottom: 18px;
      margin-top: -10px;
    }
    .forgot-link:hover { text-decoration: underline; }
    .login-btn {
      width: 100%;
      padding: 13px 0;
      background: #e6e9ec;
      color: #bdbdbd;
      border: none;
      border-radius: 24px;
      font-size: 16px;
      font-weight: 600;
      margin-bottom: 18px;
      cursor: not-allowed;
    }
    .signup-row {
      text-align: center;
      font-size: 14px;
      color: #888;
      margin-top: 8px;
    }
    .signup-row a {
      color: #1976d2;
      text-decoration: none;
      font-weight: 500;
      margin-left: 4px;
    }
    .signup-row a:hover { text-decoration: underline; }
    @media (max-width: 480px) {
      .login-card { padding: 18px 4vw 8px 4vw; }
      .main-title { font-size: 1.2rem; }
    }
  </style>
</head>
<body>
  <div class="login-card">
    <div class="logo-row">
      <div class="logo-icon">🏠</div>
      <div class="logo-text">Super App</div>
    </div>
    <div class="main-title">Log in to your Account</div>
    <div class="subtitle">Enter your email and password to log in</div>
    <button class="social-btn"><span class="social-icon google">G</span>Continue with Google</button>
    <button class="social-btn"><span class="social-icon facebook">f</span>Continue with Facebook</button>
    <button class="social-btn"><span class="social-icon apple"></span>Continue with Apple</button>
    <div class="divider">Or</div>
    <form>
      <label class="form-label" for="email">Email</label>
      <input class="form-input" id="email" type="email" placeholder="Email address" required>
      <label class="form-label" for="password">Password</label>
      <input class="form-input" id="password" type="password" placeholder="Password" required>
      <a href="#" class="forgot-link">Forgot Password ?</a>
      <button class="login-btn" type="submit" disabled>Log In</button>
    </form>
    <div class="signup-row">
      Don't have an account?
      <a href="#">Sign Up</a>
    </div>
  </div>
</body>
</html>