using System.Collections.Generic;

namespace MRI.OTA.Integration.Models
{
    public class ProxyRequestModel
    {
        private object body;

        public required string FullUrl { get; set; } // Complete URL including query parameters
        public string? Method { get; set; } = "GET"; // Default HTTP method
        public object? Body { get => body; set => body = value; } // Request body for POST/PUT requests
        public required string RequestId { get; set; } // Unique request identifier for tracking
        public Dictionary<string, string>? Headers { get; set; } // HTTP headers for the request
    }
}
