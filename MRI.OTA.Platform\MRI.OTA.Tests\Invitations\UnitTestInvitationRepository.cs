using System.Data;
using System.Text;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Repositories;

namespace MRI.OTA.UnitTestCases.Invitations
{
    public class UnitTestInvitationRepository
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<InvitationRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly InvitationRepository _repository;

        public UnitTestInvitationRepository()
        {
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<InvitationRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);

            _repository = new InvitationRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object);
        }        [Fact]
        public async Task GetInvitationDetailsById_Should_Return_Invitation_Details_When_Found()
        {
            // Arrange
            var inviteCode = "test-invite-code";
            var expectedInvitation = new ViewUserInvites
            {
                UserEmail = "<EMAIL>",
                Name = "Test User",
                IsActive = true,
                DataSourceId = 1,
                PortfolioId = "portfolio123",
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "logo.png",
                AgencyColour = "#FF0000"
            };

            var expectedQuery = new StringBuilder();
            expectedQuery.Append($"SELECT UI.UserEmail,UI.Name,UI.IsActive,UI.DataSourceId,UI.PortfolioId,UI.AgencyId,UI.AgencyName,UI.AgencyLogo,UI.AgencyColour");
            expectedQuery.Append($" FROM {Constants.UserInvitesTableName} UI ");
            expectedQuery.Append($" WHERE InviteCode = @InviteCode AND IsActive = 1");

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<ViewUserInvites>(
                    It.IsAny<IDbConnection>(), 
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("InviteCode") != null && 
                        p.GetType().GetProperty("InviteCode")!.GetValue(p)!.ToString() == inviteCode),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedInvitation);

            // Act
            var result = await _repository.GetInvitationDetailsById(inviteCode);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedInvitation.UserEmail, result.UserEmail);
            Assert.Equal(expectedInvitation.Name, result.Name);
            Assert.Equal(expectedInvitation.IsActive, result.IsActive);
            Assert.Equal(expectedInvitation.DataSourceId, result.DataSourceId);
            Assert.Equal(expectedInvitation.PortfolioId, result.PortfolioId);
            Assert.Equal(expectedInvitation.AgencyId, result.AgencyId);
            Assert.Equal(expectedInvitation.AgencyName, result.AgencyName);
            Assert.Equal(expectedInvitation.AgencyLogo, result.AgencyLogo);
            Assert.Equal(expectedInvitation.AgencyColour, result.AgencyColour);
        }

        [Fact]
        public async Task GetInvitationDetailsById_Should_Return_Null_When_Not_Found()
        {
            // Arrange
            string? inviteCode = "non-existent-code";            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<ViewUserInvites>(
                    It.IsAny<IDbConnection>(), 
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("InviteCode") != null && 
                        p.GetType().GetProperty("InviteCode")!.GetValue(p)!.ToString() == inviteCode),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((ViewUserInvites)null!);

            // Act
            var result = await _repository.GetInvitationDetailsById(inviteCode);

            // Assert
            Assert.Null(result);
        }

        [Fact]
        public async Task UpdateUserInvites_Should_Update_And_Return_RowsAffected()
        {
            // Arrange
            string? inviteCode = "test-invite-code";
            string? providerId = "provider123";
            string? loginEmail = "<EMAIL>";
            bool isActive = false;
            int expectedRowsAffected = 1;

            var expectedQuery = new StringBuilder();
            expectedQuery.Append($"UPDATE {Constants.UserInvitesTableName} SET ProviderId = @ProviderId, LoginEmail = @LoginEmail, IsActive = @IsActive ");
            expectedQuery.Append($" WHERE InviteCode = @InviteCode");            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(), 
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("InviteCode") != null &&
                        p.GetType().GetProperty("ProviderId") != null &&
                        p.GetType().GetProperty("LoginEmail") != null &&
                        p.GetType().GetProperty("IsActive") != null &&
                        p.GetType().GetProperty("InviteCode")!.GetValue(p)!.ToString() == inviteCode &&
                        p.GetType().GetProperty("ProviderId")!.GetValue(p)!.ToString() == providerId &&
                        p.GetType().GetProperty("LoginEmail")!.GetValue(p)!.ToString() == loginEmail &&
                        (bool)p.GetType().GetProperty("IsActive")!.GetValue(p)! == isActive
                    ),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdateUserInvites(inviteCode, providerId, loginEmail, isActive);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
        }

        [Fact]
        public async Task UpdateUserInvites_Should_Return_Zero_When_No_Records_Updated()
        {
            // Arrange
            string? inviteCode = "non-existent-code";
            string? providerId = "provider123";
            string? loginEmail = "<EMAIL>";
            bool isActive = false;
            int expectedRowsAffected = 0;            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(
                    It.IsAny<IDbConnection>(), 
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("InviteCode") != null &&
                        p.GetType().GetProperty("InviteCode")!.GetValue(p)!.ToString() == inviteCode),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedRowsAffected);

            // Act
            var result = await _repository.UpdateUserInvites(inviteCode, providerId, loginEmail, isActive);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
        }

        [Fact]
        public async Task GetInvitationByPortfolioIdAndEmail_Should_Return_Invitation_When_Found()
        {
            // Arrange
            string portfolioId = "portfolio123";
            string userEmail = "<EMAIL>";

            var expectedInvitation = new UserInvites
            {
                UserInvitesId = 1,
                UserEmail = userEmail,
                Name = "Test User",
                InviteCode = "test-invite-code",
                InviteLink = "https://example.com/invite/test-code",
                IsActive = true,
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "logo.png",
                AgencyColour = "#FF0000",
                PortfolioId = portfolioId,
                DataSourceId = 1
            };

            var expectedQuery = new StringBuilder();
            expectedQuery.Append($"SELECT * FROM {Constants.UserInvitesTableName} ");
            expectedQuery.Append($"WHERE PortfolioId = @PortfolioId AND UserEmail = @UserEmail AND IsActive = 1");            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<UserInvites>(
                    It.IsAny<IDbConnection>(), 
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("PortfolioId") != null &&
                        p.GetType().GetProperty("UserEmail") != null &&
                        p.GetType().GetProperty("PortfolioId")!.GetValue(p)!.ToString() == portfolioId &&
                        p.GetType().GetProperty("UserEmail")!.GetValue(p)!.ToString() == userEmail
                    ),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync(expectedInvitation);

            // Act
            var result = await _repository.GetInvitationByPortfolioIdAndEmail(portfolioId, userEmail);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedInvitation.UserInvitesId, result.UserInvitesId);
            Assert.Equal(expectedInvitation.UserEmail, result.UserEmail);
            Assert.Equal(expectedInvitation.Name, result.Name);
            Assert.Equal(expectedInvitation.InviteCode, result.InviteCode);
            Assert.Equal(expectedInvitation.InviteLink, result.InviteLink);
            Assert.Equal(expectedInvitation.IsActive, result.IsActive);
            Assert.Equal(expectedInvitation.AgencyId, result.AgencyId);
            Assert.Equal(expectedInvitation.AgencyName, result.AgencyName);
            Assert.Equal(expectedInvitation.AgencyLogo, result.AgencyLogo);
            Assert.Equal(expectedInvitation.AgencyColour, result.AgencyColour);
            Assert.Equal(expectedInvitation.PortfolioId, result.PortfolioId);
            Assert.Equal(expectedInvitation.DataSourceId, result.DataSourceId);
        }

        [Fact]
        public async Task GetInvitationByPortfolioIdAndEmail_Should_Return_Null_When_Not_Found()
        {
            // Arrange
            string portfolioId = "non-existent-portfolio";
            string userEmail = "<EMAIL>";            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<UserInvites>(
                    It.IsAny<IDbConnection>(), 
                    It.IsAny<string>(),
                    It.Is<object>(p => 
                        p.GetType().GetProperty("PortfolioId") != null &&
                        p.GetType().GetProperty("UserEmail") != null &&
                        p.GetType().GetProperty("PortfolioId")!.GetValue(p)!.ToString() == portfolioId &&
                        p.GetType().GetProperty("UserEmail")!.GetValue(p)!.ToString() == userEmail
                    ),
                    It.IsAny<IDbTransaction>(),
                    It.IsAny<int?>(),
                    It.IsAny<CommandType?>()))
                .ReturnsAsync((UserInvites)null!);

            // Act
            var result = await _repository.GetInvitationByPortfolioIdAndEmail(portfolioId, userEmail);

            // Assert
            Assert.Null(result);
        }
    }
}
