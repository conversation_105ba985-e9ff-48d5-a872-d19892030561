﻿using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.DBCore.Interfaces
{
    public interface IInvitationRepository : IBaseRepository<UserInvites, int>
    {
        public Task<ViewUserInvites> GetInvitationDetailsById(string? inviteCode);

        public Task<int> UpdateUserInvites(string? inviteCode, string? providerId, string? loginEmail, bool isActive);

        public Task<UserInvites> GetInvitationByPortfolioIdAndEmail(string portfolioId, string userEmail);
    }
}
