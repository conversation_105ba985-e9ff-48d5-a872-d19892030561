<!DOCTYPE html>
<!-- saved from url=(0827)https://sharedplatformotadev.b2clogin.com/sharedplatformotadev.onmicrosoft.com/b2c_1_login_1/oauth2/v2.0/authorize?client_id=16bbf132-ea64-4d88-9c9c-9312916a9fe6&scope=openid%20profile%20offline_access%20email%20https%3A%2F%2Fsharedplatformotadev.onmicrosoft.com%2F39856524-a4fa-4b2f-911c-3885a8dba92b%2Fapi.read&redirect_uri=https%3A%2F%2Fca-nw02-shrdplt-dev-web.purpleflower-483ee117.australiaeast.azurecontainerapps.io&client-request-id=01981817-82c7-7899-a2c9-ea5c68dd469c&response_mode=fragment&client_info=1&nonce=01981817-82fe-7636-a66f-0860736b61c1&state=eyJpZCI6IjAxOTgxODE3LTgyZmUtN2I2My05NjY2LTVjNzhlMTQ1ZTNiZSIsIm1ldGEiOnsiaW50ZXJhY3Rpb25UeXBlIjoicG9wdXAifX0%3D&x-client-SKU=msal.js.browser&x-client-VER=4.14.0&response_type=code&code_challenge=k_G-U7SENNX7AgFaarU207iehN1aFHqHotCFow7dz8g&code_challenge_method=S256 -->
<html lang="en-US"><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><link rel="stylesheet" href="./LoginOTA_files/common.css">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    
    <meta name="locale" content="en-US">
    <meta name="ROBOTS" content="NONE, NOARCHIVE">
    <meta name="GOOGLEBOT" content="NOARCHIVE">
    
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=2.0, user-scalable=yes">
    <style>
      body.loading .container { display: none !important; }
      body.loading #shimmer-loader { display: block; }
      body:not(.loading) #shimmer-loader { display: none; }
      #shimmer-loader {
        width: 100%;
        max-width: 400px;
        margin: 60px auto 0 auto;
        padding: 40px 0;
        display: none;
      }
      .shimmer {
        width: 100%;
        height: 180px;
        background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
        background-size: 200% 100%;
        animation: shimmer 1.5s infinite;
        border-radius: 12px;
      }
      @keyframes shimmer {
        0% { background-position: -200% 0; }
        100% { background-position: 200% 0; }
      }
    </style>

<style>
    .intro,
    .heading {
        display: none !important;
    }

    button {
        width: auto;
        min-width: 50px;
        height: 32px;
        margin-top: 2px;
        -moz-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
        background: rgb(0, 122, 198);
        border: 1px solid #fff;
        color: #fff;
        font-size: 100%;
        padding: 0 2px
    }

    button:hover {
        border: 1px solid  rgb(0, 122, 198);
        -moz-box-shadow: 0 0 0;
        -webkit-box-shadow: 0 0 0;
        box-shadow: 0 0 0
    }

    .buttons button {
        -moz-user-select: none;
        cursor: pointer;
        margin-right: 4px;
        margin-left: 0;
        padding: 6px 12px;
        font-size: 100%
    }

    .buttons button:disabled,
    button.disabled,
    button[aria-disabled=true],
    button[disabled] {
        background-color: #767676
    }

    .buttons button:focus {
        outline: #8a8886 solid 1px
    }

    .accountButton,
    button {
        -moz-user-select: none;
        user-select: none
    }

    .accountButton,
    a,
    button {
        cursor: pointer
    }


    

    /* Form controls */
    .entry {
        position: relative;
        width: 100%;
    }

    .entry::before {
        content: "Enter your email and password to log in";
        color: #6C7278;
        margin-bottom: 20px;
        text-align: center;
        display: block;
        width: 100%;
        font-size: small;
    }


    .entry-item {
        margin-top: 10px;
        width: 100%;
        display: flex;
        flex-direction: column;
    }

    .error.itemLevel p {
        margin-top: 3px;
    }

    .entry-item input {
        width: 100% !important;
        padding: 12px;
        border-radius: 8px;
        border: 1px solid #d1d5db;
        font-size: 16px;
        /* height: 45px !important; */
    }

    .entry-item label {
        display: block;
        text-align: left;
        margin-bottom: 4px;
        color: #607184;
        font-family: 'Roboto', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Helvetica Neue', Arial, sans-serif; 
        font-size: 12px;
        font-style: normal;
        font-weight: 400;
        line-height: 160%;
        letter-spacing: -0.24px;
    }

    .entry-item:has(input#password) {
        position: relative;
        padding-bottom: 2.5em;
    }

    .entry-item:has(input#password) {
        display: flex;
        flex-direction: column;
    }

    /* Use display: contents on .password-label so its children
   (label and a#forgotPassword) become direct flex items of .entry-item */
    .password-label {
        display: contents;
    }

    /* Now, we can control the visual order of the label, input, error, and link */

    label[for="password"] {
        order: 1;
    }

    input#password {
        order: 2;
    }

    .entry-item:has(input#password)>.error.itemLevel {
        order: 3;
    }

    input[type="email"],
    input[type="password"] {
        width: 100%;
        padding: 6px;
        border-radius: 12px;
        border: 1px solid #d1d5db;
        font-size: 16px;
    }

    #api {
        margin-top: 1em;
    }

    #forgotPassword a {
        font-size: 14px;
        color: var(--primary-color);
        text-decoration: none;
    }

    #forgotPassword a:hover {
        text-decoration: underline;
    }

    #forgotPassword {
        text-align: right;
        order: 4;
        display: block;
        font-size: small;
        text-decoration: none;
        text-align: right;
    }

    #forgotPassword:hover {
        text-decoration: underline;
    }

    .entry-item .error.itemLevel {
        order: 2;
        font-size: smaller;
        color: var(--error-color);
    }

    .create {
        position: relative;
        width: 100%;
        margin: 30px auto 20px;
        text-align: center;
        padding: 0 15px;
        box-sizing: border-box;
        clear: both;
        margin-top: 40px;
    }

    .create a {
        color: var(--primary-color);
        text-decoration: none;
        font-family: 'Inter', 'Open Sans', sans-serif;
        font-size: 14px;
        font-weight: 500;
    }

    .create p {
        font-size: smaller;
    }

    .create a:hover {
        text-decoration: underline;
    }
</style>




<style id="common">
    .pageLevel,
    body {
        text-align: left
    }

    #panel,
    .pageLevel,
    .panel li,
    label {
        display: block
    }

    button {
        width: auto;
        min-width: 50px;
        height: 32px;
        margin-top: 2px;
        -moz-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
        background: var(--primary-color);
        border: 1px solid #fff;
        color: #fff;
        font-size: 100%;
        padding: 0 2px
    }

    button:hover {
        border: 1px solid var(--primary-color);
        -moz-box-shadow: 0 0 0;
        -webkit-box-shadow: 0 0 0;
        box-shadow: 0 0 0
    }

    .buttons button {
        -moz-user-select: none;
        cursor: pointer;
        margin-right: 4px;
        margin-left: 0;
        padding: 6px 12px;
        font-size: 100%
    }

    .buttons button:disabled,
    button.disabled,
    button[aria-disabled=true],
    button[disabled] {
        background-color: #767676
    }

    .buttons button:focus {
        outline: #8a8886 solid 1px
    }

    .accountButton,
    button {
        -moz-user-select: none;
        user-select: none
    }

    .accountButton,
    a,
    button {
        cursor: pointer
    }

    ::-webkit-input-placeholder {
        color: #6d6d6d
    }

    :-moz-placeholder {
        color: #6d6d6d
    }

    ::-moz-placeholder {
        color: #6d6d6d
    }

    :-ms-input-placeholder {
        color: #6d6d6d !important
    }

    h1,
    h2 {
        font-weight: 400
    }

    .panel li {
        list-style: none
    }

    .panel .companyLogo {
        width: 160px;
        margin-bottom: 20px
    }

    #api ul li {
        display: inline;
        list-style-type: none;
        margin-left: 0
    }

    .heading h1 {
        margin-bottom: 20px
    }

    /* html {
        background-color: #00abec
    } */

    .normaltext,
    .smalltext,
    .tinytext {
        font-family: 'Segoe UI', Segoe, SegoeUI-Regular-final, Tahoma, Helvetica, Arial, sans-serif
    }

    div#background_branding_container,
    div#background_page_overlay {
        width: 100%;
        z-index: 0;
        position: fixed;
        left: 0;
        overflow: hidden;
        top: 0
    }

    *,
    .panel,
    body {
        margin: 0;
        padding: 0
    }

    body {
        z-index: -999
    }

    .panel,
    .panel_layout,
    .panel_layout_row,
    body,
    html {
        height: 100%
    }

    #panel {
        border-left: 1px solid #fff
    }

    a {
        background-color: transparent;
        color: var(--primary-color);
        text-decoration: none
    }

    a:hover {
        text-decoration: underline
    }

    a:focus {
        outline: #8a8886 solid 1px
    }

    img {
        border: 0
    }

    form {
        height: auto;
        width: auto
    }

    h2 {
        font-size: 1em
    }

    .normaltext {
        font-size: .9em
    }

    input:focus,
    textarea:focus {
        outline: 0
    }

    .error {
        color: #a61e0c
    }

    .pageLevel {
        width: 293px;
        margin-top: 5px;
        font-size: 1.1em;
        height: auto
    }

    /* .highlightError {
        border: 1px solid #a61e0c !important
    } */

    img#background_background_image {
        height: 100%;
        width: 100%
    }

    div#background_page_overlay {
        background: left top no-repeat fixed #f7f7f7;
        height: 100%;
        opacity: 1
    }

    .panel {
        background: #fff;
        float: right;
        height: 100%;
        overflow-x: hidden;
        overflow-y: auto;
        position: fixed;
        right: 0;
        width: 500px;
        z-index: 1
    }

    .inner_container {
        max-height: 90%;
        min-height: 90%;
        width: 100%
    }

    #panel_center,
    #panel_left,
    #panel_right {
        display: inline-block;
        border: 0;
        height: 100%;
        margin: 0
    }

    #panel_left {
        padding: 0;
        width: 50px
    }

    #panel_center {
        min-height: 100%;
        padding: 0;
        width: 378px
    }

    .hide {
        opacity: 0
    }

    .api_container {
        display: inline-block;
        padding-left: 0;
        padding-top: 120px;
        position: relative;
        width: 100%;
        height: 100%
    }

    select {
        height: 28px
    }

    input[type=email],
    input[type=number],
    input[type=password],
    input[type=text] {
        height: 28px;
        /* width: 300px; */
        z-index: 3;
        color: #000;
        padding: 8px;
        -moz-box-shadow: 0 0 0;
        -webkit-box-shadow: 0 0 0;
        box-shadow: 0 0 0;
        margin-right: 3px
    }

    .accountButton {
        border: 1px solid #fff;
        color: #fff;
        margin-left: 0;
        margin-right: 2px;
        -moz-border-radius: 0;
        -webkit-border-radius: 0;
        border-radius: 0;
        text-align: center;
        word-wrap: break-word;
        height: 34px;
        width: 158px;
        padding-left: 30px;
        background-color: #505050
    }

    .accountButton:hover {
        background-color: #b9b9b9;
        border: 1px solid #fff;
        -moz-box-shadow: 0 0 0;
        -webkit-box-shadow: 0 0 0;
        box-shadow: 0 0 0
    }

    @-webkit-keyframes fadeIn {
        from {
            opacity: 0
        }

        to {
            opacity: 1
        }
    }

    @media only screen and (min-height:250px) and (max-width:450px),
    (min-device-height:250px) and (max-device-width:450px) {
        html {
            overflow: hidden
        }

        .tinytext {
            font-size: .6em
        }

        .smalltext {
            font-size: .7em
        }

        .normaltext {
            font-size: .8em
        }

        .bigtext {
            font-size: .9em
        }

        .gianttext {
            font-size: 1.2em
        }

        div#background_branding_container {
            display: none;
            opacity: 0;
            z-index: -999
        }

        div#background_page_overlay {
            display: none;
            z-index: -999
        }

        .panel_layout,
        .panel_layout_row {
            width: 100%
        }

        #panel_left {
            width: 40px
        }

        #panel_center {
            width: calc(100% - 80px)
        }

        .inner_container,
        .panel {
            width: 100%
        }

        #panel_right {
            display: none
        }

        .panel {
            float: none;
            height: 100%;
            margin: 0;
            min-width: 220px;
            overflow: auto;
            padding: 0;
            z-index: 1
        }
    }

    @media only screen and (max-width:501px),
    (max-device-width:501px) {

        #panel {
            width: 100%
        }

        #panel_layout_row {
            width: 100%
        }

        #panel_left {
            display: none
        }

        #panel_center {
            width: 100%
        }

        .panel_layout {
            width: 100%
        }

        .attrEntry input[type=text],
        input[type=email],
        input[type=password] {
            width: calc(100% - 15px) !important
        }

        .attrEntry .dropdown_single {
            width: 100% !important
        }
    }

    /* endinject */
</style>

<style>
    .rememberMe input[type=checkbox] {
        vertical-align: middle;
        position: relative;
        bottom: -2.5px;
        width: 13px;
    }

    .rememberMe label {
        display: inline-block
    }

    /* Following are generated styles via gulp. */
    /* inject: css */
    .intro {
        display: inline;
        margin-bottom: 5px
    }

    .intro p {
        padding-bottom: 7px
    }

    .entry {
        padding-top: 8px;
        padding-bottom: 0 !important
    }

    /* Login button */
    #next {
        width: 100%;
        padding: 12px 0;
        border-radius: 100px;
            background: var(--primary-color);
        color: white;
        font-size: 16px;
        font-weight: 500;
        cursor: pointer;
        border: none;
        display: flex;
        align-items: center;
        justify-content: center;
        height: 45px;
        color: transparent;
    }

    button#next::after {
        content: "Log in";
        font-size: 16px;
        font-weight: 500;
        white-space: nowrap;
        color: #ffffff;
        display: flex;
        align-items: center;
        justify-content: center;
        text-align: center;
        position: absolute
    }

    #next:hover,
    .accountButton:hover {
        -moz-box-shadow: 0 0 0;
        -webkit-box-shadow: 0 0 0;
        box-shadow: 0 0 0
    }

    #createAccount {
        margin-left: 5px
    }

    .password-label label {
        display: inline-block;
        vertical-align: baseline
    }

    .invalid,
    .required,
    .unknown {
        display: none
    }

    #forgotPassword {
        font-size: .75em;
        padding-left: 5px
    }

    .passwordReqs {
        display: block;
        font-size: .8em;
        padding-bottom: 2px
    }

    .localAccount .divider
    {
        display: none
    }

    .tiny {
        font-size: .8em;
        font-weight: 400;
        padding-left: 5px
    }

    .divider {
        margin-top: 20px;
        margin-bottom: 10px
    }


    .divider h2:after,
    .divider h2:before {
        border-top: 1px solid #b8b8b8;
        content: '';
        display: table-cell;
        position: relative;
        top: .7em;
        width: 50%
    }

    .divider h2:before {
        right: 1.8%
    }

    .divider h2:after {
        left: 1.8%
    }

    .verificationErrorText {
        color: var(--error-color)
    }

   


    /* endinject */
</style>


    <link rel="icon" href="data:;base64,iVBORw0KGgo="><script data-container="true" nonce="">var CP = {"list":[{"id":"GoogleExchange","description":"Google"},{"id":"FacebookExchange","description":"Facebook"},{"id":"AppleManagedExchange","description":"Apple"}]};
var SA_FIELDS = {"AttributeFields":[{"UX_INPUT_TYPE":"EmailBox","USER_INPUT_TYPE":"TextBox","IS_TEXT":true,"IS_EMAIL":false,"IS_PASSWORD":false,"IS_DATE":false,"IS_RADIO":false,"IS_DROP":false,"IS_TEXT_IN_PARAGRAPH":false,"IS_CHECK_MULTI":false,"IS_LINK":false,"VERIFY":false,"DN":"Email Address","ID":"email","U_HELP":"Email address that can be used to contact you.","DAY_PRE":"0","MONTH_PRE":"0","YEAR_PRE":"0","PAT":"^[a-zA-Z0-9!#$%&amp;&#39;+^_`{}~-]+(?:\\.[a-zA-Z0-9!#$%&amp;&#39;+^_`{}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$","PAT_DESC":"Please enter a valid email address.","IS_REQ":false,"IS_RDO":false,"OPTIONS":[]},{"UX_INPUT_TYPE":"Password","USER_INPUT_TYPE":"Password","IS_TEXT":false,"IS_EMAIL":false,"IS_PASSWORD":true,"IS_DATE":false,"IS_RADIO":false,"IS_DROP":false,"IS_TEXT_IN_PARAGRAPH":false,"IS_CHECK_MULTI":false,"IS_LINK":false,"VERIFY":false,"DN":"Password","ID":"password","U_HELP":"Enter password","DAY_PRE":"0","MONTH_PRE":"0","YEAR_PRE":"0","IS_REQ":false,"IS_RDO":false,"OPTIONS":[]}]};


var CONTENT = {"remember_me":"Keep me signed in","invalid_generic":"Please enter a valid {0}","createaccount_one_link":"Sign up now","forgotpassword_link":"Forgot your password?","createaccount_three_links":"Sign up with {0}, {1}, or {2}","requiredField_generic":"Please enter your {0}","local_intro_generic":"Sign in with your {0}","unknown_error":"We are having trouble signing you in. Please try again later.","requiredField_password":"Please enter your password","divider_title":"OR","createaccount_intro":"Don&#39;t have an account?","social_intro":"Sign in with your social account","button_signin":"Sign in","heading":"Sign in","createaccount_two_links":"Sign up with {0} or {1}"};

var SETTINGS = {"remoteResource":"https://stgnw02shrdpltdev.blob.core.windows.net/custom-b2c-flow/basic.html","retryLimit":3,"trimSpacesInPassword":true,"api":"CombinedSigninAndSignup","csrf":"MitReTYxdWg5UXNGQ1BGVVh6SWFLK0tpNkt1d1BtWWZ0Y2J2NVIyYStSaE1XS0JrOGgyVTFaNHlTQ2E2TWFySVU2dEpTQWZPMW81NUlMaVUzNGdhbFE9PTsyMDI1LTA3LTE3VDExOjE0OjEyLjczNTYyMTVaO0NBaWpPVFRzODhHdEp2enhpUnVnZGc9PTt7Ik9yY2hlc3RyYXRpb25TdGVwIjoxfQ==","transId":"StateProperties=eyJUSUQiOiJjYTE4YjkxNS1kOTJkLTQyOTktODg3OC02MTE4YzU0YWYzNjIifQ","pageViewId":"97423b36-ad7f-456d-bf9b-a99df59a8958","suppressElementCss":false,"isPageViewIdSentWithHeader":false,"allowAutoFocusOnPasswordField":true,"pageMode":0,"config":{"showSignupLink":"True","showHeading":"True","sendHintOnSignup":"False","includePasswordRequirements":"true","enableRememberMe":"false","bottomUnderFormClaimsProviderSelections":"ChangePhoneNumber","forgotPasswordLinkOverride":"ForgotPassword","operatingMode":"Email","announceVerCompleteMsg":"True"},"sanitizerPolicy":{allowedTags:['h1','u','h2','h3','h4','h5','h6','blockquote','p','a','ul','ol','nl','li','b','i','strong','em','strike','code','hr','br','div','table','thead','caption','tbody','tr','th','td','pre','img','video','source','span','footer','header','nav','main','style','meta','title','link','section','input','form','button','marquee','label'],allowedAttributes:{'*':['id','class','href','name','data-*','aria-*','type','lang','src','sizes','role','placeholder','title','width','height','style'],form:['method','action','target','accept-charset','novalidate'],input:['value'],a:['target'],img:['alt'],video:['controls','preload','poster'],meta:['http-equiv','content','charset'],link:['rel']},selfClosing:['img','br','hr','area','base','basefont','input','link','meta'],allowedSchemes:['http','https','mailto'],allowProtocolRelative:true,exclusiveFilter: function (frame) {return frame.tag === 'meta' && (frame.attribs['http-equiv'] && frame.attribs['http-equiv'].toUpperCase().indexOf('REFRESH') >= 0) || (frame.attribs['content'] && /data:/gmi.test(frame.attribs['content'])) || frame.tag === 'link' && (frame.attribs['rel'] && frame.attribs['rel'].toUpperCase().indexOf('IMPORT') >= 0)}},"hosts":{"tenant":"/sharedplatformotadev.onmicrosoft.com/B2C_1_login_1","policy":"B2C_1_login_1","static":"https://sharedplatformotadev.b2clogin.com/static/"},"locale":{"lang":"en","country":"US"},"tenantBranding":{"Locale":"0","backgroundImageUrl":"https://aadcdn.msftauthimages.net/81d6b03a-g4wkd53je2qgz1ns1bv0x0gvxgh98uf4ykkv2guycry/logintenantbranding/0/illustration?ts=638875486234725229","backgroundColor":"#10689d"},"xhrSettings":{"retryEnabled":true,"retryMaxAttempts":3,"retryDelay":200,"retryExponent":2,"retryOn":["error","timeout"]}};

</script><script nonce="">window.staticHost="https://sharedplatformotadev.b2clogin.com/static";window.targetSlice="001-000";window.targetDc="SYD";window.initializationTimeout=30000;window.diagsAlways=true;window.maxTrace=1000</script><script nonce="">(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.sanitizeHtml = f()}})(function(){var define,module,exports;return (function e(t,n,r){function s(o,u){if(!n[o]){if(!t[o]){var a=typeof require=="function"&&require;if(!u&&a)return a(o,!0);if(i)return i(o,!0);var f=new Error("Cannot find module '"+o+"'");throw f.code="MODULE_NOT_FOUND",f}var l=n[o]={exports:{}};t[o][0].call(l.exports,function(e){var n=t[o][1][e];return s(n?n:e)},l,l.exports,e,t,n,r)}return n[o].exports}var i=typeof require=="function"&&require;for(var o=0;o<r.length;o++)s(r[o]);return s})({1:[function(require,module,exports){
var htmlparser = require('htmlparser2');
var extend = require('xtend');
var quoteRegexp = require('regexp-quote');

function each(obj, cb) {
  if (obj) Object.keys(obj).forEach(function (key) {
    cb(obj[key], key);
  });
}

// Avoid false positives with .__proto__, .hasOwnProperty, etc.
function has(obj, key) {
  return ({}).hasOwnProperty.call(obj, key);
}

module.exports = sanitizeHtml;

// Ignore the _recursing flag; it's there for recursive
// invocation as a guard against this exploit:
// https://github.com/fb55/htmlparser2/issues/105

function sanitizeHtml(html, options, _recursing) {
  var result = '';

  function Frame(tag, attribs) {
    var that = this;
    this.tag = tag;
    this.attribs = attribs || {};
    this.tagPosition = result.length;
    this.text = ''; // Node inner text

    this.updateParentNodeText = function() {
      if (stack.length) {
          var parentFrame = stack[stack.length - 1];
          parentFrame.text += that.text;
      }
    };
  }

  if (!options) {
    options = sanitizeHtml.defaults;
    options.parser = htmlParserDefaults;
  } else {
    options = extend(sanitizeHtml.defaults, options);
    if (options.parser) {
      options.parser = extend(htmlParserDefaults, options.parser);
    } else {
      options.parser = htmlParserDefaults;
    }
  }

  // Tags that contain something other than HTML, or where discarding
  // the text when the tag is disallowed makes sense for other reasons.
  // If we are not allowing these tags, we should drop their content too.
  // For other tags you would drop the tag but keep its content.
  var nonTextTagsArray = options.nonTextTags || [ 'script', 'style', 'textarea' ];
  var allowedAttributesMap;
  var allowedAttributesGlobMap;
  if(options.allowedAttributes) {
    allowedAttributesMap = {};
    allowedAttributesGlobMap = {};
    each(options.allowedAttributes, function(attributes, tag) {
      allowedAttributesMap[tag] = [];
      var globRegex = [];
      attributes.forEach(function(name) {
        if(name.indexOf('*') >= 0) {
          globRegex.push(quoteRegexp(name).replace(/\\\*/g, '.*'));
        } else {
          allowedAttributesMap[tag].push(name);
        }
      });
      allowedAttributesGlobMap[tag] = new RegExp('^(' + globRegex.join('|') + ')$');
    });
  }
  var allowedClassesMap = {};
  each(options.allowedClasses, function(classes, tag) {
    // Implicitly allows the class attribute
    if(allowedAttributesMap) {
      if (!has(allowedAttributesMap, tag)) {
        allowedAttributesMap[tag] = [];
      }
      allowedAttributesMap[tag].push('class');
    }

    allowedClassesMap[tag] = classes;
  });

  var transformTagsMap = {};
  var transformTagsAll;
  each(options.transformTags, function(transform, tag) {
    var transFun;
    if (typeof transform === 'function') {
      transFun = transform;
    } else if (typeof transform === "string") {
      transFun = sanitizeHtml.simpleTransform(transform);
    }
    if (tag === '*') {
      transformTagsAll = transFun;
    } else {
      transformTagsMap[tag] = transFun;
    }
  });

  var depth = 0;
  var stack = [];
  var skipMap = {};
  var transformMap = {};
  var skipText = false;
  var skipTextDepth = 0;

  var parser = new htmlparser.Parser({
    onopentag: function(name, attribs) {
      if (skipText) {
        skipTextDepth++;
        return;
      }
      var frame = new Frame(name, attribs);
      stack.push(frame);

      var skip = false;
      var hasText = frame.text ? true : false;
      var transformedTag;
      if (has(transformTagsMap, name)) {
        transformedTag = transformTagsMap[name](name, attribs);

        frame.attribs = attribs = transformedTag.attribs;

        if (transformedTag.text !== undefined) {
          frame.innerText = transformedTag.text;
        }

        if (name !== transformedTag.tagName) {
          frame.name = name = transformedTag.tagName;
          transformMap[depth] = transformedTag.tagName;
        }
      }
      if (transformTagsAll) {
        transformedTag = transformTagsAll(name, attribs);

        frame.attribs = attribs = transformedTag.attribs;
        if (name !== transformedTag.tagName) {
          frame.name = name = transformedTag.tagName;
          transformMap[depth] = transformedTag.tagName;
        }
      }

      if (options.allowedTags && options.allowedTags.indexOf(name) === -1) {
        skip = true;
        if (nonTextTagsArray.indexOf(name) !== -1) {
          skipText = true;
          skipTextDepth = 1;
        }
        skipMap[depth] = true;
      }
      depth++;
      if (skip) {
        // We want the contents but not this tag
        return;
      }
      result += '<' + name;
      if (!allowedAttributesMap || has(allowedAttributesMap, name) || allowedAttributesMap['*']) {
        each(attribs, function(value, a) {
          if (!allowedAttributesMap ||
              (has(allowedAttributesMap, name) && allowedAttributesMap[name].indexOf(a) !== -1 ) ||
              (allowedAttributesMap['*'] && allowedAttributesMap['*'].indexOf(a) !== -1 ) ||
              (has(allowedAttributesGlobMap, name) && allowedAttributesGlobMap[name].test(a)) ||
              (allowedAttributesGlobMap['*'] && allowedAttributesGlobMap['*'].test(a))) {
            if ((a === 'href') || (a === 'src') || (a === 'action')) {
              if (naughtyHref(name, value)) {
                delete frame.attribs[a];
                return;
              }
            }
            if (a === 'class') {
              value = filterClasses(value, allowedClassesMap[name]);
              if (!value.length) {
                delete frame.attribs[a];
                return;
              }
            }
            result += ' ' + a;
            if (value.length) {
              result += '="' + escapeHtml(value) + '"';
            }
          } else {
            delete frame.attribs[a];
          }
        });
      }
      if (options.selfClosing.indexOf(name) !== -1) {
        result += " />";
      } else {
        result += ">";
        if (frame.innerText && !hasText && !options.textFilter) {
          result += frame.innerText;
        }
      }
    },
    ontext: function(text) {
      if (skipText) {
        return;
      }
      var lastFrame = stack[stack.length-1];
      var tag;

      if (lastFrame) {
        tag = lastFrame.tag;
        // If inner text was set by transform function then let's use it
        text = lastFrame.innerText !== undefined ? lastFrame.innerText : text;
      }

      if (tag === 'script') {
        // htmlparser2 gives us these as-is. Escaping them ruins the content. Allowing
        // script tags is, by definition, game over for XSS protection, so if that's
        // your concern, don't allow them. The same is essentially true for style tags
        // which have their own collection of XSS vectors.
        result += text;
      } else if (tag === 'style') {
        result += text.replace(/</g, '&lt;');
      } else {
        var escaped = escapeHtml(text);
        if (options.textFilter) {
          result += options.textFilter(escaped);
        } else {
          result += escaped;
        }
      }
      if (stack.length) {
           var frame = stack[stack.length - 1];
           frame.text += text;
      }
    },
    onclosetag: function(name) {

      if (skipText) {
        skipTextDepth--;
        if (!skipTextDepth) {
          skipText = false;
        } else {
          return;
        }
      }

      var frame = stack.pop();
      if (!frame) {
        // Do not crash on bad markup
        return;
      }
      skipText = false;
      depth--;
      if (skipMap[depth]) {
        delete skipMap[depth];
        frame.updateParentNodeText();
        return;
      }

      if (transformMap[depth]) {
        name = transformMap[depth];
        delete transformMap[depth];
      }

      if (options.exclusiveFilter && options.exclusiveFilter(frame)) {
         result = result.substr(0, frame.tagPosition);
         return;
      }

      frame.updateParentNodeText();

      if (options.selfClosing.indexOf(name) !== -1) {
         // Already output />
         return;
      }

      result += "</" + name + ">";
    }
  }, options.parser);
  parser.write(html);
  parser.end();

  return result;

  function escapeHtml(s) {
    if (typeof(s) !== 'string') {
      s = s + '';
    }
    return s.replace(/\&/g, '&amp;').replace(/</g, '&lt;').replace(/\>/g, '&gt;').replace(/\"/g, '&quot;');
  }

  function naughtyHref(name, href) {
    // Browsers ignore character codes of 32 (space) and below in a surprising
    // number of situations. Start reading here:
    // https://www.owasp.org/index.php/XSS_Filter_Evasion_Cheat_Sheet#Embedded_tab
    href = href.replace(/[\x00-\x20]+/g, '');
    // Clobber any comments in URLs, which the browser might
    // interpret inside an XML data island, allowing
    // a javascript: URL to be snuck through
    href = href.replace(/<\!\-\-.*?\-\-\>/g, '');
    // Case insensitive so we don't get faked out by JAVASCRIPT #1
    var matches = href.match(/^([a-zA-Z]+)\:/);
    if (!matches) {
      // Protocol-relative URL: "//some.evil.com/nasty"
      if (href.match(/^\/\//)) {
        return !options.allowProtocolRelative;
      }

      // No scheme
      return false;
    }
    var scheme = matches[1].toLowerCase();

    if (has(options.allowedSchemesByTag, name)) {
      return options.allowedSchemesByTag[name].indexOf(scheme) === -1;
    }

    return !options.allowedSchemes || options.allowedSchemes.indexOf(scheme) === -1;
  }

  function filterClasses(classes, allowed) {
    if (!allowed) {
      // The class attribute is allowed without filtering on this tag
      return classes;
    }
    classes = classes.split(/\s+/);
    return classes.filter(function(clss) {
      return allowed.indexOf(clss) !== -1;
    }).join(' ');
  }
}

// Defaults are accessible to you so that you can use them as a starting point
// programmatically if you wish

var htmlParserDefaults = {
  decodeEntities: true
};
sanitizeHtml.defaults = {
  allowedTags: [ 'h3', 'h4', 'h5', 'h6', 'blockquote', 'p', 'a', 'ul', 'ol',
    'nl', 'li', 'b', 'i', 'strong', 'em', 'strike', 'code', 'hr', 'br', 'div',
    'table', 'thead', 'caption', 'tbody', 'tr', 'th', 'td', 'pre' ],
  allowedAttributes: {
    a: [ 'href', 'name', 'target' ],
    // We don't currently allow img itself by default, but this
    // would make sense if we did
    img: [ 'src' ]
  },
  // Lots of these won't come up by default because we don't allow them
  selfClosing: [ 'img', 'br', 'hr', 'area', 'base', 'basefont', 'input', 'link', 'meta' ],
  // URL schemes we permit
  allowedSchemes: [ 'http', 'https', 'ftp', 'mailto' ],
  allowedSchemesByTag: {},
  allowProtocolRelative: true
};

sanitizeHtml.simpleTransform = function(newTagName, newAttribs, merge) {
  merge = (merge === undefined) ? true : merge;
  newAttribs = newAttribs || {};

  return function(tagName, attribs) {
    var attrib;
    if (merge) {
      for (attrib in newAttribs) {
        attribs[attrib] = newAttribs[attrib];
      }
    } else {
      attribs = newAttribs;
    }

    return {
      tagName: newTagName,
      attribs: attribs
    };
  };
};

},{"htmlparser2":36,"regexp-quote":59,"xtend":60}],2:[function(require,module,exports){

},{}],3:[function(require,module,exports){
(function (global){
/*!
 * The buffer module from node.js, for the browser.
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */
/* eslint-disable no-proto */

'use strict'

var base64 = require('base64-js')
var ieee754 = require('ieee754')
var isArray = require('isarray')

exports.Buffer = Buffer
exports.SlowBuffer = SlowBuffer
exports.INSPECT_MAX_BYTES = 50

/**
 * If `Buffer.TYPED_ARRAY_SUPPORT`:
 *   === true    Use Uint8Array implementation (fastest)
 *   === false   Use Object implementation (most compatible, even IE6)
 *
 * Browsers that support typed arrays are IE 10+, Firefox 4+, Chrome 7+, Safari 5.1+,
 * Opera 11.6+, iOS 4.2+.
 *
 * Due to various browser bugs, sometimes the Object implementation will be used even
 * when the browser supports typed arrays.
 *
 * Note:
 *
 *   - Firefox 4-29 lacks support for adding new properties to `Uint8Array` instances,
 *     See: https://bugzilla.mozilla.org/show_bug.cgi?id=695438.
 *
 *   - Chrome 9-10 is missing the `TypedArray.prototype.subarray` function.
 *
 *   - IE10 has a broken `TypedArray.prototype.subarray` function which returns arrays of
 *     incorrect length in some situations.

 * We detect these buggy browsers and set `Buffer.TYPED_ARRAY_SUPPORT` to `false` so they
 * get the Object implementation, which is slower but behaves correctly.
 */
Buffer.TYPED_ARRAY_SUPPORT = global.TYPED_ARRAY_SUPPORT !== undefined
  ? global.TYPED_ARRAY_SUPPORT
  : typedArraySupport()

/*
 * Export kMaxLength after typed array support is determined.
 */
exports.kMaxLength = kMaxLength()

function typedArraySupport () {
  try {
    var arr = new Uint8Array(1)
    arr.__proto__ = {__proto__: Uint8Array.prototype, foo: function () { return 42 }}
    return arr.foo() === 42 && // typed array instances can be augmented
        typeof arr.subarray === 'function' && // chrome 9-10 lack `subarray`
        arr.subarray(1, 1).byteLength === 0 // ie10 has broken `subarray`
  } catch (e) {
    return false
  }
}

function kMaxLength () {
  return Buffer.TYPED_ARRAY_SUPPORT
    ? 0x7fffffff
    : 0x3fffffff
}

function createBuffer (that, length) {
  if (kMaxLength() < length) {
    throw new RangeError('Invalid typed array length')
  }
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    // Return an augmented `Uint8Array` instance, for best performance
    that = new Uint8Array(length)
    that.__proto__ = Buffer.prototype
  } else {
    // Fallback: Return an object instance of the Buffer class
    if (that === null) {
      that = new Buffer(length)
    }
    that.length = length
  }

  return that
}

/**
 * The Buffer constructor returns instances of `Uint8Array` that have their
 * prototype changed to `Buffer.prototype`. Furthermore, `Buffer` is a subclass of
 * `Uint8Array`, so the returned instances will have all the node `Buffer` methods
 * and the `Uint8Array` methods. Square bracket notation works as expected -- it
 * returns a single octet.
 *
 * The `Uint8Array` prototype remains unmodified.
 */

function Buffer (arg, encodingOrOffset, length) {
  if (!Buffer.TYPED_ARRAY_SUPPORT && !(this instanceof Buffer)) {
    return new Buffer(arg, encodingOrOffset, length)
  }

  // Common case.
  if (typeof arg === 'number') {
    if (typeof encodingOrOffset === 'string') {
      throw new Error(
        'If encoding is specified then the first argument must be a string'
      )
    }
    return allocUnsafe(this, arg)
  }
  return from(this, arg, encodingOrOffset, length)
}

Buffer.poolSize = 8192 // not used by this implementation

// TODO: Legacy, not needed anymore. Remove in next major version.
Buffer._augment = function (arr) {
  arr.__proto__ = Buffer.prototype
  return arr
}

function from (that, value, encodingOrOffset, length) {
  if (typeof value === 'number') {
    throw new TypeError('"value" argument must not be a number')
  }

  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {
    return fromArrayBuffer(that, value, encodingOrOffset, length)
  }

  if (typeof value === 'string') {
    return fromString(that, value, encodingOrOffset)
  }

  return fromObject(that, value)
}

/**
 * Functionally equivalent to Buffer(arg, encoding) but throws a TypeError
 * if value is a number.
 * Buffer.from(str[, encoding])
 * Buffer.from(array)
 * Buffer.from(buffer)
 * Buffer.from(arrayBuffer[, byteOffset[, length]])
 **/
Buffer.from = function (value, encodingOrOffset, length) {
  return from(null, value, encodingOrOffset, length)
}

if (Buffer.TYPED_ARRAY_SUPPORT) {
  Buffer.prototype.__proto__ = Uint8Array.prototype
  Buffer.__proto__ = Uint8Array
  if (typeof Symbol !== 'undefined' && Symbol.species &&
      Buffer[Symbol.species] === Buffer) {
    // Fix subarray() in ES2016. See: https://github.com/feross/buffer/pull/97
    Object.defineProperty(Buffer, Symbol.species, {
      value: null,
      configurable: true
    })
  }
}

function assertSize (size) {
  if (typeof size !== 'number') {
    throw new TypeError('"size" argument must be a number')
  } else if (size < 0) {
    throw new RangeError('"size" argument must not be negative')
  }
}

function alloc (that, size, fill, encoding) {
  assertSize(size)
  if (size <= 0) {
    return createBuffer(that, size)
  }
  if (fill !== undefined) {
    // Only pay attention to encoding if it's a string. This
    // prevents accidentally sending in a number that would
    // be interpretted as a start offset.
    return typeof encoding === 'string'
      ? createBuffer(that, size).fill(fill, encoding)
      : createBuffer(that, size).fill(fill)
  }
  return createBuffer(that, size)
}

/**
 * Creates a new filled Buffer instance.
 * alloc(size[, fill[, encoding]])
 **/
Buffer.alloc = function (size, fill, encoding) {
  return alloc(null, size, fill, encoding)
}

function allocUnsafe (that, size) {
  assertSize(size)
  that = createBuffer(that, size < 0 ? 0 : checked(size) | 0)
  if (!Buffer.TYPED_ARRAY_SUPPORT) {
    for (var i = 0; i < size; ++i) {
      that[i] = 0
    }
  }
  return that
}

/**
 * Equivalent to Buffer(num), by default creates a non-zero-filled Buffer instance.
 * */
Buffer.allocUnsafe = function (size) {
  return allocUnsafe(null, size)
}
/**
 * Equivalent to SlowBuffer(num), by default creates a non-zero-filled Buffer instance.
 */
Buffer.allocUnsafeSlow = function (size) {
  return allocUnsafe(null, size)
}

function fromString (that, string, encoding) {
  if (typeof encoding !== 'string' || encoding === '') {
    encoding = 'utf8'
  }

  if (!Buffer.isEncoding(encoding)) {
    throw new TypeError('"encoding" must be a valid string encoding')
  }

  var length = byteLength(string, encoding) | 0
  that = createBuffer(that, length)

  var actual = that.write(string, encoding)

  if (actual !== length) {
    // Writing a hex string, for example, that contains invalid characters will
    // cause everything after the first invalid character to be ignored. (e.g.
    // 'abxxcd' will be treated as 'ab')
    that = that.slice(0, actual)
  }

  return that
}

function fromArrayLike (that, array) {
  var length = array.length < 0 ? 0 : checked(array.length) | 0
  that = createBuffer(that, length)
  for (var i = 0; i < length; i += 1) {
    that[i] = array[i] & 255
  }
  return that
}

function fromArrayBuffer (that, array, byteOffset, length) {
  array.byteLength // this throws if `array` is not a valid ArrayBuffer

  if (byteOffset < 0 || array.byteLength < byteOffset) {
    throw new RangeError('\'offset\' is out of bounds')
  }

  if (array.byteLength < byteOffset + (length || 0)) {
    throw new RangeError('\'length\' is out of bounds')
  }

  if (byteOffset === undefined && length === undefined) {
    array = new Uint8Array(array)
  } else if (length === undefined) {
    array = new Uint8Array(array, byteOffset)
  } else {
    array = new Uint8Array(array, byteOffset, length)
  }

  if (Buffer.TYPED_ARRAY_SUPPORT) {
    // Return an augmented `Uint8Array` instance, for best performance
    that = array
    that.__proto__ = Buffer.prototype
  } else {
    // Fallback: Return an object instance of the Buffer class
    that = fromArrayLike(that, array)
  }
  return that
}

function fromObject (that, obj) {
  if (Buffer.isBuffer(obj)) {
    var len = checked(obj.length) | 0
    that = createBuffer(that, len)

    if (that.length === 0) {
      return that
    }

    obj.copy(that, 0, 0, len)
    return that
  }

  if (obj) {
    if ((typeof ArrayBuffer !== 'undefined' &&
        obj.buffer instanceof ArrayBuffer) || 'length' in obj) {
      if (typeof obj.length !== 'number' || isnan(obj.length)) {
        return createBuffer(that, 0)
      }
      return fromArrayLike(that, obj)
    }

    if (obj.type === 'Buffer' && isArray(obj.data)) {
      return fromArrayLike(that, obj.data)
    }
  }

  throw new TypeError('First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.')
}

function checked (length) {
  // Note: cannot use `length < kMaxLength()` here because that fails when
  // length is NaN (which is otherwise coerced to zero.)
  if (length >= kMaxLength()) {
    throw new RangeError('Attempt to allocate Buffer larger than maximum ' +
                         'size: 0x' + kMaxLength().toString(16) + ' bytes')
  }
  return length | 0
}

function SlowBuffer (length) {
  if (+length != length) { // eslint-disable-line eqeqeq
    length = 0
  }
  return Buffer.alloc(+length)
}

Buffer.isBuffer = function isBuffer (b) {
  return !!(b != null && b._isBuffer)
}

Buffer.compare = function compare (a, b) {
  if (!Buffer.isBuffer(a) || !Buffer.isBuffer(b)) {
    throw new TypeError('Arguments must be Buffers')
  }

  if (a === b) return 0

  var x = a.length
  var y = b.length

  for (var i = 0, len = Math.min(x, y); i < len; ++i) {
    if (a[i] !== b[i]) {
      x = a[i]
      y = b[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

Buffer.isEncoding = function isEncoding (encoding) {
  switch (String(encoding).toLowerCase()) {
    case 'hex':
    case 'utf8':
    case 'utf-8':
    case 'ascii':
    case 'latin1':
    case 'binary':
    case 'base64':
    case 'ucs2':
    case 'ucs-2':
    case 'utf16le':
    case 'utf-16le':
      return true
    default:
      return false
  }
}

Buffer.concat = function concat (list, length) {
  if (!isArray(list)) {
    throw new TypeError('"list" argument must be an Array of Buffers')
  }

  if (list.length === 0) {
    return Buffer.alloc(0)
  }

  var i
  if (length === undefined) {
    length = 0
    for (i = 0; i < list.length; ++i) {
      length += list[i].length
    }
  }

  var buffer = Buffer.allocUnsafe(length)
  var pos = 0
  for (i = 0; i < list.length; ++i) {
    var buf = list[i]
    if (!Buffer.isBuffer(buf)) {
      throw new TypeError('"list" argument must be an Array of Buffers')
    }
    buf.copy(buffer, pos)
    pos += buf.length
  }
  return buffer
}

function byteLength (string, encoding) {
  if (Buffer.isBuffer(string)) {
    return string.length
  }
  if (typeof ArrayBuffer !== 'undefined' && typeof ArrayBuffer.isView === 'function' &&
      (ArrayBuffer.isView(string) || string instanceof ArrayBuffer)) {
    return string.byteLength
  }
  if (typeof string !== 'string') {
    string = '' + string
  }

  var len = string.length
  if (len === 0) return 0

  // Use a for loop to avoid recursion
  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'ascii':
      case 'latin1':
      case 'binary':
        return len
      case 'utf8':
      case 'utf-8':
      case undefined:
        return utf8ToBytes(string).length
      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return len * 2
      case 'hex':
        return len >>> 1
      case 'base64':
        return base64ToBytes(string).length
      default:
        if (loweredCase) return utf8ToBytes(string).length // assume utf8
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}
Buffer.byteLength = byteLength

function slowToString (encoding, start, end) {
  var loweredCase = false

  // No need to verify that "this.length <= MAX_UINT32" since it's a read-only
  // property of a typed array.

  // This behaves neither like String nor Uint8Array in that we set start/end
  // to their upper/lower bounds if the value passed is out of range.
  // undefined is handled specially as per ECMA-262 6th Edition,
  // Section 13.3.3.7 Runtime Semantics: KeyedBindingInitialization.
  if (start === undefined || start < 0) {
    start = 0
  }
  // Return early if start > this.length. Done here to prevent potential uint32
  // coercion fail below.
  if (start > this.length) {
    return ''
  }

  if (end === undefined || end > this.length) {
    end = this.length
  }

  if (end <= 0) {
    return ''
  }

  // Force coersion to uint32. This will also coerce falsey/NaN values to 0.
  end >>>= 0
  start >>>= 0

  if (end <= start) {
    return ''
  }

  if (!encoding) encoding = 'utf8'

  while (true) {
    switch (encoding) {
      case 'hex':
        return hexSlice(this, start, end)

      case 'utf8':
      case 'utf-8':
        return utf8Slice(this, start, end)

      case 'ascii':
        return asciiSlice(this, start, end)

      case 'latin1':
      case 'binary':
        return latin1Slice(this, start, end)

      case 'base64':
        return base64Slice(this, start, end)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return utf16leSlice(this, start, end)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = (encoding + '').toLowerCase()
        loweredCase = true
    }
  }
}

// The property is used by `Buffer.isBuffer` and `is-buffer` (in Safari 5-7) to detect
// Buffer instances.
Buffer.prototype._isBuffer = true

function swap (b, n, m) {
  var i = b[n]
  b[n] = b[m]
  b[m] = i
}

Buffer.prototype.swap16 = function swap16 () {
  var len = this.length
  if (len % 2 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 16-bits')
  }
  for (var i = 0; i < len; i += 2) {
    swap(this, i, i + 1)
  }
  return this
}

Buffer.prototype.swap32 = function swap32 () {
  var len = this.length
  if (len % 4 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 32-bits')
  }
  for (var i = 0; i < len; i += 4) {
    swap(this, i, i + 3)
    swap(this, i + 1, i + 2)
  }
  return this
}

Buffer.prototype.swap64 = function swap64 () {
  var len = this.length
  if (len % 8 !== 0) {
    throw new RangeError('Buffer size must be a multiple of 64-bits')
  }
  for (var i = 0; i < len; i += 8) {
    swap(this, i, i + 7)
    swap(this, i + 1, i + 6)
    swap(this, i + 2, i + 5)
    swap(this, i + 3, i + 4)
  }
  return this
}

Buffer.prototype.toString = function toString () {
  var length = this.length | 0
  if (length === 0) return ''
  if (arguments.length === 0) return utf8Slice(this, 0, length)
  return slowToString.apply(this, arguments)
}

Buffer.prototype.equals = function equals (b) {
  if (!Buffer.isBuffer(b)) throw new TypeError('Argument must be a Buffer')
  if (this === b) return true
  return Buffer.compare(this, b) === 0
}

Buffer.prototype.inspect = function inspect () {
  var str = ''
  var max = exports.INSPECT_MAX_BYTES
  if (this.length > 0) {
    str = this.toString('hex', 0, max).match(/.{2}/g).join(' ')
    if (this.length > max) str += ' ... '
  }
  return '<Buffer ' + str + '>'
}

Buffer.prototype.compare = function compare (target, start, end, thisStart, thisEnd) {
  if (!Buffer.isBuffer(target)) {
    throw new TypeError('Argument must be a Buffer')
  }

  if (start === undefined) {
    start = 0
  }
  if (end === undefined) {
    end = target ? target.length : 0
  }
  if (thisStart === undefined) {
    thisStart = 0
  }
  if (thisEnd === undefined) {
    thisEnd = this.length
  }

  if (start < 0 || end > target.length || thisStart < 0 || thisEnd > this.length) {
    throw new RangeError('out of range index')
  }

  if (thisStart >= thisEnd && start >= end) {
    return 0
  }
  if (thisStart >= thisEnd) {
    return -1
  }
  if (start >= end) {
    return 1
  }

  start >>>= 0
  end >>>= 0
  thisStart >>>= 0
  thisEnd >>>= 0

  if (this === target) return 0

  var x = thisEnd - thisStart
  var y = end - start
  var len = Math.min(x, y)

  var thisCopy = this.slice(thisStart, thisEnd)
  var targetCopy = target.slice(start, end)

  for (var i = 0; i < len; ++i) {
    if (thisCopy[i] !== targetCopy[i]) {
      x = thisCopy[i]
      y = targetCopy[i]
      break
    }
  }

  if (x < y) return -1
  if (y < x) return 1
  return 0
}

// Finds either the first index of `val` in `buffer` at offset >= `byteOffset`,
// OR the last index of `val` in `buffer` at offset <= `byteOffset`.
//
// Arguments:
// - buffer - a Buffer to search
// - val - a string, Buffer, or number
// - byteOffset - an index into `buffer`; will be clamped to an int32
// - encoding - an optional encoding, relevant is val is a string
// - dir - true for indexOf, false for lastIndexOf
function bidirectionalIndexOf (buffer, val, byteOffset, encoding, dir) {
  // Empty buffer means no match
  if (buffer.length === 0) return -1

  // Normalize byteOffset
  if (typeof byteOffset === 'string') {
    encoding = byteOffset
    byteOffset = 0
  } else if (byteOffset > 0x7fffffff) {
    byteOffset = 0x7fffffff
  } else if (byteOffset < -0x80000000) {
    byteOffset = -0x80000000
  }
  byteOffset = +byteOffset  // Coerce to Number.
  if (isNaN(byteOffset)) {
    // byteOffset: it it's undefined, null, NaN, "foo", etc, search whole buffer
    byteOffset = dir ? 0 : (buffer.length - 1)
  }

  // Normalize byteOffset: negative offsets start from the end of the buffer
  if (byteOffset < 0) byteOffset = buffer.length + byteOffset
  if (byteOffset >= buffer.length) {
    if (dir) return -1
    else byteOffset = buffer.length - 1
  } else if (byteOffset < 0) {
    if (dir) byteOffset = 0
    else return -1
  }

  // Normalize val
  if (typeof val === 'string') {
    val = Buffer.from(val, encoding)
  }

  // Finally, search either indexOf (if dir is true) or lastIndexOf
  if (Buffer.isBuffer(val)) {
    // Special case: looking for empty string/buffer always fails
    if (val.length === 0) {
      return -1
    }
    return arrayIndexOf(buffer, val, byteOffset, encoding, dir)
  } else if (typeof val === 'number') {
    val = val & 0xFF // Search for a byte value [0-255]
    if (Buffer.TYPED_ARRAY_SUPPORT &&
        typeof Uint8Array.prototype.indexOf === 'function') {
      if (dir) {
        return Uint8Array.prototype.indexOf.call(buffer, val, byteOffset)
      } else {
        return Uint8Array.prototype.lastIndexOf.call(buffer, val, byteOffset)
      }
    }
    return arrayIndexOf(buffer, [ val ], byteOffset, encoding, dir)
  }

  throw new TypeError('val must be string, number or Buffer')
}

function arrayIndexOf (arr, val, byteOffset, encoding, dir) {
  var indexSize = 1
  var arrLength = arr.length
  var valLength = val.length

  if (encoding !== undefined) {
    encoding = String(encoding).toLowerCase()
    if (encoding === 'ucs2' || encoding === 'ucs-2' ||
        encoding === 'utf16le' || encoding === 'utf-16le') {
      if (arr.length < 2 || val.length < 2) {
        return -1
      }
      indexSize = 2
      arrLength /= 2
      valLength /= 2
      byteOffset /= 2
    }
  }

  function read (buf, i) {
    if (indexSize === 1) {
      return buf[i]
    } else {
      return buf.readUInt16BE(i * indexSize)
    }
  }

  var i
  if (dir) {
    var foundIndex = -1
    for (i = byteOffset; i < arrLength; i++) {
      if (read(arr, i) === read(val, foundIndex === -1 ? 0 : i - foundIndex)) {
        if (foundIndex === -1) foundIndex = i
        if (i - foundIndex + 1 === valLength) return foundIndex * indexSize
      } else {
        if (foundIndex !== -1) i -= i - foundIndex
        foundIndex = -1
      }
    }
  } else {
    if (byteOffset + valLength > arrLength) byteOffset = arrLength - valLength
    for (i = byteOffset; i >= 0; i--) {
      var found = true
      for (var j = 0; j < valLength; j++) {
        if (read(arr, i + j) !== read(val, j)) {
          found = false
          break
        }
      }
      if (found) return i
    }
  }

  return -1
}

Buffer.prototype.includes = function includes (val, byteOffset, encoding) {
  return this.indexOf(val, byteOffset, encoding) !== -1
}

Buffer.prototype.indexOf = function indexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, true)
}

Buffer.prototype.lastIndexOf = function lastIndexOf (val, byteOffset, encoding) {
  return bidirectionalIndexOf(this, val, byteOffset, encoding, false)
}

function hexWrite (buf, string, offset, length) {
  offset = Number(offset) || 0
  var remaining = buf.length - offset
  if (!length) {
    length = remaining
  } else {
    length = Number(length)
    if (length > remaining) {
      length = remaining
    }
  }

  // must be an even number of digits
  var strLen = string.length
  if (strLen % 2 !== 0) throw new TypeError('Invalid hex string')

  if (length > strLen / 2) {
    length = strLen / 2
  }
  for (var i = 0; i < length; ++i) {
    var parsed = parseInt(string.substr(i * 2, 2), 16)
    if (isNaN(parsed)) return i
    buf[offset + i] = parsed
  }
  return i
}

function utf8Write (buf, string, offset, length) {
  return blitBuffer(utf8ToBytes(string, buf.length - offset), buf, offset, length)
}

function asciiWrite (buf, string, offset, length) {
  return blitBuffer(asciiToBytes(string), buf, offset, length)
}

function latin1Write (buf, string, offset, length) {
  return asciiWrite(buf, string, offset, length)
}

function base64Write (buf, string, offset, length) {
  return blitBuffer(base64ToBytes(string), buf, offset, length)
}

function ucs2Write (buf, string, offset, length) {
  return blitBuffer(utf16leToBytes(string, buf.length - offset), buf, offset, length)
}

Buffer.prototype.write = function write (string, offset, length, encoding) {
  // Buffer#write(string)
  if (offset === undefined) {
    encoding = 'utf8'
    length = this.length
    offset = 0
  // Buffer#write(string, encoding)
  } else if (length === undefined && typeof offset === 'string') {
    encoding = offset
    length = this.length
    offset = 0
  // Buffer#write(string, offset[, length][, encoding])
  } else if (isFinite(offset)) {
    offset = offset | 0
    if (isFinite(length)) {
      length = length | 0
      if (encoding === undefined) encoding = 'utf8'
    } else {
      encoding = length
      length = undefined
    }
  // legacy write(string, encoding, offset, length) - remove in v0.13
  } else {
    throw new Error(
      'Buffer.write(string, encoding, offset[, length]) is no longer supported'
    )
  }

  var remaining = this.length - offset
  if (length === undefined || length > remaining) length = remaining

  if ((string.length > 0 && (length < 0 || offset < 0)) || offset > this.length) {
    throw new RangeError('Attempt to write outside buffer bounds')
  }

  if (!encoding) encoding = 'utf8'

  var loweredCase = false
  for (;;) {
    switch (encoding) {
      case 'hex':
        return hexWrite(this, string, offset, length)

      case 'utf8':
      case 'utf-8':
        return utf8Write(this, string, offset, length)

      case 'ascii':
        return asciiWrite(this, string, offset, length)

      case 'latin1':
      case 'binary':
        return latin1Write(this, string, offset, length)

      case 'base64':
        // Warning: maxLength not taken into account in base64Write
        return base64Write(this, string, offset, length)

      case 'ucs2':
      case 'ucs-2':
      case 'utf16le':
      case 'utf-16le':
        return ucs2Write(this, string, offset, length)

      default:
        if (loweredCase) throw new TypeError('Unknown encoding: ' + encoding)
        encoding = ('' + encoding).toLowerCase()
        loweredCase = true
    }
  }
}

Buffer.prototype.toJSON = function toJSON () {
  return {
    type: 'Buffer',
    data: Array.prototype.slice.call(this._arr || this, 0)
  }
}

function base64Slice (buf, start, end) {
  if (start === 0 && end === buf.length) {
    return base64.fromByteArray(buf)
  } else {
    return base64.fromByteArray(buf.slice(start, end))
  }
}

function utf8Slice (buf, start, end) {
  end = Math.min(buf.length, end)
  var res = []

  var i = start
  while (i < end) {
    var firstByte = buf[i]
    var codePoint = null
    var bytesPerSequence = (firstByte > 0xEF) ? 4
      : (firstByte > 0xDF) ? 3
      : (firstByte > 0xBF) ? 2
      : 1

    if (i + bytesPerSequence <= end) {
      var secondByte, thirdByte, fourthByte, tempCodePoint

      switch (bytesPerSequence) {
        case 1:
          if (firstByte < 0x80) {
            codePoint = firstByte
          }
          break
        case 2:
          secondByte = buf[i + 1]
          if ((secondByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0x1F) << 0x6 | (secondByte & 0x3F)
            if (tempCodePoint > 0x7F) {
              codePoint = tempCodePoint
            }
          }
          break
        case 3:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0xC | (secondByte & 0x3F) << 0x6 | (thirdByte & 0x3F)
            if (tempCodePoint > 0x7FF && (tempCodePoint < 0xD800 || tempCodePoint > 0xDFFF)) {
              codePoint = tempCodePoint
            }
          }
          break
        case 4:
          secondByte = buf[i + 1]
          thirdByte = buf[i + 2]
          fourthByte = buf[i + 3]
          if ((secondByte & 0xC0) === 0x80 && (thirdByte & 0xC0) === 0x80 && (fourthByte & 0xC0) === 0x80) {
            tempCodePoint = (firstByte & 0xF) << 0x12 | (secondByte & 0x3F) << 0xC | (thirdByte & 0x3F) << 0x6 | (fourthByte & 0x3F)
            if (tempCodePoint > 0xFFFF && tempCodePoint < 0x110000) {
              codePoint = tempCodePoint
            }
          }
      }
    }

    if (codePoint === null) {
      // we did not generate a valid codePoint so insert a
      // replacement char (U+FFFD) and advance only 1 byte
      codePoint = 0xFFFD
      bytesPerSequence = 1
    } else if (codePoint > 0xFFFF) {
      // encode to utf16 (surrogate pair dance)
      codePoint -= 0x10000
      res.push(codePoint >>> 10 & 0x3FF | 0xD800)
      codePoint = 0xDC00 | codePoint & 0x3FF
    }

    res.push(codePoint)
    i += bytesPerSequence
  }

  return decodeCodePointsArray(res)
}

// Based on http://stackoverflow.com/a/22747272/680742, the browser with
// the lowest limit is Chrome, with 0x10000 args.
// We go 1 magnitude less, for safety
var MAX_ARGUMENTS_LENGTH = 0x1000

function decodeCodePointsArray (codePoints) {
  var len = codePoints.length
  if (len <= MAX_ARGUMENTS_LENGTH) {
    return String.fromCharCode.apply(String, codePoints) // avoid extra slice()
  }

  // Decode in chunks to avoid "call stack size exceeded".
  var res = ''
  var i = 0
  while (i < len) {
    res += String.fromCharCode.apply(
      String,
      codePoints.slice(i, i += MAX_ARGUMENTS_LENGTH)
    )
  }
  return res
}

function asciiSlice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i] & 0x7F)
  }
  return ret
}

function latin1Slice (buf, start, end) {
  var ret = ''
  end = Math.min(buf.length, end)

  for (var i = start; i < end; ++i) {
    ret += String.fromCharCode(buf[i])
  }
  return ret
}

function hexSlice (buf, start, end) {
  var len = buf.length

  if (!start || start < 0) start = 0
  if (!end || end < 0 || end > len) end = len

  var out = ''
  for (var i = start; i < end; ++i) {
    out += toHex(buf[i])
  }
  return out
}

function utf16leSlice (buf, start, end) {
  var bytes = buf.slice(start, end)
  var res = ''
  for (var i = 0; i < bytes.length; i += 2) {
    res += String.fromCharCode(bytes[i] + bytes[i + 1] * 256)
  }
  return res
}

Buffer.prototype.slice = function slice (start, end) {
  var len = this.length
  start = ~~start
  end = end === undefined ? len : ~~end

  if (start < 0) {
    start += len
    if (start < 0) start = 0
  } else if (start > len) {
    start = len
  }

  if (end < 0) {
    end += len
    if (end < 0) end = 0
  } else if (end > len) {
    end = len
  }

  if (end < start) end = start

  var newBuf
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    newBuf = this.subarray(start, end)
    newBuf.__proto__ = Buffer.prototype
  } else {
    var sliceLen = end - start
    newBuf = new Buffer(sliceLen, undefined)
    for (var i = 0; i < sliceLen; ++i) {
      newBuf[i] = this[i + start]
    }
  }

  return newBuf
}

/*
 * Need to make sure that buffer isn't trying to write out of bounds.
 */
function checkOffset (offset, ext, length) {
  if ((offset % 1) !== 0 || offset < 0) throw new RangeError('offset is not uint')
  if (offset + ext > length) throw new RangeError('Trying to access beyond buffer length')
}

Buffer.prototype.readUIntLE = function readUIntLE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }

  return val
}

Buffer.prototype.readUIntBE = function readUIntBE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) {
    checkOffset(offset, byteLength, this.length)
  }

  var val = this[offset + --byteLength]
  var mul = 1
  while (byteLength > 0 && (mul *= 0x100)) {
    val += this[offset + --byteLength] * mul
  }

  return val
}

Buffer.prototype.readUInt8 = function readUInt8 (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 1, this.length)
  return this[offset]
}

Buffer.prototype.readUInt16LE = function readUInt16LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  return this[offset] | (this[offset + 1] << 8)
}

Buffer.prototype.readUInt16BE = function readUInt16BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  return (this[offset] << 8) | this[offset + 1]
}

Buffer.prototype.readUInt32LE = function readUInt32LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return ((this[offset]) |
      (this[offset + 1] << 8) |
      (this[offset + 2] << 16)) +
      (this[offset + 3] * 0x1000000)
}

Buffer.prototype.readUInt32BE = function readUInt32BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] * 0x1000000) +
    ((this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    this[offset + 3])
}

Buffer.prototype.readIntLE = function readIntLE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var val = this[offset]
  var mul = 1
  var i = 0
  while (++i < byteLength && (mul *= 0x100)) {
    val += this[offset + i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readIntBE = function readIntBE (offset, byteLength, noAssert) {
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) checkOffset(offset, byteLength, this.length)

  var i = byteLength
  var mul = 1
  var val = this[offset + --i]
  while (i > 0 && (mul *= 0x100)) {
    val += this[offset + --i] * mul
  }
  mul *= 0x80

  if (val >= mul) val -= Math.pow(2, 8 * byteLength)

  return val
}

Buffer.prototype.readInt8 = function readInt8 (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 1, this.length)
  if (!(this[offset] & 0x80)) return (this[offset])
  return ((0xff - this[offset] + 1) * -1)
}

Buffer.prototype.readInt16LE = function readInt16LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset] | (this[offset + 1] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt16BE = function readInt16BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 2, this.length)
  var val = this[offset + 1] | (this[offset] << 8)
  return (val & 0x8000) ? val | 0xFFFF0000 : val
}

Buffer.prototype.readInt32LE = function readInt32LE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset]) |
    (this[offset + 1] << 8) |
    (this[offset + 2] << 16) |
    (this[offset + 3] << 24)
}

Buffer.prototype.readInt32BE = function readInt32BE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)

  return (this[offset] << 24) |
    (this[offset + 1] << 16) |
    (this[offset + 2] << 8) |
    (this[offset + 3])
}

Buffer.prototype.readFloatLE = function readFloatLE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, true, 23, 4)
}

Buffer.prototype.readFloatBE = function readFloatBE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 4, this.length)
  return ieee754.read(this, offset, false, 23, 4)
}

Buffer.prototype.readDoubleLE = function readDoubleLE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, true, 52, 8)
}

Buffer.prototype.readDoubleBE = function readDoubleBE (offset, noAssert) {
  if (!noAssert) checkOffset(offset, 8, this.length)
  return ieee754.read(this, offset, false, 52, 8)
}

function checkInt (buf, value, offset, ext, max, min) {
  if (!Buffer.isBuffer(buf)) throw new TypeError('"buffer" argument must be a Buffer instance')
  if (value > max || value < min) throw new RangeError('"value" argument is out of bounds')
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
}

Buffer.prototype.writeUIntLE = function writeUIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var mul = 1
  var i = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUIntBE = function writeUIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  byteLength = byteLength | 0
  if (!noAssert) {
    var maxBytes = Math.pow(2, 8 * byteLength) - 1
    checkInt(this, value, offset, byteLength, maxBytes, 0)
  }

  var i = byteLength - 1
  var mul = 1
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    this[offset + i] = (value / mul) & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeUInt8 = function writeUInt8 (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 1, 0xff, 0)
  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)
  this[offset] = (value & 0xff)
  return offset + 1
}

function objectWriteUInt16 (buf, value, offset, littleEndian) {
  if (value < 0) value = 0xffff + value + 1
  for (var i = 0, j = Math.min(buf.length - offset, 2); i < j; ++i) {
    buf[offset + i] = (value & (0xff << (8 * (littleEndian ? i : 1 - i)))) >>>
      (littleEndian ? i : 1 - i) * 8
  }
}

Buffer.prototype.writeUInt16LE = function writeUInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value & 0xff)
    this[offset + 1] = (value >>> 8)
  } else {
    objectWriteUInt16(this, value, offset, true)
  }
  return offset + 2
}

Buffer.prototype.writeUInt16BE = function writeUInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0xffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 8)
    this[offset + 1] = (value & 0xff)
  } else {
    objectWriteUInt16(this, value, offset, false)
  }
  return offset + 2
}

function objectWriteUInt32 (buf, value, offset, littleEndian) {
  if (value < 0) value = 0xffffffff + value + 1
  for (var i = 0, j = Math.min(buf.length - offset, 4); i < j; ++i) {
    buf[offset + i] = (value >>> (littleEndian ? i : 3 - i) * 8) & 0xff
  }
}

Buffer.prototype.writeUInt32LE = function writeUInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset + 3] = (value >>> 24)
    this[offset + 2] = (value >>> 16)
    this[offset + 1] = (value >>> 8)
    this[offset] = (value & 0xff)
  } else {
    objectWriteUInt32(this, value, offset, true)
  }
  return offset + 4
}

Buffer.prototype.writeUInt32BE = function writeUInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0xffffffff, 0)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 24)
    this[offset + 1] = (value >>> 16)
    this[offset + 2] = (value >>> 8)
    this[offset + 3] = (value & 0xff)
  } else {
    objectWriteUInt32(this, value, offset, false)
  }
  return offset + 4
}

Buffer.prototype.writeIntLE = function writeIntLE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) {
    var limit = Math.pow(2, 8 * byteLength - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = 0
  var mul = 1
  var sub = 0
  this[offset] = value & 0xFF
  while (++i < byteLength && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i - 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeIntBE = function writeIntBE (value, offset, byteLength, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) {
    var limit = Math.pow(2, 8 * byteLength - 1)

    checkInt(this, value, offset, byteLength, limit - 1, -limit)
  }

  var i = byteLength - 1
  var mul = 1
  var sub = 0
  this[offset + i] = value & 0xFF
  while (--i >= 0 && (mul *= 0x100)) {
    if (value < 0 && sub === 0 && this[offset + i + 1] !== 0) {
      sub = 1
    }
    this[offset + i] = ((value / mul) >> 0) - sub & 0xFF
  }

  return offset + byteLength
}

Buffer.prototype.writeInt8 = function writeInt8 (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 1, 0x7f, -0x80)
  if (!Buffer.TYPED_ARRAY_SUPPORT) value = Math.floor(value)
  if (value < 0) value = 0xff + value + 1
  this[offset] = (value & 0xff)
  return offset + 1
}

Buffer.prototype.writeInt16LE = function writeInt16LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value & 0xff)
    this[offset + 1] = (value >>> 8)
  } else {
    objectWriteUInt16(this, value, offset, true)
  }
  return offset + 2
}

Buffer.prototype.writeInt16BE = function writeInt16BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 2, 0x7fff, -0x8000)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 8)
    this[offset + 1] = (value & 0xff)
  } else {
    objectWriteUInt16(this, value, offset, false)
  }
  return offset + 2
}

Buffer.prototype.writeInt32LE = function writeInt32LE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value & 0xff)
    this[offset + 1] = (value >>> 8)
    this[offset + 2] = (value >>> 16)
    this[offset + 3] = (value >>> 24)
  } else {
    objectWriteUInt32(this, value, offset, true)
  }
  return offset + 4
}

Buffer.prototype.writeInt32BE = function writeInt32BE (value, offset, noAssert) {
  value = +value
  offset = offset | 0
  if (!noAssert) checkInt(this, value, offset, 4, 0x7fffffff, -0x80000000)
  if (value < 0) value = 0xffffffff + value + 1
  if (Buffer.TYPED_ARRAY_SUPPORT) {
    this[offset] = (value >>> 24)
    this[offset + 1] = (value >>> 16)
    this[offset + 2] = (value >>> 8)
    this[offset + 3] = (value & 0xff)
  } else {
    objectWriteUInt32(this, value, offset, false)
  }
  return offset + 4
}

function checkIEEE754 (buf, value, offset, ext, max, min) {
  if (offset + ext > buf.length) throw new RangeError('Index out of range')
  if (offset < 0) throw new RangeError('Index out of range')
}

function writeFloat (buf, value, offset, littleEndian, noAssert) {
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 4, 3.4028234663852886e+38, -3.4028234663852886e+38)
  }
  ieee754.write(buf, value, offset, littleEndian, 23, 4)
  return offset + 4
}

Buffer.prototype.writeFloatLE = function writeFloatLE (value, offset, noAssert) {
  return writeFloat(this, value, offset, true, noAssert)
}

Buffer.prototype.writeFloatBE = function writeFloatBE (value, offset, noAssert) {
  return writeFloat(this, value, offset, false, noAssert)
}

function writeDouble (buf, value, offset, littleEndian, noAssert) {
  if (!noAssert) {
    checkIEEE754(buf, value, offset, 8, 1.7976931348623157E+308, -1.7976931348623157E+308)
  }
  ieee754.write(buf, value, offset, littleEndian, 52, 8)
  return offset + 8
}

Buffer.prototype.writeDoubleLE = function writeDoubleLE (value, offset, noAssert) {
  return writeDouble(this, value, offset, true, noAssert)
}

Buffer.prototype.writeDoubleBE = function writeDoubleBE (value, offset, noAssert) {
  return writeDouble(this, value, offset, false, noAssert)
}

// copy(targetBuffer, targetStart=0, sourceStart=0, sourceEnd=buffer.length)
Buffer.prototype.copy = function copy (target, targetStart, start, end) {
  if (!start) start = 0
  if (!end && end !== 0) end = this.length
  if (targetStart >= target.length) targetStart = target.length
  if (!targetStart) targetStart = 0
  if (end > 0 && end < start) end = start

  // Copy 0 bytes; we're done
  if (end === start) return 0
  if (target.length === 0 || this.length === 0) return 0

  // Fatal error conditions
  if (targetStart < 0) {
    throw new RangeError('targetStart out of bounds')
  }
  if (start < 0 || start >= this.length) throw new RangeError('sourceStart out of bounds')
  if (end < 0) throw new RangeError('sourceEnd out of bounds')

  // Are we oob?
  if (end > this.length) end = this.length
  if (target.length - targetStart < end - start) {
    end = target.length - targetStart + start
  }

  var len = end - start
  var i

  if (this === target && start < targetStart && targetStart < end) {
    // descending copy from end
    for (i = len - 1; i >= 0; --i) {
      target[i + targetStart] = this[i + start]
    }
  } else if (len < 1000 || !Buffer.TYPED_ARRAY_SUPPORT) {
    // ascending copy from start
    for (i = 0; i < len; ++i) {
      target[i + targetStart] = this[i + start]
    }
  } else {
    Uint8Array.prototype.set.call(
      target,
      this.subarray(start, start + len),
      targetStart
    )
  }

  return len
}

// Usage:
//    buffer.fill(number[, offset[, end]])
//    buffer.fill(buffer[, offset[, end]])
//    buffer.fill(string[, offset[, end]][, encoding])
Buffer.prototype.fill = function fill (val, start, end, encoding) {
  // Handle string cases:
  if (typeof val === 'string') {
    if (typeof start === 'string') {
      encoding = start
      start = 0
      end = this.length
    } else if (typeof end === 'string') {
      encoding = end
      end = this.length
    }
    if (val.length === 1) {
      var code = val.charCodeAt(0)
      if (code < 256) {
        val = code
      }
    }
    if (encoding !== undefined && typeof encoding !== 'string') {
      throw new TypeError('encoding must be a string')
    }
    if (typeof encoding === 'string' && !Buffer.isEncoding(encoding)) {
      throw new TypeError('Unknown encoding: ' + encoding)
    }
  } else if (typeof val === 'number') {
    val = val & 255
  }

  // Invalid ranges are not set to a default, so can range check early.
  if (start < 0 || this.length < start || this.length < end) {
    throw new RangeError('Out of range index')
  }

  if (end <= start) {
    return this
  }

  start = start >>> 0
  end = end === undefined ? this.length : end >>> 0

  if (!val) val = 0

  var i
  if (typeof val === 'number') {
    for (i = start; i < end; ++i) {
      this[i] = val
    }
  } else {
    var bytes = Buffer.isBuffer(val)
      ? val
      : utf8ToBytes(new Buffer(val, encoding).toString())
    var len = bytes.length
    for (i = 0; i < end - start; ++i) {
      this[i + start] = bytes[i % len]
    }
  }

  return this
}

// HELPER FUNCTIONS
// ================

var INVALID_BASE64_RE = /[^+\/0-9A-Za-z-_]/g

function base64clean (str) {
  // Node strips out invalid characters like \n and \t from the string, base64-js does not
  str = stringtrim(str).replace(INVALID_BASE64_RE, '')
  // Node converts strings with length < 2 to ''
  if (str.length < 2) return ''
  // Node allows for non-padded base64 strings (missing trailing ===), base64-js does not
  while (str.length % 4 !== 0) {
    str = str + '='
  }
  return str
}

function stringtrim (str) {
  if (str.trim) return str.trim()
  return str.replace(/^\s+|\s+$/g, '')
}

function toHex (n) {
  if (n < 16) return '0' + n.toString(16)
  return n.toString(16)
}

function utf8ToBytes (string, units) {
  units = units || Infinity
  var codePoint
  var length = string.length
  var leadSurrogate = null
  var bytes = []

  for (var i = 0; i < length; ++i) {
    codePoint = string.charCodeAt(i)

    // is surrogate component
    if (codePoint > 0xD7FF && codePoint < 0xE000) {
      // last char was a lead
      if (!leadSurrogate) {
        // no lead yet
        if (codePoint > 0xDBFF) {
          // unexpected trail
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        } else if (i + 1 === length) {
          // unpaired lead
          if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
          continue
        }

        // valid lead
        leadSurrogate = codePoint

        continue
      }

      // 2 leads in a row
      if (codePoint < 0xDC00) {
        if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
        leadSurrogate = codePoint
        continue
      }

      // valid surrogate pair
      codePoint = (leadSurrogate - 0xD800 << 10 | codePoint - 0xDC00) + 0x10000
    } else if (leadSurrogate) {
      // valid bmp char, but last char was a lead
      if ((units -= 3) > -1) bytes.push(0xEF, 0xBF, 0xBD)
    }

    leadSurrogate = null

    // encode utf8
    if (codePoint < 0x80) {
      if ((units -= 1) < 0) break
      bytes.push(codePoint)
    } else if (codePoint < 0x800) {
      if ((units -= 2) < 0) break
      bytes.push(
        codePoint >> 0x6 | 0xC0,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x10000) {
      if ((units -= 3) < 0) break
      bytes.push(
        codePoint >> 0xC | 0xE0,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else if (codePoint < 0x110000) {
      if ((units -= 4) < 0) break
      bytes.push(
        codePoint >> 0x12 | 0xF0,
        codePoint >> 0xC & 0x3F | 0x80,
        codePoint >> 0x6 & 0x3F | 0x80,
        codePoint & 0x3F | 0x80
      )
    } else {
      throw new Error('Invalid code point')
    }
  }

  return bytes
}

function asciiToBytes (str) {
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    // Node's code seems to be doing this and not & 0x7F..
    byteArray.push(str.charCodeAt(i) & 0xFF)
  }
  return byteArray
}

function utf16leToBytes (str, units) {
  var c, hi, lo
  var byteArray = []
  for (var i = 0; i < str.length; ++i) {
    if ((units -= 2) < 0) break

    c = str.charCodeAt(i)
    hi = c >> 8
    lo = c % 256
    byteArray.push(lo)
    byteArray.push(hi)
  }

  return byteArray
}

function base64ToBytes (str) {
  return base64.toByteArray(base64clean(str))
}

function blitBuffer (src, dst, offset, length) {
  for (var i = 0; i < length; ++i) {
    if ((i + offset >= dst.length) || (i >= src.length)) break
    dst[i + offset] = src[i]
  }
  return i
}

function isnan (val) {
  return val !== val // eslint-disable-line no-self-compare
}

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
},{"base64-js":4,"ieee754":5,"isarray":6}],4:[function(require,module,exports){
'use strict'

exports.byteLength = byteLength
exports.toByteArray = toByteArray
exports.fromByteArray = fromByteArray

var lookup = []
var revLookup = []
var Arr = typeof Uint8Array !== 'undefined' ? Uint8Array : Array

var code = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/'
for (var i = 0, len = code.length; i < len; ++i) {
  lookup[i] = code[i]
  revLookup[code.charCodeAt(i)] = i
}

revLookup['-'.charCodeAt(0)] = 62
revLookup['_'.charCodeAt(0)] = 63

function placeHoldersCount (b64) {
  var len = b64.length
  if (len % 4 > 0) {
    throw new Error('Invalid string. Length must be a multiple of 4')
  }

  // the number of equal signs (place holders)
  // if there are two placeholders, than the two characters before it
  // represent one byte
  // if there is only one, then the three characters before it represent 2 bytes
  // this is just a cheap hack to not do indexOf twice
  return b64[len - 2] === '=' ? 2 : b64[len - 1] === '=' ? 1 : 0
}

function byteLength (b64) {
  // base64 is 4/3 + up to two characters of the original data
  return b64.length * 3 / 4 - placeHoldersCount(b64)
}

function toByteArray (b64) {
  var i, j, l, tmp, placeHolders, arr
  var len = b64.length
  placeHolders = placeHoldersCount(b64)

  arr = new Arr(len * 3 / 4 - placeHolders)

  // if there are placeholders, only get up to the last complete 4 chars
  l = placeHolders > 0 ? len - 4 : len

  var L = 0

  for (i = 0, j = 0; i < l; i += 4, j += 3) {
    tmp = (revLookup[b64.charCodeAt(i)] << 18) | (revLookup[b64.charCodeAt(i + 1)] << 12) | (revLookup[b64.charCodeAt(i + 2)] << 6) | revLookup[b64.charCodeAt(i + 3)]
    arr[L++] = (tmp >> 16) & 0xFF
    arr[L++] = (tmp >> 8) & 0xFF
    arr[L++] = tmp & 0xFF
  }

  if (placeHolders === 2) {
    tmp = (revLookup[b64.charCodeAt(i)] << 2) | (revLookup[b64.charCodeAt(i + 1)] >> 4)
    arr[L++] = tmp & 0xFF
  } else if (placeHolders === 1) {
    tmp = (revLookup[b64.charCodeAt(i)] << 10) | (revLookup[b64.charCodeAt(i + 1)] << 4) | (revLookup[b64.charCodeAt(i + 2)] >> 2)
    arr[L++] = (tmp >> 8) & 0xFF
    arr[L++] = tmp & 0xFF
  }

  return arr
}

function tripletToBase64 (num) {
  return lookup[num >> 18 & 0x3F] + lookup[num >> 12 & 0x3F] + lookup[num >> 6 & 0x3F] + lookup[num & 0x3F]
}

function encodeChunk (uint8, start, end) {
  var tmp
  var output = []
  for (var i = start; i < end; i += 3) {
    tmp = (uint8[i] << 16) + (uint8[i + 1] << 8) + (uint8[i + 2])
    output.push(tripletToBase64(tmp))
  }
  return output.join('')
}

function fromByteArray (uint8) {
  var tmp
  var len = uint8.length
  var extraBytes = len % 3 // if we have 1 byte left, pad 2 bytes
  var output = ''
  var parts = []
  var maxChunkLength = 16383 // must be multiple of 3

  // go through the array every three bytes, we'll deal with trailing stuff later
  for (var i = 0, len2 = len - extraBytes; i < len2; i += maxChunkLength) {
    parts.push(encodeChunk(uint8, i, (i + maxChunkLength) > len2 ? len2 : (i + maxChunkLength)))
  }

  // pad the end with zeros, but make sure to not forget the extra bytes
  if (extraBytes === 1) {
    tmp = uint8[len - 1]
    output += lookup[tmp >> 2]
    output += lookup[(tmp << 4) & 0x3F]
    output += '=='
  } else if (extraBytes === 2) {
    tmp = (uint8[len - 2] << 8) + (uint8[len - 1])
    output += lookup[tmp >> 10]
    output += lookup[(tmp >> 4) & 0x3F]
    output += lookup[(tmp << 2) & 0x3F]
    output += '='
  }

  parts.push(output)

  return parts.join('')
}

},{}],5:[function(require,module,exports){
exports.read = function (buffer, offset, isLE, mLen, nBytes) {
  var e, m
  var eLen = nBytes * 8 - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var nBits = -7
  var i = isLE ? (nBytes - 1) : 0
  var d = isLE ? -1 : 1
  var s = buffer[offset + i]

  i += d

  e = s & ((1 << (-nBits)) - 1)
  s >>= (-nBits)
  nBits += eLen
  for (; nBits > 0; e = e * 256 + buffer[offset + i], i += d, nBits -= 8) {}

  m = e & ((1 << (-nBits)) - 1)
  e >>= (-nBits)
  nBits += mLen
  for (; nBits > 0; m = m * 256 + buffer[offset + i], i += d, nBits -= 8) {}

  if (e === 0) {
    e = 1 - eBias
  } else if (e === eMax) {
    return m ? NaN : ((s ? -1 : 1) * Infinity)
  } else {
    m = m + Math.pow(2, mLen)
    e = e - eBias
  }
  return (s ? -1 : 1) * m * Math.pow(2, e - mLen)
}

exports.write = function (buffer, value, offset, isLE, mLen, nBytes) {
  var e, m, c
  var eLen = nBytes * 8 - mLen - 1
  var eMax = (1 << eLen) - 1
  var eBias = eMax >> 1
  var rt = (mLen === 23 ? Math.pow(2, -24) - Math.pow(2, -77) : 0)
  var i = isLE ? 0 : (nBytes - 1)
  var d = isLE ? 1 : -1
  var s = value < 0 || (value === 0 && 1 / value < 0) ? 1 : 0

  value = Math.abs(value)

  if (isNaN(value) || value === Infinity) {
    m = isNaN(value) ? 1 : 0
    e = eMax
  } else {
    e = Math.floor(Math.log(value) / Math.LN2)
    if (value * (c = Math.pow(2, -e)) < 1) {
      e--
      c *= 2
    }
    if (e + eBias >= 1) {
      value += rt / c
    } else {
      value += rt * Math.pow(2, 1 - eBias)
    }
    if (value * c >= 2) {
      e++
      c /= 2
    }

    if (e + eBias >= eMax) {
      m = 0
      e = eMax
    } else if (e + eBias >= 1) {
      m = (value * c - 1) * Math.pow(2, mLen)
      e = e + eBias
    } else {
      m = value * Math.pow(2, eBias - 1) * Math.pow(2, mLen)
      e = 0
    }
  }

  for (; mLen >= 8; buffer[offset + i] = m & 0xff, i += d, m /= 256, mLen -= 8) {}

  e = (e << mLen) | m
  eLen += mLen
  for (; eLen > 0; buffer[offset + i] = e & 0xff, i += d, e /= 256, eLen -= 8) {}

  buffer[offset + i - d] |= s * 128
}

},{}],6:[function(require,module,exports){
var toString = {}.toString;

module.exports = Array.isArray || function (arr) {
  return toString.call(arr) == '[object Array]';
};

},{}],7:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

function EventEmitter() {
  this._events = this._events || {};
  this._maxListeners = this._maxListeners || undefined;
}
module.exports = EventEmitter;

// Backwards-compat with node 0.10.x
EventEmitter.EventEmitter = EventEmitter;

EventEmitter.prototype._events = undefined;
EventEmitter.prototype._maxListeners = undefined;

// By default EventEmitters will print a warning if more than 10 listeners are
// added to it. This is a useful default which helps finding memory leaks.
EventEmitter.defaultMaxListeners = 10;

// Obviously not all Emitters should be limited to 10. This function allows
// that to be increased. Set to zero for unlimited.
EventEmitter.prototype.setMaxListeners = function(n) {
  if (!isNumber(n) || n < 0 || isNaN(n))
    throw TypeError('n must be a positive number');
  this._maxListeners = n;
  return this;
};

EventEmitter.prototype.emit = function(type) {
  var er, handler, len, args, i, listeners;

  if (!this._events)
    this._events = {};

  // If there is no 'error' event listener then throw.
  if (type === 'error') {
    if (!this._events.error ||
        (isObject(this._events.error) && !this._events.error.length)) {
      er = arguments[1];
      if (er instanceof Error) {
        throw er; // Unhandled 'error' event
      } else {
        // At least give some kind of context to the user
        var err = new Error('Uncaught, unspecified "error" event. (' + er + ')');
        err.context = er;
        throw err;
      }
    }
  }

  handler = this._events[type];

  if (isUndefined(handler))
    return false;

  if (isFunction(handler)) {
    switch (arguments.length) {
      // fast cases
      case 1:
        handler.call(this);
        break;
      case 2:
        handler.call(this, arguments[1]);
        break;
      case 3:
        handler.call(this, arguments[1], arguments[2]);
        break;
      // slower
      default:
        args = Array.prototype.slice.call(arguments, 1);
        handler.apply(this, args);
    }
  } else if (isObject(handler)) {
    args = Array.prototype.slice.call(arguments, 1);
    listeners = handler.slice();
    len = listeners.length;
    for (i = 0; i < len; i++)
      listeners[i].apply(this, args);
  }

  return true;
};

EventEmitter.prototype.addListener = function(type, listener) {
  var m;

  if (!isFunction(listener))
    throw TypeError('listener must be a function');

  if (!this._events)
    this._events = {};

  // To avoid recursion in the case that type === "newListener"! Before
  // adding it to the listeners, first emit "newListener".
  if (this._events.newListener)
    this.emit('newListener', type,
              isFunction(listener.listener) ?
              listener.listener : listener);

  if (!this._events[type])
    // Optimize the case of one listener. Don't need the extra array object.
    this._events[type] = listener;
  else if (isObject(this._events[type]))
    // If we've already got an array, just append.
    this._events[type].push(listener);
  else
    // Adding the second element, need to change to array.
    this._events[type] = [this._events[type], listener];

  // Check for listener leak
  if (isObject(this._events[type]) && !this._events[type].warned) {
    if (!isUndefined(this._maxListeners)) {
      m = this._maxListeners;
    } else {
      m = EventEmitter.defaultMaxListeners;
    }

    if (m && m > 0 && this._events[type].length > m) {
      this._events[type].warned = true;
      console.error('(node) warning: possible EventEmitter memory ' +
                    'leak detected. %d listeners added. ' +
                    'Use emitter.setMaxListeners() to increase limit.',
                    this._events[type].length);
      if (typeof console.trace === 'function') {
        // not supported in IE 10
        console.trace();
      }
    }
  }

  return this;
};

EventEmitter.prototype.on = EventEmitter.prototype.addListener;

EventEmitter.prototype.once = function(type, listener) {
  if (!isFunction(listener))
    throw TypeError('listener must be a function');

  var fired = false;

  function g() {
    this.removeListener(type, g);

    if (!fired) {
      fired = true;
      listener.apply(this, arguments);
    }
  }

  g.listener = listener;
  this.on(type, g);

  return this;
};

// emits a 'removeListener' event iff the listener was removed
EventEmitter.prototype.removeListener = function(type, listener) {
  var list, position, length, i;

  if (!isFunction(listener))
    throw TypeError('listener must be a function');

  if (!this._events || !this._events[type])
    return this;

  list = this._events[type];
  length = list.length;
  position = -1;

  if (list === listener ||
      (isFunction(list.listener) && list.listener === listener)) {
    delete this._events[type];
    if (this._events.removeListener)
      this.emit('removeListener', type, listener);

  } else if (isObject(list)) {
    for (i = length; i-- > 0;) {
      if (list[i] === listener ||
          (list[i].listener && list[i].listener === listener)) {
        position = i;
        break;
      }
    }

    if (position < 0)
      return this;

    if (list.length === 1) {
      list.length = 0;
      delete this._events[type];
    } else {
      list.splice(position, 1);
    }

    if (this._events.removeListener)
      this.emit('removeListener', type, listener);
  }

  return this;
};

EventEmitter.prototype.removeAllListeners = function(type) {
  var key, listeners;

  if (!this._events)
    return this;

  // not listening for removeListener, no need to emit
  if (!this._events.removeListener) {
    if (arguments.length === 0)
      this._events = {};
    else if (this._events[type])
      delete this._events[type];
    return this;
  }

  // emit removeListener for all listeners on all events
  if (arguments.length === 0) {
    for (key in this._events) {
      if (key === 'removeListener') continue;
      this.removeAllListeners(key);
    }
    this.removeAllListeners('removeListener');
    this._events = {};
    return this;
  }

  listeners = this._events[type];

  if (isFunction(listeners)) {
    this.removeListener(type, listeners);
  } else if (listeners) {
    // LIFO order
    while (listeners.length)
      this.removeListener(type, listeners[listeners.length - 1]);
  }
  delete this._events[type];

  return this;
};

EventEmitter.prototype.listeners = function(type) {
  var ret;
  if (!this._events || !this._events[type])
    ret = [];
  else if (isFunction(this._events[type]))
    ret = [this._events[type]];
  else
    ret = this._events[type].slice();
  return ret;
};

EventEmitter.prototype.listenerCount = function(type) {
  if (this._events) {
    var evlistener = this._events[type];

    if (isFunction(evlistener))
      return 1;
    else if (evlistener)
      return evlistener.length;
  }
  return 0;
};

EventEmitter.listenerCount = function(emitter, type) {
  return emitter.listenerCount(type);
};

function isFunction(arg) {
  return typeof arg === 'function';
}

function isNumber(arg) {
  return typeof arg === 'number';
}

function isObject(arg) {
  return typeof arg === 'object' && arg !== null;
}

function isUndefined(arg) {
  return arg === void 0;
}

},{}],8:[function(require,module,exports){
if (typeof Object.create === 'function') {
  // implementation from standard node.js 'util' module
  module.exports = function inherits(ctor, superCtor) {
    ctor.super_ = superCtor
    ctor.prototype = Object.create(superCtor.prototype, {
      constructor: {
        value: ctor,
        enumerable: false,
        writable: true,
        configurable: true
      }
    });
  };
} else {
  // old school shim for old browsers
  module.exports = function inherits(ctor, superCtor) {
    ctor.super_ = superCtor
    var TempCtor = function () {}
    TempCtor.prototype = superCtor.prototype
    ctor.prototype = new TempCtor()
    ctor.prototype.constructor = ctor
  }
}

},{}],9:[function(require,module,exports){
/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <<EMAIL>> <http://feross.org>
 * @license  MIT
 */

// The _isBuffer check is for Safari 5-7 support, because it's missing
// Object.prototype.constructor. Remove this eventually
module.exports = function (obj) {
  return obj != null && (isBuffer(obj) || isSlowBuffer(obj) || !!obj._isBuffer)
}

function isBuffer (obj) {
  return !!obj.constructor && typeof obj.constructor.isBuffer === 'function' && obj.constructor.isBuffer(obj)
}

// For Node v0.10 support. Remove this eventually.
function isSlowBuffer (obj) {
  return typeof obj.readFloatLE === 'function' && typeof obj.slice === 'function' && isBuffer(obj.slice(0, 0))
}

},{}],10:[function(require,module,exports){
// shim for using process in browser
var process = module.exports = {};

// cached from whatever global is present so that test runners that stub it
// don't break things.  But we need to wrap it in a try catch in case it is
// wrapped in strict mode code which doesn't define any globals.  It's inside a
// function because try/catches deoptimize in certain engines.

var cachedSetTimeout;
var cachedClearTimeout;

function defaultSetTimout() {
    throw new Error('setTimeout has not been defined');
}
function defaultClearTimeout () {
    throw new Error('clearTimeout has not been defined');
}
(function () {
    try {
        if (typeof setTimeout === 'function') {
            cachedSetTimeout = setTimeout;
        } else {
            cachedSetTimeout = defaultSetTimout;
        }
    } catch (e) {
        cachedSetTimeout = defaultSetTimout;
    }
    try {
        if (typeof clearTimeout === 'function') {
            cachedClearTimeout = clearTimeout;
        } else {
            cachedClearTimeout = defaultClearTimeout;
        }
    } catch (e) {
        cachedClearTimeout = defaultClearTimeout;
    }
} ())
function runTimeout(fun) {
    if (cachedSetTimeout === setTimeout) {
        //normal enviroments in sane situations
        return setTimeout(fun, 0);
    }
    // if setTimeout wasn't available but was latter defined
    if ((cachedSetTimeout === defaultSetTimout || !cachedSetTimeout) && setTimeout) {
        cachedSetTimeout = setTimeout;
        return setTimeout(fun, 0);
    }
    try {
        // when when somebody has screwed with setTimeout but no I.E. maddness
        return cachedSetTimeout(fun, 0);
    } catch(e){
        try {
            // When we are in I.E. but the script has been evaled so I.E. doesn't trust the global object when called normally
            return cachedSetTimeout.call(null, fun, 0);
        } catch(e){
            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error
            return cachedSetTimeout.call(this, fun, 0);
        }
    }


}
function runClearTimeout(marker) {
    if (cachedClearTimeout === clearTimeout) {
        //normal enviroments in sane situations
        return clearTimeout(marker);
    }
    // if clearTimeout wasn't available but was latter defined
    if ((cachedClearTimeout === defaultClearTimeout || !cachedClearTimeout) && clearTimeout) {
        cachedClearTimeout = clearTimeout;
        return clearTimeout(marker);
    }
    try {
        // when when somebody has screwed with setTimeout but no I.E. maddness
        return cachedClearTimeout(marker);
    } catch (e){
        try {
            // When we are in I.E. but the script has been evaled so I.E. doesn't  trust the global object when called normally
            return cachedClearTimeout.call(null, marker);
        } catch (e){
            // same as above but when it's a version of I.E. that must have the global object for 'this', hopfully our context correct otherwise it will throw a global error.
            // Some versions of I.E. have different rules for clearTimeout vs setTimeout
            return cachedClearTimeout.call(this, marker);
        }
    }



}
var queue = [];
var draining = false;
var currentQueue;
var queueIndex = -1;

function cleanUpNextTick() {
    if (!draining || !currentQueue) {
        return;
    }
    draining = false;
    if (currentQueue.length) {
        queue = currentQueue.concat(queue);
    } else {
        queueIndex = -1;
    }
    if (queue.length) {
        drainQueue();
    }
}

function drainQueue() {
    if (draining) {
        return;
    }
    var timeout = runTimeout(cleanUpNextTick);
    draining = true;

    var len = queue.length;
    while(len) {
        currentQueue = queue;
        queue = [];
        while (++queueIndex < len) {
            if (currentQueue) {
                currentQueue[queueIndex].run();
            }
        }
        queueIndex = -1;
        len = queue.length;
    }
    currentQueue = null;
    draining = false;
    runClearTimeout(timeout);
}

process.nextTick = function (fun) {
    var args = new Array(arguments.length - 1);
    if (arguments.length > 1) {
        for (var i = 1; i < arguments.length; i++) {
            args[i - 1] = arguments[i];
        }
    }
    queue.push(new Item(fun, args));
    if (queue.length === 1 && !draining) {
        runTimeout(drainQueue);
    }
};

// v8 likes predictible objects
function Item(fun, array) {
    this.fun = fun;
    this.array = array;
}
Item.prototype.run = function () {
    this.fun.apply(null, this.array);
};
process.title = 'browser';
process.browser = true;
process.env = {};
process.argv = [];
process.version = ''; // empty string to avoid regexp issues
process.versions = {};

function noop() {}

process.on = noop;
process.addListener = noop;
process.once = noop;
process.off = noop;
process.removeListener = noop;
process.removeAllListeners = noop;
process.emit = noop;

process.binding = function (name) {
    throw new Error('process.binding is not supported');
};

process.cwd = function () { return '/' };
process.chdir = function (dir) {
    throw new Error('process.chdir is not supported');
};
process.umask = function() { return 0; };

},{}],11:[function(require,module,exports){
module.exports = require("./lib/_stream_duplex.js")

},{"./lib/_stream_duplex.js":12}],12:[function(require,module,exports){
// a duplex stream is just a stream that is both readable and writable.
// Since JS doesn't have multiple prototypal inheritance, this class
// prototypally inherits from Readable, and then parasitically from
// Writable.

'use strict';

/*<replacement>*/

var objectKeys = Object.keys || function (obj) {
  var keys = [];
  for (var key in obj) {
    keys.push(key);
  }return keys;
};
/*</replacement>*/

module.exports = Duplex;

/*<replacement>*/
var processNextTick = require('process-nextick-args');
/*</replacement>*/

/*<replacement>*/
var util = require('core-util-is');
util.inherits = require('inherits');
/*</replacement>*/

var Readable = require('./_stream_readable');
var Writable = require('./_stream_writable');

util.inherits(Duplex, Readable);

var keys = objectKeys(Writable.prototype);
for (var v = 0; v < keys.length; v++) {
  var method = keys[v];
  if (!Duplex.prototype[method]) Duplex.prototype[method] = Writable.prototype[method];
}

function Duplex(options) {
  if (!(this instanceof Duplex)) return new Duplex(options);

  Readable.call(this, options);
  Writable.call(this, options);

  if (options && options.readable === false) this.readable = false;

  if (options && options.writable === false) this.writable = false;

  this.allowHalfOpen = true;
  if (options && options.allowHalfOpen === false) this.allowHalfOpen = false;

  this.once('end', onend);
}

// the no-half-open enforcer
function onend() {
  // if we allow half-open state, or if the writable side ended,
  // then we're ok.
  if (this.allowHalfOpen || this._writableState.ended) return;

  // no more data can be written.
  // But allow more writes to happen in this tick.
  processNextTick(onEndNT, this);
}

function onEndNT(self) {
  self.end();
}

function forEach(xs, f) {
  for (var i = 0, l = xs.length; i < l; i++) {
    f(xs[i], i);
  }
}
},{"./_stream_readable":14,"./_stream_writable":16,"core-util-is":19,"inherits":8,"process-nextick-args":21}],13:[function(require,module,exports){
// a passthrough stream.
// basically just the most minimal sort of Transform stream.
// Every written chunk gets output as-is.

'use strict';

module.exports = PassThrough;

var Transform = require('./_stream_transform');

/*<replacement>*/
var util = require('core-util-is');
util.inherits = require('inherits');
/*</replacement>*/

util.inherits(PassThrough, Transform);

function PassThrough(options) {
  if (!(this instanceof PassThrough)) return new PassThrough(options);

  Transform.call(this, options);
}

PassThrough.prototype._transform = function (chunk, encoding, cb) {
  cb(null, chunk);
};
},{"./_stream_transform":15,"core-util-is":19,"inherits":8}],14:[function(require,module,exports){
(function (process){
'use strict';

module.exports = Readable;

/*<replacement>*/
var processNextTick = require('process-nextick-args');
/*</replacement>*/

/*<replacement>*/
var isArray = require('isarray');
/*</replacement>*/

/*<replacement>*/
var Duplex;
/*</replacement>*/

Readable.ReadableState = ReadableState;

/*<replacement>*/
var EE = require('events').EventEmitter;

var EElistenerCount = function (emitter, type) {
  return emitter.listeners(type).length;
};
/*</replacement>*/

/*<replacement>*/
var Stream;
(function () {
  try {
    Stream = require('st' + 'ream');
  } catch (_) {} finally {
    if (!Stream) Stream = require('events').EventEmitter;
  }
})();
/*</replacement>*/

var Buffer = require('buffer').Buffer;
/*<replacement>*/
var bufferShim = require('buffer-shims');
/*</replacement>*/

/*<replacement>*/
var util = require('core-util-is');
util.inherits = require('inherits');
/*</replacement>*/

/*<replacement>*/
var debugUtil = require('util');
var debug = void 0;
if (debugUtil && debugUtil.debuglog) {
  debug = debugUtil.debuglog('stream');
} else {
  debug = function () {};
}
/*</replacement>*/

var BufferList = require('./internal/streams/BufferList');
var StringDecoder;

util.inherits(Readable, Stream);

function prependListener(emitter, event, fn) {
  // Sadly this is not cacheable as some libraries bundle their own
  // event emitter implementation with them.
  if (typeof emitter.prependListener === 'function') {
    return emitter.prependListener(event, fn);
  } else {
    // This is a hack to make sure that our error handler is attached before any
    // userland ones.  NEVER DO THIS. This is here only because this code needs
    // to continue to work with older versions of Node.js that do not include
    // the prependListener() method. The goal is to eventually remove this hack.
    if (!emitter._events || !emitter._events[event]) emitter.on(event, fn);else if (isArray(emitter._events[event])) emitter._events[event].unshift(fn);else emitter._events[event] = [fn, emitter._events[event]];
  }
}

function ReadableState(options, stream) {
  Duplex = Duplex || require('./_stream_duplex');

  options = options || {};

  // object stream flag. Used to make read(n) ignore n and to
  // make all the buffer merging and length checks go away
  this.objectMode = !!options.objectMode;

  if (stream instanceof Duplex) this.objectMode = this.objectMode || !!options.readableObjectMode;

  // the point at which it stops calling _read() to fill the buffer
  // Note: 0 is a valid value, means "don't call _read preemptively ever"
  var hwm = options.highWaterMark;
  var defaultHwm = this.objectMode ? 16 : 16 * 1024;
  this.highWaterMark = hwm || hwm === 0 ? hwm : defaultHwm;

  // cast to ints.
  this.highWaterMark = ~~this.highWaterMark;

  // A linked list is used to store data chunks instead of an array because the
  // linked list can remove elements from the beginning faster than
  // array.shift()
  this.buffer = new BufferList();
  this.length = 0;
  this.pipes = null;
  this.pipesCount = 0;
  this.flowing = null;
  this.ended = false;
  this.endEmitted = false;
  this.reading = false;

  // a flag to be able to tell if the onwrite cb is called immediately,
  // or on a later tick.  We set this to true at first, because any
  // actions that shouldn't happen until "later" should generally also
  // not happen before the first write call.
  this.sync = true;

  // whenever we return null, then we set a flag to say
  // that we're awaiting a 'readable' event emission.
  this.needReadable = false;
  this.emittedReadable = false;
  this.readableListening = false;
  this.resumeScheduled = false;

  // Crypto is kind of old and crusty.  Historically, its default string
  // encoding is 'binary' so we have to make this configurable.
  // Everything else in the universe uses 'utf8', though.
  this.defaultEncoding = options.defaultEncoding || 'utf8';

  // when piping, we only care about 'readable' events that happen
  // after read()ing all the bytes and not getting any pushback.
  this.ranOut = false;

  // the number of writers that are awaiting a drain event in .pipe()s
  this.awaitDrain = 0;

  // if true, a maybeReadMore has been scheduled
  this.readingMore = false;

  this.decoder = null;
  this.encoding = null;
  if (options.encoding) {
    if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;
    this.decoder = new StringDecoder(options.encoding);
    this.encoding = options.encoding;
  }
}

function Readable(options) {
  Duplex = Duplex || require('./_stream_duplex');

  if (!(this instanceof Readable)) return new Readable(options);

  this._readableState = new ReadableState(options, this);

  // legacy
  this.readable = true;

  if (options && typeof options.read === 'function') this._read = options.read;

  Stream.call(this);
}

// Manually shove something into the read() buffer.
// This returns true if the highWaterMark has not been hit yet,
// similar to how Writable.write() returns true if you should
// write() some more.
Readable.prototype.push = function (chunk, encoding) {
  var state = this._readableState;

  if (!state.objectMode && typeof chunk === 'string') {
    encoding = encoding || state.defaultEncoding;
    if (encoding !== state.encoding) {
      chunk = bufferShim.from(chunk, encoding);
      encoding = '';
    }
  }

  return readableAddChunk(this, state, chunk, encoding, false);
};

// Unshift should *always* be something directly out of read()
Readable.prototype.unshift = function (chunk) {
  var state = this._readableState;
  return readableAddChunk(this, state, chunk, '', true);
};

Readable.prototype.isPaused = function () {
  return this._readableState.flowing === false;
};

function readableAddChunk(stream, state, chunk, encoding, addToFront) {
  var er = chunkInvalid(state, chunk);
  if (er) {
    stream.emit('error', er);
  } else if (chunk === null) {
    state.reading = false;
    onEofChunk(stream, state);
  } else if (state.objectMode || chunk && chunk.length > 0) {
    if (state.ended && !addToFront) {
      var e = new Error('stream.push() after EOF');
      stream.emit('error', e);
    } else if (state.endEmitted && addToFront) {
      var _e = new Error('stream.unshift() after end event');
      stream.emit('error', _e);
    } else {
      var skipAdd;
      if (state.decoder && !addToFront && !encoding) {
        chunk = state.decoder.write(chunk);
        skipAdd = !state.objectMode && chunk.length === 0;
      }

      if (!addToFront) state.reading = false;

      // Don't add to the buffer if we've decoded to an empty string chunk and
      // we're not in object mode
      if (!skipAdd) {
        // if we want the data now, just emit it.
        if (state.flowing && state.length === 0 && !state.sync) {
          stream.emit('data', chunk);
          stream.read(0);
        } else {
          // update the buffer info.
          state.length += state.objectMode ? 1 : chunk.length;
          if (addToFront) state.buffer.unshift(chunk);else state.buffer.push(chunk);

          if (state.needReadable) emitReadable(stream);
        }
      }

      maybeReadMore(stream, state);
    }
  } else if (!addToFront) {
    state.reading = false;
  }

  return needMoreData(state);
}

// if it's past the high water mark, we can push in some more.
// Also, if we have no data yet, we can stand some
// more bytes.  This is to work around cases where hwm=0,
// such as the repl.  Also, if the push() triggered a
// readable event, and the user called read(largeNumber) such that
// needReadable was set, then we ought to push more, so that another
// 'readable' event will be triggered.
function needMoreData(state) {
  return !state.ended && (state.needReadable || state.length < state.highWaterMark || state.length === 0);
}

// backwards compatibility.
Readable.prototype.setEncoding = function (enc) {
  if (!StringDecoder) StringDecoder = require('string_decoder/').StringDecoder;
  this._readableState.decoder = new StringDecoder(enc);
  this._readableState.encoding = enc;
  return this;
};

// Don't raise the hwm > 8MB
var MAX_HWM = 0x800000;
function computeNewHighWaterMark(n) {
  if (n >= MAX_HWM) {
    n = MAX_HWM;
  } else {
    // Get the next highest power of 2 to prevent increasing hwm excessively in
    // tiny amounts
    n--;
    n |= n >>> 1;
    n |= n >>> 2;
    n |= n >>> 4;
    n |= n >>> 8;
    n |= n >>> 16;
    n++;
  }
  return n;
}

// This function is designed to be inlinable, so please take care when making
// changes to the function body.
function howMuchToRead(n, state) {
  if (n <= 0 || state.length === 0 && state.ended) return 0;
  if (state.objectMode) return 1;
  if (n !== n) {
    // Only flow one buffer at a time
    if (state.flowing && state.length) return state.buffer.head.data.length;else return state.length;
  }
  // If we're asking for more than the current hwm, then raise the hwm.
  if (n > state.highWaterMark) state.highWaterMark = computeNewHighWaterMark(n);
  if (n <= state.length) return n;
  // Don't have enough
  if (!state.ended) {
    state.needReadable = true;
    return 0;
  }
  return state.length;
}

// you can override either this method, or the async _read(n) below.
Readable.prototype.read = function (n) {
  debug('read', n);
  n = parseInt(n, 10);
  var state = this._readableState;
  var nOrig = n;

  if (n !== 0) state.emittedReadable = false;

  // if we're doing read(0) to trigger a readable event, but we
  // already have a bunch of data in the buffer, then just trigger
  // the 'readable' event and move on.
  if (n === 0 && state.needReadable && (state.length >= state.highWaterMark || state.ended)) {
    debug('read: emitReadable', state.length, state.ended);
    if (state.length === 0 && state.ended) endReadable(this);else emitReadable(this);
    return null;
  }

  n = howMuchToRead(n, state);

  // if we've ended, and we're now clear, then finish it up.
  if (n === 0 && state.ended) {
    if (state.length === 0) endReadable(this);
    return null;
  }

  // All the actual chunk generation logic needs to be
  // *below* the call to _read.  The reason is that in certain
  // synthetic stream cases, such as passthrough streams, _read
  // may be a completely synchronous operation which may change
  // the state of the read buffer, providing enough data when
  // before there was *not* enough.
  //
  // So, the steps are:
  // 1. Figure out what the state of things will be after we do
  // a read from the buffer.
  //
  // 2. If that resulting state will trigger a _read, then call _read.
  // Note that this may be asynchronous, or synchronous.  Yes, it is
  // deeply ugly to write APIs this way, but that still doesn't mean
  // that the Readable class should behave improperly, as streams are
  // designed to be sync/async agnostic.
  // Take note if the _read call is sync or async (ie, if the read call
  // has returned yet), so that we know whether or not it's safe to emit
  // 'readable' etc.
  //
  // 3. Actually pull the requested chunks out of the buffer and return.

  // if we need a readable event, then we need to do some reading.
  var doRead = state.needReadable;
  debug('need readable', doRead);

  // if we currently have less than the highWaterMark, then also read some
  if (state.length === 0 || state.length - n < state.highWaterMark) {
    doRead = true;
    debug('length less than watermark', doRead);
  }

  // however, if we've ended, then there's no point, and if we're already
  // reading, then it's unnecessary.
  if (state.ended || state.reading) {
    doRead = false;
    debug('reading or ended', doRead);
  } else if (doRead) {
    debug('do read');
    state.reading = true;
    state.sync = true;
    // if the length is currently zero, then we *need* a readable event.
    if (state.length === 0) state.needReadable = true;
    // call internal read method
    this._read(state.highWaterMark);
    state.sync = false;
    // If _read pushed data synchronously, then `reading` will be false,
    // and we need to re-evaluate how much data we can return to the user.
    if (!state.reading) n = howMuchToRead(nOrig, state);
  }

  var ret;
  if (n > 0) ret = fromList(n, state);else ret = null;

  if (ret === null) {
    state.needReadable = true;
    n = 0;
  } else {
    state.length -= n;
  }

  if (state.length === 0) {
    // If we have nothing in the buffer, then we want to know
    // as soon as we *do* get something into the buffer.
    if (!state.ended) state.needReadable = true;

    // If we tried to read() past the EOF, then emit end on the next tick.
    if (nOrig !== n && state.ended) endReadable(this);
  }

  if (ret !== null) this.emit('data', ret);

  return ret;
};

function chunkInvalid(state, chunk) {
  var er = null;
  if (!Buffer.isBuffer(chunk) && typeof chunk !== 'string' && chunk !== null && chunk !== undefined && !state.objectMode) {
    er = new TypeError('Invalid non-string/buffer chunk');
  }
  return er;
}

function onEofChunk(stream, state) {
  if (state.ended) return;
  if (state.decoder) {
    var chunk = state.decoder.end();
    if (chunk && chunk.length) {
      state.buffer.push(chunk);
      state.length += state.objectMode ? 1 : chunk.length;
    }
  }
  state.ended = true;

  // emit 'readable' now to make sure it gets picked up.
  emitReadable(stream);
}

// Don't emit readable right away in sync mode, because this can trigger
// another read() call => stack overflow.  This way, it might trigger
// a nextTick recursion warning, but that's not so bad.
function emitReadable(stream) {
  var state = stream._readableState;
  state.needReadable = false;
  if (!state.emittedReadable) {
    debug('emitReadable', state.flowing);
    state.emittedReadable = true;
    if (state.sync) processNextTick(emitReadable_, stream);else emitReadable_(stream);
  }
}

function emitReadable_(stream) {
  debug('emit readable');
  stream.emit('readable');
  flow(stream);
}

// at this point, the user has presumably seen the 'readable' event,
// and called read() to consume some data.  that may have triggered
// in turn another _read(n) call, in which case reading = true if
// it's in progress.
// However, if we're not ended, or reading, and the length < hwm,
// then go ahead and try to read some more preemptively.
function maybeReadMore(stream, state) {
  if (!state.readingMore) {
    state.readingMore = true;
    processNextTick(maybeReadMore_, stream, state);
  }
}

function maybeReadMore_(stream, state) {
  var len = state.length;
  while (!state.reading && !state.flowing && !state.ended && state.length < state.highWaterMark) {
    debug('maybeReadMore read 0');
    stream.read(0);
    if (len === state.length)
      // didn't get any data, stop spinning.
      break;else len = state.length;
  }
  state.readingMore = false;
}

// abstract method.  to be overridden in specific implementation classes.
// call cb(er, data) where data is <= n in length.
// for virtual (non-string, non-buffer) streams, "length" is somewhat
// arbitrary, and perhaps not very meaningful.
Readable.prototype._read = function (n) {
  this.emit('error', new Error('_read() is not implemented'));
};

Readable.prototype.pipe = function (dest, pipeOpts) {
  var src = this;
  var state = this._readableState;

  switch (state.pipesCount) {
    case 0:
      state.pipes = dest;
      break;
    case 1:
      state.pipes = [state.pipes, dest];
      break;
    default:
      state.pipes.push(dest);
      break;
  }
  state.pipesCount += 1;
  debug('pipe count=%d opts=%j', state.pipesCount, pipeOpts);

  var doEnd = (!pipeOpts || pipeOpts.end !== false) && dest !== process.stdout && dest !== process.stderr;

  var endFn = doEnd ? onend : cleanup;
  if (state.endEmitted) processNextTick(endFn);else src.once('end', endFn);

  dest.on('unpipe', onunpipe);
  function onunpipe(readable) {
    debug('onunpipe');
    if (readable === src) {
      cleanup();
    }
  }

  function onend() {
    debug('onend');
    dest.end();
  }

  // when the dest drains, it reduces the awaitDrain counter
  // on the source.  This would be more elegant with a .once()
  // handler in flow(), but adding and removing repeatedly is
  // too slow.
  var ondrain = pipeOnDrain(src);
  dest.on('drain', ondrain);

  var cleanedUp = false;
  function cleanup() {
    debug('cleanup');
    // cleanup event handlers once the pipe is broken
    dest.removeListener('close', onclose);
    dest.removeListener('finish', onfinish);
    dest.removeListener('drain', ondrain);
    dest.removeListener('error', onerror);
    dest.removeListener('unpipe', onunpipe);
    src.removeListener('end', onend);
    src.removeListener('end', cleanup);
    src.removeListener('data', ondata);

    cleanedUp = true;

    // if the reader is waiting for a drain event from this
    // specific writer, then it would cause it to never start
    // flowing again.
    // So, if this is awaiting a drain, then we just call it now.
    // If we don't know, then assume that we are waiting for one.
    if (state.awaitDrain && (!dest._writableState || dest._writableState.needDrain)) ondrain();
  }

  // If the user pushes more data while we're writing to dest then we'll end up
  // in ondata again. However, we only want to increase awaitDrain once because
  // dest will only emit one 'drain' event for the multiple writes.
  // => Introduce a guard on increasing awaitDrain.
  var increasedAwaitDrain = false;
  src.on('data', ondata);
  function ondata(chunk) {
    debug('ondata');
    increasedAwaitDrain = false;
    var ret = dest.write(chunk);
    if (false === ret && !increasedAwaitDrain) {
      // If the user unpiped during `dest.write()`, it is possible
      // to get stuck in a permanently paused state if that write
      // also returned false.
      // => Check whether `dest` is still a piping destination.
      if ((state.pipesCount === 1 && state.pipes === dest || state.pipesCount > 1 && indexOf(state.pipes, dest) !== -1) && !cleanedUp) {
        debug('false write response, pause', src._readableState.awaitDrain);
        src._readableState.awaitDrain++;
        increasedAwaitDrain = true;
      }
      src.pause();
    }
  }

  // if the dest has an error, then stop piping into it.
  // however, don't suppress the throwing behavior for this.
  function onerror(er) {
    debug('onerror', er);
    unpipe();
    dest.removeListener('error', onerror);
    if (EElistenerCount(dest, 'error') === 0) dest.emit('error', er);
  }

  // Make sure our error handler is attached before userland ones.
  prependListener(dest, 'error', onerror);

  // Both close and finish should trigger unpipe, but only once.
  function onclose() {
    dest.removeListener('finish', onfinish);
    unpipe();
  }
  dest.once('close', onclose);
  function onfinish() {
    debug('onfinish');
    dest.removeListener('close', onclose);
    unpipe();
  }
  dest.once('finish', onfinish);

  function unpipe() {
    debug('unpipe');
    src.unpipe(dest);
  }

  // tell the dest that it's being piped to
  dest.emit('pipe', src);

  // start the flow if it hasn't been started already.
  if (!state.flowing) {
    debug('pipe resume');
    src.resume();
  }

  return dest;
};

function pipeOnDrain(src) {
  return function () {
    var state = src._readableState;
    debug('pipeOnDrain', state.awaitDrain);
    if (state.awaitDrain) state.awaitDrain--;
    if (state.awaitDrain === 0 && EElistenerCount(src, 'data')) {
      state.flowing = true;
      flow(src);
    }
  };
}

Readable.prototype.unpipe = function (dest) {
  var state = this._readableState;

  // if we're not piping anywhere, then do nothing.
  if (state.pipesCount === 0) return this;

  // just one destination.  most common case.
  if (state.pipesCount === 1) {
    // passed in one, but it's not the right one.
    if (dest && dest !== state.pipes) return this;

    if (!dest) dest = state.pipes;

    // got a match.
    state.pipes = null;
    state.pipesCount = 0;
    state.flowing = false;
    if (dest) dest.emit('unpipe', this);
    return this;
  }

  // slow case. multiple pipe destinations.

  if (!dest) {
    // remove all.
    var dests = state.pipes;
    var len = state.pipesCount;
    state.pipes = null;
    state.pipesCount = 0;
    state.flowing = false;

    for (var i = 0; i < len; i++) {
      dests[i].emit('unpipe', this);
    }return this;
  }

  // try to find the right one.
  var index = indexOf(state.pipes, dest);
  if (index === -1) return this;

  state.pipes.splice(index, 1);
  state.pipesCount -= 1;
  if (state.pipesCount === 1) state.pipes = state.pipes[0];

  dest.emit('unpipe', this);

  return this;
};

// set up data events if they are asked for
// Ensure readable listeners eventually get something
Readable.prototype.on = function (ev, fn) {
  var res = Stream.prototype.on.call(this, ev, fn);

  if (ev === 'data') {
    // Start flowing on next tick if stream isn't explicitly paused
    if (this._readableState.flowing !== false) this.resume();
  } else if (ev === 'readable') {
    var state = this._readableState;
    if (!state.endEmitted && !state.readableListening) {
      state.readableListening = state.needReadable = true;
      state.emittedReadable = false;
      if (!state.reading) {
        processNextTick(nReadingNextTick, this);
      } else if (state.length) {
        emitReadable(this, state);
      }
    }
  }

  return res;
};
Readable.prototype.addListener = Readable.prototype.on;

function nReadingNextTick(self) {
  debug('readable nexttick read 0');
  self.read(0);
}

// pause() and resume() are remnants of the legacy readable stream API
// If the user uses them, then switch into old mode.
Readable.prototype.resume = function () {
  var state = this._readableState;
  if (!state.flowing) {
    debug('resume');
    state.flowing = true;
    resume(this, state);
  }
  return this;
};

function resume(stream, state) {
  if (!state.resumeScheduled) {
    state.resumeScheduled = true;
    processNextTick(resume_, stream, state);
  }
}

function resume_(stream, state) {
  if (!state.reading) {
    debug('resume read 0');
    stream.read(0);
  }

  state.resumeScheduled = false;
  state.awaitDrain = 0;
  stream.emit('resume');
  flow(stream);
  if (state.flowing && !state.reading) stream.read(0);
}

Readable.prototype.pause = function () {
  debug('call pause flowing=%j', this._readableState.flowing);
  if (false !== this._readableState.flowing) {
    debug('pause');
    this._readableState.flowing = false;
    this.emit('pause');
  }
  return this;
};

function flow(stream) {
  var state = stream._readableState;
  debug('flow', state.flowing);
  while (state.flowing && stream.read() !== null) {}
}

// wrap an old-style stream as the async data source.
// This is *not* part of the readable stream interface.
// It is an ugly unfortunate mess of history.
Readable.prototype.wrap = function (stream) {
  var state = this._readableState;
  var paused = false;

  var self = this;
  stream.on('end', function () {
    debug('wrapped end');
    if (state.decoder && !state.ended) {
      var chunk = state.decoder.end();
      if (chunk && chunk.length) self.push(chunk);
    }

    self.push(null);
  });

  stream.on('data', function (chunk) {
    debug('wrapped data');
    if (state.decoder) chunk = state.decoder.write(chunk);

    // don't skip over falsy values in objectMode
    if (state.objectMode && (chunk === null || chunk === undefined)) return;else if (!state.objectMode && (!chunk || !chunk.length)) return;

    var ret = self.push(chunk);
    if (!ret) {
      paused = true;
      stream.pause();
    }
  });

  // proxy all the other methods.
  // important when wrapping filters and duplexes.
  for (var i in stream) {
    if (this[i] === undefined && typeof stream[i] === 'function') {
      this[i] = function (method) {
        return function () {
          return stream[method].apply(stream, arguments);
        };
      }(i);
    }
  }

  // proxy certain important events.
  var events = ['error', 'close', 'destroy', 'pause', 'resume'];
  forEach(events, function (ev) {
    stream.on(ev, self.emit.bind(self, ev));
  });

  // when we try to consume some more bytes, simply unpause the
  // underlying stream.
  self._read = function (n) {
    debug('wrapped _read', n);
    if (paused) {
      paused = false;
      stream.resume();
    }
  };

  return self;
};

// exposed for testing purposes only.
Readable._fromList = fromList;

// Pluck off n bytes from an array of buffers.
// Length is the combined lengths of all the buffers in the list.
// This function is designed to be inlinable, so please take care when making
// changes to the function body.
function fromList(n, state) {
  // nothing buffered
  if (state.length === 0) return null;

  var ret;
  if (state.objectMode) ret = state.buffer.shift();else if (!n || n >= state.length) {
    // read it all, truncate the list
    if (state.decoder) ret = state.buffer.join('');else if (state.buffer.length === 1) ret = state.buffer.head.data;else ret = state.buffer.concat(state.length);
    state.buffer.clear();
  } else {
    // read part of list
    ret = fromListPartial(n, state.buffer, state.decoder);
  }

  return ret;
}

// Extracts only enough buffered data to satisfy the amount requested.
// This function is designed to be inlinable, so please take care when making
// changes to the function body.
function fromListPartial(n, list, hasStrings) {
  var ret;
  if (n < list.head.data.length) {
    // slice is the same for buffers and strings
    ret = list.head.data.slice(0, n);
    list.head.data = list.head.data.slice(n);
  } else if (n === list.head.data.length) {
    // first chunk is a perfect match
    ret = list.shift();
  } else {
    // result spans more than one buffer
    ret = hasStrings ? copyFromBufferString(n, list) : copyFromBuffer(n, list);
  }
  return ret;
}

// Copies a specified amount of characters from the list of buffered data
// chunks.
// This function is designed to be inlinable, so please take care when making
// changes to the function body.
function copyFromBufferString(n, list) {
  var p = list.head;
  var c = 1;
  var ret = p.data;
  n -= ret.length;
  while (p = p.next) {
    var str = p.data;
    var nb = n > str.length ? str.length : n;
    if (nb === str.length) ret += str;else ret += str.slice(0, n);
    n -= nb;
    if (n === 0) {
      if (nb === str.length) {
        ++c;
        if (p.next) list.head = p.next;else list.head = list.tail = null;
      } else {
        list.head = p;
        p.data = str.slice(nb);
      }
      break;
    }
    ++c;
  }
  list.length -= c;
  return ret;
}

// Copies a specified amount of bytes from the list of buffered data chunks.
// This function is designed to be inlinable, so please take care when making
// changes to the function body.
function copyFromBuffer(n, list) {
  var ret = bufferShim.allocUnsafe(n);
  var p = list.head;
  var c = 1;
  p.data.copy(ret);
  n -= p.data.length;
  while (p = p.next) {
    var buf = p.data;
    var nb = n > buf.length ? buf.length : n;
    buf.copy(ret, ret.length - n, 0, nb);
    n -= nb;
    if (n === 0) {
      if (nb === buf.length) {
        ++c;
        if (p.next) list.head = p.next;else list.head = list.tail = null;
      } else {
        list.head = p;
        p.data = buf.slice(nb);
      }
      break;
    }
    ++c;
  }
  list.length -= c;
  return ret;
}

function endReadable(stream) {
  var state = stream._readableState;

  // If we get here before consuming all the bytes, then that is a
  // bug in node.  Should never happen.
  if (state.length > 0) throw new Error('"endReadable()" called on non-empty stream');

  if (!state.endEmitted) {
    state.ended = true;
    processNextTick(endReadableNT, state, stream);
  }
}

function endReadableNT(state, stream) {
  // Check that we didn't get one last unshift.
  if (!state.endEmitted && state.length === 0) {
    state.endEmitted = true;
    stream.readable = false;
    stream.emit('end');
  }
}

function forEach(xs, f) {
  for (var i = 0, l = xs.length; i < l; i++) {
    f(xs[i], i);
  }
}

function indexOf(xs, x) {
  for (var i = 0, l = xs.length; i < l; i++) {
    if (xs[i] === x) return i;
  }
  return -1;
}
}).call(this,require('_process'))
},{"./_stream_duplex":12,"./internal/streams/BufferList":17,"_process":10,"buffer":3,"buffer-shims":18,"core-util-is":19,"events":7,"inherits":8,"isarray":20,"process-nextick-args":21,"string_decoder/":28,"util":2}],15:[function(require,module,exports){
// a transform stream is a readable/writable stream where you do
// something with the data.  Sometimes it's called a "filter",
// but that's not a great name for it, since that implies a thing where
// some bits pass through, and others are simply ignored.  (That would
// be a valid example of a transform, of course.)
//
// While the output is causally related to the input, it's not a
// necessarily symmetric or synchronous transformation.  For example,
// a zlib stream might take multiple plain-text writes(), and then
// emit a single compressed chunk some time in the future.
//
// Here's how this works:
//
// The Transform stream has all the aspects of the readable and writable
// stream classes.  When you write(chunk), that calls _write(chunk,cb)
// internally, and returns false if there's a lot of pending writes
// buffered up.  When you call read(), that calls _read(n) until
// there's enough pending readable data buffered up.
//
// In a transform stream, the written data is placed in a buffer.  When
// _read(n) is called, it transforms the queued up data, calling the
// buffered _write cb's as it consumes chunks.  If consuming a single
// written chunk would result in multiple output chunks, then the first
// outputted bit calls the readcb, and subsequent chunks just go into
// the read buffer, and will cause it to emit 'readable' if necessary.
//
// This way, back-pressure is actually determined by the reading side,
// since _read has to be called to start processing a new chunk.  However,
// a pathological inflate type of transform can cause excessive buffering
// here.  For example, imagine a stream where every byte of input is
// interpreted as an integer from 0-255, and then results in that many
// bytes of output.  Writing the 4 bytes {ff,ff,ff,ff} would result in
// 1kb of data being output.  In this case, you could write a very small
// amount of input, and end up with a very large amount of output.  In
// such a pathological inflating mechanism, there'd be no way to tell
// the system to stop doing the transform.  A single 4MB write could
// cause the system to run out of memory.
//
// However, even in such a pathological case, only a single written chunk
// would be consumed, and then the rest would wait (un-transformed) until
// the results of the previous transformed chunk were consumed.

'use strict';

module.exports = Transform;

var Duplex = require('./_stream_duplex');

/*<replacement>*/
var util = require('core-util-is');
util.inherits = require('inherits');
/*</replacement>*/

util.inherits(Transform, Duplex);

function TransformState(stream) {
  this.afterTransform = function (er, data) {
    return afterTransform(stream, er, data);
  };

  this.needTransform = false;
  this.transforming = false;
  this.writecb = null;
  this.writechunk = null;
  this.writeencoding = null;
}

function afterTransform(stream, er, data) {
  var ts = stream._transformState;
  ts.transforming = false;

  var cb = ts.writecb;

  if (!cb) return stream.emit('error', new Error('no writecb in Transform class'));

  ts.writechunk = null;
  ts.writecb = null;

  if (data !== null && data !== undefined) stream.push(data);

  cb(er);

  var rs = stream._readableState;
  rs.reading = false;
  if (rs.needReadable || rs.length < rs.highWaterMark) {
    stream._read(rs.highWaterMark);
  }
}

function Transform(options) {
  if (!(this instanceof Transform)) return new Transform(options);

  Duplex.call(this, options);

  this._transformState = new TransformState(this);

  var stream = this;

  // start out asking for a readable event once data is transformed.
  this._readableState.needReadable = true;

  // we have implemented the _read method, and done the other things
  // that Readable wants before the first _read call, so unset the
  // sync guard flag.
  this._readableState.sync = false;

  if (options) {
    if (typeof options.transform === 'function') this._transform = options.transform;

    if (typeof options.flush === 'function') this._flush = options.flush;
  }

  // When the writable side finishes, then flush out anything remaining.
  this.once('prefinish', function () {
    if (typeof this._flush === 'function') this._flush(function (er, data) {
      done(stream, er, data);
    });else done(stream);
  });
}

Transform.prototype.push = function (chunk, encoding) {
  this._transformState.needTransform = false;
  return Duplex.prototype.push.call(this, chunk, encoding);
};

// This is the part where you do stuff!
// override this function in implementation classes.
// 'chunk' is an input chunk.
//
// Call `push(newChunk)` to pass along transformed output
// to the readable side.  You may call 'push' zero or more times.
//
// Call `cb(err)` when you are done with this chunk.  If you pass
// an error, then that'll put the hurt on the whole operation.  If you
// never call cb(), then you'll never get another chunk.
Transform.prototype._transform = function (chunk, encoding, cb) {
  throw new Error('_transform() is not implemented');
};

Transform.prototype._write = function (chunk, encoding, cb) {
  var ts = this._transformState;
  ts.writecb = cb;
  ts.writechunk = chunk;
  ts.writeencoding = encoding;
  if (!ts.transforming) {
    var rs = this._readableState;
    if (ts.needTransform || rs.needReadable || rs.length < rs.highWaterMark) this._read(rs.highWaterMark);
  }
};

// Doesn't matter what the args are here.
// _transform does all the work.
// That we got here means that the readable side wants more data.
Transform.prototype._read = function (n) {
  var ts = this._transformState;

  if (ts.writechunk !== null && ts.writecb && !ts.transforming) {
    ts.transforming = true;
    this._transform(ts.writechunk, ts.writeencoding, ts.afterTransform);
  } else {
    // mark that we need a transform, so that any data that comes in
    // will get processed, now that we've asked for it.
    ts.needTransform = true;
  }
};

function done(stream, er, data) {
  if (er) return stream.emit('error', er);

  if (data !== null && data !== undefined) stream.push(data);

  // if there's nothing in the write buffer, then that means
  // that nothing more will ever be provided
  var ws = stream._writableState;
  var ts = stream._transformState;

  if (ws.length) throw new Error('Calling transform done when ws.length != 0');

  if (ts.transforming) throw new Error('Calling transform done when still transforming');

  return stream.push(null);
}
},{"./_stream_duplex":12,"core-util-is":19,"inherits":8}],16:[function(require,module,exports){
(function (process){
// A bit simpler than readable streams.
// Implement an async ._write(chunk, encoding, cb), and it'll handle all
// the drain event emission and buffering.

'use strict';

module.exports = Writable;

/*<replacement>*/
var processNextTick = require('process-nextick-args');
/*</replacement>*/

/*<replacement>*/
var asyncWrite = !process.browser && ['v0.10', 'v0.9.'].indexOf(process.version.slice(0, 5)) > -1 ? setImmediate : processNextTick;
/*</replacement>*/

/*<replacement>*/
var Duplex;
/*</replacement>*/

Writable.WritableState = WritableState;

/*<replacement>*/
var util = require('core-util-is');
util.inherits = require('inherits');
/*</replacement>*/

/*<replacement>*/
var internalUtil = {
  deprecate: require('util-deprecate')
};
/*</replacement>*/

/*<replacement>*/
var Stream;
(function () {
  try {
    Stream = require('st' + 'ream');
  } catch (_) {} finally {
    if (!Stream) Stream = require('events').EventEmitter;
  }
})();
/*</replacement>*/

var Buffer = require('buffer').Buffer;
/*<replacement>*/
var bufferShim = require('buffer-shims');
/*</replacement>*/

util.inherits(Writable, Stream);

function nop() {}

function WriteReq(chunk, encoding, cb) {
  this.chunk = chunk;
  this.encoding = encoding;
  this.callback = cb;
  this.next = null;
}

function WritableState(options, stream) {
  Duplex = Duplex || require('./_stream_duplex');

  options = options || {};

  // object stream flag to indicate whether or not this stream
  // contains buffers or objects.
  this.objectMode = !!options.objectMode;

  if (stream instanceof Duplex) this.objectMode = this.objectMode || !!options.writableObjectMode;

  // the point at which write() starts returning false
  // Note: 0 is a valid value, means that we always return false if
  // the entire buffer is not flushed immediately on write()
  var hwm = options.highWaterMark;
  var defaultHwm = this.objectMode ? 16 : 16 * 1024;
  this.highWaterMark = hwm || hwm === 0 ? hwm : defaultHwm;

  // cast to ints.
  this.highWaterMark = ~~this.highWaterMark;

  // drain event flag.
  this.needDrain = false;
  // at the start of calling end()
  this.ending = false;
  // when end() has been called, and returned
  this.ended = false;
  // when 'finish' is emitted
  this.finished = false;

  // should we decode strings into buffers before passing to _write?
  // this is here so that some node-core streams can optimize string
  // handling at a lower level.
  var noDecode = options.decodeStrings === false;
  this.decodeStrings = !noDecode;

  // Crypto is kind of old and crusty.  Historically, its default string
  // encoding is 'binary' so we have to make this configurable.
  // Everything else in the universe uses 'utf8', though.
  this.defaultEncoding = options.defaultEncoding || 'utf8';

  // not an actual buffer we keep track of, but a measurement
  // of how much we're waiting to get pushed to some underlying
  // socket or file.
  this.length = 0;

  // a flag to see when we're in the middle of a write.
  this.writing = false;

  // when true all writes will be buffered until .uncork() call
  this.corked = 0;

  // a flag to be able to tell if the onwrite cb is called immediately,
  // or on a later tick.  We set this to true at first, because any
  // actions that shouldn't happen until "later" should generally also
  // not happen before the first write call.
  this.sync = true;

  // a flag to know if we're processing previously buffered items, which
  // may call the _write() callback in the same tick, so that we don't
  // end up in an overlapped onwrite situation.
  this.bufferProcessing = false;

  // the callback that's passed to _write(chunk,cb)
  this.onwrite = function (er) {
    onwrite(stream, er);
  };

  // the callback that the user supplies to write(chunk,encoding,cb)
  this.writecb = null;

  // the amount that is being written when _write is called.
  this.writelen = 0;

  this.bufferedRequest = null;
  this.lastBufferedRequest = null;

  // number of pending user-supplied write callbacks
  // this must be 0 before 'finish' can be emitted
  this.pendingcb = 0;

  // emit prefinish if the only thing we're waiting for is _write cbs
  // This is relevant for synchronous Transform streams
  this.prefinished = false;

  // True if the error was already emitted and should not be thrown again
  this.errorEmitted = false;

  // count buffered requests
  this.bufferedRequestCount = 0;

  // allocate the first CorkedRequest, there is always
  // one allocated and free to use, and we maintain at most two
  this.corkedRequestsFree = new CorkedRequest(this);
}

WritableState.prototype.getBuffer = function getBuffer() {
  var current = this.bufferedRequest;
  var out = [];
  while (current) {
    out.push(current);
    current = current.next;
  }
  return out;
};

(function () {
  try {
    Object.defineProperty(WritableState.prototype, 'buffer', {
      get: internalUtil.deprecate(function () {
        return this.getBuffer();
      }, '_writableState.buffer is deprecated. Use _writableState.getBuffer ' + 'instead.')
    });
  } catch (_) {}
})();

// Test _writableState for inheritance to account for Duplex streams,
// whose prototype chain only points to Readable.
var realHasInstance;
if (typeof Symbol === 'function' && Symbol.hasInstance && typeof Function.prototype[Symbol.hasInstance] === 'function') {
  realHasInstance = Function.prototype[Symbol.hasInstance];
  Object.defineProperty(Writable, Symbol.hasInstance, {
    value: function (object) {
      if (realHasInstance.call(this, object)) return true;

      return object && object._writableState instanceof WritableState;
    }
  });
} else {
  realHasInstance = function (object) {
    return object instanceof this;
  };
}

function Writable(options) {
  Duplex = Duplex || require('./_stream_duplex');

  // Writable ctor is applied to Duplexes, too.
  // `realHasInstance` is necessary because using plain `instanceof`
  // would return false, as no `_writableState` property is attached.

  // Trying to use the custom `instanceof` for Writable here will also break the
  // Node.js LazyTransform implementation, which has a non-trivial getter for
  // `_writableState` that would lead to infinite recursion.
  if (!realHasInstance.call(Writable, this) && !(this instanceof Duplex)) {
    return new Writable(options);
  }

  this._writableState = new WritableState(options, this);

  // legacy.
  this.writable = true;

  if (options) {
    if (typeof options.write === 'function') this._write = options.write;

    if (typeof options.writev === 'function') this._writev = options.writev;
  }

  Stream.call(this);
}

// Otherwise people can pipe Writable streams, which is just wrong.
Writable.prototype.pipe = function () {
  this.emit('error', new Error('Cannot pipe, not readable'));
};

function writeAfterEnd(stream, cb) {
  var er = new Error('write after end');
  // TODO: defer error events consistently everywhere, not just the cb
  stream.emit('error', er);
  processNextTick(cb, er);
}

// Checks that a user-supplied chunk is valid, especially for the particular
// mode the stream is in. Currently this means that `null` is never accepted
// and undefined/non-string values are only allowed in object mode.
function validChunk(stream, state, chunk, cb) {
  var valid = true;
  var er = false;

  if (chunk === null) {
    er = new TypeError('May not write null values to stream');
  } else if (typeof chunk !== 'string' && chunk !== undefined && !state.objectMode) {
    er = new TypeError('Invalid non-string/buffer chunk');
  }
  if (er) {
    stream.emit('error', er);
    processNextTick(cb, er);
    valid = false;
  }
  return valid;
}

Writable.prototype.write = function (chunk, encoding, cb) {
  var state = this._writableState;
  var ret = false;
  var isBuf = Buffer.isBuffer(chunk);

  if (typeof encoding === 'function') {
    cb = encoding;
    encoding = null;
  }

  if (isBuf) encoding = 'buffer';else if (!encoding) encoding = state.defaultEncoding;

  if (typeof cb !== 'function') cb = nop;

  if (state.ended) writeAfterEnd(this, cb);else if (isBuf || validChunk(this, state, chunk, cb)) {
    state.pendingcb++;
    ret = writeOrBuffer(this, state, isBuf, chunk, encoding, cb);
  }

  return ret;
};

Writable.prototype.cork = function () {
  var state = this._writableState;

  state.corked++;
};

Writable.prototype.uncork = function () {
  var state = this._writableState;

  if (state.corked) {
    state.corked--;

    if (!state.writing && !state.corked && !state.finished && !state.bufferProcessing && state.bufferedRequest) clearBuffer(this, state);
  }
};

Writable.prototype.setDefaultEncoding = function setDefaultEncoding(encoding) {
  // node::ParseEncoding() requires lower case.
  if (typeof encoding === 'string') encoding = encoding.toLowerCase();
  if (!(['hex', 'utf8', 'utf-8', 'ascii', 'binary', 'base64', 'ucs2', 'ucs-2', 'utf16le', 'utf-16le', 'raw'].indexOf((encoding + '').toLowerCase()) > -1)) throw new TypeError('Unknown encoding: ' + encoding);
  this._writableState.defaultEncoding = encoding;
  return this;
};

function decodeChunk(state, chunk, encoding) {
  if (!state.objectMode && state.decodeStrings !== false && typeof chunk === 'string') {
    chunk = bufferShim.from(chunk, encoding);
  }
  return chunk;
}

// if we're already writing something, then just put this
// in the queue, and wait our turn.  Otherwise, call _write
// If we return false, then we need a drain event, so set that flag.
function writeOrBuffer(stream, state, isBuf, chunk, encoding, cb) {
  if (!isBuf) {
    chunk = decodeChunk(state, chunk, encoding);
    if (Buffer.isBuffer(chunk)) encoding = 'buffer';
  }
  var len = state.objectMode ? 1 : chunk.length;

  state.length += len;

  var ret = state.length < state.highWaterMark;
  // we must ensure that previous needDrain will not be reset to false.
  if (!ret) state.needDrain = true;

  if (state.writing || state.corked) {
    var last = state.lastBufferedRequest;
    state.lastBufferedRequest = new WriteReq(chunk, encoding, cb);
    if (last) {
      last.next = state.lastBufferedRequest;
    } else {
      state.bufferedRequest = state.lastBufferedRequest;
    }
    state.bufferedRequestCount += 1;
  } else {
    doWrite(stream, state, false, len, chunk, encoding, cb);
  }

  return ret;
}

function doWrite(stream, state, writev, len, chunk, encoding, cb) {
  state.writelen = len;
  state.writecb = cb;
  state.writing = true;
  state.sync = true;
  if (writev) stream._writev(chunk, state.onwrite);else stream._write(chunk, encoding, state.onwrite);
  state.sync = false;
}

function onwriteError(stream, state, sync, er, cb) {
  --state.pendingcb;
  if (sync) processNextTick(cb, er);else cb(er);

  stream._writableState.errorEmitted = true;
  stream.emit('error', er);
}

function onwriteStateUpdate(state) {
  state.writing = false;
  state.writecb = null;
  state.length -= state.writelen;
  state.writelen = 0;
}

function onwrite(stream, er) {
  var state = stream._writableState;
  var sync = state.sync;
  var cb = state.writecb;

  onwriteStateUpdate(state);

  if (er) onwriteError(stream, state, sync, er, cb);else {
    // Check if we're actually ready to finish, but don't emit yet
    var finished = needFinish(state);

    if (!finished && !state.corked && !state.bufferProcessing && state.bufferedRequest) {
      clearBuffer(stream, state);
    }

    if (sync) {
      /*<replacement>*/
      asyncWrite(afterWrite, stream, state, finished, cb);
      /*</replacement>*/
    } else {
      afterWrite(stream, state, finished, cb);
    }
  }
}

function afterWrite(stream, state, finished, cb) {
  if (!finished) onwriteDrain(stream, state);
  state.pendingcb--;
  cb();
  finishMaybe(stream, state);
}

// Must force callback to be called on nextTick, so that we don't
// emit 'drain' before the write() consumer gets the 'false' return
// value, and has a chance to attach a 'drain' listener.
function onwriteDrain(stream, state) {
  if (state.length === 0 && state.needDrain) {
    state.needDrain = false;
    stream.emit('drain');
  }
}

// if there's something in the buffer waiting, then process it
function clearBuffer(stream, state) {
  state.bufferProcessing = true;
  var entry = state.bufferedRequest;

  if (stream._writev && entry && entry.next) {
    // Fast case, write everything using _writev()
    var l = state.bufferedRequestCount;
    var buffer = new Array(l);
    var holder = state.corkedRequestsFree;
    holder.entry = entry;

    var count = 0;
    while (entry) {
      buffer[count] = entry;
      entry = entry.next;
      count += 1;
    }

    doWrite(stream, state, true, state.length, buffer, '', holder.finish);

    // doWrite is almost always async, defer these to save a bit of time
    // as the hot path ends with doWrite
    state.pendingcb++;
    state.lastBufferedRequest = null;
    if (holder.next) {
      state.corkedRequestsFree = holder.next;
      holder.next = null;
    } else {
      state.corkedRequestsFree = new CorkedRequest(state);
    }
  } else {
    // Slow case, write chunks one-by-one
    while (entry) {
      var chunk = entry.chunk;
      var encoding = entry.encoding;
      var cb = entry.callback;
      var len = state.objectMode ? 1 : chunk.length;

      doWrite(stream, state, false, len, chunk, encoding, cb);
      entry = entry.next;
      // if we didn't call the onwrite immediately, then
      // it means that we need to wait until it does.
      // also, that means that the chunk and cb are currently
      // being processed, so move the buffer counter past them.
      if (state.writing) {
        break;
      }
    }

    if (entry === null) state.lastBufferedRequest = null;
  }

  state.bufferedRequestCount = 0;
  state.bufferedRequest = entry;
  state.bufferProcessing = false;
}

Writable.prototype._write = function (chunk, encoding, cb) {
  cb(new Error('_write() is not implemented'));
};

Writable.prototype._writev = null;

Writable.prototype.end = function (chunk, encoding, cb) {
  var state = this._writableState;

  if (typeof chunk === 'function') {
    cb = chunk;
    chunk = null;
    encoding = null;
  } else if (typeof encoding === 'function') {
    cb = encoding;
    encoding = null;
  }

  if (chunk !== null && chunk !== undefined) this.write(chunk, encoding);

  // .end() fully uncorks
  if (state.corked) {
    state.corked = 1;
    this.uncork();
  }

  // ignore unnecessary end() calls.
  if (!state.ending && !state.finished) endWritable(this, state, cb);
};

function needFinish(state) {
  return state.ending && state.length === 0 && state.bufferedRequest === null && !state.finished && !state.writing;
}

function prefinish(stream, state) {
  if (!state.prefinished) {
    state.prefinished = true;
    stream.emit('prefinish');
  }
}

function finishMaybe(stream, state) {
  var need = needFinish(state);
  if (need) {
    if (state.pendingcb === 0) {
      prefinish(stream, state);
      state.finished = true;
      stream.emit('finish');
    } else {
      prefinish(stream, state);
    }
  }
  return need;
}

function endWritable(stream, state, cb) {
  state.ending = true;
  finishMaybe(stream, state);
  if (cb) {
    if (state.finished) processNextTick(cb);else stream.once('finish', cb);
  }
  state.ended = true;
  stream.writable = false;
}

// It seems a linked list but it is not
// there will be only 2 of these for each stream
function CorkedRequest(state) {
  var _this = this;

  this.next = null;
  this.entry = null;
  this.finish = function (err) {
    var entry = _this.entry;
    _this.entry = null;
    while (entry) {
      var cb = entry.callback;
      state.pendingcb--;
      cb(err);
      entry = entry.next;
    }
    if (state.corkedRequestsFree) {
      state.corkedRequestsFree.next = _this;
    } else {
      state.corkedRequestsFree = _this;
    }
  };
}
}).call(this,require('_process'))
},{"./_stream_duplex":12,"_process":10,"buffer":3,"buffer-shims":18,"core-util-is":19,"events":7,"inherits":8,"process-nextick-args":21,"util-deprecate":22}],17:[function(require,module,exports){
'use strict';

var Buffer = require('buffer').Buffer;
/*<replacement>*/
var bufferShim = require('buffer-shims');
/*</replacement>*/

module.exports = BufferList;

function BufferList() {
  this.head = null;
  this.tail = null;
  this.length = 0;
}

BufferList.prototype.push = function (v) {
  var entry = { data: v, next: null };
  if (this.length > 0) this.tail.next = entry;else this.head = entry;
  this.tail = entry;
  ++this.length;
};

BufferList.prototype.unshift = function (v) {
  var entry = { data: v, next: this.head };
  if (this.length === 0) this.tail = entry;
  this.head = entry;
  ++this.length;
};

BufferList.prototype.shift = function () {
  if (this.length === 0) return;
  var ret = this.head.data;
  if (this.length === 1) this.head = this.tail = null;else this.head = this.head.next;
  --this.length;
  return ret;
};

BufferList.prototype.clear = function () {
  this.head = this.tail = null;
  this.length = 0;
};

BufferList.prototype.join = function (s) {
  if (this.length === 0) return '';
  var p = this.head;
  var ret = '' + p.data;
  while (p = p.next) {
    ret += s + p.data;
  }return ret;
};

BufferList.prototype.concat = function (n) {
  if (this.length === 0) return bufferShim.alloc(0);
  if (this.length === 1) return this.head.data;
  var ret = bufferShim.allocUnsafe(n >>> 0);
  var p = this.head;
  var i = 0;
  while (p) {
    p.data.copy(ret, i);
    i += p.data.length;
    p = p.next;
  }
  return ret;
};
},{"buffer":3,"buffer-shims":18}],18:[function(require,module,exports){
(function (global){
'use strict';

var buffer = require('buffer');
var Buffer = buffer.Buffer;
var SlowBuffer = buffer.SlowBuffer;
var MAX_LEN = buffer.kMaxLength || 2147483647;
exports.alloc = function alloc(size, fill, encoding) {
  if (typeof Buffer.alloc === 'function') {
    return Buffer.alloc(size, fill, encoding);
  }
  if (typeof encoding === 'number') {
    throw new TypeError('encoding must not be number');
  }
  if (typeof size !== 'number') {
    throw new TypeError('size must be a number');
  }
  if (size > MAX_LEN) {
    throw new RangeError('size is too large');
  }
  var enc = encoding;
  var _fill = fill;
  if (_fill === undefined) {
    enc = undefined;
    _fill = 0;
  }
  var buf = new Buffer(size);
  if (typeof _fill === 'string') {
    var fillBuf = new Buffer(_fill, enc);
    var flen = fillBuf.length;
    var i = -1;
    while (++i < size) {
      buf[i] = fillBuf[i % flen];
    }
  } else {
    buf.fill(_fill);
  }
  return buf;
}
exports.allocUnsafe = function allocUnsafe(size) {
  if (typeof Buffer.allocUnsafe === 'function') {
    return Buffer.allocUnsafe(size);
  }
  if (typeof size !== 'number') {
    throw new TypeError('size must be a number');
  }
  if (size > MAX_LEN) {
    throw new RangeError('size is too large');
  }
  return new Buffer(size);
}
exports.from = function from(value, encodingOrOffset, length) {
  if (typeof Buffer.from === 'function' && (!global.Uint8Array || Uint8Array.from !== Buffer.from)) {
    return Buffer.from(value, encodingOrOffset, length);
  }
  if (typeof value === 'number') {
    throw new TypeError('"value" argument must not be a number');
  }
  if (typeof value === 'string') {
    return new Buffer(value, encodingOrOffset);
  }
  if (typeof ArrayBuffer !== 'undefined' && value instanceof ArrayBuffer) {
    var offset = encodingOrOffset;
    if (arguments.length === 1) {
      return new Buffer(value);
    }
    if (typeof offset === 'undefined') {
      offset = 0;
    }
    var len = length;
    if (typeof len === 'undefined') {
      len = value.byteLength - offset;
    }
    if (offset >= value.byteLength) {
      throw new RangeError('\'offset\' is out of bounds');
    }
    if (len > value.byteLength - offset) {
      throw new RangeError('\'length\' is out of bounds');
    }
    return new Buffer(value.slice(offset, offset + len));
  }
  if (Buffer.isBuffer(value)) {
    var out = new Buffer(value.length);
    value.copy(out, 0, 0, value.length);
    return out;
  }
  if (value) {
    if (Array.isArray(value) || (typeof ArrayBuffer !== 'undefined' && value.buffer instanceof ArrayBuffer) || 'length' in value) {
      return new Buffer(value);
    }
    if (value.type === 'Buffer' && Array.isArray(value.data)) {
      return new Buffer(value.data);
    }
  }

  throw new TypeError('First argument must be a string, Buffer, ' + 'ArrayBuffer, Array, or array-like object.');
}
exports.allocUnsafeSlow = function allocUnsafeSlow(size) {
  if (typeof Buffer.allocUnsafeSlow === 'function') {
    return Buffer.allocUnsafeSlow(size);
  }
  if (typeof size !== 'number') {
    throw new TypeError('size must be a number');
  }
  if (size >= MAX_LEN) {
    throw new RangeError('size is too large');
  }
  return new SlowBuffer(size);
}

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
},{"buffer":3}],19:[function(require,module,exports){
(function (Buffer){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

// NOTE: These type checking functions intentionally don't use `instanceof`
// because it is fragile and can be easily faked with `Object.create()`.

function isArray(arg) {
  if (Array.isArray) {
    return Array.isArray(arg);
  }
  return objectToString(arg) === '[object Array]';
}
exports.isArray = isArray;

function isBoolean(arg) {
  return typeof arg === 'boolean';
}
exports.isBoolean = isBoolean;

function isNull(arg) {
  return arg === null;
}
exports.isNull = isNull;

function isNullOrUndefined(arg) {
  return arg == null;
}
exports.isNullOrUndefined = isNullOrUndefined;

function isNumber(arg) {
  return typeof arg === 'number';
}
exports.isNumber = isNumber;

function isString(arg) {
  return typeof arg === 'string';
}
exports.isString = isString;

function isSymbol(arg) {
  return typeof arg === 'symbol';
}
exports.isSymbol = isSymbol;

function isUndefined(arg) {
  return arg === void 0;
}
exports.isUndefined = isUndefined;

function isRegExp(re) {
  return objectToString(re) === '[object RegExp]';
}
exports.isRegExp = isRegExp;

function isObject(arg) {
  return typeof arg === 'object' && arg !== null;
}
exports.isObject = isObject;

function isDate(d) {
  return objectToString(d) === '[object Date]';
}
exports.isDate = isDate;

function isError(e) {
  return (objectToString(e) === '[object Error]' || e instanceof Error);
}
exports.isError = isError;

function isFunction(arg) {
  return typeof arg === 'function';
}
exports.isFunction = isFunction;

function isPrimitive(arg) {
  return arg === null ||
         typeof arg === 'boolean' ||
         typeof arg === 'number' ||
         typeof arg === 'string' ||
         typeof arg === 'symbol' ||  // ES6 symbol
         typeof arg === 'undefined';
}
exports.isPrimitive = isPrimitive;

exports.isBuffer = Buffer.isBuffer;

function objectToString(o) {
  return Object.prototype.toString.call(o);
}

}).call(this,{"isBuffer":require("../../../../insert-module-globals/node_modules/is-buffer/index.js")})
},{"../../../../insert-module-globals/node_modules/is-buffer/index.js":9}],20:[function(require,module,exports){
arguments[4][6][0].apply(exports,arguments)
},{"dup":6}],21:[function(require,module,exports){
(function (process){
'use strict';

if (!process.version ||
    process.version.indexOf('v0.') === 0 ||
    process.version.indexOf('v1.') === 0 && process.version.indexOf('v1.8.') !== 0) {
  module.exports = nextTick;
} else {
  module.exports = process.nextTick;
}

function nextTick(fn, arg1, arg2, arg3) {
  if (typeof fn !== 'function') {
    throw new TypeError('"callback" argument must be a function');
  }
  var len = arguments.length;
  var args, i;
  switch (len) {
  case 0:
  case 1:
    return process.nextTick(fn);
  case 2:
    return process.nextTick(function afterTickOne() {
      fn.call(null, arg1);
    });
  case 3:
    return process.nextTick(function afterTickTwo() {
      fn.call(null, arg1, arg2);
    });
  case 4:
    return process.nextTick(function afterTickThree() {
      fn.call(null, arg1, arg2, arg3);
    });
  default:
    args = new Array(len - 1);
    i = 0;
    while (i < args.length) {
      args[i++] = arguments[i];
    }
    return process.nextTick(function afterTick() {
      fn.apply(null, args);
    });
  }
}

}).call(this,require('_process'))
},{"_process":10}],22:[function(require,module,exports){
(function (global){

/**
 * Module exports.
 */

module.exports = deprecate;

/**
 * Mark that a method should not be used.
 * Returns a modified function which warns once by default.
 *
 * If `localStorage.noDeprecation = true` is set, then it is a no-op.
 *
 * If `localStorage.throwDeprecation = true` is set, then deprecated functions
 * will throw an Error when invoked.
 *
 * If `localStorage.traceDeprecation = true` is set, then deprecated functions
 * will invoke `console.trace()` instead of `console.error()`.
 *
 * @param {Function} fn - the function to deprecate
 * @param {String} msg - the string to print to the console when `fn` is invoked
 * @returns {Function} a new "deprecated" version of `fn`
 * @api public
 */

function deprecate (fn, msg) {
  if (config('noDeprecation')) {
    return fn;
  }

  var warned = false;
  function deprecated() {
    if (!warned) {
      if (config('throwDeprecation')) {
        throw new Error(msg);
      } else if (config('traceDeprecation')) {
        console.trace(msg);
      } else {
        console.warn(msg);
      }
      warned = true;
    }
    return fn.apply(this, arguments);
  }

  return deprecated;
}

/**
 * Checks `localStorage` for boolean values for the given `name`.
 *
 * @param {String} name
 * @returns {Boolean}
 * @api private
 */

function config (name) {
  // accessing global.localStorage can trigger a DOMException in sandboxed iframes
  try {
    if (!global.localStorage) return false;
  } catch (_) {
    return false;
  }
  var val = global.localStorage[name];
  if (null == val) return false;
  return String(val).toLowerCase() === 'true';
}

}).call(this,typeof global !== "undefined" ? global : typeof self !== "undefined" ? self : typeof window !== "undefined" ? window : {})
},{}],23:[function(require,module,exports){
module.exports = require("./lib/_stream_passthrough.js")

},{"./lib/_stream_passthrough.js":13}],24:[function(require,module,exports){
(function (process){
var Stream = (function (){
  try {
    return require('st' + 'ream'); // hack to fix a circular dependency issue when used with browserify
  } catch(_){}
}());
exports = module.exports = require('./lib/_stream_readable.js');
exports.Stream = Stream || exports;
exports.Readable = exports;
exports.Writable = require('./lib/_stream_writable.js');
exports.Duplex = require('./lib/_stream_duplex.js');
exports.Transform = require('./lib/_stream_transform.js');
exports.PassThrough = require('./lib/_stream_passthrough.js');

if (!process.browser && process.env.READABLE_STREAM === 'disable' && Stream) {
  module.exports = Stream;
}

}).call(this,require('_process'))
},{"./lib/_stream_duplex.js":12,"./lib/_stream_passthrough.js":13,"./lib/_stream_readable.js":14,"./lib/_stream_transform.js":15,"./lib/_stream_writable.js":16,"_process":10}],25:[function(require,module,exports){
module.exports = require("./lib/_stream_transform.js")

},{"./lib/_stream_transform.js":15}],26:[function(require,module,exports){
module.exports = require("./lib/_stream_writable.js")

},{"./lib/_stream_writable.js":16}],27:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

module.exports = Stream;

var EE = require('events').EventEmitter;
var inherits = require('inherits');

inherits(Stream, EE);
Stream.Readable = require('readable-stream/readable.js');
Stream.Writable = require('readable-stream/writable.js');
Stream.Duplex = require('readable-stream/duplex.js');
Stream.Transform = require('readable-stream/transform.js');
Stream.PassThrough = require('readable-stream/passthrough.js');

// Backwards-compat with node 0.4.x
Stream.Stream = Stream;



// old-style streams.  Note that the pipe method (the only relevant
// part of this class) is overridden in the Readable class.

function Stream() {
  EE.call(this);
}

Stream.prototype.pipe = function(dest, options) {
  var source = this;

  function ondata(chunk) {
    if (dest.writable) {
      if (false === dest.write(chunk) && source.pause) {
        source.pause();
      }
    }
  }

  source.on('data', ondata);

  function ondrain() {
    if (source.readable && source.resume) {
      source.resume();
    }
  }

  dest.on('drain', ondrain);

  // If the 'end' option is not supplied, dest.end() will be called when
  // source gets the 'end' or 'close' events.  Only dest.end() once.
  if (!dest._isStdio && (!options || options.end !== false)) {
    source.on('end', onend);
    source.on('close', onclose);
  }

  var didOnEnd = false;
  function onend() {
    if (didOnEnd) return;
    didOnEnd = true;

    dest.end();
  }


  function onclose() {
    if (didOnEnd) return;
    didOnEnd = true;

    if (typeof dest.destroy === 'function') dest.destroy();
  }

  // don't leave dangling pipes when there are errors.
  function onerror(er) {
    cleanup();
    if (EE.listenerCount(this, 'error') === 0) {
      throw er; // Unhandled stream error in pipe.
    }
  }

  source.on('error', onerror);
  dest.on('error', onerror);

  // remove all the event listeners that were added.
  function cleanup() {
    source.removeListener('data', ondata);
    dest.removeListener('drain', ondrain);

    source.removeListener('end', onend);
    source.removeListener('close', onclose);

    source.removeListener('error', onerror);
    dest.removeListener('error', onerror);

    source.removeListener('end', cleanup);
    source.removeListener('close', cleanup);

    dest.removeListener('close', cleanup);
  }

  source.on('end', cleanup);
  source.on('close', cleanup);

  dest.on('close', cleanup);

  dest.emit('pipe', source);

  // Allow for unix-like usage: A.pipe(B).pipe(C)
  return dest;
};

},{"events":7,"inherits":8,"readable-stream/duplex.js":11,"readable-stream/passthrough.js":23,"readable-stream/readable.js":24,"readable-stream/transform.js":25,"readable-stream/writable.js":26}],28:[function(require,module,exports){
// Copyright Joyent, Inc. and other Node contributors.
//
// Permission is hereby granted, free of charge, to any person obtaining a
// copy of this software and associated documentation files (the
// "Software"), to deal in the Software without restriction, including
// without limitation the rights to use, copy, modify, merge, publish,
// distribute, sublicense, and/or sell copies of the Software, and to permit
// persons to whom the Software is furnished to do so, subject to the
// following conditions:
//
// The above copyright notice and this permission notice shall be included
// in all copies or substantial portions of the Software.
//
// THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS
// OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
// MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN
// NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM,
// DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR
// OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE
// USE OR OTHER DEALINGS IN THE SOFTWARE.

var Buffer = require('buffer').Buffer;

var isBufferEncoding = Buffer.isEncoding
  || function(encoding) {
       switch (encoding && encoding.toLowerCase()) {
         case 'hex': case 'utf8': case 'utf-8': case 'ascii': case 'binary': case 'base64': case 'ucs2': case 'ucs-2': case 'utf16le': case 'utf-16le': case 'raw': return true;
         default: return false;
       }
     }


function assertEncoding(encoding) {
  if (encoding && !isBufferEncoding(encoding)) {
    throw new Error('Unknown encoding: ' + encoding);
  }
}

// StringDecoder provides an interface for efficiently splitting a series of
// buffers into a series of JS strings without breaking apart multi-byte
// characters. CESU-8 is handled as part of the UTF-8 encoding.
//
// @TODO Handling all encodings inside a single object makes it very difficult
// to reason about this code, so it should be split up in the future.
// @TODO There should be a utf8-strict encoding that rejects invalid UTF-8 code
// points as used by CESU-8.
var StringDecoder = exports.StringDecoder = function(encoding) {
  this.encoding = (encoding || 'utf8').toLowerCase().replace(/[-_]/, '');
  assertEncoding(encoding);
  switch (this.encoding) {
    case 'utf8':
      // CESU-8 represents each of Surrogate Pair by 3-bytes
      this.surrogateSize = 3;
      break;
    case 'ucs2':
    case 'utf16le':
      // UTF-16 represents each of Surrogate Pair by 2-bytes
      this.surrogateSize = 2;
      this.detectIncompleteChar = utf16DetectIncompleteChar;
      break;
    case 'base64':
      // Base-64 stores 3 bytes in 4 chars, and pads the remainder.
      this.surrogateSize = 3;
      this.detectIncompleteChar = base64DetectIncompleteChar;
      break;
    default:
      this.write = passThroughWrite;
      return;
  }

  // Enough space to store all bytes of a single character. UTF-8 needs 4
  // bytes, but CESU-8 may require up to 6 (3 bytes per surrogate).
  this.charBuffer = new Buffer(6);
  // Number of bytes received for the current incomplete multi-byte character.
  this.charReceived = 0;
  // Number of bytes expected for the current incomplete multi-byte character.
  this.charLength = 0;
};


// write decodes the given buffer and returns it as JS string that is
// guaranteed to not contain any partial multi-byte characters. Any partial
// character found at the end of the buffer is buffered up, and will be
// returned when calling write again with the remaining bytes.
//
// Note: Converting a Buffer containing an orphan surrogate to a String
// currently works, but converting a String to a Buffer (via `new Buffer`, or
// Buffer#write) will replace incomplete surrogates with the unicode
// replacement character. See https://codereview.chromium.org/121173009/ .
StringDecoder.prototype.write = function(buffer) {
  var charStr = '';
  // if our last write ended with an incomplete multibyte character
  while (this.charLength) {
    // determine how many remaining bytes this buffer has to offer for this char
    var available = (buffer.length >= this.charLength - this.charReceived) ?
        this.charLength - this.charReceived :
        buffer.length;

    // add the new bytes to the char buffer
    buffer.copy(this.charBuffer, this.charReceived, 0, available);
    this.charReceived += available;

    if (this.charReceived < this.charLength) {
      // still not enough chars in this buffer? wait for more ...
      return '';
    }

    // remove bytes belonging to the current character from the buffer
    buffer = buffer.slice(available, buffer.length);

    // get the character that was split
    charStr = this.charBuffer.slice(0, this.charLength).toString(this.encoding);

    // CESU-8: lead surrogate (D800-DBFF) is also the incomplete character
    var charCode = charStr.charCodeAt(charStr.length - 1);
    if (charCode >= 0xD800 && charCode <= 0xDBFF) {
      this.charLength += this.surrogateSize;
      charStr = '';
      continue;
    }
    this.charReceived = this.charLength = 0;

    // if there are no more bytes in this buffer, just emit our char
    if (buffer.length === 0) {
      return charStr;
    }
    break;
  }

  // determine and set charLength / charReceived
  this.detectIncompleteChar(buffer);

  var end = buffer.length;
  if (this.charLength) {
    // buffer the incomplete character bytes we got
    buffer.copy(this.charBuffer, 0, buffer.length - this.charReceived, end);
    end -= this.charReceived;
  }

  charStr += buffer.toString(this.encoding, 0, end);

  var end = charStr.length - 1;
  var charCode = charStr.charCodeAt(end);
  // CESU-8: lead surrogate (D800-DBFF) is also the incomplete character
  if (charCode >= 0xD800 && charCode <= 0xDBFF) {
    var size = this.surrogateSize;
    this.charLength += size;
    this.charReceived += size;
    this.charBuffer.copy(this.charBuffer, size, 0, size);
    buffer.copy(this.charBuffer, 0, 0, size);
    return charStr.substring(0, end);
  }

  // or just emit the charStr
  return charStr;
};

// detectIncompleteChar determines if there is an incomplete UTF-8 character at
// the end of the given buffer. If so, it sets this.charLength to the byte
// length that character, and sets this.charReceived to the number of bytes
// that are available for this character.
StringDecoder.prototype.detectIncompleteChar = function(buffer) {
  // determine how many bytes we have to check at the end of this buffer
  var i = (buffer.length >= 3) ? 3 : buffer.length;

  // Figure out if one of the last i bytes of our buffer announces an
  // incomplete char.
  for (; i > 0; i--) {
    var c = buffer[buffer.length - i];

    // See http://en.wikipedia.org/wiki/UTF-8#Description

    // 110XXXXX
    if (i == 1 && c >> 5 == 0x06) {
      this.charLength = 2;
      break;
    }

    // 1110XXXX
    if (i <= 2 && c >> 4 == 0x0E) {
      this.charLength = 3;
      break;
    }

    // 11110XXX
    if (i <= 3 && c >> 3 == 0x1E) {
      this.charLength = 4;
      break;
    }
  }
  this.charReceived = i;
};

StringDecoder.prototype.end = function(buffer) {
  var res = '';
  if (buffer && buffer.length)
    res = this.write(buffer);

  if (this.charReceived) {
    var cr = this.charReceived;
    var buf = this.charBuffer;
    var enc = this.encoding;
    res += buf.slice(0, cr).toString(enc);
  }

  return res;
};

function passThroughWrite(buffer) {
  return buffer.toString(this.encoding);
}

function utf16DetectIncompleteChar(buffer) {
  this.charReceived = buffer.length % 2;
  this.charLength = this.charReceived ? 2 : 0;
}

function base64DetectIncompleteChar(buffer) {
  this.charReceived = buffer.length % 3;
  this.charLength = this.charReceived ? 3 : 0;
}

},{"buffer":3}],29:[function(require,module,exports){
module.exports = CollectingHandler;

function CollectingHandler(cbs){
	this._cbs = cbs || {};
	this.events = [];
}

var EVENTS = require("./").EVENTS;
Object.keys(EVENTS).forEach(function(name){
	if(EVENTS[name] === 0){
		name = "on" + name;
		CollectingHandler.prototype[name] = function(){
			this.events.push([name]);
			if(this._cbs[name]) this._cbs[name]();
		};
	} else if(EVENTS[name] === 1){
		name = "on" + name;
		CollectingHandler.prototype[name] = function(a){
			this.events.push([name, a]);
			if(this._cbs[name]) this._cbs[name](a);
		};
	} else if(EVENTS[name] === 2){
		name = "on" + name;
		CollectingHandler.prototype[name] = function(a, b){
			this.events.push([name, a, b]);
			if(this._cbs[name]) this._cbs[name](a, b);
		};
	} else {
		throw Error("wrong number of arguments");
	}
});

CollectingHandler.prototype.onreset = function(){
	this.events = [];
	if(this._cbs.onreset) this._cbs.onreset();
};

CollectingHandler.prototype.restart = function(){
	if(this._cbs.onreset) this._cbs.onreset();

	for(var i = 0, len = this.events.length; i < len; i++){
		if(this._cbs[this.events[i][0]]){

			var num = this.events[i].length;

			if(num === 1){
				this._cbs[this.events[i][0]]();
			} else if(num === 2){
				this._cbs[this.events[i][0]](this.events[i][1]);
			} else {
				this._cbs[this.events[i][0]](this.events[i][1], this.events[i][2]);
			}
		}
	}
};

},{"./":36}],30:[function(require,module,exports){
var index = require("./index.js"),
    DomHandler = index.DomHandler,
    DomUtils = index.DomUtils;

//TODO: make this a streamable handler
function FeedHandler(callback, options){
	this.init(callback, options);
}

require("inherits")(FeedHandler, DomHandler);

FeedHandler.prototype.init = DomHandler;

function getElements(what, where){
	return DomUtils.getElementsByTagName(what, where, true);
}
function getOneElement(what, where){
	return DomUtils.getElementsByTagName(what, where, true, 1)[0];
}
function fetch(what, where, recurse){
	return DomUtils.getText(
		DomUtils.getElementsByTagName(what, where, recurse, 1)
	).trim();
}

function addConditionally(obj, prop, what, where, recurse){
	var tmp = fetch(what, where, recurse);
	if(tmp) obj[prop] = tmp;
}

var isValidFeed = function(value){
	return value === "rss" || value === "feed" || value === "rdf:RDF";
};

FeedHandler.prototype.onend = function(){
	var feed = {},
	    feedRoot = getOneElement(isValidFeed, this.dom),
	    tmp, childs;

	if(feedRoot){
		if(feedRoot.name === "feed"){
			childs = feedRoot.children;

			feed.type = "atom";
			addConditionally(feed, "id", "id", childs);
			addConditionally(feed, "title", "title", childs);
			if((tmp = getOneElement("link", childs)) && (tmp = tmp.attribs) && (tmp = tmp.href)) feed.link = tmp;
			addConditionally(feed, "description", "subtitle", childs);
			if((tmp = fetch("updated", childs))) feed.updated = new Date(tmp);
			addConditionally(feed, "author", "email", childs, true);

			feed.items = getElements("entry", childs).map(function(item){
				var entry = {}, tmp;

				item = item.children;

				addConditionally(entry, "id", "id", item);
				addConditionally(entry, "title", "title", item);
				if((tmp = getOneElement("link", item)) && (tmp = tmp.attribs) && (tmp = tmp.href)) entry.link = tmp;
				if((tmp = fetch("summary", item) || fetch("content", item))) entry.description = tmp;
				if((tmp = fetch("updated", item))) entry.pubDate = new Date(tmp);
				return entry;
			});
		} else {
			childs = getOneElement("channel", feedRoot.children).children;

			feed.type = feedRoot.name.substr(0, 3);
			feed.id = "";
			addConditionally(feed, "title", "title", childs);
			addConditionally(feed, "link", "link", childs);
			addConditionally(feed, "description", "description", childs);
			if((tmp = fetch("lastBuildDate", childs))) feed.updated = new Date(tmp);
			addConditionally(feed, "author", "managingEditor", childs, true);

			feed.items = getElements("item", feedRoot.children).map(function(item){
				var entry = {}, tmp;

				item = item.children;

				addConditionally(entry, "id", "guid", item);
				addConditionally(entry, "title", "title", item);
				addConditionally(entry, "link", "link", item);
				addConditionally(entry, "description", "description", item);
				if((tmp = fetch("pubDate", item))) entry.pubDate = new Date(tmp);
				return entry;
			});
		}
	}
	this.dom = feed;
	DomHandler.prototype._handleCallback.call(
		this, feedRoot ? null : Error("couldn't find root of feed")
	);
};

module.exports = FeedHandler;

},{"./index.js":36,"inherits":58}],31:[function(require,module,exports){
var Tokenizer = require("./Tokenizer.js");

/*
	Options:

	xmlMode: Disables the special behavior for script/style tags (false by default)
	lowerCaseAttributeNames: call .toLowerCase for each attribute name (true if xmlMode is `false`)
	lowerCaseTags: call .toLowerCase for each tag name (true if xmlMode is `false`)
*/

/*
	Callbacks:

	oncdataend,
	oncdatastart,
	onclosetag,
	oncomment,
	oncommentend,
	onerror,
	onopentag,
	onprocessinginstruction,
	onreset,
	ontext
*/

var formTags = {
	input: true,
	option: true,
	optgroup: true,
	select: true,
	button: true,
	datalist: true,
	textarea: true
};

var openImpliesClose = {
	tr      : { tr:true, th:true, td:true },
	th      : { th:true },
	td      : { thead:true, th:true, td:true },
	body    : { head:true, link:true, script:true },
	li      : { li:true },
	p       : { p:true },
	h1      : { p:true },
	h2      : { p:true },
	h3      : { p:true },
	h4      : { p:true },
	h5      : { p:true },
	h6      : { p:true },
	select  : formTags,
	input   : formTags,
	output  : formTags,
	button  : formTags,
	datalist: formTags,
	textarea: formTags,
	option  : { option:true },
	optgroup: { optgroup:true }
};

var voidElements = {
	__proto__: null,
	area: true,
	base: true,
	basefont: true,
	br: true,
	col: true,
	command: true,
	embed: true,
	frame: true,
	hr: true,
	img: true,
	input: true,
	isindex: true,
	keygen: true,
	link: true,
	meta: true,
	param: true,
	source: true,
	track: true,
	wbr: true,

	//common self closing svg elements
	path: true,
	circle: true,
	ellipse: true,
	line: true,
	rect: true,
	use: true,
	stop: true,
	polyline: true,
	polygon: true
};

var re_nameEnd = /\s|\//;

function Parser(cbs, options){
	this._options = options || {};
	this._cbs = cbs || {};

	this._tagname = "";
	this._attribname = "";
	this._attribvalue = "";
	this._attribs = null;
	this._stack = [];

	this.startIndex = 0;
	this.endIndex = null;

	this._lowerCaseTagNames = "lowerCaseTags" in this._options ?
									!!this._options.lowerCaseTags :
									!this._options.xmlMode;
	this._lowerCaseAttributeNames = "lowerCaseAttributeNames" in this._options ?
									!!this._options.lowerCaseAttributeNames :
									!this._options.xmlMode;

	if(this._options.Tokenizer) {
		Tokenizer = this._options.Tokenizer;
	}
	this._tokenizer = new Tokenizer(this._options, this);

	if(this._cbs.onparserinit) this._cbs.onparserinit(this);
}

require("inherits")(Parser, require("events").EventEmitter);

Parser.prototype._updatePosition = function(initialOffset){
	if(this.endIndex === null){
		if(this._tokenizer._sectionStart <= initialOffset){
			this.startIndex = 0;
		} else {
			this.startIndex = this._tokenizer._sectionStart - initialOffset;
		}
	}
	else this.startIndex = this.endIndex + 1;
	this.endIndex = this._tokenizer.getAbsoluteIndex();
};

//Tokenizer event handlers
Parser.prototype.ontext = function(data){
	this._updatePosition(1);
	this.endIndex--;

	if(this._cbs.ontext) this._cbs.ontext(data);
};

Parser.prototype.onopentagname = function(name){
	if(this._lowerCaseTagNames){
		name = name.toLowerCase();
	}

	this._tagname = name;

	if(!this._options.xmlMode && name in openImpliesClose) {
		for(
			var el;
			(el = this._stack[this._stack.length - 1]) in openImpliesClose[name];
			this.onclosetag(el)
		);
	}

	if(this._options.xmlMode || !(name in voidElements)){
		this._stack.push(name);
	}

	if(this._cbs.onopentagname) this._cbs.onopentagname(name);
	if(this._cbs.onopentag) this._attribs = {};
};

Parser.prototype.onopentagend = function(){
	this._updatePosition(1);

	if(this._attribs){
		if(this._cbs.onopentag) this._cbs.onopentag(this._tagname, this._attribs);
		this._attribs = null;
	}

	if(!this._options.xmlMode && this._cbs.onclosetag && this._tagname in voidElements){
		this._cbs.onclosetag(this._tagname);
	}

	this._tagname = "";
};

Parser.prototype.onclosetag = function(name){
	this._updatePosition(1);

	if(this._lowerCaseTagNames){
		name = name.toLowerCase();
	}

	if(this._stack.length && (!(name in voidElements) || this._options.xmlMode)){
		var pos = this._stack.lastIndexOf(name);
		if(pos !== -1){
			if(this._cbs.onclosetag){
				pos = this._stack.length - pos;
				while(pos--) this._cbs.onclosetag(this._stack.pop());
			}
			else this._stack.length = pos;
		} else if(name === "p" && !this._options.xmlMode){
			this.onopentagname(name);
			this._closeCurrentTag();
		}
	} else if(!this._options.xmlMode && (name === "br" || name === "p")){
		this.onopentagname(name);
		this._closeCurrentTag();
	}
};

Parser.prototype.onselfclosingtag = function(){
	if(this._options.xmlMode || this._options.recognizeSelfClosing){
		this._closeCurrentTag();
	} else {
		this.onopentagend();
	}
};

Parser.prototype._closeCurrentTag = function(){
	var name = this._tagname;

	this.onopentagend();

	//self-closing tags will be on the top of the stack
	//(cheaper check than in onclosetag)
	if(this._stack[this._stack.length - 1] === name){
		if(this._cbs.onclosetag){
			this._cbs.onclosetag(name);
		}
		this._stack.pop();
	}
};

Parser.prototype.onattribname = function(name){
	if(this._lowerCaseAttributeNames){
		name = name.toLowerCase();
	}
	this._attribname = name;
};

Parser.prototype.onattribdata = function(value){
	this._attribvalue += value;
};

Parser.prototype.onattribend = function(){
	if(this._cbs.onattribute) this._cbs.onattribute(this._attribname, this._attribvalue);
	if(
		this._attribs &&
		!Object.prototype.hasOwnProperty.call(this._attribs, this._attribname)
	){
		this._attribs[this._attribname] = this._attribvalue;
	}
	this._attribname = "";
	this._attribvalue = "";
};

Parser.prototype._getInstructionName = function(value){
	var idx = value.search(re_nameEnd),
	    name = idx < 0 ? value : value.substr(0, idx);

	if(this._lowerCaseTagNames){
		name = name.toLowerCase();
	}

	return name;
};

Parser.prototype.ondeclaration = function(value){
	if(this._cbs.onprocessinginstruction){
		var name = this._getInstructionName(value);
		this._cbs.onprocessinginstruction("!" + name, "!" + value);
	}
};

Parser.prototype.onprocessinginstruction = function(value){
	if(this._cbs.onprocessinginstruction){
		var name = this._getInstructionName(value);
		this._cbs.onprocessinginstruction("?" + name, "?" + value);
	}
};

Parser.prototype.oncomment = function(value){
	this._updatePosition(4);

	if(this._cbs.oncomment) this._cbs.oncomment(value);
	if(this._cbs.oncommentend) this._cbs.oncommentend();
};

Parser.prototype.oncdata = function(value){
	this._updatePosition(1);

	if(this._options.xmlMode || this._options.recognizeCDATA){
		if(this._cbs.oncdatastart) this._cbs.oncdatastart();
		if(this._cbs.ontext) this._cbs.ontext(value);
		if(this._cbs.oncdataend) this._cbs.oncdataend();
	} else {
		this.oncomment("[CDATA[" + value + "]]");
	}
};

Parser.prototype.onerror = function(err){
	if(this._cbs.onerror) this._cbs.onerror(err);
};

Parser.prototype.onend = function(){
	if(this._cbs.onclosetag){
		for(
			var i = this._stack.length;
			i > 0;
			this._cbs.onclosetag(this._stack[--i])
		);
	}
	if(this._cbs.onend) this._cbs.onend();
};


//Resets the parser to a blank state, ready to parse a new HTML document
Parser.prototype.reset = function(){
	if(this._cbs.onreset) this._cbs.onreset();
	this._tokenizer.reset();

	this._tagname = "";
	this._attribname = "";
	this._attribs = null;
	this._stack = [];

	if(this._cbs.onparserinit) this._cbs.onparserinit(this);
};

//Parses a complete HTML document and pushes it to the handler
Parser.prototype.parseComplete = function(data){
	this.reset();
	this.end(data);
};

Parser.prototype.write = function(chunk){
	this._tokenizer.write(chunk);
};

Parser.prototype.end = function(chunk){
	this._tokenizer.end(chunk);
};

Parser.prototype.pause = function(){
	this._tokenizer.pause();
};

Parser.prototype.resume = function(){
	this._tokenizer.resume();
};

//alias for backwards compat
Parser.prototype.parseChunk = Parser.prototype.write;
Parser.prototype.done = Parser.prototype.end;

module.exports = Parser;

},{"./Tokenizer.js":34,"events":7,"inherits":58}],32:[function(require,module,exports){
module.exports = ProxyHandler;

function ProxyHandler(cbs){
	this._cbs = cbs || {};
}

var EVENTS = require("./").EVENTS;
Object.keys(EVENTS).forEach(function(name){
	if(EVENTS[name] === 0){
		name = "on" + name;
		ProxyHandler.prototype[name] = function(){
			if(this._cbs[name]) this._cbs[name]();
		};
	} else if(EVENTS[name] === 1){
		name = "on" + name;
		ProxyHandler.prototype[name] = function(a){
			if(this._cbs[name]) this._cbs[name](a);
		};
	} else if(EVENTS[name] === 2){
		name = "on" + name;
		ProxyHandler.prototype[name] = function(a, b){
			if(this._cbs[name]) this._cbs[name](a, b);
		};
	} else {
		throw Error("wrong number of arguments");
	}
});
},{"./":36}],33:[function(require,module,exports){
module.exports = Stream;

var Parser = require("./WritableStream.js");

function Stream(options){
	Parser.call(this, new Cbs(this), options);
}

require("inherits")(Stream, Parser);

Stream.prototype.readable = true;

function Cbs(scope){
	this.scope = scope;
}

var EVENTS = require("../").EVENTS;

Object.keys(EVENTS).forEach(function(name){
	if(EVENTS[name] === 0){
		Cbs.prototype["on" + name] = function(){
			this.scope.emit(name);
		};
	} else if(EVENTS[name] === 1){
		Cbs.prototype["on" + name] = function(a){
			this.scope.emit(name, a);
		};
	} else if(EVENTS[name] === 2){
		Cbs.prototype["on" + name] = function(a, b){
			this.scope.emit(name, a, b);
		};
	} else {
		throw Error("wrong number of arguments!");
	}
});
},{"../":36,"./WritableStream.js":35,"inherits":58}],34:[function(require,module,exports){
module.exports = Tokenizer;

var decodeCodePoint = require("entities/lib/decode_codepoint.js"),
    entityMap = require("entities/maps/entities.json"),
    legacyMap = require("entities/maps/legacy.json"),
    xmlMap    = require("entities/maps/xml.json"),

    i = 0,

    TEXT                      = i++,
    BEFORE_TAG_NAME           = i++, //after <
    IN_TAG_NAME               = i++,
    IN_SELF_CLOSING_TAG       = i++,
    BEFORE_CLOSING_TAG_NAME   = i++,
    IN_CLOSING_TAG_NAME       = i++,
    AFTER_CLOSING_TAG_NAME    = i++,

    //attributes
    BEFORE_ATTRIBUTE_NAME     = i++,
    IN_ATTRIBUTE_NAME         = i++,
    AFTER_ATTRIBUTE_NAME      = i++,
    BEFORE_ATTRIBUTE_VALUE    = i++,
    IN_ATTRIBUTE_VALUE_DQ     = i++, // "
    IN_ATTRIBUTE_VALUE_SQ     = i++, // '
    IN_ATTRIBUTE_VALUE_NQ     = i++,

    //declarations
    BEFORE_DECLARATION        = i++, // !
    IN_DECLARATION            = i++,

    //processing instructions
    IN_PROCESSING_INSTRUCTION = i++, // ?

    //comments
    BEFORE_COMMENT            = i++,
    IN_COMMENT                = i++,
    AFTER_COMMENT_1           = i++,
    AFTER_COMMENT_2           = i++,

    //cdata
    BEFORE_CDATA_1            = i++, // [
    BEFORE_CDATA_2            = i++, // C
    BEFORE_CDATA_3            = i++, // D
    BEFORE_CDATA_4            = i++, // A
    BEFORE_CDATA_5            = i++, // T
    BEFORE_CDATA_6            = i++, // A
    IN_CDATA                  = i++, // [
    AFTER_CDATA_1             = i++, // ]
    AFTER_CDATA_2             = i++, // ]

    //special tags
    BEFORE_SPECIAL            = i++, //S
    BEFORE_SPECIAL_END        = i++,   //S

    BEFORE_SCRIPT_1           = i++, //C
    BEFORE_SCRIPT_2           = i++, //R
    BEFORE_SCRIPT_3           = i++, //I
    BEFORE_SCRIPT_4           = i++, //P
    BEFORE_SCRIPT_5           = i++, //T
    AFTER_SCRIPT_1            = i++, //C
    AFTER_SCRIPT_2            = i++, //R
    AFTER_SCRIPT_3            = i++, //I
    AFTER_SCRIPT_4            = i++, //P
    AFTER_SCRIPT_5            = i++, //T

    BEFORE_STYLE_1            = i++, //T
    BEFORE_STYLE_2            = i++, //Y
    BEFORE_STYLE_3            = i++, //L
    BEFORE_STYLE_4            = i++, //E
    AFTER_STYLE_1             = i++, //T
    AFTER_STYLE_2             = i++, //Y
    AFTER_STYLE_3             = i++, //L
    AFTER_STYLE_4             = i++, //E

    BEFORE_ENTITY             = i++, //&
    BEFORE_NUMERIC_ENTITY     = i++, //#
    IN_NAMED_ENTITY           = i++,
    IN_NUMERIC_ENTITY         = i++,
    IN_HEX_ENTITY             = i++, //X

    j = 0,

    SPECIAL_NONE              = j++,
    SPECIAL_SCRIPT            = j++,
    SPECIAL_STYLE             = j++;

function whitespace(c){
	return c === " " || c === "\n" || c === "\t" || c === "\f" || c === "\r";
}

function characterState(char, SUCCESS){
	return function(c){
		if(c === char) this._state = SUCCESS;
	};
}

function ifElseState(upper, SUCCESS, FAILURE){
	var lower = upper.toLowerCase();

	if(upper === lower){
		return function(c){
			if(c === lower){
				this._state = SUCCESS;
			} else {
				this._state = FAILURE;
				this._index--;
			}
		};
	} else {
		return function(c){
			if(c === lower || c === upper){
				this._state = SUCCESS;
			} else {
				this._state = FAILURE;
				this._index--;
			}
		};
	}
}

function consumeSpecialNameChar(upper, NEXT_STATE){
	var lower = upper.toLowerCase();

	return function(c){
		if(c === lower || c === upper){
			this._state = NEXT_STATE;
		} else {
			this._state = IN_TAG_NAME;
			this._index--; //consume the token again
		}
	};
}

function Tokenizer(options, cbs){
	this._state = TEXT;
	this._buffer = "";
	this._sectionStart = 0;
	this._index = 0;
	this._bufferOffset = 0; //chars removed from _buffer
	this._baseState = TEXT;
	this._special = SPECIAL_NONE;
	this._cbs = cbs;
	this._running = true;
	this._ended = false;
	this._xmlMode = !!(options && options.xmlMode);
	this._decodeEntities = !!(options && options.decodeEntities);
}

Tokenizer.prototype._stateText = function(c){
	if(c === "<"){
		if(this._index > this._sectionStart){
			this._cbs.ontext(this._getSection());
		}
		this._state = BEFORE_TAG_NAME;
		this._sectionStart = this._index;
	} else if(this._decodeEntities && this._special === SPECIAL_NONE && c === "&"){
		if(this._index > this._sectionStart){
			this._cbs.ontext(this._getSection());
		}
		this._baseState = TEXT;
		this._state = BEFORE_ENTITY;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateBeforeTagName = function(c){
	if(c === "/"){
		this._state = BEFORE_CLOSING_TAG_NAME;
	} else if(c === "<"){
		this._cbs.ontext(this._getSection());
		this._sectionStart = this._index;
	} else if(c === ">" || this._special !== SPECIAL_NONE || whitespace(c)) {
		this._state = TEXT;
	} else if(c === "!"){
		this._state = BEFORE_DECLARATION;
		this._sectionStart = this._index + 1;
	} else if(c === "?"){
		this._state = IN_PROCESSING_INSTRUCTION;
		this._sectionStart = this._index + 1;
	} else {
		this._state = (!this._xmlMode && (c === "s" || c === "S")) ?
						BEFORE_SPECIAL : IN_TAG_NAME;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateInTagName = function(c){
	if(c === "/" || c === ">" || whitespace(c)){
		this._emitToken("onopentagname");
		this._state = BEFORE_ATTRIBUTE_NAME;
		this._index--;
	}
};

Tokenizer.prototype._stateBeforeCloseingTagName = function(c){
	if(whitespace(c));
	else if(c === ">"){
		this._state = TEXT;
	} else if(this._special !== SPECIAL_NONE){
		if(c === "s" || c === "S"){
			this._state = BEFORE_SPECIAL_END;
		} else {
			this._state = TEXT;
			this._index--;
		}
	} else {
		this._state = IN_CLOSING_TAG_NAME;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateInCloseingTagName = function(c){
	if(c === ">" || whitespace(c)){
		this._emitToken("onclosetag");
		this._state = AFTER_CLOSING_TAG_NAME;
		this._index--;
	}
};

Tokenizer.prototype._stateAfterCloseingTagName = function(c){
	//skip everything until ">"
	if(c === ">"){
		this._state = TEXT;
		this._sectionStart = this._index + 1;
	}
};

Tokenizer.prototype._stateBeforeAttributeName = function(c){
	if(c === ">"){
		this._cbs.onopentagend();
		this._state = TEXT;
		this._sectionStart = this._index + 1;
	} else if(c === "/"){
		this._state = IN_SELF_CLOSING_TAG;
	} else if(!whitespace(c)){
		this._state = IN_ATTRIBUTE_NAME;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateInSelfClosingTag = function(c){
	if(c === ">"){
		this._cbs.onselfclosingtag();
		this._state = TEXT;
		this._sectionStart = this._index + 1;
	} else if(!whitespace(c)){
		this._state = BEFORE_ATTRIBUTE_NAME;
		this._index--;
	}
};

Tokenizer.prototype._stateInAttributeName = function(c){
	if(c === "=" || c === "/" || c === ">" || whitespace(c)){
		this._cbs.onattribname(this._getSection());
		this._sectionStart = -1;
		this._state = AFTER_ATTRIBUTE_NAME;
		this._index--;
	}
};

Tokenizer.prototype._stateAfterAttributeName = function(c){
	if(c === "="){
		this._state = BEFORE_ATTRIBUTE_VALUE;
	} else if(c === "/" || c === ">"){
		this._cbs.onattribend();
		this._state = BEFORE_ATTRIBUTE_NAME;
		this._index--;
	} else if(!whitespace(c)){
		this._cbs.onattribend();
		this._state = IN_ATTRIBUTE_NAME;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateBeforeAttributeValue = function(c){
	if(c === "\""){
		this._state = IN_ATTRIBUTE_VALUE_DQ;
		this._sectionStart = this._index + 1;
	} else if(c === "'"){
		this._state = IN_ATTRIBUTE_VALUE_SQ;
		this._sectionStart = this._index + 1;
	} else if(!whitespace(c)){
		this._state = IN_ATTRIBUTE_VALUE_NQ;
		this._sectionStart = this._index;
		this._index--; //reconsume token
	}
};

Tokenizer.prototype._stateInAttributeValueDoubleQuotes = function(c){
	if(c === "\""){
		this._emitToken("onattribdata");
		this._cbs.onattribend();
		this._state = BEFORE_ATTRIBUTE_NAME;
	} else if(this._decodeEntities && c === "&"){
		this._emitToken("onattribdata");
		this._baseState = this._state;
		this._state = BEFORE_ENTITY;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateInAttributeValueSingleQuotes = function(c){
	if(c === "'"){
		this._emitToken("onattribdata");
		this._cbs.onattribend();
		this._state = BEFORE_ATTRIBUTE_NAME;
	} else if(this._decodeEntities && c === "&"){
		this._emitToken("onattribdata");
		this._baseState = this._state;
		this._state = BEFORE_ENTITY;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateInAttributeValueNoQuotes = function(c){
	if(whitespace(c) || c === ">"){
		this._emitToken("onattribdata");
		this._cbs.onattribend();
		this._state = BEFORE_ATTRIBUTE_NAME;
		this._index--;
	} else if(this._decodeEntities && c === "&"){
		this._emitToken("onattribdata");
		this._baseState = this._state;
		this._state = BEFORE_ENTITY;
		this._sectionStart = this._index;
	}
};

Tokenizer.prototype._stateBeforeDeclaration = function(c){
	this._state = c === "[" ? BEFORE_CDATA_1 :
					c === "-" ? BEFORE_COMMENT :
						IN_DECLARATION;
};

Tokenizer.prototype._stateInDeclaration = function(c){
	if(c === ">"){
		this._cbs.ondeclaration(this._getSection());
		this._state = TEXT;
		this._sectionStart = this._index + 1;
	}
};

Tokenizer.prototype._stateInProcessingInstruction = function(c){
	if(c === ">"){
		this._cbs.onprocessinginstruction(this._getSection());
		this._state = TEXT;
		this._sectionStart = this._index + 1;
	}
};

Tokenizer.prototype._stateBeforeComment = function(c){
	if(c === "-"){
		this._state = IN_COMMENT;
		this._sectionStart = this._index + 1;
	} else {
		this._state = IN_DECLARATION;
	}
};

Tokenizer.prototype._stateInComment = function(c){
	if(c === "-") this._state = AFTER_COMMENT_1;
};

Tokenizer.prototype._stateAfterComment1 = function(c){
	if(c === "-"){
		this._state = AFTER_COMMENT_2;
	} else {
		this._state = IN_COMMENT;
	}
};

Tokenizer.prototype._stateAfterComment2 = function(c){
	if(c === ">"){
		//remove 2 trailing chars
		this._cbs.oncomment(this._buffer.substring(this._sectionStart, this._index - 2));
		this._state = TEXT;
		this._sectionStart = this._index + 1;
	} else if(c !== "-"){
		this._state = IN_COMMENT;
	}
	// else: stay in AFTER_COMMENT_2 (`--->`)
};

Tokenizer.prototype._stateBeforeCdata1 = ifElseState("C", BEFORE_CDATA_2, IN_DECLARATION);
Tokenizer.prototype._stateBeforeCdata2 = ifElseState("D", BEFORE_CDATA_3, IN_DECLARATION);
Tokenizer.prototype._stateBeforeCdata3 = ifElseState("A", BEFORE_CDATA_4, IN_DECLARATION);
Tokenizer.prototype._stateBeforeCdata4 = ifElseState("T", BEFORE_CDATA_5, IN_DECLARATION);
Tokenizer.prototype._stateBeforeCdata5 = ifElseState("A", BEFORE_CDATA_6, IN_DECLARATION);

Tokenizer.prototype._stateBeforeCdata6 = function(c){
	if(c === "["){
		this._state = IN_CDATA;
		this._sectionStart = this._index + 1;
	} else {
		this._state = IN_DECLARATION;
		this._index--;
	}
};

Tokenizer.prototype._stateInCdata = function(c){
	if(c === "]") this._state = AFTER_CDATA_1;
};

Tokenizer.prototype._stateAfterCdata1 = characterState("]", AFTER_CDATA_2);

Tokenizer.prototype._stateAfterCdata2 = function(c){
	if(c === ">"){
		//remove 2 trailing chars
		this._cbs.oncdata(this._buffer.substring(this._sectionStart, this._index - 2));
		this._state = TEXT;
		this._sectionStart = this._index + 1;
	} else if(c !== "]") {
		this._state = IN_CDATA;
	}
	//else: stay in AFTER_CDATA_2 (`]]]>`)
};

Tokenizer.prototype._stateBeforeSpecial = function(c){
	if(c === "c" || c === "C"){
		this._state = BEFORE_SCRIPT_1;
	} else if(c === "t" || c === "T"){
		this._state = BEFORE_STYLE_1;
	} else {
		this._state = IN_TAG_NAME;
		this._index--; //consume the token again
	}
};

Tokenizer.prototype._stateBeforeSpecialEnd = function(c){
	if(this._special === SPECIAL_SCRIPT && (c === "c" || c === "C")){
		this._state = AFTER_SCRIPT_1;
	} else if(this._special === SPECIAL_STYLE && (c === "t" || c === "T")){
		this._state = AFTER_STYLE_1;
	}
	else this._state = TEXT;
};

Tokenizer.prototype._stateBeforeScript1 = consumeSpecialNameChar("R", BEFORE_SCRIPT_2);
Tokenizer.prototype._stateBeforeScript2 = consumeSpecialNameChar("I", BEFORE_SCRIPT_3);
Tokenizer.prototype._stateBeforeScript3 = consumeSpecialNameChar("P", BEFORE_SCRIPT_4);
Tokenizer.prototype._stateBeforeScript4 = consumeSpecialNameChar("T", BEFORE_SCRIPT_5);

Tokenizer.prototype._stateBeforeScript5 = function(c){
	if(c === "/" || c === ">" || whitespace(c)){
		this._special = SPECIAL_SCRIPT;
	}
	this._state = IN_TAG_NAME;
	this._index--; //consume the token again
};

Tokenizer.prototype._stateAfterScript1 = ifElseState("R", AFTER_SCRIPT_2, TEXT);
Tokenizer.prototype._stateAfterScript2 = ifElseState("I", AFTER_SCRIPT_3, TEXT);
Tokenizer.prototype._stateAfterScript3 = ifElseState("P", AFTER_SCRIPT_4, TEXT);
Tokenizer.prototype._stateAfterScript4 = ifElseState("T", AFTER_SCRIPT_5, TEXT);

Tokenizer.prototype._stateAfterScript5 = function(c){
	if(c === ">" || whitespace(c)){
		this._special = SPECIAL_NONE;
		this._state = IN_CLOSING_TAG_NAME;
		this._sectionStart = this._index - 6;
		this._index--; //reconsume the token
	}
	else this._state = TEXT;
};

Tokenizer.prototype._stateBeforeStyle1 = consumeSpecialNameChar("Y", BEFORE_STYLE_2);
Tokenizer.prototype._stateBeforeStyle2 = consumeSpecialNameChar("L", BEFORE_STYLE_3);
Tokenizer.prototype._stateBeforeStyle3 = consumeSpecialNameChar("E", BEFORE_STYLE_4);

Tokenizer.prototype._stateBeforeStyle4 = function(c){
	if(c === "/" || c === ">" || whitespace(c)){
		this._special = SPECIAL_STYLE;
	}
	this._state = IN_TAG_NAME;
	this._index--; //consume the token again
};

Tokenizer.prototype._stateAfterStyle1 = ifElseState("Y", AFTER_STYLE_2, TEXT);
Tokenizer.prototype._stateAfterStyle2 = ifElseState("L", AFTER_STYLE_3, TEXT);
Tokenizer.prototype._stateAfterStyle3 = ifElseState("E", AFTER_STYLE_4, TEXT);

Tokenizer.prototype._stateAfterStyle4 = function(c){
	if(c === ">" || whitespace(c)){
		this._special = SPECIAL_NONE;
		this._state = IN_CLOSING_TAG_NAME;
		this._sectionStart = this._index - 5;
		this._index--; //reconsume the token
	}
	else this._state = TEXT;
};

Tokenizer.prototype._stateBeforeEntity = ifElseState("#", BEFORE_NUMERIC_ENTITY, IN_NAMED_ENTITY);
Tokenizer.prototype._stateBeforeNumericEntity = ifElseState("X", IN_HEX_ENTITY, IN_NUMERIC_ENTITY);

//for entities terminated with a semicolon
Tokenizer.prototype._parseNamedEntityStrict = function(){
	//offset = 1
	if(this._sectionStart + 1 < this._index){
		var entity = this._buffer.substring(this._sectionStart + 1, this._index),
		    map = this._xmlMode ? xmlMap : entityMap;

		if(map.hasOwnProperty(entity)){
			this._emitPartial(map[entity]);
			this._sectionStart = this._index + 1;
		}
	}
};


//parses legacy entities (without trailing semicolon)
Tokenizer.prototype._parseLegacyEntity = function(){
	var start = this._sectionStart + 1,
	    limit = this._index - start;

	if(limit > 6) limit = 6; //the max length of legacy entities is 6

	while(limit >= 2){ //the min length of legacy entities is 2
		var entity = this._buffer.substr(start, limit);

		if(legacyMap.hasOwnProperty(entity)){
			this._emitPartial(legacyMap[entity]);
			this._sectionStart += limit + 1;
			return;
		} else {
			limit--;
		}
	}
};

Tokenizer.prototype._stateInNamedEntity = function(c){
	if(c === ";"){
		this._parseNamedEntityStrict();
		if(this._sectionStart + 1 < this._index && !this._xmlMode){
			this._parseLegacyEntity();
		}
		this._state = this._baseState;
	} else if((c < "a" || c > "z") && (c < "A" || c > "Z") && (c < "0" || c > "9")){
		if(this._xmlMode);
		else if(this._sectionStart + 1 === this._index);
		else if(this._baseState !== TEXT){
			if(c !== "="){
				this._parseNamedEntityStrict();
			}
		} else {
			this._parseLegacyEntity();
		}

		this._state = this._baseState;
		this._index--;
	}
};

Tokenizer.prototype._decodeNumericEntity = function(offset, base){
	var sectionStart = this._sectionStart + offset;

	if(sectionStart !== this._index){
		//parse entity
		var entity = this._buffer.substring(sectionStart, this._index);
		var parsed = parseInt(entity, base);

		this._emitPartial(decodeCodePoint(parsed));
		this._sectionStart = this._index;
	} else {
		this._sectionStart--;
	}

	this._state = this._baseState;
};

Tokenizer.prototype._stateInNumericEntity = function(c){
	if(c === ";"){
		this._decodeNumericEntity(2, 10);
		this._sectionStart++;
	} else if(c < "0" || c > "9"){
		if(!this._xmlMode){
			this._decodeNumericEntity(2, 10);
		} else {
			this._state = this._baseState;
		}
		this._index--;
	}
};

Tokenizer.prototype._stateInHexEntity = function(c){
	if(c === ";"){
		this._decodeNumericEntity(3, 16);
		this._sectionStart++;
	} else if((c < "a" || c > "f") && (c < "A" || c > "F") && (c < "0" || c > "9")){
		if(!this._xmlMode){
			this._decodeNumericEntity(3, 16);
		} else {
			this._state = this._baseState;
		}
		this._index--;
	}
};

Tokenizer.prototype._cleanup = function (){
	if(this._sectionStart < 0){
		this._buffer = "";
		this._bufferOffset += this._index;
		this._index = 0;
	} else if(this._running){
		if(this._state === TEXT){
			if(this._sectionStart !== this._index){
				this._cbs.ontext(this._buffer.substr(this._sectionStart));
			}
			this._buffer = "";
			this._bufferOffset += this._index;
			this._index = 0;
		} else if(this._sectionStart === this._index){
			//the section just started
			this._buffer = "";
			this._bufferOffset += this._index;
			this._index = 0;
		} else {
			//remove everything unnecessary
			this._buffer = this._buffer.substr(this._sectionStart);
			this._index -= this._sectionStart;
			this._bufferOffset += this._sectionStart;
		}

		this._sectionStart = 0;
	}
};

//TODO make events conditional
Tokenizer.prototype.write = function(chunk){
	if(this._ended) this._cbs.onerror(Error(".write() after done!"));

	this._buffer += chunk;
	this._parse();
};

Tokenizer.prototype._parse = function(){
	while(this._index < this._buffer.length && this._running){
		var c = this._buffer.charAt(this._index);
		if(this._state === TEXT) {
			this._stateText(c);
		} else if(this._state === BEFORE_TAG_NAME){
			this._stateBeforeTagName(c);
		} else if(this._state === IN_TAG_NAME) {
			this._stateInTagName(c);
		} else if(this._state === BEFORE_CLOSING_TAG_NAME){
			this._stateBeforeCloseingTagName(c);
		} else if(this._state === IN_CLOSING_TAG_NAME){
			this._stateInCloseingTagName(c);
		} else if(this._state === AFTER_CLOSING_TAG_NAME){
			this._stateAfterCloseingTagName(c);
		} else if(this._state === IN_SELF_CLOSING_TAG){
			this._stateInSelfClosingTag(c);
		}

		/*
		*	attributes
		*/
		else if(this._state === BEFORE_ATTRIBUTE_NAME){
			this._stateBeforeAttributeName(c);
		} else if(this._state === IN_ATTRIBUTE_NAME){
			this._stateInAttributeName(c);
		} else if(this._state === AFTER_ATTRIBUTE_NAME){
			this._stateAfterAttributeName(c);
		} else if(this._state === BEFORE_ATTRIBUTE_VALUE){
			this._stateBeforeAttributeValue(c);
		} else if(this._state === IN_ATTRIBUTE_VALUE_DQ){
			this._stateInAttributeValueDoubleQuotes(c);
		} else if(this._state === IN_ATTRIBUTE_VALUE_SQ){
			this._stateInAttributeValueSingleQuotes(c);
		} else if(this._state === IN_ATTRIBUTE_VALUE_NQ){
			this._stateInAttributeValueNoQuotes(c);
		}

		/*
		*	declarations
		*/
		else if(this._state === BEFORE_DECLARATION){
			this._stateBeforeDeclaration(c);
		} else if(this._state === IN_DECLARATION){
			this._stateInDeclaration(c);
		}

		/*
		*	processing instructions
		*/
		else if(this._state === IN_PROCESSING_INSTRUCTION){
			this._stateInProcessingInstruction(c);
		}

		/*
		*	comments
		*/
		else if(this._state === BEFORE_COMMENT){
			this._stateBeforeComment(c);
		} else if(this._state === IN_COMMENT){
			this._stateInComment(c);
		} else if(this._state === AFTER_COMMENT_1){
			this._stateAfterComment1(c);
		} else if(this._state === AFTER_COMMENT_2){
			this._stateAfterComment2(c);
		}

		/*
		*	cdata
		*/
		else if(this._state === BEFORE_CDATA_1){
			this._stateBeforeCdata1(c);
		} else if(this._state === BEFORE_CDATA_2){
			this._stateBeforeCdata2(c);
		} else if(this._state === BEFORE_CDATA_3){
			this._stateBeforeCdata3(c);
		} else if(this._state === BEFORE_CDATA_4){
			this._stateBeforeCdata4(c);
		} else if(this._state === BEFORE_CDATA_5){
			this._stateBeforeCdata5(c);
		} else if(this._state === BEFORE_CDATA_6){
			this._stateBeforeCdata6(c);
		} else if(this._state === IN_CDATA){
			this._stateInCdata(c);
		} else if(this._state === AFTER_CDATA_1){
			this._stateAfterCdata1(c);
		} else if(this._state === AFTER_CDATA_2){
			this._stateAfterCdata2(c);
		}

		/*
		* special tags
		*/
		else if(this._state === BEFORE_SPECIAL){
			this._stateBeforeSpecial(c);
		} else if(this._state === BEFORE_SPECIAL_END){
			this._stateBeforeSpecialEnd(c);
		}

		/*
		* script
		*/
		else if(this._state === BEFORE_SCRIPT_1){
			this._stateBeforeScript1(c);
		} else if(this._state === BEFORE_SCRIPT_2){
			this._stateBeforeScript2(c);
		} else if(this._state === BEFORE_SCRIPT_3){
			this._stateBeforeScript3(c);
		} else if(this._state === BEFORE_SCRIPT_4){
			this._stateBeforeScript4(c);
		} else if(this._state === BEFORE_SCRIPT_5){
			this._stateBeforeScript5(c);
		}

		else if(this._state === AFTER_SCRIPT_1){
			this._stateAfterScript1(c);
		} else if(this._state === AFTER_SCRIPT_2){
			this._stateAfterScript2(c);
		} else if(this._state === AFTER_SCRIPT_3){
			this._stateAfterScript3(c);
		} else if(this._state === AFTER_SCRIPT_4){
			this._stateAfterScript4(c);
		} else if(this._state === AFTER_SCRIPT_5){
			this._stateAfterScript5(c);
		}

		/*
		* style
		*/
		else if(this._state === BEFORE_STYLE_1){
			this._stateBeforeStyle1(c);
		} else if(this._state === BEFORE_STYLE_2){
			this._stateBeforeStyle2(c);
		} else if(this._state === BEFORE_STYLE_3){
			this._stateBeforeStyle3(c);
		} else if(this._state === BEFORE_STYLE_4){
			this._stateBeforeStyle4(c);
		}

		else if(this._state === AFTER_STYLE_1){
			this._stateAfterStyle1(c);
		} else if(this._state === AFTER_STYLE_2){
			this._stateAfterStyle2(c);
		} else if(this._state === AFTER_STYLE_3){
			this._stateAfterStyle3(c);
		} else if(this._state === AFTER_STYLE_4){
			this._stateAfterStyle4(c);
		}

		/*
		* entities
		*/
		else if(this._state === BEFORE_ENTITY){
			this._stateBeforeEntity(c);
		} else if(this._state === BEFORE_NUMERIC_ENTITY){
			this._stateBeforeNumericEntity(c);
		} else if(this._state === IN_NAMED_ENTITY){
			this._stateInNamedEntity(c);
		} else if(this._state === IN_NUMERIC_ENTITY){
			this._stateInNumericEntity(c);
		} else if(this._state === IN_HEX_ENTITY){
			this._stateInHexEntity(c);
		}

		else {
			this._cbs.onerror(Error("unknown _state"), this._state);
		}

		this._index++;
	}

	this._cleanup();
};

Tokenizer.prototype.pause = function(){
	this._running = false;
};
Tokenizer.prototype.resume = function(){
	this._running = true;

	if(this._index < this._buffer.length){
		this._parse();
	}
	if(this._ended){
		this._finish();
	}
};

Tokenizer.prototype.end = function(chunk){
	if(this._ended) this._cbs.onerror(Error(".end() after done!"));
	if(chunk) this.write(chunk);

	this._ended = true;

	if(this._running) this._finish();
};

Tokenizer.prototype._finish = function(){
	//if there is remaining data, emit it in a reasonable way
	if(this._sectionStart < this._index){
		this._handleTrailingData();
	}

	this._cbs.onend();
};

Tokenizer.prototype._handleTrailingData = function(){
	var data = this._buffer.substr(this._sectionStart);

	if(this._state === IN_CDATA || this._state === AFTER_CDATA_1 || this._state === AFTER_CDATA_2){
		this._cbs.oncdata(data);
	} else if(this._state === IN_COMMENT || this._state === AFTER_COMMENT_1 || this._state === AFTER_COMMENT_2){
		this._cbs.oncomment(data);
	} else if(this._state === IN_NAMED_ENTITY && !this._xmlMode){
		this._parseLegacyEntity();
		if(this._sectionStart < this._index){
			this._state = this._baseState;
			this._handleTrailingData();
		}
	} else if(this._state === IN_NUMERIC_ENTITY && !this._xmlMode){
		this._decodeNumericEntity(2, 10);
		if(this._sectionStart < this._index){
			this._state = this._baseState;
			this._handleTrailingData();
		}
	} else if(this._state === IN_HEX_ENTITY && !this._xmlMode){
		this._decodeNumericEntity(3, 16);
		if(this._sectionStart < this._index){
			this._state = this._baseState;
			this._handleTrailingData();
		}
	} else if(
		this._state !== IN_TAG_NAME &&
		this._state !== BEFORE_ATTRIBUTE_NAME &&
		this._state !== BEFORE_ATTRIBUTE_VALUE &&
		this._state !== AFTER_ATTRIBUTE_NAME &&
		this._state !== IN_ATTRIBUTE_NAME &&
		this._state !== IN_ATTRIBUTE_VALUE_SQ &&
		this._state !== IN_ATTRIBUTE_VALUE_DQ &&
		this._state !== IN_ATTRIBUTE_VALUE_NQ &&
		this._state !== IN_CLOSING_TAG_NAME
	){
		this._cbs.ontext(data);
	}
	//else, ignore remaining data
	//TODO add a way to remove current tag
};

Tokenizer.prototype.reset = function(){
	Tokenizer.call(this, {xmlMode: this._xmlMode, decodeEntities: this._decodeEntities}, this._cbs);
};

Tokenizer.prototype.getAbsoluteIndex = function(){
	return this._bufferOffset + this._index;
};

Tokenizer.prototype._getSection = function(){
	return this._buffer.substring(this._sectionStart, this._index);
};

Tokenizer.prototype._emitToken = function(name){
	this._cbs[name](this._getSection());
	this._sectionStart = -1;
};

Tokenizer.prototype._emitPartial = function(value){
	if(this._baseState !== TEXT){
		this._cbs.onattribdata(value); //TODO implement the new event
	} else {
		this._cbs.ontext(value);
	}
};

},{"entities/lib/decode_codepoint.js":52,"entities/maps/entities.json":55,"entities/maps/legacy.json":56,"entities/maps/xml.json":57}],35:[function(require,module,exports){
module.exports = Stream;

var Parser = require("./Parser.js"),
    WritableStream = require("stream").Writable || require("readable-stream").Writable,
    StringDecoder = require("string_decoder").StringDecoder,
    Buffer = require("buffer").Buffer;

function Stream(cbs, options){
	var parser = this._parser = new Parser(cbs, options);
	var decoder = this._decoder = new StringDecoder();

	WritableStream.call(this, {decodeStrings: false});

	this.once("finish", function(){
		parser.end(decoder.end());
	});
}

require("inherits")(Stream, WritableStream);

WritableStream.prototype._write = function(chunk, encoding, cb){
	if(chunk instanceof Buffer) chunk = this._decoder.write(chunk);
	this._parser.write(chunk);
	cb();
};
},{"./Parser.js":31,"buffer":3,"inherits":58,"readable-stream":2,"stream":27,"string_decoder":28}],36:[function(require,module,exports){
var Parser = require("./Parser.js"),
    DomHandler = require("domhandler");

function defineProp(name, value){
	delete module.exports[name];
	module.exports[name] = value;
	return value;
}

module.exports = {
	Parser: Parser,
	Tokenizer: require("./Tokenizer.js"),
	ElementType: require("domelementtype"),
	DomHandler: DomHandler,
	get FeedHandler(){
		return defineProp("FeedHandler", require("./FeedHandler.js"));
	},
	get Stream(){
		return defineProp("Stream", require("./Stream.js"));
	},
	get WritableStream(){
		return defineProp("WritableStream", require("./WritableStream.js"));
	},
	get ProxyHandler(){
		return defineProp("ProxyHandler", require("./ProxyHandler.js"));
	},
	get DomUtils(){
		return defineProp("DomUtils", require("domutils"));
	},
	get CollectingHandler(){
		return defineProp("CollectingHandler", require("./CollectingHandler.js"));
	},
	// For legacy support
	DefaultHandler: DomHandler,
	get RssHandler(){
		return defineProp("RssHandler", this.FeedHandler);
	},
	//helper methods
	parseDOM: function(data, options){
		var handler = new DomHandler(options);
		new Parser(handler, options).end(data);
		return handler.dom;
	},
	parseFeed: function(feed, options){
		var handler = new module.exports.FeedHandler(options);
		new Parser(handler, options).end(feed);
		return handler.dom;
	},
	createDomStream: function(cb, options, elementCb){
		var handler = new DomHandler(cb, options, elementCb);
		return new Parser(handler, options);
	},
	// List of all events that the parser emits
	EVENTS: { /* Format: eventname: number of arguments */
		attribute: 2,
		cdatastart: 0,
		cdataend: 0,
		text: 1,
		processinginstruction: 2,
		comment: 1,
		commentend: 0,
		closetag: 1,
		opentag: 2,
		opentagname: 1,
		error: 1,
		end: 0
	}
};

},{"./CollectingHandler.js":29,"./FeedHandler.js":30,"./Parser.js":31,"./ProxyHandler.js":32,"./Stream.js":33,"./Tokenizer.js":34,"./WritableStream.js":35,"domelementtype":37,"domhandler":38,"domutils":41}],37:[function(require,module,exports){
//Types of elements found in the DOM
module.exports = {
	Text: "text", //Text
	Directive: "directive", //<? ... ?>
	Comment: "comment", //<!-- ... -->
	Script: "script", //<script> tags
	Style: "style", //<style> tags
	Tag: "tag", //Any tag
	CDATA: "cdata", //<![CDATA[ ... ]]>
	Doctype: "doctype",

	isTag: function(elem){
		return elem.type === "tag" || elem.type === "script" || elem.type === "style";
	}
};

},{}],38:[function(require,module,exports){
var ElementType = require("domelementtype");

var re_whitespace = /\s+/g;
var NodePrototype = require("./lib/node");
var ElementPrototype = require("./lib/element");

function DomHandler(callback, options, elementCB){
	if(typeof callback === "object"){
		elementCB = options;
		options = callback;
		callback = null;
	} else if(typeof options === "function"){
		elementCB = options;
		options = defaultOpts;
	}
	this._callback = callback;
	this._options = options || defaultOpts;
	this._elementCB = elementCB;
	this.dom = [];
	this._done = false;
	this._tagStack = [];
	this._parser = this._parser || null;
}

//default options
var defaultOpts = {
	normalizeWhitespace: false, //Replace all whitespace with single spaces
	withStartIndices: false, //Add startIndex properties to nodes
};

DomHandler.prototype.onparserinit = function(parser){
	this._parser = parser;
};

//Resets the handler back to starting state
DomHandler.prototype.onreset = function(){
	DomHandler.call(this, this._callback, this._options, this._elementCB);
};

//Signals the handler that parsing is done
DomHandler.prototype.onend = function(){
	if(this._done) return;
	this._done = true;
	this._parser = null;
	this._handleCallback(null);
};

DomHandler.prototype._handleCallback =
DomHandler.prototype.onerror = function(error){
	if(typeof this._callback === "function"){
		this._callback(error, this.dom);
	} else {
		if(error) throw error;
	}
};

DomHandler.prototype.onclosetag = function(){
	//if(this._tagStack.pop().name !== name) this._handleCallback(Error("Tagname didn't match!"));
	var elem = this._tagStack.pop();
	if(this._elementCB) this._elementCB(elem);
};

DomHandler.prototype._addDomElement = function(element){
	var parent = this._tagStack[this._tagStack.length - 1];
	var siblings = parent ? parent.children : this.dom;
	var previousSibling = siblings[siblings.length - 1];

	element.next = null;

	if(this._options.withStartIndices){
		element.startIndex = this._parser.startIndex;
	}

	if (this._options.withDomLvl1) {
		element.__proto__ = element.type === "tag" ? ElementPrototype : NodePrototype;
	}

	if(previousSibling){
		element.prev = previousSibling;
		previousSibling.next = element;
	} else {
		element.prev = null;
	}

	siblings.push(element);
	element.parent = parent || null;
};

DomHandler.prototype.onopentag = function(name, attribs){
	var element = {
		type: name === "script" ? ElementType.Script : name === "style" ? ElementType.Style : ElementType.Tag,
		name: name,
		attribs: attribs,
		children: []
	};

	this._addDomElement(element);

	this._tagStack.push(element);
};

DomHandler.prototype.ontext = function(data){
	//the ignoreWhitespace is officially dropped, but for now,
	//it's an alias for normalizeWhitespace
	var normalize = this._options.normalizeWhitespace || this._options.ignoreWhitespace;

	var lastTag;

	if(!this._tagStack.length && this.dom.length && (lastTag = this.dom[this.dom.length-1]).type === ElementType.Text){
		if(normalize){
			lastTag.data = (lastTag.data + data).replace(re_whitespace, " ");
		} else {
			lastTag.data += data;
		}
	} else {
		if(
			this._tagStack.length &&
			(lastTag = this._tagStack[this._tagStack.length - 1]) &&
			(lastTag = lastTag.children[lastTag.children.length - 1]) &&
			lastTag.type === ElementType.Text
		){
			if(normalize){
				lastTag.data = (lastTag.data + data).replace(re_whitespace, " ");
			} else {
				lastTag.data += data;
			}
		} else {
			if(normalize){
				data = data.replace(re_whitespace, " ");
			}

			this._addDomElement({
				data: data,
				type: ElementType.Text
			});
		}
	}
};

DomHandler.prototype.oncomment = function(data){
	var lastTag = this._tagStack[this._tagStack.length - 1];

	if(lastTag && lastTag.type === ElementType.Comment){
		lastTag.data += data;
		return;
	}

	var element = {
		data: data,
		type: ElementType.Comment
	};

	this._addDomElement(element);
	this._tagStack.push(element);
};

DomHandler.prototype.oncdatastart = function(){
	var element = {
		children: [{
			data: "",
			type: ElementType.Text
		}],
		type: ElementType.CDATA
	};

	this._addDomElement(element);
	this._tagStack.push(element);
};

DomHandler.prototype.oncommentend = DomHandler.prototype.oncdataend = function(){
	this._tagStack.pop();
};

DomHandler.prototype.onprocessinginstruction = function(name, data){
	this._addDomElement({
		name: name,
		data: data,
		type: ElementType.Directive
	});
};

module.exports = DomHandler;

},{"./lib/element":39,"./lib/node":40,"domelementtype":37}],39:[function(require,module,exports){
// DOM-Level-1-compliant structure
var NodePrototype = require('./node');
var ElementPrototype = module.exports = Object.create(NodePrototype);

var domLvl1 = {
	tagName: "name"
};

Object.keys(domLvl1).forEach(function(key) {
	var shorthand = domLvl1[key];
	Object.defineProperty(ElementPrototype, key, {
		get: function() {
			return this[shorthand] || null;
		},
		set: function(val) {
			this[shorthand] = val;
			return val;
		}
	});
});

},{"./node":40}],40:[function(require,module,exports){
// This object will be used as the prototype for Nodes when creating a
// DOM-Level-1-compliant structure.
var NodePrototype = module.exports = {
	get firstChild() {
		var children = this.children;
		return children && children[0] || null;
	},
	get lastChild() {
		var children = this.children;
		return children && children[children.length - 1] || null;
	},
	get nodeType() {
		return nodeTypes[this.type] || nodeTypes.element;
	}
};

var domLvl1 = {
	tagName: "name",
	childNodes: "children",
	parentNode: "parent",
	previousSibling: "prev",
	nextSibling: "next",
	nodeValue: "data"
};

var nodeTypes = {
	element: 1,
	text: 3,
	cdata: 4,
	comment: 8
};

Object.keys(domLvl1).forEach(function(key) {
	var shorthand = domLvl1[key];
	Object.defineProperty(NodePrototype, key, {
		get: function() {
			return this[shorthand] || null;
		},
		set: function(val) {
			this[shorthand] = val;
			return val;
		}
	});
});

},{}],41:[function(require,module,exports){
var DomUtils = module.exports;

[
	require("./lib/stringify"),
	require("./lib/traversal"),
	require("./lib/manipulation"),
	require("./lib/querying"),
	require("./lib/legacy"),
	require("./lib/helpers")
].forEach(function(ext){
	Object.keys(ext).forEach(function(key){
		DomUtils[key] = ext[key].bind(DomUtils);
	});
});

},{"./lib/helpers":42,"./lib/legacy":43,"./lib/manipulation":44,"./lib/querying":45,"./lib/stringify":46,"./lib/traversal":47}],42:[function(require,module,exports){
// removeSubsets
// Given an array of nodes, remove any member that is contained by another.
exports.removeSubsets = function(nodes) {
	var idx = nodes.length, node, ancestor, replace;

	// Check if each node (or one of its ancestors) is already contained in the
	// array.
	while (--idx > -1) {
		node = ancestor = nodes[idx];

		// Temporarily remove the node under consideration
		nodes[idx] = null;
		replace = true;

		while (ancestor) {
			if (nodes.indexOf(ancestor) > -1) {
				replace = false;
				nodes.splice(idx, 1);
				break;
			}
			ancestor = ancestor.parent;
		}

		// If the node has been found to be unique, re-insert it.
		if (replace) {
			nodes[idx] = node;
		}
	}

	return nodes;
};

// Source: http://dom.spec.whatwg.org/#dom-node-comparedocumentposition
var POSITION = {
	DISCONNECTED: 1,
	PRECEDING: 2,
	FOLLOWING: 4,
	CONTAINS: 8,
	CONTAINED_BY: 16
};

// Compare the position of one node against another node in any other document.
// The return value is a bitmask with the following values:
//
// document order:
// > There is an ordering, document order, defined on all the nodes in the
// > document corresponding to the order in which the first character of the
// > XML representation of each node occurs in the XML representation of the
// > document after expansion of general entities. Thus, the document element
// > node will be the first node. Element nodes occur before their children.
// > Thus, document order orders element nodes in order of the occurrence of
// > their start-tag in the XML (after expansion of entities). The attribute
// > nodes of an element occur after the element and before its children. The
// > relative order of attribute nodes is implementation-dependent./
// Source:
// http://www.w3.org/TR/DOM-Level-3-Core/glossary.html#dt-document-order
//
// @argument {Node} nodaA The first node to use in the comparison
// @argument {Node} nodeB The second node to use in the comparison
//
// @return {Number} A bitmask describing the input nodes' relative position.
//         See http://dom.spec.whatwg.org/#dom-node-comparedocumentposition for
//         a description of these values.
var comparePos = exports.compareDocumentPosition = function(nodeA, nodeB) {
	var aParents = [];
	var bParents = [];
	var current, sharedParent, siblings, aSibling, bSibling, idx;

	if (nodeA === nodeB) {
		return 0;
	}

	current = nodeA;
	while (current) {
		aParents.unshift(current);
		current = current.parent;
	}
	current = nodeB;
	while (current) {
		bParents.unshift(current);
		current = current.parent;
	}

	idx = 0;
	while (aParents[idx] === bParents[idx]) {
		idx++;
	}

	if (idx === 0) {
		return POSITION.DISCONNECTED;
	}

	sharedParent = aParents[idx - 1];
	siblings = sharedParent.children;
	aSibling = aParents[idx];
	bSibling = bParents[idx];

	if (siblings.indexOf(aSibling) > siblings.indexOf(bSibling)) {
		if (sharedParent === nodeB) {
			return POSITION.FOLLOWING | POSITION.CONTAINED_BY;
		}
		return POSITION.FOLLOWING;
	} else {
		if (sharedParent === nodeA) {
			return POSITION.PRECEDING | POSITION.CONTAINS;
		}
		return POSITION.PRECEDING;
	}
};

// Sort an array of nodes based on their relative position in the document and
// remove any duplicate nodes. If the array contains nodes that do not belong
// to the same document, sort order is unspecified.
//
// @argument {Array} nodes Array of DOM nodes
//
// @returns {Array} collection of unique nodes, sorted in document order
exports.uniqueSort = function(nodes) {
	var idx = nodes.length, node, position;

	nodes = nodes.slice();

	while (--idx > -1) {
		node = nodes[idx];
		position = nodes.indexOf(node);
		if (position > -1 && position < idx) {
			nodes.splice(idx, 1);
		}
	}
	nodes.sort(function(a, b) {
		var relative = comparePos(a, b);
		if (relative & POSITION.PRECEDING) {
			return -1;
		} else if (relative & POSITION.FOLLOWING) {
			return 1;
		}
		return 0;
	});

	return nodes;
};

},{}],43:[function(require,module,exports){
var ElementType = require("domelementtype");
var isTag = exports.isTag = ElementType.isTag;

exports.testElement = function(options, element){
	for(var key in options){
		if(!options.hasOwnProperty(key));
		else if(key === "tag_name"){
			if(!isTag(element) || !options.tag_name(element.name)){
				return false;
			}
		} else if(key === "tag_type"){
			if(!options.tag_type(element.type)) return false;
		} else if(key === "tag_contains"){
			if(isTag(element) || !options.tag_contains(element.data)){
				return false;
			}
		} else if(!element.attribs || !options[key](element.attribs[key])){
			return false;
		}
	}
	return true;
};

var Checks = {
	tag_name: function(name){
		if(typeof name === "function"){
			return function(elem){ return isTag(elem) && name(elem.name); };
		} else if(name === "*"){
			return isTag;
		} else {
			return function(elem){ return isTag(elem) && elem.name === name; };
		}
	},
	tag_type: function(type){
		if(typeof type === "function"){
			return function(elem){ return type(elem.type); };
		} else {
			return function(elem){ return elem.type === type; };
		}
	},
	tag_contains: function(data){
		if(typeof data === "function"){
			return function(elem){ return !isTag(elem) && data(elem.data); };
		} else {
			return function(elem){ return !isTag(elem) && elem.data === data; };
		}
	}
};

function getAttribCheck(attrib, value){
	if(typeof value === "function"){
		return function(elem){ return elem.attribs && value(elem.attribs[attrib]); };
	} else {
		return function(elem){ return elem.attribs && elem.attribs[attrib] === value; };
	}
}

function combineFuncs(a, b){
	return function(elem){
		return a(elem) || b(elem);
	};
}

exports.getElements = function(options, element, recurse, limit){
	var funcs = Object.keys(options).map(function(key){
		var value = options[key];
		return key in Checks ? Checks[key](value) : getAttribCheck(key, value);
	});

	return funcs.length === 0 ? [] : this.filter(
		funcs.reduce(combineFuncs),
		element, recurse, limit
	);
};

exports.getElementById = function(id, element, recurse){
	if(!Array.isArray(element)) element = [element];
	return this.findOne(getAttribCheck("id", id), element, recurse !== false);
};

exports.getElementsByTagName = function(name, element, recurse, limit){
	return this.filter(Checks.tag_name(name), element, recurse, limit);
};

exports.getElementsByTagType = function(type, element, recurse, limit){
	return this.filter(Checks.tag_type(type), element, recurse, limit);
};

},{"domelementtype":37}],44:[function(require,module,exports){
exports.removeElement = function(elem){
	if(elem.prev) elem.prev.next = elem.next;
	if(elem.next) elem.next.prev = elem.prev;

	if(elem.parent){
		var childs = elem.parent.children;
		childs.splice(childs.lastIndexOf(elem), 1);
	}
};

exports.replaceElement = function(elem, replacement){
	var prev = replacement.prev = elem.prev;
	if(prev){
		prev.next = replacement;
	}

	var next = replacement.next = elem.next;
	if(next){
		next.prev = replacement;
	}

	var parent = replacement.parent = elem.parent;
	if(parent){
		var childs = parent.children;
		childs[childs.lastIndexOf(elem)] = replacement;
	}
};

exports.appendChild = function(elem, child){
	child.parent = elem;

	if(elem.children.push(child) !== 1){
		var sibling = elem.children[elem.children.length - 2];
		sibling.next = child;
		child.prev = sibling;
		child.next = null;
	}
};

exports.append = function(elem, next){
	var parent = elem.parent,
		currNext = elem.next;

	next.next = currNext;
	next.prev = elem;
	elem.next = next;
	next.parent = parent;

	if(currNext){
		currNext.prev = next;
		if(parent){
			var childs = parent.children;
			childs.splice(childs.lastIndexOf(currNext), 0, next);
		}
	} else if(parent){
		parent.children.push(next);
	}
};

exports.prepend = function(elem, prev){
	var parent = elem.parent;
	if(parent){
		var childs = parent.children;
		childs.splice(childs.lastIndexOf(elem), 0, prev);
	}

	if(elem.prev){
		elem.prev.next = prev;
	}
	
	prev.parent = parent;
	prev.prev = elem.prev;
	prev.next = elem;
	elem.prev = prev;
};



},{}],45:[function(require,module,exports){
var isTag = require("domelementtype").isTag;

module.exports = {
	filter: filter,
	find: find,
	findOneChild: findOneChild,
	findOne: findOne,
	existsOne: existsOne,
	findAll: findAll
};

function filter(test, element, recurse, limit){
	if(!Array.isArray(element)) element = [element];

	if(typeof limit !== "number" || !isFinite(limit)){
		limit = Infinity;
	}
	return find(test, element, recurse !== false, limit);
}

function find(test, elems, recurse, limit){
	var result = [], childs;

	for(var i = 0, j = elems.length; i < j; i++){
		if(test(elems[i])){
			result.push(elems[i]);
			if(--limit <= 0) break;
		}

		childs = elems[i].children;
		if(recurse && childs && childs.length > 0){
			childs = find(test, childs, recurse, limit);
			result = result.concat(childs);
			limit -= childs.length;
			if(limit <= 0) break;
		}
	}

	return result;
}

function findOneChild(test, elems){
	for(var i = 0, l = elems.length; i < l; i++){
		if(test(elems[i])) return elems[i];
	}

	return null;
}

function findOne(test, elems){
	var elem = null;

	for(var i = 0, l = elems.length; i < l && !elem; i++){
		if(!isTag(elems[i])){
			continue;
		} else if(test(elems[i])){
			elem = elems[i];
		} else if(elems[i].children.length > 0){
			elem = findOne(test, elems[i].children);
		}
	}

	return elem;
}

function existsOne(test, elems){
	for(var i = 0, l = elems.length; i < l; i++){
		if(
			isTag(elems[i]) && (
				test(elems[i]) || (
					elems[i].children.length > 0 &&
					existsOne(test, elems[i].children)
				)
			)
		){
			return true;
		}
	}

	return false;
}

function findAll(test, elems){
	var result = [];
	for(var i = 0, j = elems.length; i < j; i++){
		if(!isTag(elems[i])) continue;
		if(test(elems[i])) result.push(elems[i]);

		if(elems[i].children.length > 0){
			result = result.concat(findAll(test, elems[i].children));
		}
	}
	return result;
}

},{"domelementtype":37}],46:[function(require,module,exports){
var ElementType = require("domelementtype"),
    getOuterHTML = require("dom-serializer"),
    isTag = ElementType.isTag;

module.exports = {
	getInnerHTML: getInnerHTML,
	getOuterHTML: getOuterHTML,
	getText: getText
};

function getInnerHTML(elem, opts){
	return elem.children ? elem.children.map(function(elem){
		return getOuterHTML(elem, opts);
	}).join("") : "";
}

function getText(elem){
	if(Array.isArray(elem)) return elem.map(getText).join("");
	if(isTag(elem) || elem.type === ElementType.CDATA) return getText(elem.children);
	if(elem.type === ElementType.Text) return elem.data;
	return "";
}

},{"dom-serializer":48,"domelementtype":37}],47:[function(require,module,exports){
var getChildren = exports.getChildren = function(elem){
	return elem.children;
};

var getParent = exports.getParent = function(elem){
	return elem.parent;
};

exports.getSiblings = function(elem){
	var parent = getParent(elem);
	return parent ? getChildren(parent) : [elem];
};

exports.getAttributeValue = function(elem, name){
	return elem.attribs && elem.attribs[name];
};

exports.hasAttrib = function(elem, name){
	return !!elem.attribs && hasOwnProperty.call(elem.attribs, name);
};

exports.getName = function(elem){
	return elem.name;
};

},{}],48:[function(require,module,exports){
/*
  Module dependencies
*/
var ElementType = require('domelementtype');
var entities = require('entities');

/*
  Boolean Attributes
*/
var booleanAttributes = {
  __proto__: null,
  allowfullscreen: true,
  async: true,
  autofocus: true,
  autoplay: true,
  checked: true,
  controls: true,
  default: true,
  defer: true,
  disabled: true,
  hidden: true,
  ismap: true,
  loop: true,
  multiple: true,
  muted: true,
  open: true,
  readonly: true,
  required: true,
  reversed: true,
  scoped: true,
  seamless: true,
  selected: true,
  typemustmatch: true
};

var unencodedElements = {
  __proto__: null,
  style: true,
  script: true,
  xmp: true,
  iframe: true,
  noembed: true,
  noframes: true,
  plaintext: true,
  noscript: true
};

/*
  Format attributes
*/
function formatAttrs(attributes, opts) {
  if (!attributes) return;

  var output = '',
      value;

  // Loop through the attributes
  for (var key in attributes) {
    value = attributes[key];
    if (output) {
      output += ' ';
    }

    if (!value && booleanAttributes[key]) {
      output += key;
    } else {
      output += key + '="' + (opts.decodeEntities ? entities.encodeXML(value) : value) + '"';
    }
  }

  return output;
}

/*
  Self-enclosing tags (stolen from node-htmlparser)
*/
var singleTag = {
  __proto__: null,
  area: true,
  base: true,
  basefont: true,
  br: true,
  col: true,
  command: true,
  embed: true,
  frame: true,
  hr: true,
  img: true,
  input: true,
  isindex: true,
  keygen: true,
  link: true,
  meta: true,
  param: true,
  source: true,
  track: true,
  wbr: true,
};


var render = module.exports = function(dom, opts) {
  if (!Array.isArray(dom) && !dom.cheerio) dom = [dom];
  opts = opts || {};

  var output = '';

  for(var i = 0; i < dom.length; i++){
    var elem = dom[i];

    if (elem.type === 'root')
      output += render(elem.children, opts);
    else if (ElementType.isTag(elem))
      output += renderTag(elem, opts);
    else if (elem.type === ElementType.Directive)
      output += renderDirective(elem);
    else if (elem.type === ElementType.Comment)
      output += renderComment(elem);
    else if (elem.type === ElementType.CDATA)
      output += renderCdata(elem);
    else
      output += renderText(elem, opts);
  }

  return output;
};

function renderTag(elem, opts) {
  // Handle SVG
  if (elem.name === "svg") opts = {decodeEntities: opts.decodeEntities, xmlMode: true};

  var tag = '<' + elem.name,
      attribs = formatAttrs(elem.attribs, opts);

  if (attribs) {
    tag += ' ' + attribs;
  }

  if (
    opts.xmlMode
    && (!elem.children || elem.children.length === 0)
  ) {
    tag += '/>';
  } else {
    tag += '>';
    if (elem.children) {
      tag += render(elem.children, opts);
    }

    if (!singleTag[elem.name] || opts.xmlMode) {
      tag += '</' + elem.name + '>';
    }
  }

  return tag;
}

function renderDirective(elem) {
  return '<' + elem.data + '>';
}

function renderText(elem, opts) {
  var data = elem.data || '';

  // if entities weren't decoded, no need to encode them back
  if (opts.decodeEntities && !(elem.parent && elem.parent.name in unencodedElements)) {
    data = entities.encodeXML(data);
  }

  return data;
}

function renderCdata(elem) {
  return '<![CDATA[' + elem.children[0].data + ']]>';
}

function renderComment(elem) {
  return '<!--' + elem.data + '-->';
}

},{"domelementtype":49,"entities":50}],49:[function(require,module,exports){
//Types of elements found in the DOM
module.exports = {
	Text: "text", //Text
	Directive: "directive", //<? ... ?>
	Comment: "comment", //<!-- ... -->
	Script: "script", //<script> tags
	Style: "style", //<style> tags
	Tag: "tag", //Any tag
	CDATA: "cdata", //<![CDATA[ ... ]]>

	isTag: function(elem){
		return elem.type === "tag" || elem.type === "script" || elem.type === "style";
	}
};
},{}],50:[function(require,module,exports){
var encode = require("./lib/encode.js"),
    decode = require("./lib/decode.js");

exports.decode = function(data, level){
	return (!level || level <= 0 ? decode.XML : decode.HTML)(data);
};

exports.decodeStrict = function(data, level){
	return (!level || level <= 0 ? decode.XML : decode.HTMLStrict)(data);
};

exports.encode = function(data, level){
	return (!level || level <= 0 ? encode.XML : encode.HTML)(data);
};

exports.encodeXML = encode.XML;

exports.encodeHTML4 =
exports.encodeHTML5 =
exports.encodeHTML  = encode.HTML;

exports.decodeXML =
exports.decodeXMLStrict = decode.XML;

exports.decodeHTML4 =
exports.decodeHTML5 =
exports.decodeHTML = decode.HTML;

exports.decodeHTML4Strict =
exports.decodeHTML5Strict =
exports.decodeHTMLStrict = decode.HTMLStrict;

exports.escape = encode.escape;

},{"./lib/decode.js":51,"./lib/encode.js":53}],51:[function(require,module,exports){
var entityMap = require("../maps/entities.json"),
    legacyMap = require("../maps/legacy.json"),
    xmlMap    = require("../maps/xml.json"),
    decodeCodePoint = require("./decode_codepoint.js");

var decodeXMLStrict  = getStrictDecoder(xmlMap),
    decodeHTMLStrict = getStrictDecoder(entityMap);

function getStrictDecoder(map){
	var keys = Object.keys(map).join("|"),
	    replace = getReplacer(map);

	keys += "|#[xX][\\da-fA-F]+|#\\d+";

	var re = new RegExp("&(?:" + keys + ");", "g");

	return function(str){
		return String(str).replace(re, replace);
	};
}

var decodeHTML = (function(){
	var legacy = Object.keys(legacyMap)
		.sort(sorter);

	var keys = Object.keys(entityMap)
		.sort(sorter);

	for(var i = 0, j = 0; i < keys.length; i++){
		if(legacy[j] === keys[i]){
			keys[i] += ";?";
			j++;
		} else {
			keys[i] += ";";
		}
	}

	var re = new RegExp("&(?:" + keys.join("|") + "|#[xX][\\da-fA-F]+;?|#\\d+;?)", "g"),
	    replace = getReplacer(entityMap);

	function replacer(str){
		if(str.substr(-1) !== ";") str += ";";
		return replace(str);
	}

	//TODO consider creating a merged map
	return function(str){
		return String(str).replace(re, replacer);
	};
}());

function sorter(a, b){
	return a < b ? 1 : -1;
}

function getReplacer(map){
	return function replace(str){
		if(str.charAt(1) === "#"){
			if(str.charAt(2) === "X" || str.charAt(2) === "x"){
				return decodeCodePoint(parseInt(str.substr(3), 16));
			}
			return decodeCodePoint(parseInt(str.substr(2), 10));
		}
		return map[str.slice(1, -1)];
	};
}

module.exports = {
	XML: decodeXMLStrict,
	HTML: decodeHTML,
	HTMLStrict: decodeHTMLStrict
};
},{"../maps/entities.json":55,"../maps/legacy.json":56,"../maps/xml.json":57,"./decode_codepoint.js":52}],52:[function(require,module,exports){
var decodeMap = require("../maps/decode.json");

module.exports = decodeCodePoint;

// modified version of https://github.com/mathiasbynens/he/blob/master/src/he.js#L94-L119
function decodeCodePoint(codePoint){

	if((codePoint >= 0xD800 && codePoint <= 0xDFFF) || codePoint > 0x10FFFF){
		return "\uFFFD";
	}

	if(codePoint in decodeMap){
		codePoint = decodeMap[codePoint];
	}

	var output = "";

	if(codePoint > 0xFFFF){
		codePoint -= 0x10000;
		output += String.fromCharCode(codePoint >>> 10 & 0x3FF | 0xD800);
		codePoint = 0xDC00 | codePoint & 0x3FF;
	}

	output += String.fromCharCode(codePoint);
	return output;
}

},{"../maps/decode.json":54}],53:[function(require,module,exports){
var inverseXML = getInverseObj(require("../maps/xml.json")),
    xmlReplacer = getInverseReplacer(inverseXML);

exports.XML = getInverse(inverseXML, xmlReplacer);

var inverseHTML = getInverseObj(require("../maps/entities.json")),
    htmlReplacer = getInverseReplacer(inverseHTML);

exports.HTML = getInverse(inverseHTML, htmlReplacer);

function getInverseObj(obj){
	return Object.keys(obj).sort().reduce(function(inverse, name){
		inverse[obj[name]] = "&" + name + ";";
		return inverse;
	}, {});
}

function getInverseReplacer(inverse){
	var single = [],
	    multiple = [];

	Object.keys(inverse).forEach(function(k){
		if(k.length === 1){
			single.push("\\" + k);
		} else {
			multiple.push(k);
		}
	});

	//TODO add ranges
	multiple.unshift("[" + single.join("") + "]");

	return new RegExp(multiple.join("|"), "g");
}

var re_nonASCII = /[^\0-\x7F]/g,
    re_astralSymbols = /[\uD800-\uDBFF][\uDC00-\uDFFF]/g;

function singleCharReplacer(c){
	return "&#x" + c.charCodeAt(0).toString(16).toUpperCase() + ";";
}

function astralReplacer(c){
	// http://mathiasbynens.be/notes/javascript-encoding#surrogate-formulae
	var high = c.charCodeAt(0);
	var low  = c.charCodeAt(1);
	var codePoint = (high - 0xD800) * 0x400 + low - 0xDC00 + 0x10000;
	return "&#x" + codePoint.toString(16).toUpperCase() + ";";
}

function getInverse(inverse, re){
	function func(name){
		return inverse[name];
	}

	return function(data){
		return data
				.replace(re, func)
				.replace(re_astralSymbols, astralReplacer)
				.replace(re_nonASCII, singleCharReplacer);
	};
}

var re_xmlChars = getInverseReplacer(inverseXML);

function escapeXML(data){
	return data
			.replace(re_xmlChars, singleCharReplacer)
			.replace(re_astralSymbols, astralReplacer)
			.replace(re_nonASCII, singleCharReplacer);
}

exports.escape = escapeXML;

},{"../maps/entities.json":55,"../maps/xml.json":57}],54:[function(require,module,exports){
module.exports={"0":65533,"128":8364,"130":8218,"131":402,"132":8222,"133":8230,"134":8224,"135":8225,"136":710,"137":8240,"138":352,"139":8249,"140":338,"142":381,"145":8216,"146":8217,"147":8220,"148":8221,"149":8226,"150":8211,"151":8212,"152":732,"153":8482,"154":353,"155":8250,"156":339,"158":382,"159":376}
},{}],55:[function(require,module,exports){
module.exports={"Aacute":"\u00C1","aacute":"\u00E1","Abreve":"\u0102","abreve":"\u0103","ac":"\u223E","acd":"\u223F","acE":"\u223E\u0333","Acirc":"\u00C2","acirc":"\u00E2","acute":"\u00B4","Acy":"\u0410","acy":"\u0430","AElig":"\u00C6","aelig":"\u00E6","af":"\u2061","Afr":"\uD835\uDD04","afr":"\uD835\uDD1E","Agrave":"\u00C0","agrave":"\u00E0","alefsym":"\u2135","aleph":"\u2135","Alpha":"\u0391","alpha":"\u03B1","Amacr":"\u0100","amacr":"\u0101","amalg":"\u2A3F","amp":"&","AMP":"&","andand":"\u2A55","And":"\u2A53","and":"\u2227","andd":"\u2A5C","andslope":"\u2A58","andv":"\u2A5A","ang":"\u2220","ange":"\u29A4","angle":"\u2220","angmsdaa":"\u29A8","angmsdab":"\u29A9","angmsdac":"\u29AA","angmsdad":"\u29AB","angmsdae":"\u29AC","angmsdaf":"\u29AD","angmsdag":"\u29AE","angmsdah":"\u29AF","angmsd":"\u2221","angrt":"\u221F","angrtvb":"\u22BE","angrtvbd":"\u299D","angsph":"\u2222","angst":"\u00C5","angzarr":"\u237C","Aogon":"\u0104","aogon":"\u0105","Aopf":"\uD835\uDD38","aopf":"\uD835\uDD52","apacir":"\u2A6F","ap":"\u2248","apE":"\u2A70","ape":"\u224A","apid":"\u224B","apos":"'","ApplyFunction":"\u2061","approx":"\u2248","approxeq":"\u224A","Aring":"\u00C5","aring":"\u00E5","Ascr":"\uD835\uDC9C","ascr":"\uD835\uDCB6","Assign":"\u2254","ast":"*","asymp":"\u2248","asympeq":"\u224D","Atilde":"\u00C3","atilde":"\u00E3","Auml":"\u00C4","auml":"\u00E4","awconint":"\u2233","awint":"\u2A11","backcong":"\u224C","backepsilon":"\u03F6","backprime":"\u2035","backsim":"\u223D","backsimeq":"\u22CD","Backslash":"\u2216","Barv":"\u2AE7","barvee":"\u22BD","barwed":"\u2305","Barwed":"\u2306","barwedge":"\u2305","bbrk":"\u23B5","bbrktbrk":"\u23B6","bcong":"\u224C","Bcy":"\u0411","bcy":"\u0431","bdquo":"\u201E","becaus":"\u2235","because":"\u2235","Because":"\u2235","bemptyv":"\u29B0","bepsi":"\u03F6","bernou":"\u212C","Bernoullis":"\u212C","Beta":"\u0392","beta":"\u03B2","beth":"\u2136","between":"\u226C","Bfr":"\uD835\uDD05","bfr":"\uD835\uDD1F","bigcap":"\u22C2","bigcirc":"\u25EF","bigcup":"\u22C3","bigodot":"\u2A00","bigoplus":"\u2A01","bigotimes":"\u2A02","bigsqcup":"\u2A06","bigstar":"\u2605","bigtriangledown":"\u25BD","bigtriangleup":"\u25B3","biguplus":"\u2A04","bigvee":"\u22C1","bigwedge":"\u22C0","bkarow":"\u290D","blacklozenge":"\u29EB","blacksquare":"\u25AA","blacktriangle":"\u25B4","blacktriangledown":"\u25BE","blacktriangleleft":"\u25C2","blacktriangleright":"\u25B8","blank":"\u2423","blk12":"\u2592","blk14":"\u2591","blk34":"\u2593","block":"\u2588","bne":"=\u20E5","bnequiv":"\u2261\u20E5","bNot":"\u2AED","bnot":"\u2310","Bopf":"\uD835\uDD39","bopf":"\uD835\uDD53","bot":"\u22A5","bottom":"\u22A5","bowtie":"\u22C8","boxbox":"\u29C9","boxdl":"\u2510","boxdL":"\u2555","boxDl":"\u2556","boxDL":"\u2557","boxdr":"\u250C","boxdR":"\u2552","boxDr":"\u2553","boxDR":"\u2554","boxh":"\u2500","boxH":"\u2550","boxhd":"\u252C","boxHd":"\u2564","boxhD":"\u2565","boxHD":"\u2566","boxhu":"\u2534","boxHu":"\u2567","boxhU":"\u2568","boxHU":"\u2569","boxminus":"\u229F","boxplus":"\u229E","boxtimes":"\u22A0","boxul":"\u2518","boxuL":"\u255B","boxUl":"\u255C","boxUL":"\u255D","boxur":"\u2514","boxuR":"\u2558","boxUr":"\u2559","boxUR":"\u255A","boxv":"\u2502","boxV":"\u2551","boxvh":"\u253C","boxvH":"\u256A","boxVh":"\u256B","boxVH":"\u256C","boxvl":"\u2524","boxvL":"\u2561","boxVl":"\u2562","boxVL":"\u2563","boxvr":"\u251C","boxvR":"\u255E","boxVr":"\u255F","boxVR":"\u2560","bprime":"\u2035","breve":"\u02D8","Breve":"\u02D8","brvbar":"\u00A6","bscr":"\uD835\uDCB7","Bscr":"\u212C","bsemi":"\u204F","bsim":"\u223D","bsime":"\u22CD","bsolb":"\u29C5","bsol":"\\","bsolhsub":"\u27C8","bull":"\u2022","bullet":"\u2022","bump":"\u224E","bumpE":"\u2AAE","bumpe":"\u224F","Bumpeq":"\u224E","bumpeq":"\u224F","Cacute":"\u0106","cacute":"\u0107","capand":"\u2A44","capbrcup":"\u2A49","capcap":"\u2A4B","cap":"\u2229","Cap":"\u22D2","capcup":"\u2A47","capdot":"\u2A40","CapitalDifferentialD":"\u2145","caps":"\u2229\uFE00","caret":"\u2041","caron":"\u02C7","Cayleys":"\u212D","ccaps":"\u2A4D","Ccaron":"\u010C","ccaron":"\u010D","Ccedil":"\u00C7","ccedil":"\u00E7","Ccirc":"\u0108","ccirc":"\u0109","Cconint":"\u2230","ccups":"\u2A4C","ccupssm":"\u2A50","Cdot":"\u010A","cdot":"\u010B","cedil":"\u00B8","Cedilla":"\u00B8","cemptyv":"\u29B2","cent":"\u00A2","centerdot":"\u00B7","CenterDot":"\u00B7","cfr":"\uD835\uDD20","Cfr":"\u212D","CHcy":"\u0427","chcy":"\u0447","check":"\u2713","checkmark":"\u2713","Chi":"\u03A7","chi":"\u03C7","circ":"\u02C6","circeq":"\u2257","circlearrowleft":"\u21BA","circlearrowright":"\u21BB","circledast":"\u229B","circledcirc":"\u229A","circleddash":"\u229D","CircleDot":"\u2299","circledR":"\u00AE","circledS":"\u24C8","CircleMinus":"\u2296","CirclePlus":"\u2295","CircleTimes":"\u2297","cir":"\u25CB","cirE":"\u29C3","cire":"\u2257","cirfnint":"\u2A10","cirmid":"\u2AEF","cirscir":"\u29C2","ClockwiseContourIntegral":"\u2232","CloseCurlyDoubleQuote":"\u201D","CloseCurlyQuote":"\u2019","clubs":"\u2663","clubsuit":"\u2663","colon":":","Colon":"\u2237","Colone":"\u2A74","colone":"\u2254","coloneq":"\u2254","comma":",","commat":"@","comp":"\u2201","compfn":"\u2218","complement":"\u2201","complexes":"\u2102","cong":"\u2245","congdot":"\u2A6D","Congruent":"\u2261","conint":"\u222E","Conint":"\u222F","ContourIntegral":"\u222E","copf":"\uD835\uDD54","Copf":"\u2102","coprod":"\u2210","Coproduct":"\u2210","copy":"\u00A9","COPY":"\u00A9","copysr":"\u2117","CounterClockwiseContourIntegral":"\u2233","crarr":"\u21B5","cross":"\u2717","Cross":"\u2A2F","Cscr":"\uD835\uDC9E","cscr":"\uD835\uDCB8","csub":"\u2ACF","csube":"\u2AD1","csup":"\u2AD0","csupe":"\u2AD2","ctdot":"\u22EF","cudarrl":"\u2938","cudarrr":"\u2935","cuepr":"\u22DE","cuesc":"\u22DF","cularr":"\u21B6","cularrp":"\u293D","cupbrcap":"\u2A48","cupcap":"\u2A46","CupCap":"\u224D","cup":"\u222A","Cup":"\u22D3","cupcup":"\u2A4A","cupdot":"\u228D","cupor":"\u2A45","cups":"\u222A\uFE00","curarr":"\u21B7","curarrm":"\u293C","curlyeqprec":"\u22DE","curlyeqsucc":"\u22DF","curlyvee":"\u22CE","curlywedge":"\u22CF","curren":"\u00A4","curvearrowleft":"\u21B6","curvearrowright":"\u21B7","cuvee":"\u22CE","cuwed":"\u22CF","cwconint":"\u2232","cwint":"\u2231","cylcty":"\u232D","dagger":"\u2020","Dagger":"\u2021","daleth":"\u2138","darr":"\u2193","Darr":"\u21A1","dArr":"\u21D3","dash":"\u2010","Dashv":"\u2AE4","dashv":"\u22A3","dbkarow":"\u290F","dblac":"\u02DD","Dcaron":"\u010E","dcaron":"\u010F","Dcy":"\u0414","dcy":"\u0434","ddagger":"\u2021","ddarr":"\u21CA","DD":"\u2145","dd":"\u2146","DDotrahd":"\u2911","ddotseq":"\u2A77","deg":"\u00B0","Del":"\u2207","Delta":"\u0394","delta":"\u03B4","demptyv":"\u29B1","dfisht":"\u297F","Dfr":"\uD835\uDD07","dfr":"\uD835\uDD21","dHar":"\u2965","dharl":"\u21C3","dharr":"\u21C2","DiacriticalAcute":"\u00B4","DiacriticalDot":"\u02D9","DiacriticalDoubleAcute":"\u02DD","DiacriticalGrave":"`","DiacriticalTilde":"\u02DC","diam":"\u22C4","diamond":"\u22C4","Diamond":"\u22C4","diamondsuit":"\u2666","diams":"\u2666","die":"\u00A8","DifferentialD":"\u2146","digamma":"\u03DD","disin":"\u22F2","div":"\u00F7","divide":"\u00F7","divideontimes":"\u22C7","divonx":"\u22C7","DJcy":"\u0402","djcy":"\u0452","dlcorn":"\u231E","dlcrop":"\u230D","dollar":"$","Dopf":"\uD835\uDD3B","dopf":"\uD835\uDD55","Dot":"\u00A8","dot":"\u02D9","DotDot":"\u20DC","doteq":"\u2250","doteqdot":"\u2251","DotEqual":"\u2250","dotminus":"\u2238","dotplus":"\u2214","dotsquare":"\u22A1","doublebarwedge":"\u2306","DoubleContourIntegral":"\u222F","DoubleDot":"\u00A8","DoubleDownArrow":"\u21D3","DoubleLeftArrow":"\u21D0","DoubleLeftRightArrow":"\u21D4","DoubleLeftTee":"\u2AE4","DoubleLongLeftArrow":"\u27F8","DoubleLongLeftRightArrow":"\u27FA","DoubleLongRightArrow":"\u27F9","DoubleRightArrow":"\u21D2","DoubleRightTee":"\u22A8","DoubleUpArrow":"\u21D1","DoubleUpDownArrow":"\u21D5","DoubleVerticalBar":"\u2225","DownArrowBar":"\u2913","downarrow":"\u2193","DownArrow":"\u2193","Downarrow":"\u21D3","DownArrowUpArrow":"\u21F5","DownBreve":"\u0311","downdownarrows":"\u21CA","downharpoonleft":"\u21C3","downharpoonright":"\u21C2","DownLeftRightVector":"\u2950","DownLeftTeeVector":"\u295E","DownLeftVectorBar":"\u2956","DownLeftVector":"\u21BD","DownRightTeeVector":"\u295F","DownRightVectorBar":"\u2957","DownRightVector":"\u21C1","DownTeeArrow":"\u21A7","DownTee":"\u22A4","drbkarow":"\u2910","drcorn":"\u231F","drcrop":"\u230C","Dscr":"\uD835\uDC9F","dscr":"\uD835\uDCB9","DScy":"\u0405","dscy":"\u0455","dsol":"\u29F6","Dstrok":"\u0110","dstrok":"\u0111","dtdot":"\u22F1","dtri":"\u25BF","dtrif":"\u25BE","duarr":"\u21F5","duhar":"\u296F","dwangle":"\u29A6","DZcy":"\u040F","dzcy":"\u045F","dzigrarr":"\u27FF","Eacute":"\u00C9","eacute":"\u00E9","easter":"\u2A6E","Ecaron":"\u011A","ecaron":"\u011B","Ecirc":"\u00CA","ecirc":"\u00EA","ecir":"\u2256","ecolon":"\u2255","Ecy":"\u042D","ecy":"\u044D","eDDot":"\u2A77","Edot":"\u0116","edot":"\u0117","eDot":"\u2251","ee":"\u2147","efDot":"\u2252","Efr":"\uD835\uDD08","efr":"\uD835\uDD22","eg":"\u2A9A","Egrave":"\u00C8","egrave":"\u00E8","egs":"\u2A96","egsdot":"\u2A98","el":"\u2A99","Element":"\u2208","elinters":"\u23E7","ell":"\u2113","els":"\u2A95","elsdot":"\u2A97","Emacr":"\u0112","emacr":"\u0113","empty":"\u2205","emptyset":"\u2205","EmptySmallSquare":"\u25FB","emptyv":"\u2205","EmptyVerySmallSquare":"\u25AB","emsp13":"\u2004","emsp14":"\u2005","emsp":"\u2003","ENG":"\u014A","eng":"\u014B","ensp":"\u2002","Eogon":"\u0118","eogon":"\u0119","Eopf":"\uD835\uDD3C","eopf":"\uD835\uDD56","epar":"\u22D5","eparsl":"\u29E3","eplus":"\u2A71","epsi":"\u03B5","Epsilon":"\u0395","epsilon":"\u03B5","epsiv":"\u03F5","eqcirc":"\u2256","eqcolon":"\u2255","eqsim":"\u2242","eqslantgtr":"\u2A96","eqslantless":"\u2A95","Equal":"\u2A75","equals":"=","EqualTilde":"\u2242","equest":"\u225F","Equilibrium":"\u21CC","equiv":"\u2261","equivDD":"\u2A78","eqvparsl":"\u29E5","erarr":"\u2971","erDot":"\u2253","escr":"\u212F","Escr":"\u2130","esdot":"\u2250","Esim":"\u2A73","esim":"\u2242","Eta":"\u0397","eta":"\u03B7","ETH":"\u00D0","eth":"\u00F0","Euml":"\u00CB","euml":"\u00EB","euro":"\u20AC","excl":"!","exist":"\u2203","Exists":"\u2203","expectation":"\u2130","exponentiale":"\u2147","ExponentialE":"\u2147","fallingdotseq":"\u2252","Fcy":"\u0424","fcy":"\u0444","female":"\u2640","ffilig":"\uFB03","fflig":"\uFB00","ffllig":"\uFB04","Ffr":"\uD835\uDD09","ffr":"\uD835\uDD23","filig":"\uFB01","FilledSmallSquare":"\u25FC","FilledVerySmallSquare":"\u25AA","fjlig":"fj","flat":"\u266D","fllig":"\uFB02","fltns":"\u25B1","fnof":"\u0192","Fopf":"\uD835\uDD3D","fopf":"\uD835\uDD57","forall":"\u2200","ForAll":"\u2200","fork":"\u22D4","forkv":"\u2AD9","Fouriertrf":"\u2131","fpartint":"\u2A0D","frac12":"\u00BD","frac13":"\u2153","frac14":"\u00BC","frac15":"\u2155","frac16":"\u2159","frac18":"\u215B","frac23":"\u2154","frac25":"\u2156","frac34":"\u00BE","frac35":"\u2157","frac38":"\u215C","frac45":"\u2158","frac56":"\u215A","frac58":"\u215D","frac78":"\u215E","frasl":"\u2044","frown":"\u2322","fscr":"\uD835\uDCBB","Fscr":"\u2131","gacute":"\u01F5","Gamma":"\u0393","gamma":"\u03B3","Gammad":"\u03DC","gammad":"\u03DD","gap":"\u2A86","Gbreve":"\u011E","gbreve":"\u011F","Gcedil":"\u0122","Gcirc":"\u011C","gcirc":"\u011D","Gcy":"\u0413","gcy":"\u0433","Gdot":"\u0120","gdot":"\u0121","ge":"\u2265","gE":"\u2267","gEl":"\u2A8C","gel":"\u22DB","geq":"\u2265","geqq":"\u2267","geqslant":"\u2A7E","gescc":"\u2AA9","ges":"\u2A7E","gesdot":"\u2A80","gesdoto":"\u2A82","gesdotol":"\u2A84","gesl":"\u22DB\uFE00","gesles":"\u2A94","Gfr":"\uD835\uDD0A","gfr":"\uD835\uDD24","gg":"\u226B","Gg":"\u22D9","ggg":"\u22D9","gimel":"\u2137","GJcy":"\u0403","gjcy":"\u0453","gla":"\u2AA5","gl":"\u2277","glE":"\u2A92","glj":"\u2AA4","gnap":"\u2A8A","gnapprox":"\u2A8A","gne":"\u2A88","gnE":"\u2269","gneq":"\u2A88","gneqq":"\u2269","gnsim":"\u22E7","Gopf":"\uD835\uDD3E","gopf":"\uD835\uDD58","grave":"`","GreaterEqual":"\u2265","GreaterEqualLess":"\u22DB","GreaterFullEqual":"\u2267","GreaterGreater":"\u2AA2","GreaterLess":"\u2277","GreaterSlantEqual":"\u2A7E","GreaterTilde":"\u2273","Gscr":"\uD835\uDCA2","gscr":"\u210A","gsim":"\u2273","gsime":"\u2A8E","gsiml":"\u2A90","gtcc":"\u2AA7","gtcir":"\u2A7A","gt":">","GT":">","Gt":"\u226B","gtdot":"\u22D7","gtlPar":"\u2995","gtquest":"\u2A7C","gtrapprox":"\u2A86","gtrarr":"\u2978","gtrdot":"\u22D7","gtreqless":"\u22DB","gtreqqless":"\u2A8C","gtrless":"\u2277","gtrsim":"\u2273","gvertneqq":"\u2269\uFE00","gvnE":"\u2269\uFE00","Hacek":"\u02C7","hairsp":"\u200A","half":"\u00BD","hamilt":"\u210B","HARDcy":"\u042A","hardcy":"\u044A","harrcir":"\u2948","harr":"\u2194","hArr":"\u21D4","harrw":"\u21AD","Hat":"^","hbar":"\u210F","Hcirc":"\u0124","hcirc":"\u0125","hearts":"\u2665","heartsuit":"\u2665","hellip":"\u2026","hercon":"\u22B9","hfr":"\uD835\uDD25","Hfr":"\u210C","HilbertSpace":"\u210B","hksearow":"\u2925","hkswarow":"\u2926","hoarr":"\u21FF","homtht":"\u223B","hookleftarrow":"\u21A9","hookrightarrow":"\u21AA","hopf":"\uD835\uDD59","Hopf":"\u210D","horbar":"\u2015","HorizontalLine":"\u2500","hscr":"\uD835\uDCBD","Hscr":"\u210B","hslash":"\u210F","Hstrok":"\u0126","hstrok":"\u0127","HumpDownHump":"\u224E","HumpEqual":"\u224F","hybull":"\u2043","hyphen":"\u2010","Iacute":"\u00CD","iacute":"\u00ED","ic":"\u2063","Icirc":"\u00CE","icirc":"\u00EE","Icy":"\u0418","icy":"\u0438","Idot":"\u0130","IEcy":"\u0415","iecy":"\u0435","iexcl":"\u00A1","iff":"\u21D4","ifr":"\uD835\uDD26","Ifr":"\u2111","Igrave":"\u00CC","igrave":"\u00EC","ii":"\u2148","iiiint":"\u2A0C","iiint":"\u222D","iinfin":"\u29DC","iiota":"\u2129","IJlig":"\u0132","ijlig":"\u0133","Imacr":"\u012A","imacr":"\u012B","image":"\u2111","ImaginaryI":"\u2148","imagline":"\u2110","imagpart":"\u2111","imath":"\u0131","Im":"\u2111","imof":"\u22B7","imped":"\u01B5","Implies":"\u21D2","incare":"\u2105","in":"\u2208","infin":"\u221E","infintie":"\u29DD","inodot":"\u0131","intcal":"\u22BA","int":"\u222B","Int":"\u222C","integers":"\u2124","Integral":"\u222B","intercal":"\u22BA","Intersection":"\u22C2","intlarhk":"\u2A17","intprod":"\u2A3C","InvisibleComma":"\u2063","InvisibleTimes":"\u2062","IOcy":"\u0401","iocy":"\u0451","Iogon":"\u012E","iogon":"\u012F","Iopf":"\uD835\uDD40","iopf":"\uD835\uDD5A","Iota":"\u0399","iota":"\u03B9","iprod":"\u2A3C","iquest":"\u00BF","iscr":"\uD835\uDCBE","Iscr":"\u2110","isin":"\u2208","isindot":"\u22F5","isinE":"\u22F9","isins":"\u22F4","isinsv":"\u22F3","isinv":"\u2208","it":"\u2062","Itilde":"\u0128","itilde":"\u0129","Iukcy":"\u0406","iukcy":"\u0456","Iuml":"\u00CF","iuml":"\u00EF","Jcirc":"\u0134","jcirc":"\u0135","Jcy":"\u0419","jcy":"\u0439","Jfr":"\uD835\uDD0D","jfr":"\uD835\uDD27","jmath":"\u0237","Jopf":"\uD835\uDD41","jopf":"\uD835\uDD5B","Jscr":"\uD835\uDCA5","jscr":"\uD835\uDCBF","Jsercy":"\u0408","jsercy":"\u0458","Jukcy":"\u0404","jukcy":"\u0454","Kappa":"\u039A","kappa":"\u03BA","kappav":"\u03F0","Kcedil":"\u0136","kcedil":"\u0137","Kcy":"\u041A","kcy":"\u043A","Kfr":"\uD835\uDD0E","kfr":"\uD835\uDD28","kgreen":"\u0138","KHcy":"\u0425","khcy":"\u0445","KJcy":"\u040C","kjcy":"\u045C","Kopf":"\uD835\uDD42","kopf":"\uD835\uDD5C","Kscr":"\uD835\uDCA6","kscr":"\uD835\uDCC0","lAarr":"\u21DA","Lacute":"\u0139","lacute":"\u013A","laemptyv":"\u29B4","lagran":"\u2112","Lambda":"\u039B","lambda":"\u03BB","lang":"\u27E8","Lang":"\u27EA","langd":"\u2991","langle":"\u27E8","lap":"\u2A85","Laplacetrf":"\u2112","laquo":"\u00AB","larrb":"\u21E4","larrbfs":"\u291F","larr":"\u2190","Larr":"\u219E","lArr":"\u21D0","larrfs":"\u291D","larrhk":"\u21A9","larrlp":"\u21AB","larrpl":"\u2939","larrsim":"\u2973","larrtl":"\u21A2","latail":"\u2919","lAtail":"\u291B","lat":"\u2AAB","late":"\u2AAD","lates":"\u2AAD\uFE00","lbarr":"\u290C","lBarr":"\u290E","lbbrk":"\u2772","lbrace":"{","lbrack":"[","lbrke":"\u298B","lbrksld":"\u298F","lbrkslu":"\u298D","Lcaron":"\u013D","lcaron":"\u013E","Lcedil":"\u013B","lcedil":"\u013C","lceil":"\u2308","lcub":"{","Lcy":"\u041B","lcy":"\u043B","ldca":"\u2936","ldquo":"\u201C","ldquor":"\u201E","ldrdhar":"\u2967","ldrushar":"\u294B","ldsh":"\u21B2","le":"\u2264","lE":"\u2266","LeftAngleBracket":"\u27E8","LeftArrowBar":"\u21E4","leftarrow":"\u2190","LeftArrow":"\u2190","Leftarrow":"\u21D0","LeftArrowRightArrow":"\u21C6","leftarrowtail":"\u21A2","LeftCeiling":"\u2308","LeftDoubleBracket":"\u27E6","LeftDownTeeVector":"\u2961","LeftDownVectorBar":"\u2959","LeftDownVector":"\u21C3","LeftFloor":"\u230A","leftharpoondown":"\u21BD","leftharpoonup":"\u21BC","leftleftarrows":"\u21C7","leftrightarrow":"\u2194","LeftRightArrow":"\u2194","Leftrightarrow":"\u21D4","leftrightarrows":"\u21C6","leftrightharpoons":"\u21CB","leftrightsquigarrow":"\u21AD","LeftRightVector":"\u294E","LeftTeeArrow":"\u21A4","LeftTee":"\u22A3","LeftTeeVector":"\u295A","leftthreetimes":"\u22CB","LeftTriangleBar":"\u29CF","LeftTriangle":"\u22B2","LeftTriangleEqual":"\u22B4","LeftUpDownVector":"\u2951","LeftUpTeeVector":"\u2960","LeftUpVectorBar":"\u2958","LeftUpVector":"\u21BF","LeftVectorBar":"\u2952","LeftVector":"\u21BC","lEg":"\u2A8B","leg":"\u22DA","leq":"\u2264","leqq":"\u2266","leqslant":"\u2A7D","lescc":"\u2AA8","les":"\u2A7D","lesdot":"\u2A7F","lesdoto":"\u2A81","lesdotor":"\u2A83","lesg":"\u22DA\uFE00","lesges":"\u2A93","lessapprox":"\u2A85","lessdot":"\u22D6","lesseqgtr":"\u22DA","lesseqqgtr":"\u2A8B","LessEqualGreater":"\u22DA","LessFullEqual":"\u2266","LessGreater":"\u2276","lessgtr":"\u2276","LessLess":"\u2AA1","lesssim":"\u2272","LessSlantEqual":"\u2A7D","LessTilde":"\u2272","lfisht":"\u297C","lfloor":"\u230A","Lfr":"\uD835\uDD0F","lfr":"\uD835\uDD29","lg":"\u2276","lgE":"\u2A91","lHar":"\u2962","lhard":"\u21BD","lharu":"\u21BC","lharul":"\u296A","lhblk":"\u2584","LJcy":"\u0409","ljcy":"\u0459","llarr":"\u21C7","ll":"\u226A","Ll":"\u22D8","llcorner":"\u231E","Lleftarrow":"\u21DA","llhard":"\u296B","lltri":"\u25FA","Lmidot":"\u013F","lmidot":"\u0140","lmoustache":"\u23B0","lmoust":"\u23B0","lnap":"\u2A89","lnapprox":"\u2A89","lne":"\u2A87","lnE":"\u2268","lneq":"\u2A87","lneqq":"\u2268","lnsim":"\u22E6","loang":"\u27EC","loarr":"\u21FD","lobrk":"\u27E6","longleftarrow":"\u27F5","LongLeftArrow":"\u27F5","Longleftarrow":"\u27F8","longleftrightarrow":"\u27F7","LongLeftRightArrow":"\u27F7","Longleftrightarrow":"\u27FA","longmapsto":"\u27FC","longrightarrow":"\u27F6","LongRightArrow":"\u27F6","Longrightarrow":"\u27F9","looparrowleft":"\u21AB","looparrowright":"\u21AC","lopar":"\u2985","Lopf":"\uD835\uDD43","lopf":"\uD835\uDD5D","loplus":"\u2A2D","lotimes":"\u2A34","lowast":"\u2217","lowbar":"_","LowerLeftArrow":"\u2199","LowerRightArrow":"\u2198","loz":"\u25CA","lozenge":"\u25CA","lozf":"\u29EB","lpar":"(","lparlt":"\u2993","lrarr":"\u21C6","lrcorner":"\u231F","lrhar":"\u21CB","lrhard":"\u296D","lrm":"\u200E","lrtri":"\u22BF","lsaquo":"\u2039","lscr":"\uD835\uDCC1","Lscr":"\u2112","lsh":"\u21B0","Lsh":"\u21B0","lsim":"\u2272","lsime":"\u2A8D","lsimg":"\u2A8F","lsqb":"[","lsquo":"\u2018","lsquor":"\u201A","Lstrok":"\u0141","lstrok":"\u0142","ltcc":"\u2AA6","ltcir":"\u2A79","lt":"<","LT":"<","Lt":"\u226A","ltdot":"\u22D6","lthree":"\u22CB","ltimes":"\u22C9","ltlarr":"\u2976","ltquest":"\u2A7B","ltri":"\u25C3","ltrie":"\u22B4","ltrif":"\u25C2","ltrPar":"\u2996","lurdshar":"\u294A","luruhar":"\u2966","lvertneqq":"\u2268\uFE00","lvnE":"\u2268\uFE00","macr":"\u00AF","male":"\u2642","malt":"\u2720","maltese":"\u2720","Map":"\u2905","map":"\u21A6","mapsto":"\u21A6","mapstodown":"\u21A7","mapstoleft":"\u21A4","mapstoup":"\u21A5","marker":"\u25AE","mcomma":"\u2A29","Mcy":"\u041C","mcy":"\u043C","mdash":"\u2014","mDDot":"\u223A","measuredangle":"\u2221","MediumSpace":"\u205F","Mellintrf":"\u2133","Mfr":"\uD835\uDD10","mfr":"\uD835\uDD2A","mho":"\u2127","micro":"\u00B5","midast":"*","midcir":"\u2AF0","mid":"\u2223","middot":"\u00B7","minusb":"\u229F","minus":"\u2212","minusd":"\u2238","minusdu":"\u2A2A","MinusPlus":"\u2213","mlcp":"\u2ADB","mldr":"\u2026","mnplus":"\u2213","models":"\u22A7","Mopf":"\uD835\uDD44","mopf":"\uD835\uDD5E","mp":"\u2213","mscr":"\uD835\uDCC2","Mscr":"\u2133","mstpos":"\u223E","Mu":"\u039C","mu":"\u03BC","multimap":"\u22B8","mumap":"\u22B8","nabla":"\u2207","Nacute":"\u0143","nacute":"\u0144","nang":"\u2220\u20D2","nap":"\u2249","napE":"\u2A70\u0338","napid":"\u224B\u0338","napos":"\u0149","napprox":"\u2249","natural":"\u266E","naturals":"\u2115","natur":"\u266E","nbsp":"\u00A0","nbump":"\u224E\u0338","nbumpe":"\u224F\u0338","ncap":"\u2A43","Ncaron":"\u0147","ncaron":"\u0148","Ncedil":"\u0145","ncedil":"\u0146","ncong":"\u2247","ncongdot":"\u2A6D\u0338","ncup":"\u2A42","Ncy":"\u041D","ncy":"\u043D","ndash":"\u2013","nearhk":"\u2924","nearr":"\u2197","neArr":"\u21D7","nearrow":"\u2197","ne":"\u2260","nedot":"\u2250\u0338","NegativeMediumSpace":"\u200B","NegativeThickSpace":"\u200B","NegativeThinSpace":"\u200B","NegativeVeryThinSpace":"\u200B","nequiv":"\u2262","nesear":"\u2928","nesim":"\u2242\u0338","NestedGreaterGreater":"\u226B","NestedLessLess":"\u226A","NewLine":"\n","nexist":"\u2204","nexists":"\u2204","Nfr":"\uD835\uDD11","nfr":"\uD835\uDD2B","ngE":"\u2267\u0338","nge":"\u2271","ngeq":"\u2271","ngeqq":"\u2267\u0338","ngeqslant":"\u2A7E\u0338","nges":"\u2A7E\u0338","nGg":"\u22D9\u0338","ngsim":"\u2275","nGt":"\u226B\u20D2","ngt":"\u226F","ngtr":"\u226F","nGtv":"\u226B\u0338","nharr":"\u21AE","nhArr":"\u21CE","nhpar":"\u2AF2","ni":"\u220B","nis":"\u22FC","nisd":"\u22FA","niv":"\u220B","NJcy":"\u040A","njcy":"\u045A","nlarr":"\u219A","nlArr":"\u21CD","nldr":"\u2025","nlE":"\u2266\u0338","nle":"\u2270","nleftarrow":"\u219A","nLeftarrow":"\u21CD","nleftrightarrow":"\u21AE","nLeftrightarrow":"\u21CE","nleq":"\u2270","nleqq":"\u2266\u0338","nleqslant":"\u2A7D\u0338","nles":"\u2A7D\u0338","nless":"\u226E","nLl":"\u22D8\u0338","nlsim":"\u2274","nLt":"\u226A\u20D2","nlt":"\u226E","nltri":"\u22EA","nltrie":"\u22EC","nLtv":"\u226A\u0338","nmid":"\u2224","NoBreak":"\u2060","NonBreakingSpace":"\u00A0","nopf":"\uD835\uDD5F","Nopf":"\u2115","Not":"\u2AEC","not":"\u00AC","NotCongruent":"\u2262","NotCupCap":"\u226D","NotDoubleVerticalBar":"\u2226","NotElement":"\u2209","NotEqual":"\u2260","NotEqualTilde":"\u2242\u0338","NotExists":"\u2204","NotGreater":"\u226F","NotGreaterEqual":"\u2271","NotGreaterFullEqual":"\u2267\u0338","NotGreaterGreater":"\u226B\u0338","NotGreaterLess":"\u2279","NotGreaterSlantEqual":"\u2A7E\u0338","NotGreaterTilde":"\u2275","NotHumpDownHump":"\u224E\u0338","NotHumpEqual":"\u224F\u0338","notin":"\u2209","notindot":"\u22F5\u0338","notinE":"\u22F9\u0338","notinva":"\u2209","notinvb":"\u22F7","notinvc":"\u22F6","NotLeftTriangleBar":"\u29CF\u0338","NotLeftTriangle":"\u22EA","NotLeftTriangleEqual":"\u22EC","NotLess":"\u226E","NotLessEqual":"\u2270","NotLessGreater":"\u2278","NotLessLess":"\u226A\u0338","NotLessSlantEqual":"\u2A7D\u0338","NotLessTilde":"\u2274","NotNestedGreaterGreater":"\u2AA2\u0338","NotNestedLessLess":"\u2AA1\u0338","notni":"\u220C","notniva":"\u220C","notnivb":"\u22FE","notnivc":"\u22FD","NotPrecedes":"\u2280","NotPrecedesEqual":"\u2AAF\u0338","NotPrecedesSlantEqual":"\u22E0","NotReverseElement":"\u220C","NotRightTriangleBar":"\u29D0\u0338","NotRightTriangle":"\u22EB","NotRightTriangleEqual":"\u22ED","NotSquareSubset":"\u228F\u0338","NotSquareSubsetEqual":"\u22E2","NotSquareSuperset":"\u2290\u0338","NotSquareSupersetEqual":"\u22E3","NotSubset":"\u2282\u20D2","NotSubsetEqual":"\u2288","NotSucceeds":"\u2281","NotSucceedsEqual":"\u2AB0\u0338","NotSucceedsSlantEqual":"\u22E1","NotSucceedsTilde":"\u227F\u0338","NotSuperset":"\u2283\u20D2","NotSupersetEqual":"\u2289","NotTilde":"\u2241","NotTildeEqual":"\u2244","NotTildeFullEqual":"\u2247","NotTildeTilde":"\u2249","NotVerticalBar":"\u2224","nparallel":"\u2226","npar":"\u2226","nparsl":"\u2AFD\u20E5","npart":"\u2202\u0338","npolint":"\u2A14","npr":"\u2280","nprcue":"\u22E0","nprec":"\u2280","npreceq":"\u2AAF\u0338","npre":"\u2AAF\u0338","nrarrc":"\u2933\u0338","nrarr":"\u219B","nrArr":"\u21CF","nrarrw":"\u219D\u0338","nrightarrow":"\u219B","nRightarrow":"\u21CF","nrtri":"\u22EB","nrtrie":"\u22ED","nsc":"\u2281","nsccue":"\u22E1","nsce":"\u2AB0\u0338","Nscr":"\uD835\uDCA9","nscr":"\uD835\uDCC3","nshortmid":"\u2224","nshortparallel":"\u2226","nsim":"\u2241","nsime":"\u2244","nsimeq":"\u2244","nsmid":"\u2224","nspar":"\u2226","nsqsube":"\u22E2","nsqsupe":"\u22E3","nsub":"\u2284","nsubE":"\u2AC5\u0338","nsube":"\u2288","nsubset":"\u2282\u20D2","nsubseteq":"\u2288","nsubseteqq":"\u2AC5\u0338","nsucc":"\u2281","nsucceq":"\u2AB0\u0338","nsup":"\u2285","nsupE":"\u2AC6\u0338","nsupe":"\u2289","nsupset":"\u2283\u20D2","nsupseteq":"\u2289","nsupseteqq":"\u2AC6\u0338","ntgl":"\u2279","Ntilde":"\u00D1","ntilde":"\u00F1","ntlg":"\u2278","ntriangleleft":"\u22EA","ntrianglelefteq":"\u22EC","ntriangleright":"\u22EB","ntrianglerighteq":"\u22ED","Nu":"\u039D","nu":"\u03BD","num":"#","numero":"\u2116","numsp":"\u2007","nvap":"\u224D\u20D2","nvdash":"\u22AC","nvDash":"\u22AD","nVdash":"\u22AE","nVDash":"\u22AF","nvge":"\u2265\u20D2","nvgt":">\u20D2","nvHarr":"\u2904","nvinfin":"\u29DE","nvlArr":"\u2902","nvle":"\u2264\u20D2","nvlt":"<\u20D2","nvltrie":"\u22B4\u20D2","nvrArr":"\u2903","nvrtrie":"\u22B5\u20D2","nvsim":"\u223C\u20D2","nwarhk":"\u2923","nwarr":"\u2196","nwArr":"\u21D6","nwarrow":"\u2196","nwnear":"\u2927","Oacute":"\u00D3","oacute":"\u00F3","oast":"\u229B","Ocirc":"\u00D4","ocirc":"\u00F4","ocir":"\u229A","Ocy":"\u041E","ocy":"\u043E","odash":"\u229D","Odblac":"\u0150","odblac":"\u0151","odiv":"\u2A38","odot":"\u2299","odsold":"\u29BC","OElig":"\u0152","oelig":"\u0153","ofcir":"\u29BF","Ofr":"\uD835\uDD12","ofr":"\uD835\uDD2C","ogon":"\u02DB","Ograve":"\u00D2","ograve":"\u00F2","ogt":"\u29C1","ohbar":"\u29B5","ohm":"\u03A9","oint":"\u222E","olarr":"\u21BA","olcir":"\u29BE","olcross":"\u29BB","oline":"\u203E","olt":"\u29C0","Omacr":"\u014C","omacr":"\u014D","Omega":"\u03A9","omega":"\u03C9","Omicron":"\u039F","omicron":"\u03BF","omid":"\u29B6","ominus":"\u2296","Oopf":"\uD835\uDD46","oopf":"\uD835\uDD60","opar":"\u29B7","OpenCurlyDoubleQuote":"\u201C","OpenCurlyQuote":"\u2018","operp":"\u29B9","oplus":"\u2295","orarr":"\u21BB","Or":"\u2A54","or":"\u2228","ord":"\u2A5D","order":"\u2134","orderof":"\u2134","ordf":"\u00AA","ordm":"\u00BA","origof":"\u22B6","oror":"\u2A56","orslope":"\u2A57","orv":"\u2A5B","oS":"\u24C8","Oscr":"\uD835\uDCAA","oscr":"\u2134","Oslash":"\u00D8","oslash":"\u00F8","osol":"\u2298","Otilde":"\u00D5","otilde":"\u00F5","otimesas":"\u2A36","Otimes":"\u2A37","otimes":"\u2297","Ouml":"\u00D6","ouml":"\u00F6","ovbar":"\u233D","OverBar":"\u203E","OverBrace":"\u23DE","OverBracket":"\u23B4","OverParenthesis":"\u23DC","para":"\u00B6","parallel":"\u2225","par":"\u2225","parsim":"\u2AF3","parsl":"\u2AFD","part":"\u2202","PartialD":"\u2202","Pcy":"\u041F","pcy":"\u043F","percnt":"%","period":".","permil":"\u2030","perp":"\u22A5","pertenk":"\u2031","Pfr":"\uD835\uDD13","pfr":"\uD835\uDD2D","Phi":"\u03A6","phi":"\u03C6","phiv":"\u03D5","phmmat":"\u2133","phone":"\u260E","Pi":"\u03A0","pi":"\u03C0","pitchfork":"\u22D4","piv":"\u03D6","planck":"\u210F","planckh":"\u210E","plankv":"\u210F","plusacir":"\u2A23","plusb":"\u229E","pluscir":"\u2A22","plus":"+","plusdo":"\u2214","plusdu":"\u2A25","pluse":"\u2A72","PlusMinus":"\u00B1","plusmn":"\u00B1","plussim":"\u2A26","plustwo":"\u2A27","pm":"\u00B1","Poincareplane":"\u210C","pointint":"\u2A15","popf":"\uD835\uDD61","Popf":"\u2119","pound":"\u00A3","prap":"\u2AB7","Pr":"\u2ABB","pr":"\u227A","prcue":"\u227C","precapprox":"\u2AB7","prec":"\u227A","preccurlyeq":"\u227C","Precedes":"\u227A","PrecedesEqual":"\u2AAF","PrecedesSlantEqual":"\u227C","PrecedesTilde":"\u227E","preceq":"\u2AAF","precnapprox":"\u2AB9","precneqq":"\u2AB5","precnsim":"\u22E8","pre":"\u2AAF","prE":"\u2AB3","precsim":"\u227E","prime":"\u2032","Prime":"\u2033","primes":"\u2119","prnap":"\u2AB9","prnE":"\u2AB5","prnsim":"\u22E8","prod":"\u220F","Product":"\u220F","profalar":"\u232E","profline":"\u2312","profsurf":"\u2313","prop":"\u221D","Proportional":"\u221D","Proportion":"\u2237","propto":"\u221D","prsim":"\u227E","prurel":"\u22B0","Pscr":"\uD835\uDCAB","pscr":"\uD835\uDCC5","Psi":"\u03A8","psi":"\u03C8","puncsp":"\u2008","Qfr":"\uD835\uDD14","qfr":"\uD835\uDD2E","qint":"\u2A0C","qopf":"\uD835\uDD62","Qopf":"\u211A","qprime":"\u2057","Qscr":"\uD835\uDCAC","qscr":"\uD835\uDCC6","quaternions":"\u210D","quatint":"\u2A16","quest":"?","questeq":"\u225F","quot":"\"","QUOT":"\"","rAarr":"\u21DB","race":"\u223D\u0331","Racute":"\u0154","racute":"\u0155","radic":"\u221A","raemptyv":"\u29B3","rang":"\u27E9","Rang":"\u27EB","rangd":"\u2992","range":"\u29A5","rangle":"\u27E9","raquo":"\u00BB","rarrap":"\u2975","rarrb":"\u21E5","rarrbfs":"\u2920","rarrc":"\u2933","rarr":"\u2192","Rarr":"\u21A0","rArr":"\u21D2","rarrfs":"\u291E","rarrhk":"\u21AA","rarrlp":"\u21AC","rarrpl":"\u2945","rarrsim":"\u2974","Rarrtl":"\u2916","rarrtl":"\u21A3","rarrw":"\u219D","ratail":"\u291A","rAtail":"\u291C","ratio":"\u2236","rationals":"\u211A","rbarr":"\u290D","rBarr":"\u290F","RBarr":"\u2910","rbbrk":"\u2773","rbrace":"}","rbrack":"]","rbrke":"\u298C","rbrksld":"\u298E","rbrkslu":"\u2990","Rcaron":"\u0158","rcaron":"\u0159","Rcedil":"\u0156","rcedil":"\u0157","rceil":"\u2309","rcub":"}","Rcy":"\u0420","rcy":"\u0440","rdca":"\u2937","rdldhar":"\u2969","rdquo":"\u201D","rdquor":"\u201D","rdsh":"\u21B3","real":"\u211C","realine":"\u211B","realpart":"\u211C","reals":"\u211D","Re":"\u211C","rect":"\u25AD","reg":"\u00AE","REG":"\u00AE","ReverseElement":"\u220B","ReverseEquilibrium":"\u21CB","ReverseUpEquilibrium":"\u296F","rfisht":"\u297D","rfloor":"\u230B","rfr":"\uD835\uDD2F","Rfr":"\u211C","rHar":"\u2964","rhard":"\u21C1","rharu":"\u21C0","rharul":"\u296C","Rho":"\u03A1","rho":"\u03C1","rhov":"\u03F1","RightAngleBracket":"\u27E9","RightArrowBar":"\u21E5","rightarrow":"\u2192","RightArrow":"\u2192","Rightarrow":"\u21D2","RightArrowLeftArrow":"\u21C4","rightarrowtail":"\u21A3","RightCeiling":"\u2309","RightDoubleBracket":"\u27E7","RightDownTeeVector":"\u295D","RightDownVectorBar":"\u2955","RightDownVector":"\u21C2","RightFloor":"\u230B","rightharpoondown":"\u21C1","rightharpoonup":"\u21C0","rightleftarrows":"\u21C4","rightleftharpoons":"\u21CC","rightrightarrows":"\u21C9","rightsquigarrow":"\u219D","RightTeeArrow":"\u21A6","RightTee":"\u22A2","RightTeeVector":"\u295B","rightthreetimes":"\u22CC","RightTriangleBar":"\u29D0","RightTriangle":"\u22B3","RightTriangleEqual":"\u22B5","RightUpDownVector":"\u294F","RightUpTeeVector":"\u295C","RightUpVectorBar":"\u2954","RightUpVector":"\u21BE","RightVectorBar":"\u2953","RightVector":"\u21C0","ring":"\u02DA","risingdotseq":"\u2253","rlarr":"\u21C4","rlhar":"\u21CC","rlm":"\u200F","rmoustache":"\u23B1","rmoust":"\u23B1","rnmid":"\u2AEE","roang":"\u27ED","roarr":"\u21FE","robrk":"\u27E7","ropar":"\u2986","ropf":"\uD835\uDD63","Ropf":"\u211D","roplus":"\u2A2E","rotimes":"\u2A35","RoundImplies":"\u2970","rpar":")","rpargt":"\u2994","rppolint":"\u2A12","rrarr":"\u21C9","Rrightarrow":"\u21DB","rsaquo":"\u203A","rscr":"\uD835\uDCC7","Rscr":"\u211B","rsh":"\u21B1","Rsh":"\u21B1","rsqb":"]","rsquo":"\u2019","rsquor":"\u2019","rthree":"\u22CC","rtimes":"\u22CA","rtri":"\u25B9","rtrie":"\u22B5","rtrif":"\u25B8","rtriltri":"\u29CE","RuleDelayed":"\u29F4","ruluhar":"\u2968","rx":"\u211E","Sacute":"\u015A","sacute":"\u015B","sbquo":"\u201A","scap":"\u2AB8","Scaron":"\u0160","scaron":"\u0161","Sc":"\u2ABC","sc":"\u227B","sccue":"\u227D","sce":"\u2AB0","scE":"\u2AB4","Scedil":"\u015E","scedil":"\u015F","Scirc":"\u015C","scirc":"\u015D","scnap":"\u2ABA","scnE":"\u2AB6","scnsim":"\u22E9","scpolint":"\u2A13","scsim":"\u227F","Scy":"\u0421","scy":"\u0441","sdotb":"\u22A1","sdot":"\u22C5","sdote":"\u2A66","searhk":"\u2925","searr":"\u2198","seArr":"\u21D8","searrow":"\u2198","sect":"\u00A7","semi":";","seswar":"\u2929","setminus":"\u2216","setmn":"\u2216","sext":"\u2736","Sfr":"\uD835\uDD16","sfr":"\uD835\uDD30","sfrown":"\u2322","sharp":"\u266F","SHCHcy":"\u0429","shchcy":"\u0449","SHcy":"\u0428","shcy":"\u0448","ShortDownArrow":"\u2193","ShortLeftArrow":"\u2190","shortmid":"\u2223","shortparallel":"\u2225","ShortRightArrow":"\u2192","ShortUpArrow":"\u2191","shy":"\u00AD","Sigma":"\u03A3","sigma":"\u03C3","sigmaf":"\u03C2","sigmav":"\u03C2","sim":"\u223C","simdot":"\u2A6A","sime":"\u2243","simeq":"\u2243","simg":"\u2A9E","simgE":"\u2AA0","siml":"\u2A9D","simlE":"\u2A9F","simne":"\u2246","simplus":"\u2A24","simrarr":"\u2972","slarr":"\u2190","SmallCircle":"\u2218","smallsetminus":"\u2216","smashp":"\u2A33","smeparsl":"\u29E4","smid":"\u2223","smile":"\u2323","smt":"\u2AAA","smte":"\u2AAC","smtes":"\u2AAC\uFE00","SOFTcy":"\u042C","softcy":"\u044C","solbar":"\u233F","solb":"\u29C4","sol":"/","Sopf":"\uD835\uDD4A","sopf":"\uD835\uDD64","spades":"\u2660","spadesuit":"\u2660","spar":"\u2225","sqcap":"\u2293","sqcaps":"\u2293\uFE00","sqcup":"\u2294","sqcups":"\u2294\uFE00","Sqrt":"\u221A","sqsub":"\u228F","sqsube":"\u2291","sqsubset":"\u228F","sqsubseteq":"\u2291","sqsup":"\u2290","sqsupe":"\u2292","sqsupset":"\u2290","sqsupseteq":"\u2292","square":"\u25A1","Square":"\u25A1","SquareIntersection":"\u2293","SquareSubset":"\u228F","SquareSubsetEqual":"\u2291","SquareSuperset":"\u2290","SquareSupersetEqual":"\u2292","SquareUnion":"\u2294","squarf":"\u25AA","squ":"\u25A1","squf":"\u25AA","srarr":"\u2192","Sscr":"\uD835\uDCAE","sscr":"\uD835\uDCC8","ssetmn":"\u2216","ssmile":"\u2323","sstarf":"\u22C6","Star":"\u22C6","star":"\u2606","starf":"\u2605","straightepsilon":"\u03F5","straightphi":"\u03D5","strns":"\u00AF","sub":"\u2282","Sub":"\u22D0","subdot":"\u2ABD","subE":"\u2AC5","sube":"\u2286","subedot":"\u2AC3","submult":"\u2AC1","subnE":"\u2ACB","subne":"\u228A","subplus":"\u2ABF","subrarr":"\u2979","subset":"\u2282","Subset":"\u22D0","subseteq":"\u2286","subseteqq":"\u2AC5","SubsetEqual":"\u2286","subsetneq":"\u228A","subsetneqq":"\u2ACB","subsim":"\u2AC7","subsub":"\u2AD5","subsup":"\u2AD3","succapprox":"\u2AB8","succ":"\u227B","succcurlyeq":"\u227D","Succeeds":"\u227B","SucceedsEqual":"\u2AB0","SucceedsSlantEqual":"\u227D","SucceedsTilde":"\u227F","succeq":"\u2AB0","succnapprox":"\u2ABA","succneqq":"\u2AB6","succnsim":"\u22E9","succsim":"\u227F","SuchThat":"\u220B","sum":"\u2211","Sum":"\u2211","sung":"\u266A","sup1":"\u00B9","sup2":"\u00B2","sup3":"\u00B3","sup":"\u2283","Sup":"\u22D1","supdot":"\u2ABE","supdsub":"\u2AD8","supE":"\u2AC6","supe":"\u2287","supedot":"\u2AC4","Superset":"\u2283","SupersetEqual":"\u2287","suphsol":"\u27C9","suphsub":"\u2AD7","suplarr":"\u297B","supmult":"\u2AC2","supnE":"\u2ACC","supne":"\u228B","supplus":"\u2AC0","supset":"\u2283","Supset":"\u22D1","supseteq":"\u2287","supseteqq":"\u2AC6","supsetneq":"\u228B","supsetneqq":"\u2ACC","supsim":"\u2AC8","supsub":"\u2AD4","supsup":"\u2AD6","swarhk":"\u2926","swarr":"\u2199","swArr":"\u21D9","swarrow":"\u2199","swnwar":"\u292A","szlig":"\u00DF","Tab":"\t","target":"\u2316","Tau":"\u03A4","tau":"\u03C4","tbrk":"\u23B4","Tcaron":"\u0164","tcaron":"\u0165","Tcedil":"\u0162","tcedil":"\u0163","Tcy":"\u0422","tcy":"\u0442","tdot":"\u20DB","telrec":"\u2315","Tfr":"\uD835\uDD17","tfr":"\uD835\uDD31","there4":"\u2234","therefore":"\u2234","Therefore":"\u2234","Theta":"\u0398","theta":"\u03B8","thetasym":"\u03D1","thetav":"\u03D1","thickapprox":"\u2248","thicksim":"\u223C","ThickSpace":"\u205F\u200A","ThinSpace":"\u2009","thinsp":"\u2009","thkap":"\u2248","thksim":"\u223C","THORN":"\u00DE","thorn":"\u00FE","tilde":"\u02DC","Tilde":"\u223C","TildeEqual":"\u2243","TildeFullEqual":"\u2245","TildeTilde":"\u2248","timesbar":"\u2A31","timesb":"\u22A0","times":"\u00D7","timesd":"\u2A30","tint":"\u222D","toea":"\u2928","topbot":"\u2336","topcir":"\u2AF1","top":"\u22A4","Topf":"\uD835\uDD4B","topf":"\uD835\uDD65","topfork":"\u2ADA","tosa":"\u2929","tprime":"\u2034","trade":"\u2122","TRADE":"\u2122","triangle":"\u25B5","triangledown":"\u25BF","triangleleft":"\u25C3","trianglelefteq":"\u22B4","triangleq":"\u225C","triangleright":"\u25B9","trianglerighteq":"\u22B5","tridot":"\u25EC","trie":"\u225C","triminus":"\u2A3A","TripleDot":"\u20DB","triplus":"\u2A39","trisb":"\u29CD","tritime":"\u2A3B","trpezium":"\u23E2","Tscr":"\uD835\uDCAF","tscr":"\uD835\uDCC9","TScy":"\u0426","tscy":"\u0446","TSHcy":"\u040B","tshcy":"\u045B","Tstrok":"\u0166","tstrok":"\u0167","twixt":"\u226C","twoheadleftarrow":"\u219E","twoheadrightarrow":"\u21A0","Uacute":"\u00DA","uacute":"\u00FA","uarr":"\u2191","Uarr":"\u219F","uArr":"\u21D1","Uarrocir":"\u2949","Ubrcy":"\u040E","ubrcy":"\u045E","Ubreve":"\u016C","ubreve":"\u016D","Ucirc":"\u00DB","ucirc":"\u00FB","Ucy":"\u0423","ucy":"\u0443","udarr":"\u21C5","Udblac":"\u0170","udblac":"\u0171","udhar":"\u296E","ufisht":"\u297E","Ufr":"\uD835\uDD18","ufr":"\uD835\uDD32","Ugrave":"\u00D9","ugrave":"\u00F9","uHar":"\u2963","uharl":"\u21BF","uharr":"\u21BE","uhblk":"\u2580","ulcorn":"\u231C","ulcorner":"\u231C","ulcrop":"\u230F","ultri":"\u25F8","Umacr":"\u016A","umacr":"\u016B","uml":"\u00A8","UnderBar":"_","UnderBrace":"\u23DF","UnderBracket":"\u23B5","UnderParenthesis":"\u23DD","Union":"\u22C3","UnionPlus":"\u228E","Uogon":"\u0172","uogon":"\u0173","Uopf":"\uD835\uDD4C","uopf":"\uD835\uDD66","UpArrowBar":"\u2912","uparrow":"\u2191","UpArrow":"\u2191","Uparrow":"\u21D1","UpArrowDownArrow":"\u21C5","updownarrow":"\u2195","UpDownArrow":"\u2195","Updownarrow":"\u21D5","UpEquilibrium":"\u296E","upharpoonleft":"\u21BF","upharpoonright":"\u21BE","uplus":"\u228E","UpperLeftArrow":"\u2196","UpperRightArrow":"\u2197","upsi":"\u03C5","Upsi":"\u03D2","upsih":"\u03D2","Upsilon":"\u03A5","upsilon":"\u03C5","UpTeeArrow":"\u21A5","UpTee":"\u22A5","upuparrows":"\u21C8","urcorn":"\u231D","urcorner":"\u231D","urcrop":"\u230E","Uring":"\u016E","uring":"\u016F","urtri":"\u25F9","Uscr":"\uD835\uDCB0","uscr":"\uD835\uDCCA","utdot":"\u22F0","Utilde":"\u0168","utilde":"\u0169","utri":"\u25B5","utrif":"\u25B4","uuarr":"\u21C8","Uuml":"\u00DC","uuml":"\u00FC","uwangle":"\u29A7","vangrt":"\u299C","varepsilon":"\u03F5","varkappa":"\u03F0","varnothing":"\u2205","varphi":"\u03D5","varpi":"\u03D6","varpropto":"\u221D","varr":"\u2195","vArr":"\u21D5","varrho":"\u03F1","varsigma":"\u03C2","varsubsetneq":"\u228A\uFE00","varsubsetneqq":"\u2ACB\uFE00","varsupsetneq":"\u228B\uFE00","varsupsetneqq":"\u2ACC\uFE00","vartheta":"\u03D1","vartriangleleft":"\u22B2","vartriangleright":"\u22B3","vBar":"\u2AE8","Vbar":"\u2AEB","vBarv":"\u2AE9","Vcy":"\u0412","vcy":"\u0432","vdash":"\u22A2","vDash":"\u22A8","Vdash":"\u22A9","VDash":"\u22AB","Vdashl":"\u2AE6","veebar":"\u22BB","vee":"\u2228","Vee":"\u22C1","veeeq":"\u225A","vellip":"\u22EE","verbar":"|","Verbar":"\u2016","vert":"|","Vert":"\u2016","VerticalBar":"\u2223","VerticalLine":"|","VerticalSeparator":"\u2758","VerticalTilde":"\u2240","VeryThinSpace":"\u200A","Vfr":"\uD835\uDD19","vfr":"\uD835\uDD33","vltri":"\u22B2","vnsub":"\u2282\u20D2","vnsup":"\u2283\u20D2","Vopf":"\uD835\uDD4D","vopf":"\uD835\uDD67","vprop":"\u221D","vrtri":"\u22B3","Vscr":"\uD835\uDCB1","vscr":"\uD835\uDCCB","vsubnE":"\u2ACB\uFE00","vsubne":"\u228A\uFE00","vsupnE":"\u2ACC\uFE00","vsupne":"\u228B\uFE00","Vvdash":"\u22AA","vzigzag":"\u299A","Wcirc":"\u0174","wcirc":"\u0175","wedbar":"\u2A5F","wedge":"\u2227","Wedge":"\u22C0","wedgeq":"\u2259","weierp":"\u2118","Wfr":"\uD835\uDD1A","wfr":"\uD835\uDD34","Wopf":"\uD835\uDD4E","wopf":"\uD835\uDD68","wp":"\u2118","wr":"\u2240","wreath":"\u2240","Wscr":"\uD835\uDCB2","wscr":"\uD835\uDCCC","xcap":"\u22C2","xcirc":"\u25EF","xcup":"\u22C3","xdtri":"\u25BD","Xfr":"\uD835\uDD1B","xfr":"\uD835\uDD35","xharr":"\u27F7","xhArr":"\u27FA","Xi":"\u039E","xi":"\u03BE","xlarr":"\u27F5","xlArr":"\u27F8","xmap":"\u27FC","xnis":"\u22FB","xodot":"\u2A00","Xopf":"\uD835\uDD4F","xopf":"\uD835\uDD69","xoplus":"\u2A01","xotime":"\u2A02","xrarr":"\u27F6","xrArr":"\u27F9","Xscr":"\uD835\uDCB3","xscr":"\uD835\uDCCD","xsqcup":"\u2A06","xuplus":"\u2A04","xutri":"\u25B3","xvee":"\u22C1","xwedge":"\u22C0","Yacute":"\u00DD","yacute":"\u00FD","YAcy":"\u042F","yacy":"\u044F","Ycirc":"\u0176","ycirc":"\u0177","Ycy":"\u042B","ycy":"\u044B","yen":"\u00A5","Yfr":"\uD835\uDD1C","yfr":"\uD835\uDD36","YIcy":"\u0407","yicy":"\u0457","Yopf":"\uD835\uDD50","yopf":"\uD835\uDD6A","Yscr":"\uD835\uDCB4","yscr":"\uD835\uDCCE","YUcy":"\u042E","yucy":"\u044E","yuml":"\u00FF","Yuml":"\u0178","Zacute":"\u0179","zacute":"\u017A","Zcaron":"\u017D","zcaron":"\u017E","Zcy":"\u0417","zcy":"\u0437","Zdot":"\u017B","zdot":"\u017C","zeetrf":"\u2128","ZeroWidthSpace":"\u200B","Zeta":"\u0396","zeta":"\u03B6","zfr":"\uD835\uDD37","Zfr":"\u2128","ZHcy":"\u0416","zhcy":"\u0436","zigrarr":"\u21DD","zopf":"\uD835\uDD6B","Zopf":"\u2124","Zscr":"\uD835\uDCB5","zscr":"\uD835\uDCCF","zwj":"\u200D","zwnj":"\u200C"}
},{}],56:[function(require,module,exports){
module.exports={"Aacute":"\u00C1","aacute":"\u00E1","Acirc":"\u00C2","acirc":"\u00E2","acute":"\u00B4","AElig":"\u00C6","aelig":"\u00E6","Agrave":"\u00C0","agrave":"\u00E0","amp":"&","AMP":"&","Aring":"\u00C5","aring":"\u00E5","Atilde":"\u00C3","atilde":"\u00E3","Auml":"\u00C4","auml":"\u00E4","brvbar":"\u00A6","Ccedil":"\u00C7","ccedil":"\u00E7","cedil":"\u00B8","cent":"\u00A2","copy":"\u00A9","COPY":"\u00A9","curren":"\u00A4","deg":"\u00B0","divide":"\u00F7","Eacute":"\u00C9","eacute":"\u00E9","Ecirc":"\u00CA","ecirc":"\u00EA","Egrave":"\u00C8","egrave":"\u00E8","ETH":"\u00D0","eth":"\u00F0","Euml":"\u00CB","euml":"\u00EB","frac12":"\u00BD","frac14":"\u00BC","frac34":"\u00BE","gt":">","GT":">","Iacute":"\u00CD","iacute":"\u00ED","Icirc":"\u00CE","icirc":"\u00EE","iexcl":"\u00A1","Igrave":"\u00CC","igrave":"\u00EC","iquest":"\u00BF","Iuml":"\u00CF","iuml":"\u00EF","laquo":"\u00AB","lt":"<","LT":"<","macr":"\u00AF","micro":"\u00B5","middot":"\u00B7","nbsp":"\u00A0","not":"\u00AC","Ntilde":"\u00D1","ntilde":"\u00F1","Oacute":"\u00D3","oacute":"\u00F3","Ocirc":"\u00D4","ocirc":"\u00F4","Ograve":"\u00D2","ograve":"\u00F2","ordf":"\u00AA","ordm":"\u00BA","Oslash":"\u00D8","oslash":"\u00F8","Otilde":"\u00D5","otilde":"\u00F5","Ouml":"\u00D6","ouml":"\u00F6","para":"\u00B6","plusmn":"\u00B1","pound":"\u00A3","quot":"\"","QUOT":"\"","raquo":"\u00BB","reg":"\u00AE","REG":"\u00AE","sect":"\u00A7","shy":"\u00AD","sup1":"\u00B9","sup2":"\u00B2","sup3":"\u00B3","szlig":"\u00DF","THORN":"\u00DE","thorn":"\u00FE","times":"\u00D7","Uacute":"\u00DA","uacute":"\u00FA","Ucirc":"\u00DB","ucirc":"\u00FB","Ugrave":"\u00D9","ugrave":"\u00F9","uml":"\u00A8","Uuml":"\u00DC","uuml":"\u00FC","Yacute":"\u00DD","yacute":"\u00FD","yen":"\u00A5","yuml":"\u00FF"}
},{}],57:[function(require,module,exports){
module.exports={"amp":"&","apos":"'","gt":">","lt":"<","quot":"\""}

},{}],58:[function(require,module,exports){
arguments[4][8][0].apply(exports,arguments)
},{"dup":8}],59:[function(require,module,exports){
module.exports = function (string) {
  return string.replace(/[-\\^$*+?.()|[\]{}]/g, "\\$&")
}

},{}],60:[function(require,module,exports){
module.exports = extend

var hasOwnProperty = Object.prototype.hasOwnProperty;

function extend() {
    var target = {}

    for (var i = 0; i < arguments.length; i++) {
        var source = arguments[i]

        for (var key in source) {
            if (hasOwnProperty.call(source, key)) {
                target[key] = source[key]
            }
        }
    }

    return target
}

},{}]},{},[1])(1)
});</script><script id="element" data-script="element" nonce="">/*! jQuery v3.5.1 | (c) JS Foundation and other contributors | jquery.org/license */
function PageLoadTime(){}function _isFunction(n){return"function"==typeof n}function _mapObject(n,t){for(var r=[],n=n||[],i=0,u=n.length;i<u;i++)r.push(t(n[i],i));return r}function _getPerformanceObjectData(n){var i={},t;if(n.toJSON)return n.toJSON();for(t in n)_isFunction(n[t])||(i[t]=n[t]);return i}function HelperFunctions(){}var $trace,$diags,$santizer,preloadCssLink,$i2e,$predicateValidation,$element;if(!function(n,t){"use strict";"object"==typeof module&&"object"==typeof module.exports?module.exports=n.document?t(n,!0):function(n){if(!n.document)throw new Error("jQuery requires a window with a document");return t(n)}:t(n)}("undefined"!=typeof window?window:this,function(n,t){"use strict";function br(n,t,i){var r,e,u=(i=i||f).createElement("script");if(u.text=n,t)for(r in oe)(e=t[r]||t.getAttribute&&t.getAttribute(r))&&u.setAttribute(r,e);i.head.appendChild(u).parentNode.removeChild(u)}function ut(n){return null==n?n+"":"object"==typeof n||"function"==typeof n?ri[pr.call(n)]||"object":typeof n}function pi(n){var t=!!n&&"length"in n&&n.length,i=ut(n);return!u(n)&&!rt(n)&&("array"===i||0===t||"number"==typeof t&&0<t&&t-1 in n)}function c(n,t){return n.nodeName&&n.nodeName.toLowerCase()===t.toLowerCase()}function bi(n,t,r){return u(t)?i.grep(n,function(n,i){return!!t.call(n,i,n)!==r}):t.nodeType?i.grep(n,function(n){return n===t!==r}):"string"!=typeof t?i.grep(n,function(n){return-1<ii.call(t,n)!==r}):i.filter(t,n,r)}function uu(n,t){while((n=n[t])&&1!==n.nodeType);return n}function et(n){return n}function fi(n){throw n;}function fu(n,t,i,r){var f;try{n&&u(f=n.promise)?f.call(n).done(t).fail(i):n&&u(f=n.then)?f.call(n,t,i):t.apply(void 0,[n].slice(r))}catch(n){i.apply(void 0,[n])}}function oi(){f.removeEventListener("DOMContentLoaded",oi);n.removeEventListener("load",oi);i.ready()}function ce(n,t){return t.toUpperCase()}function y(n){return n.replace(se,"ms-").replace(he,ce)}function bt(){this.expando=i.expando+bt.uid++}function ou(n,t,i){var u,r;if(void 0===i&&1===n.nodeType)if(u="data-"+t.replace(ae,"-$&").toLowerCase(),"string"==typeof(i=n.getAttribute(u))){try{i="true"===(r=i)||"false"!==r&&("null"===r?null:r===+r+""?+r:le.test(r)?JSON.parse(r):r)}catch(n){}o.set(n,t,i)}else i=void 0;return i}function hu(n,t,r,u){var s,h,c=20,l=u?function(){return u.cur()}:function(){return i.css(n,t,"")},o=l(),e=r&&r[3]||(i.cssNumber[t]?"":"px"),f=n.nodeType&&(i.cssNumber[t]||"px"!==e&&+o)&&kt.exec(i.css(n,t));if(f&&f[3]!==e){for(o/=2,e=e||f[3],f=+o||1;c--;)i.style(n,t,f+e),(1-h)*(1-(h=l()/o||.5))<=0&&(c=0),f/=h;f*=2;i.style(n,t,f+e);r=r||[]}return r&&(f=+f||+o||0,s=r[1]?f+(r[1]+1)*r[2]:+r[2],u&&(u.unit=e,u.start=f,u.end=s)),s}function ht(n,t){for(var h,f,a,s,c,l,e,o=[],u=0,v=n.length;u<v;u++)(f=n[u]).style&&(h=f.style.display,t?("none"===h&&(o[u]=r.get(f,"display")||null,o[u]||(f.style.display="")),""===f.style.display&&dt(f)&&(o[u]=(e=c=s=void 0,c=(a=f).ownerDocument,l=a.nodeName,(e=ki[l])||(s=c.body.appendChild(c.createElement(l)),e=i.css(s,"display"),s.parentNode.removeChild(s),"none"===e&&(e="block"),ki[l]=e)))):"none"!==h&&(o[u]="none",r.set(f,"display",h)));for(u=0;u<v;u++)null!=o[u]&&(n[u].style.display=o[u]);return n}function s(n,t){var r;return r="undefined"!=typeof n.getElementsByTagName?n.getElementsByTagName(t||"*"):"undefined"!=typeof n.querySelectorAll?n.querySelectorAll(t||"*"):[],void 0===t||t&&c(n,t)?i.merge([n],r):r}function di(n,t){for(var i=0,u=n.length;i<u;i++)r.set(n[i],"globalEval",!t||r.get(t[i],"globalEval"))}function vu(n,t,r,u,f){for(var e,o,p,a,w,v,c=t.createDocumentFragment(),y=[],l=0,b=n.length;l<b;l++)if((e=n[l])||0===e)if("object"===ut(e))i.merge(y,e.nodeType?[e]:e);else if(au.test(e)){for(o=o||c.appendChild(t.createElement("div")),p=(cu.exec(e)||["",""])[1].toLowerCase(),a=h[p]||h._default,o.innerHTML=a[1]+i.htmlPrefilter(e)+a[2],v=a[0];v--;)o=o.lastChild;i.merge(y,o.childNodes);(o=c.firstChild).textContent=""}else y.push(t.createTextNode(e));for(c.textContent="",l=0;e=y[l++];)if(u&&-1<i.inArray(e,u))f&&f.push(e);else if(w=st(e),o=s(c.appendChild(e),"script"),w&&di(o),r)for(v=0;e=o[v++];)lu.test(e.type||"")&&r.push(e);return c}function ct(){return!0}function lt(){return!1}function we(n,t){return n===function(){try{return f.activeElement}catch(n){}}()==("focus"===t)}function gi(n,t,r,u,f,e){var o,s;if("object"==typeof t){for(s in"string"!=typeof r&&(u=u||r,r=void 0),t)gi(n,s,r,u,t[s],e);return n}if(null==u&&null==f?(f=r,u=r=void 0):null==f&&("string"==typeof r?(f=u,u=void 0):(f=u,u=r,r=void 0)),!1===f)f=lt;else if(!f)return n;return 1===e&&(o=f,(f=function(n){return i().off(n),o.apply(this,arguments)}).guid=o.guid||(o.guid=i.guid++)),n.each(function(){i.event.add(this,t,f,u,r)})}function hi(n,t,u){u?(r.set(n,t,!1),i.event.add(n,t,{namespace:!1,handler:function(n){var o,e,f=r.get(this,t);if(1&n.isTrigger&&this[t]){if(f.length)(i.event.special[t]||{}).delegateType&&n.stopPropagation();else if(f=k.call(arguments),r.set(this,t,f),o=u(this,t),this[t](),f!==(e=r.get(this,t))||o?r.set(this,t,!1):e={},f!==e)return n.stopImmediatePropagation(),n.preventDefault(),e.value}else f.length&&(r.set(this,t,{value:i.event.trigger(i.extend(f[0],i.Event.prototype),f.slice(1),this)}),n.stopImmediatePropagation())}})):void 0===r.get(n,t)&&i.event.add(n,t,ct)}function pu(n,t){return c(n,"table")&&c(11!==t.nodeType?t:t.firstChild,"tr")&&i(n).children("tbody")[0]||n}function ge(n){return n.type=(null!==n.getAttribute("type"))+"/"+n.type,n}function no(n){return"true/"===(n.type||"").slice(0,5)?n.type=n.type.slice(5):n.removeAttribute("type"),n}function wu(n,t){var u,s,f,h,c,e;if(1===t.nodeType){if(r.hasData(n)&&(e=r.get(n).events))for(f in r.remove(t,"handle events"),e)for(u=0,s=e[f].length;u<s;u++)i.event.add(t,f,e[f][u]);o.hasData(n)&&(h=o.access(n),c=i.extend({},h),o.set(t,c))}}function at(n,t,f,o){t=yr(t);var a,b,l,v,h,y,c=0,p=n.length,d=p-1,w=t[0],k=u(w);if(k||1<p&&"string"==typeof w&&!e.checkClone&&ke.test(w))return n.each(function(i){var r=n.eq(i);k&&(t[0]=w.call(this,i,r.html()));at(r,t,f,o)});if(p&&(b=(a=vu(t,n[0].ownerDocument,!1,n,o)).firstChild,1===a.childNodes.length&&(a=b),b||o)){for(v=(l=i.map(s(a,"script"),ge)).length;c<p;c++)h=a,c!==d&&(h=i.clone(h,!0,!0),v&&i.merge(l,s(h,"script"))),f.call(n[c],h,c);if(v)for(y=l[l.length-1].ownerDocument,i.map(l,no),c=0;c<v;c++)h=l[c],lu.test(h.type||"")&&!r.access(h,"globalEval")&&i.contains(y,h)&&(h.src&&"module"!==(h.type||"").toLowerCase()?i._evalUrl&&!h.noModule&&i._evalUrl(h.src,{nonce:h.nonce||h.getAttribute("nonce")},y):br(h.textContent.replace(de,""),h,y))}return n}function bu(n,t,r){for(var u,e=t?i.filter(t,n):n,f=0;null!=(u=e[f]);f++)r||1!==u.nodeType||i.cleanData(s(u)),u.parentNode&&(r&&st(u)&&di(s(u,"script")),u.parentNode.removeChild(u));return n}function ni(n,t,r){var o,s,h,f,u=n.style;return(r=r||ci(n))&&(""!==(f=r.getPropertyValue(t)||r[t])||st(n)||(f=i.style(n,t)),!e.pixelBoxStyles()&&nr.test(f)&&to.test(t)&&(o=u.width,s=u.minWidth,h=u.maxWidth,u.minWidth=u.maxWidth=u.width=f,f=r.width,u.width=o,u.minWidth=s,u.maxWidth=h)),void 0!==f?f+"":f}function du(n,t){return{get:function(){if(!n())return(this.get=t).apply(this,arguments);delete this.get}}}function tr(n){var t=i.cssProps[n]||tf[n];return t||(n in nf?n:tf[n]=function(n){for(var i=n[0].toUpperCase()+n.slice(1),t=gu.length;t--;)if((n=gu[t]+i)in nf)return n}(n)||n)}function ff(n,t,i){var r=kt.exec(t);return r?Math.max(0,r[2]-(i||0))+(r[3]||"px"):t}function ir(n,t,r,u,f,e){var o="width"===t?1:0,h=0,s=0;if(r===(u?"border":"content"))return 0;for(;o<4;o+=2)"margin"===r&&(s+=i.css(n,r+b[o],!0,f)),u?("content"===r&&(s-=i.css(n,"padding"+b[o],!0,f)),"margin"!==r&&(s-=i.css(n,"border"+b[o]+"Width",!0,f))):(s+=i.css(n,"padding"+b[o],!0,f),"padding"!==r?s+=i.css(n,"border"+b[o]+"Width",!0,f):h+=i.css(n,"border"+b[o]+"Width",!0,f));return!u&&0<=e&&(s+=Math.max(0,Math.ceil(n["offset"+t[0].toUpperCase()+t.slice(1)]-e-s-h-.5))||0),s}function ef(n,t,r){var f=ci(n),o=(!e.boxSizingReliable()||r)&&"border-box"===i.css(n,"boxSizing",!1,f),s=o,u=ni(n,t,f),h="offset"+t[0].toUpperCase()+t.slice(1);if(nr.test(u)){if(!r)return u;u="auto"}return(!e.boxSizingReliable()&&o||!e.reliableTrDimensions()&&c(n,"tr")||"auto"===u||!parseFloat(u)&&"inline"===i.css(n,"display",!1,f))&&n.getClientRects().length&&(o="border-box"===i.css(n,"boxSizing",!1,f),(s=h in n)&&(u=n[h])),(u=parseFloat(u)||0)+ir(n,t,r||(o?"border":"content"),s,f,u)+"px"}function a(n,t,i,r,u){return new a.prototype.init(n,t,i,r,u)}function rr(){li&&(!1===f.hidden&&n.requestAnimationFrame?n.requestAnimationFrame(rr):n.setTimeout(rr,i.fx.interval),i.fx.tick())}function cf(){return n.setTimeout(function(){vt=void 0}),vt=Date.now()}function ai(n,t){var u,r=0,i={height:n};for(t=t?1:0;r<4;r+=2-t)i["margin"+(u=b[r])]=i["padding"+u]=n;return t&&(i.opacity=i.width=n),i}function lf(n,t,i){for(var u,f=(v.tweeners[t]||[]).concat(v.tweeners["*"]),r=0,e=f.length;r<e;r++)if(u=f[r].call(i,t,n))return u}function v(n,t,r){var o,s,h=0,a=v.prefilters.length,e=i.Deferred().always(function(){delete l.elem}),l=function(){if(s)return!1;for(var o=vt||cf(),t=Math.max(0,f.startTime+f.duration-o),i=1-(t/f.duration||0),r=0,u=f.tweens.length;r<u;r++)f.tweens[r].run(i);return e.notifyWith(n,[f,i,t]),i<1&&u?t:(u||e.notifyWith(n,[f,1,0]),e.resolveWith(n,[f]),!1)},f=e.promise({elem:n,props:i.extend({},t),opts:i.extend(!0,{specialEasing:{},easing:i.easing._default},r),originalProperties:t,originalOptions:r,startTime:vt||cf(),duration:r.duration,tweens:[],createTween:function(t,r){var u=i.Tween(n,f.opts,t,r,f.opts.specialEasing[t]||f.opts.easing);return f.tweens.push(u),u},stop:function(t){var i=0,r=t?f.tweens.length:0;if(s)return this;for(s=!0;i<r;i++)f.tweens[i].run(1);return t?(e.notifyWith(n,[f,1,0]),e.resolveWith(n,[f,t])):e.rejectWith(n,[f,t]),this}}),c=f.props;for(!function(n,t){var r,f,e,u,o;for(r in n)if(e=t[f=y(r)],u=n[r],Array.isArray(u)&&(e=u[1],u=n[r]=u[0]),r!==f&&(n[f]=u,delete n[r]),(o=i.cssHooks[f])&&"expand"in o)for(r in u=o.expand(u),delete n[f],u)r in n||(n[r]=u[r],t[r]=e);else t[f]=e}(c,f.opts.specialEasing);h<a;h++)if(o=v.prefilters[h].call(f,n,c,f.opts))return u(o.stop)&&(i._queueHooks(f.elem,f.opts.queue).stop=o.stop.bind(o)),o;return i.map(c,lf,f),u(f.opts.start)&&f.opts.start.call(n,f),f.progress(f.opts.progress).done(f.opts.done,f.opts.complete).fail(f.opts.fail).always(f.opts.always),i.fx.timer(i.extend(l,{elem:n,anim:f,queue:f.opts.queue})),f}function tt(n){return(n.match(l)||[]).join(" ")}function it(n){return n.getAttribute&&n.getAttribute("class")||""}function ur(n){return Array.isArray(n)?n:"string"==typeof n&&n.match(l)||[]}function sr(n,t,r,u){var f;if(Array.isArray(t))i.each(t,function(t,i){r||uo.test(n)?u(n,i):sr(n+"["+("object"==typeof i&&null!=i?t:"")+"]",i,r,u)});else if(r||"object"!==ut(t))u(n,t);else for(f in t)sr(n+"["+f+"]",t[f],r,u)}function gf(n){return function(t,i){"string"!=typeof t&&(i=t,t="*");var r,f=0,e=t.toLowerCase().match(l)||[];if(u(i))while(r=e[f++])"+"===r[0]?(r=r.slice(1)||"*",(n[r]=n[r]||[]).unshift(i)):(n[r]=n[r]||[]).push(i)}}function ne(n,t,r,u){function e(s){var h;return f[s]=!0,i.each(n[s]||[],function(n,i){var s=i(t,r,u);return"string"!=typeof s||o||f[s]?o?!(h=s):void 0:(t.dataTypes.unshift(s),e(s),!1)}),h}var f={},o=n===hr;return e(t.dataTypes[0])||!f["*"]&&e("*")}function lr(n,t){var r,u,f=i.ajaxSettings.flatOptions||{};for(r in t)void 0!==t[r]&&((f[r]?n:u||(u={}))[r]=t[r]);return u&&i.extend(!0,n,u),n}var p=[],vr=Object.getPrototypeOf,k=p.slice,yr=p.flat?function(n){return p.flat.call(n)}:function(n){return p.concat.apply([],n)},yi=p.push,ii=p.indexOf,ri={},pr=ri.toString,ui=ri.hasOwnProperty,wr=ui.toString,ee=wr.call(Object),e={},u=function(n){return"function"==typeof n&&"number"!=typeof n.nodeType},rt=function(n){return null!=n&&n===n.window},f=n.document,oe={type:!0,src:!0,nonce:!0,noModule:!0},kr="3.5.1",i=function(n,t){return new i.fn.init(n,t)},d,wi,nu,tu,iu,ru,l,eu,ei,ot,dt,ki,h,au,vt,li,yt,of,sf,hf,af,pt,vf,yf,pf,fr,er,te,wt,ie,ar,vi,re,ue,fe;i.fn=i.prototype={jquery:kr,constructor:i,length:0,toArray:function(){return k.call(this)},get:function(n){return null==n?k.call(this):n<0?this[n+this.length]:this[n]},pushStack:function(n){var t=i.merge(this.constructor(),n);return t.prevObject=this,t},each:function(n){return i.each(this,n)},map:function(n){return this.pushStack(i.map(this,function(t,i){return n.call(t,i,t)}))},slice:function(){return this.pushStack(k.apply(this,arguments))},first:function(){return this.eq(0)},last:function(){return this.eq(-1)},even:function(){return this.pushStack(i.grep(this,function(n,t){return(t+1)%2}))},odd:function(){return this.pushStack(i.grep(this,function(n,t){return t%2}))},eq:function(n){var i=this.length,t=+n+(n<0?i:0);return this.pushStack(0<=t&&t<i?[this[t]]:[])},end:function(){return this.prevObject||this.constructor()},push:yi,sort:p.sort,splice:p.splice};i.extend=i.fn.extend=function(){var s,f,e,t,o,c,n=arguments[0]||{},r=1,l=arguments.length,h=!1;for("boolean"==typeof n&&(h=n,n=arguments[r]||{},r++),"object"==typeof n||u(n)||(n={}),r===l&&(n=this,r--);r<l;r++)if(null!=(s=arguments[r]))for(f in s)t=s[f],"__proto__"!==f&&n!==t&&(h&&t&&(i.isPlainObject(t)||(o=Array.isArray(t)))?(e=n[f],c=o&&!Array.isArray(e)?[]:o||i.isPlainObject(e)?e:{},o=!1,n[f]=i.extend(h,c,t)):void 0!==t&&(n[f]=t));return n};i.extend({expando:"jQuery"+(kr+Math.random()).replace(/\D/g,""),isReady:!0,error:function(n){throw new Error(n);},noop:function(){},isPlainObject:function(n){var t,i;return!(!n||"[object Object]"!==pr.call(n))&&(!(t=vr(n))||"function"==typeof(i=ui.call(t,"constructor")&&t.constructor)&&wr.call(i)===ee)},isEmptyObject:function(n){for(var t in n)return!1;return!0},globalEval:function(n,t,i){br(n,{nonce:t&&t.nonce},i)},each:function(n,t){var r,i=0;if(pi(n)){for(r=n.length;i<r;i++)if(!1===t.call(n[i],i,n[i]))break}else for(i in n)if(!1===t.call(n[i],i,n[i]))break;return n},makeArray:function(n,t){var r=t||[];return null!=n&&(pi(Object(n))?i.merge(r,"string"==typeof n?[n]:n):yi.call(r,n)),r},inArray:function(n,t,i){return null==t?-1:ii.call(t,n,i)},merge:function(n,t){for(var u=+t.length,i=0,r=n.length;i<u;i++)n[r++]=t[i];return n.length=r,n},grep:function(n,t,i){for(var u=[],r=0,f=n.length,e=!i;r<f;r++)!t(n[r],r)!==e&&u.push(n[r]);return u},map:function(n,t,i){var e,u,r=0,f=[];if(pi(n))for(e=n.length;r<e;r++)null!=(u=t(n[r],r,i))&&f.push(u);else for(r in n)null!=(u=t(n[r],r,i))&&f.push(u);return yr(f)},guid:1,support:e});"function"==typeof Symbol&&(i.fn[Symbol.iterator]=p[Symbol.iterator]);i.each("Boolean Number String Function Array Date RegExp Object Error Symbol".split(" "),function(n,t){ri["[object "+t+"]"]=t.toLowerCase()});d=function(n){function u(n,t,r,u){var s,y,c,l,p,w,d,v=t&&t.ownerDocument,a=t?t.nodeType:9;if(r=r||[],"string"!=typeof n||!n||1!==a&&9!==a&&11!==a)return r;if(!u&&(b(t),t=t||i,h)){if(11!==a&&(p=ar.exec(n)))if(s=p[1]){if(9===a){if(!(c=t.getElementById(s)))return r;if(c.id===s)return r.push(c),r}else if(v&&(c=v.getElementById(s))&&et(t,c)&&c.id===s)return r.push(c),r}else{if(p[2])return k.apply(r,t.getElementsByTagName(n)),r;if((s=p[3])&&f.getElementsByClassName&&t.getElementsByClassName)return k.apply(r,t.getElementsByClassName(s)),r}if(f.qsa&&!lt[n+" "]&&(!o||!o.test(n))&&(1!==a||"object"!==t.nodeName.toLowerCase())){if(d=n,v=t,1===a&&(er.test(n)||yi.test(n))){for((v=ti.test(n)&&ri(t.parentNode)||t)===t&&f.scope||((l=t.getAttribute("id"))?l=l.replace(pi,wi):t.setAttribute("id",l=e)),y=(w=ft(n)).length;y--;)w[y]=(l?"#"+l:":scope")+" "+pt(w[y]);d=w.join(",")}try{return k.apply(r,v.querySelectorAll(d)),r}catch(t){lt(n,!0)}finally{l===e&&t.removeAttribute("id")}}}return si(n.replace(at,"$1"),t,r,u)}function yt(){var n=[];return function i(r,u){return n.push(r+" ")>t.cacheLength&&delete i[n.shift()],i[r+" "]=u}}function l(n){return n[e]=!0,n}function a(n){var t=i.createElement("fieldset");try{return!!n(t)}catch(n){return!1}finally{t.parentNode&&t.parentNode.removeChild(t);t=null}}function ii(n,i){for(var r=n.split("|"),u=r.length;u--;)t.attrHandle[r[u]]=i}function ki(n,t){var i=t&&n,r=i&&1===n.nodeType&&1===t.nodeType&&n.sourceIndex-t.sourceIndex;if(r)return r;if(i)while(i=i.nextSibling)if(i===t)return-1;return n?1:-1}function yr(n){return function(t){return"input"===t.nodeName.toLowerCase()&&t.type===n}}function pr(n){return function(t){var i=t.nodeName.toLowerCase();return("input"===i||"button"===i)&&t.type===n}}function di(n){return function(t){return"form"in t?t.parentNode&&!1===t.disabled?"label"in t?"label"in t.parentNode?t.parentNode.disabled===n:t.disabled===n:t.isDisabled===n||t.isDisabled!==!n&&vr(t)===n:t.disabled===n:"label"in t&&t.disabled===n}}function it(n){return l(function(t){return t=+t,l(function(i,r){for(var u,f=n([],i.length,t),e=f.length;e--;)i[u=f[e]]&&(i[u]=!(r[u]=i[u]))})})}function ri(n){return n&&"undefined"!=typeof n.getElementsByTagName&&n}function gi(){}function pt(n){for(var t=0,r=n.length,i="";t<r;t++)i+=n[t].value;return i}function wt(n,t,i){var r=t.dir,u=t.next,f=u||r,o=i&&"parentNode"===f,s=nr++;return t.first?function(t,i,u){while(t=t[r])if(1===t.nodeType||o)return n(t,i,u);return!1}:function(t,i,h){var c,l,a,y=[v,s];if(h){while(t=t[r])if((1===t.nodeType||o)&&n(t,i,h))return!0}else while(t=t[r])if(1===t.nodeType||o)if(l=(a=t[e]||(t[e]={}))[t.uniqueID]||(a[t.uniqueID]={}),u&&u===t.nodeName.toLowerCase())t=t[r]||t;else{if((c=l[f])&&c[0]===v&&c[1]===s)return y[2]=c[2];if((l[f]=y)[2]=n(t,i,h))return!0}return!1}}function ui(n){return 1<n.length?function(t,i,r){for(var u=n.length;u--;)if(!n[u](t,i,r))return!1;return!0}:n[0]}function bt(n,t,i,r,u){for(var e,o=[],f=0,s=n.length,h=null!=t;f<s;f++)(e=n[f])&&(i&&!i(e,r,u)||(o.push(e),h&&t.push(f)));return o}function fi(n,t,i,r,f,o){return r&&!r[e]&&(r=fi(r)),f&&!f[e]&&(f=fi(f,o)),l(function(e,o,s,h){var a,l,v,w=[],p=[],b=o.length,d=e||function(n,t,i){for(var r=0,f=t.length;r<f;r++)u(n,t[r],i);return i}(t||"*",s.nodeType?[s]:s,[]),y=!n||!e&&t?d:bt(d,w,n,s,h),c=i?f||(e?n:b||r)?[]:o:y;if(i&&i(y,c,s,h),r)for(a=bt(c,p),r(a,[],s,h),l=a.length;l--;)(v=a[l])&&(c[p[l]]=!(y[p[l]]=v));if(e){if(f||n){if(f){for(a=[],l=c.length;l--;)(v=c[l])&&a.push(y[l]=v);f(null,c=[],a,h)}for(l=c.length;l--;)(v=c[l])&&-1<(a=f?nt(e,v):w[l])&&(e[a]=!(o[a]=v))}}else c=bt(c===o?c.splice(b,c.length):c),f?f(null,o,c,h):k.apply(o,c)})}function ei(n){for(var o,u,r,s=n.length,h=t.relative[n[0].type],c=h||t.relative[" "],i=h?1:0,l=wt(function(n){return n===o},c,!0),a=wt(function(n){return-1<nt(o,n)},c,!0),f=[function(n,t,i){var r=!h&&(i||t!==ht)||((o=t).nodeType?l(n,t,i):a(n,t,i));return o=null,r}];i<s;i++)if(u=t.relative[n[i].type])f=[wt(ui(f),u)];else{if((u=t.filter[n[i].type].apply(null,n[i].matches))[e]){for(r=++i;r<s;r++)if(t.relative[n[r].type])break;return fi(1<i&&ui(f),1<i&&pt(n.slice(0,i-1).concat({value:" "===n[i-2].type?"*":""})).replace(at,"$1"),u,i<r&&ei(n.slice(i,r)),r<s&&ei(n=n.slice(r)),r<s&&pt(n))}f.push(u)}return ui(f)}var rt,f,t,st,oi,ft,kt,si,ht,w,ut,b,i,s,h,o,d,ct,et,e="sizzle"+1*new Date,c=n.document,v=0,nr=0,hi=yt(),ci=yt(),li=yt(),lt=yt(),dt=function(n,t){return n===t&&(ut=!0),0},tr={}.hasOwnProperty,g=[],ir=g.pop,rr=g.push,k=g.push,ai=g.slice,nt=function(n,t){for(var i=0,r=n.length;i<r;i++)if(n[i]===t)return i;return-1},gt="checked|selected|async|autofocus|autoplay|controls|defer|disabled|hidden|ismap|loop|multiple|open|readonly|required|scoped",r="[\\x20\\t\\r\\n\\f]",tt="(?:\\\\[\\da-fA-F]{1,6}"+r+"?|\\\\[^\\r\\n\\f]|[\\w-]|[^\0-\\x7f])+",vi="\\["+r+"*("+tt+")(?:"+r+"*([*^$|!~]?=)"+r+"*(?:'((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\"|("+tt+"))|)"+r+"*\\]",ni=":("+tt+")(?:\\((('((?:\\\\.|[^\\\\'])*)'|\"((?:\\\\.|[^\\\\\"])*)\")|((?:\\\\.|[^\\\\()[\\]]|"+vi+")*)|.*)\\)|)",ur=new RegExp(r+"+","g"),at=new RegExp("^"+r+"+|((?:^|[^\\\\])(?:\\\\.)*)"+r+"+$","g"),fr=new RegExp("^"+r+"*,"+r+"*"),yi=new RegExp("^"+r+"*([>+~]|"+r+")"+r+"*"),er=new RegExp(r+"|>"),or=new RegExp(ni),sr=new RegExp("^"+tt+"$"),vt={ID:new RegExp("^#("+tt+")"),CLASS:new RegExp("^\\.("+tt+")"),TAG:new RegExp("^("+tt+"|[*])"),ATTR:new RegExp("^"+vi),PSEUDO:new RegExp("^"+ni),CHILD:new RegExp("^:(only|first|last|nth|nth-last)-(child|of-type)(?:\\("+r+"*(even|odd|(([+-]|)(\\d*)n|)"+r+"*(?:([+-]|)"+r+"*(\\d+)|))"+r+"*\\)|)","i"),bool:new RegExp("^(?:"+gt+")$","i"),needsContext:new RegExp("^"+r+"*[>+~]|:(even|odd|eq|gt|lt|nth|first|last)(?:\\("+r+"*((?:-\\d)?\\d*)"+r+"*\\)|)(?=[^-]|$)","i")},hr=/HTML$/i,cr=/^(?:input|select|textarea|button)$/i,lr=/^h\d$/i,ot=/^[^{]+\{\s*\[native \w/,ar=/^(?:#([\w-]+)|(\w+)|\.([\w-]+))$/,ti=/[+~]/,y=new RegExp("\\\\[\\da-fA-F]{1,6}"+r+"?|\\\\([^\\r\\n\\f])","g"),p=function(n,t){var i="0x"+n.slice(1)-65536;return t||(i<0?String.fromCharCode(i+65536):String.fromCharCode(i>>10|55296,1023&i|56320))},pi=/([\0-\x1f\x7f]|^-?\d)|^-$|[^\0-\x1f\x7f-\uFFFF\w-]/g,wi=function(n,t){return t?"\0"===n?"�":n.slice(0,-1)+"\\"+n.charCodeAt(n.length-1).toString(16)+" ":"\\"+n},bi=function(){b()},vr=wt(function(n){return!0===n.disabled&&"fieldset"===n.nodeName.toLowerCase()},{dir:"parentNode",next:"legend"});try{k.apply(g=ai.call(c.childNodes),c.childNodes);g[c.childNodes.length].nodeType}catch(rt){k={apply:g.length?function(n,t){rr.apply(n,ai.call(t))}:function(n,t){for(var i=n.length,r=0;n[i++]=t[r++];);n.length=i-1}}}for(rt in f=u.support={},oi=u.isXML=function(n){var i=n.namespaceURI,t=(n.ownerDocument||n).documentElement;return!hr.test(i||t&&t.nodeName||"HTML")},b=u.setDocument=function(n){var v,u,l=n?n.ownerDocument||n:c;return l!=i&&9===l.nodeType&&l.documentElement&&(s=(i=l).documentElement,h=!oi(i),c!=i&&(u=i.defaultView)&&u.top!==u&&(u.addEventListener?u.addEventListener("unload",bi,!1):u.attachEvent&&u.attachEvent("onunload",bi)),f.scope=a(function(n){return s.appendChild(n).appendChild(i.createElement("div")),"undefined"!=typeof n.querySelectorAll&&!n.querySelectorAll(":scope fieldset div").length}),f.attributes=a(function(n){return n.className="i",!n.getAttribute("className")}),f.getElementsByTagName=a(function(n){return n.appendChild(i.createComment("")),!n.getElementsByTagName("*").length}),f.getElementsByClassName=ot.test(i.getElementsByClassName),f.getById=a(function(n){return s.appendChild(n).id=e,!i.getElementsByName||!i.getElementsByName(e).length}),f.getById?(t.filter.ID=function(n){var t=n.replace(y,p);return function(n){return n.getAttribute("id")===t}},t.find.ID=function(n,t){if("undefined"!=typeof t.getElementById&&h){var i=t.getElementById(n);return i?[i]:[]}}):(t.filter.ID=function(n){var t=n.replace(y,p);return function(n){var i="undefined"!=typeof n.getAttributeNode&&n.getAttributeNode("id");return i&&i.value===t}},t.find.ID=function(n,t){if("undefined"!=typeof t.getElementById&&h){var r,u,f,i=t.getElementById(n);if(i){if((r=i.getAttributeNode("id"))&&r.value===n)return[i];for(f=t.getElementsByName(n),u=0;i=f[u++];)if((r=i.getAttributeNode("id"))&&r.value===n)return[i]}return[]}}),t.find.TAG=f.getElementsByTagName?function(n,t){return"undefined"!=typeof t.getElementsByTagName?t.getElementsByTagName(n):f.qsa?t.querySelectorAll(n):void 0}:function(n,t){var i,r=[],f=0,u=t.getElementsByTagName(n);if("*"===n){while(i=u[f++])1===i.nodeType&&r.push(i);return r}return u},t.find.CLASS=f.getElementsByClassName&&function(n,t){if("undefined"!=typeof t.getElementsByClassName&&h)return t.getElementsByClassName(n)},d=[],o=[],(f.qsa=ot.test(i.querySelectorAll))&&(a(function(n){var t;s.appendChild(n).innerHTML="<a id='"+e+"'><\/a><select id='"+e+"-\r\\' msallowcapture=''><option selected=''><\/option><\/select>";n.querySelectorAll("[msallowcapture^='']").length&&o.push("[*^$]="+r+"*(?:''|\"\")");n.querySelectorAll("[selected]").length||o.push("\\["+r+"*(?:value|"+gt+")");n.querySelectorAll("[id~="+e+"-]").length||o.push("~=");(t=i.createElement("input")).setAttribute("name","");n.appendChild(t);n.querySelectorAll("[name='']").length||o.push("\\["+r+"*name"+r+"*="+r+"*(?:''|\"\")");n.querySelectorAll(":checked").length||o.push(":checked");n.querySelectorAll("a#"+e+"+*").length||o.push(".#.+[+~]");n.querySelectorAll("\\\f");o.push("[\\r\\n\\f]")}),a(function(n){n.innerHTML="<a href='' disabled='disabled'><\/a><select disabled='disabled'><option/><\/select>";var t=i.createElement("input");t.setAttribute("type","hidden");n.appendChild(t).setAttribute("name","D");n.querySelectorAll("[name=d]").length&&o.push("name"+r+"*[*^$|!~]?=");2!==n.querySelectorAll(":enabled").length&&o.push(":enabled",":disabled");s.appendChild(n).disabled=!0;2!==n.querySelectorAll(":disabled").length&&o.push(":enabled",":disabled");n.querySelectorAll("*,:x");o.push(",.*:")})),(f.matchesSelector=ot.test(ct=s.matches||s.webkitMatchesSelector||s.mozMatchesSelector||s.oMatchesSelector||s.msMatchesSelector))&&a(function(n){f.disconnectedMatch=ct.call(n,"*");ct.call(n,"[s!='']:x");d.push("!=",ni)}),o=o.length&&new RegExp(o.join("|")),d=d.length&&new RegExp(d.join("|")),v=ot.test(s.compareDocumentPosition),et=v||ot.test(s.contains)?function(n,t){var r=9===n.nodeType?n.documentElement:n,i=t&&t.parentNode;return n===i||!(!i||1!==i.nodeType||!(r.contains?r.contains(i):n.compareDocumentPosition&&16&n.compareDocumentPosition(i)))}:function(n,t){if(t)while(t=t.parentNode)if(t===n)return!0;return!1},dt=v?function(n,t){if(n===t)return ut=!0,0;var r=!n.compareDocumentPosition-!t.compareDocumentPosition;return r||(1&(r=(n.ownerDocument||n)==(t.ownerDocument||t)?n.compareDocumentPosition(t):1)||!f.sortDetached&&t.compareDocumentPosition(n)===r?n==i||n.ownerDocument==c&&et(c,n)?-1:t==i||t.ownerDocument==c&&et(c,t)?1:w?nt(w,n)-nt(w,t):0:4&r?-1:1)}:function(n,t){if(n===t)return ut=!0,0;var r,u=0,o=n.parentNode,s=t.parentNode,f=[n],e=[t];if(!o||!s)return n==i?-1:t==i?1:o?-1:s?1:w?nt(w,n)-nt(w,t):0;if(o===s)return ki(n,t);for(r=n;r=r.parentNode;)f.unshift(r);for(r=t;r=r.parentNode;)e.unshift(r);while(f[u]===e[u])u++;return u?ki(f[u],e[u]):f[u]==c?-1:e[u]==c?1:0}),i},u.matches=function(n,t){return u(n,null,null,t)},u.matchesSelector=function(n,t){if(b(n),f.matchesSelector&&h&&!lt[t+" "]&&(!d||!d.test(t))&&(!o||!o.test(t)))try{var r=ct.call(n,t);if(r||f.disconnectedMatch||n.document&&11!==n.document.nodeType)return r}catch(n){lt(t,!0)}return 0<u(t,i,null,[n]).length},u.contains=function(n,t){return(n.ownerDocument||n)!=i&&b(n),et(n,t)},u.attr=function(n,r){(n.ownerDocument||n)!=i&&b(n);var e=t.attrHandle[r.toLowerCase()],u=e&&tr.call(t.attrHandle,r.toLowerCase())?e(n,r,!h):void 0;return void 0!==u?u:f.attributes||!h?n.getAttribute(r):(u=n.getAttributeNode(r))&&u.specified?u.value:null},u.escape=function(n){return(n+"").replace(pi,wi)},u.error=function(n){throw new Error("Syntax error, unrecognized expression: "+n);},u.uniqueSort=function(n){var r,u=[],t=0,i=0;if(ut=!f.detectDuplicates,w=!f.sortStable&&n.slice(0),n.sort(dt),ut){while(r=n[i++])r===n[i]&&(t=u.push(i));while(t--)n.splice(u[t],1)}return w=null,n},st=u.getText=function(n){var r,i="",u=0,t=n.nodeType;if(t){if(1===t||9===t||11===t){if("string"==typeof n.textContent)return n.textContent;for(n=n.firstChild;n;n=n.nextSibling)i+=st(n)}else if(3===t||4===t)return n.nodeValue}else while(r=n[u++])i+=st(r);return i},(t=u.selectors={cacheLength:50,createPseudo:l,match:vt,attrHandle:{},find:{},relative:{">":{dir:"parentNode",first:!0}," ":{dir:"parentNode"},"+":{dir:"previousSibling",first:!0},"~":{dir:"previousSibling"}},preFilter:{ATTR:function(n){return n[1]=n[1].replace(y,p),n[3]=(n[3]||n[4]||n[5]||"").replace(y,p),"~="===n[2]&&(n[3]=" "+n[3]+" "),n.slice(0,4)},CHILD:function(n){return n[1]=n[1].toLowerCase(),"nth"===n[1].slice(0,3)?(n[3]||u.error(n[0]),n[4]=+(n[4]?n[5]+(n[6]||1):2*("even"===n[3]||"odd"===n[3])),n[5]=+(n[7]+n[8]||"odd"===n[3])):n[3]&&u.error(n[0]),n},PSEUDO:function(n){var i,t=!n[6]&&n[2];return vt.CHILD.test(n[0])?null:(n[3]?n[2]=n[4]||n[5]||"":t&&or.test(t)&&(i=ft(t,!0))&&(i=t.indexOf(")",t.length-i)-t.length)&&(n[0]=n[0].slice(0,i),n[2]=t.slice(0,i)),n.slice(0,3))}},filter:{TAG:function(n){var t=n.replace(y,p).toLowerCase();return"*"===n?function(){return!0}:function(n){return n.nodeName&&n.nodeName.toLowerCase()===t}},CLASS:function(n){var t=hi[n+" "];return t||(t=new RegExp("(^|"+r+")"+n+"("+r+"|$)"))&&hi(n,function(n){return t.test("string"==typeof n.className&&n.className||"undefined"!=typeof n.getAttribute&&n.getAttribute("class")||"")})},ATTR:function(n,t,i){return function(r){var f=u.attr(r,n);return null==f?"!="===t:!t||(f+="","="===t?f===i:"!="===t?f!==i:"^="===t?i&&0===f.indexOf(i):"*="===t?i&&-1<f.indexOf(i):"$="===t?i&&f.slice(-i.length)===i:"~="===t?-1<(" "+f.replace(ur," ")+" ").indexOf(i):"|="===t&&(f===i||f.slice(0,i.length+1)===i+"-"))}},CHILD:function(n,t,i,r,u){var s="nth"!==n.slice(0,3),o="last"!==n.slice(-4),f="of-type"===t;return 1===r&&0===u?function(n){return!!n.parentNode}:function(t,i,h){var p,d,y,c,a,w,b=s!==o?"nextSibling":"previousSibling",k=t.parentNode,nt=f&&t.nodeName.toLowerCase(),g=!h&&!f,l=!1;if(k){if(s){while(b){for(c=t;c=c[b];)if(f?c.nodeName.toLowerCase()===nt:1===c.nodeType)return!1;w=b="only"===n&&!w&&"nextSibling"}return!0}if(w=[o?k.firstChild:k.lastChild],o&&g){for(l=(a=(p=(d=(y=(c=k)[e]||(c[e]={}))[c.uniqueID]||(y[c.uniqueID]={}))[n]||[])[0]===v&&p[1])&&p[2],c=a&&k.childNodes[a];c=++a&&c&&c[b]||(l=a=0)||w.pop();)if(1===c.nodeType&&++l&&c===t){d[n]=[v,a,l];break}}else if(g&&(l=a=(p=(d=(y=(c=t)[e]||(c[e]={}))[c.uniqueID]||(y[c.uniqueID]={}))[n]||[])[0]===v&&p[1]),!1===l)while(c=++a&&c&&c[b]||(l=a=0)||w.pop())if((f?c.nodeName.toLowerCase()===nt:1===c.nodeType)&&++l&&(g&&((d=(y=c[e]||(c[e]={}))[c.uniqueID]||(y[c.uniqueID]={}))[n]=[v,l]),c===t))break;return(l-=u)===r||l%r==0&&0<=l/r}}},PSEUDO:function(n,i){var f,r=t.pseudos[n]||t.setFilters[n.toLowerCase()]||u.error("unsupported pseudo: "+n);return r[e]?r(i):1<r.length?(f=[n,n,"",i],t.setFilters.hasOwnProperty(n.toLowerCase())?l(function(n,t){for(var e,u=r(n,i),f=u.length;f--;)n[e=nt(n,u[f])]=!(t[e]=u[f])}):function(n){return r(n,0,f)}):r}},pseudos:{not:l(function(n){var t=[],r=[],i=kt(n.replace(at,"$1"));return i[e]?l(function(n,t,r,u){for(var e,o=i(n,null,u,[]),f=n.length;f--;)(e=o[f])&&(n[f]=!(t[f]=e))}):function(n,u,f){return t[0]=n,i(t,null,f,r),t[0]=null,!r.pop()}}),has:l(function(n){return function(t){return 0<u(n,t).length}}),contains:l(function(n){return n=n.replace(y,p),function(t){return-1<(t.textContent||st(t)).indexOf(n)}}),lang:l(function(n){return sr.test(n||"")||u.error("unsupported lang: "+n),n=n.replace(y,p).toLowerCase(),function(t){var i;do if(i=h?t.lang:t.getAttribute("xml:lang")||t.getAttribute("lang"))return(i=i.toLowerCase())===n||0===i.indexOf(n+"-");while((t=t.parentNode)&&1===t.nodeType);return!1}}),target:function(t){var i=n.location&&n.location.hash;return i&&i.slice(1)===t.id},root:function(n){return n===s},focus:function(n){return n===i.activeElement&&(!i.hasFocus||i.hasFocus())&&!!(n.type||n.href||~n.tabIndex)},enabled:di(!1),disabled:di(!0),checked:function(n){var t=n.nodeName.toLowerCase();return"input"===t&&!!n.checked||"option"===t&&!!n.selected},selected:function(n){return n.parentNode&&n.parentNode.selectedIndex,!0===n.selected},empty:function(n){for(n=n.firstChild;n;n=n.nextSibling)if(n.nodeType<6)return!1;return!0},parent:function(n){return!t.pseudos.empty(n)},header:function(n){return lr.test(n.nodeName)},input:function(n){return cr.test(n.nodeName)},button:function(n){var t=n.nodeName.toLowerCase();return"input"===t&&"button"===n.type||"button"===t},text:function(n){var t;return"input"===n.nodeName.toLowerCase()&&"text"===n.type&&(null==(t=n.getAttribute("type"))||"text"===t.toLowerCase())},first:it(function(){return[0]}),last:it(function(n,t){return[t-1]}),eq:it(function(n,t,i){return[i<0?i+t:i]}),even:it(function(n,t){for(var i=0;i<t;i+=2)n.push(i);return n}),odd:it(function(n,t){for(var i=1;i<t;i+=2)n.push(i);return n}),lt:it(function(n,t,i){for(var r=i<0?i+t:t<i?t:i;0<=--r;)n.push(r);return n}),gt:it(function(n,t,i){for(var r=i<0?i+t:i;++r<t;)n.push(r);return n})}}).pseudos.nth=t.pseudos.eq,{radio:!0,checkbox:!0,file:!0,password:!0,image:!0})t.pseudos[rt]=yr(rt);for(rt in{submit:!0,reset:!0})t.pseudos[rt]=pr(rt);return gi.prototype=t.filters=t.pseudos,t.setFilters=new gi,ft=u.tokenize=function(n,i){var e,f,s,o,r,h,c,l=ci[n+" "];if(l)return i?0:l.slice(0);for(r=n,h=[],c=t.preFilter;r;){for(o in e&&!(f=fr.exec(r))||(f&&(r=r.slice(f[0].length)||r),h.push(s=[])),e=!1,(f=yi.exec(r))&&(e=f.shift(),s.push({value:e,type:f[0].replace(at," ")}),r=r.slice(e.length)),t.filter)(f=vt[o].exec(r))&&(!c[o]||(f=c[o](f)))&&(e=f.shift(),s.push({value:e,type:o,matches:f}),r=r.slice(e.length));if(!e)break}return i?r.length:r?u.error(n):ci(n,h).slice(0)},kt=u.compile=function(n,r){var s,c,a,o,y,p,w=[],d=[],f=li[n+" "];if(!f){for(r||(r=ft(n)),s=r.length;s--;)(f=ei(r[s]))[e]?w.push(f):d.push(f);(f=li(n,(c=d,o=0<(a=w).length,y=0<c.length,p=function(n,r,f,e,s){var l,nt,d,g=0,p="0",tt=n&&[],w=[],it=ht,rt=n||y&&t.find.TAG("*",s),ut=v+=null==it?1:Math.random()||.1,ft=rt.length;for(s&&(ht=r==i||r||s);p!==ft&&null!=(l=rt[p]);p++){if(y&&l){for(nt=0,r||l.ownerDocument==i||(b(l),f=!h);d=c[nt++];)if(d(l,r||i,f)){e.push(l);break}s&&(v=ut)}o&&((l=!d&&l)&&g--,n&&tt.push(l))}if(g+=p,o&&p!==g){for(nt=0;d=a[nt++];)d(tt,w,r,f);if(n){if(0<g)while(p--)tt[p]||w[p]||(w[p]=ir.call(e));w=bt(w)}k.apply(e,w);s&&!n&&0<w.length&&1<g+a.length&&u.uniqueSort(e)}return s&&(v=ut,ht=it),tt},o?l(p):p))).selector=n}return f},si=u.select=function(n,i,r,u){var o,f,e,l,a,c="function"==typeof n&&n,s=!u&&ft(n=c.selector||n);if(r=r||[],1===s.length){if(2<(f=s[0]=s[0].slice(0)).length&&"ID"===(e=f[0]).type&&9===i.nodeType&&h&&t.relative[f[1].type]){if(!(i=(t.find.ID(e.matches[0].replace(y,p),i)||[])[0]))return r;c&&(i=i.parentNode);n=n.slice(f.shift().value.length)}for(o=vt.needsContext.test(n)?0:f.length;o--;){if(e=f[o],t.relative[l=e.type])break;if((a=t.find[l])&&(u=a(e.matches[0].replace(y,p),ti.test(f[0].type)&&ri(i.parentNode)||i))){if(f.splice(o,1),!(n=u.length&&pt(f)))return k.apply(r,u),r;break}}}return(c||kt(n,s))(u,i,!h,r,!i||ti.test(n)&&ri(i.parentNode)||i),r},f.sortStable=e.split("").sort(dt).join("")===e,f.detectDuplicates=!!ut,b(),f.sortDetached=a(function(n){return 1&n.compareDocumentPosition(i.createElement("fieldset"))}),a(function(n){return n.innerHTML="<a href='#'><\/a>","#"===n.firstChild.getAttribute("href")})||ii("type|href|height|width",function(n,t,i){if(!i)return n.getAttribute(t,"type"===t.toLowerCase()?1:2)}),f.attributes&&a(function(n){return n.innerHTML="<input/>",n.firstChild.setAttribute("value",""),""===n.firstChild.getAttribute("value")})||ii("value",function(n,t,i){if(!i&&"input"===n.nodeName.toLowerCase())return n.defaultValue}),a(function(n){return null==n.getAttribute("disabled")})||ii(gt,function(n,t,i){var r;if(!i)return!0===n[t]?t.toLowerCase():(r=n.getAttributeNode(t))&&r.specified?r.value:null}),u}(n);i.find=d;i.expr=d.selectors;i.expr[":"]=i.expr.pseudos;i.uniqueSort=i.unique=d.uniqueSort;i.text=d.getText;i.isXMLDoc=d.isXML;i.contains=d.contains;i.escapeSelector=d.escape;var ft=function(n,t,r){for(var u=[],f=void 0!==r;(n=n[t])&&9!==n.nodeType;)if(1===n.nodeType){if(f&&i(n).is(r))break;u.push(n)}return u},dr=function(n,t){for(var i=[];n;n=n.nextSibling)1===n.nodeType&&n!==t&&i.push(n);return i},gr=i.expr.match.needsContext;wi=/^<([a-z][^\/\0>:\x20\t\r\n\f]*)[\x20\t\r\n\f]*\/?>(?:<\/\1>|)$/i;i.filter=function(n,t,r){var u=t[0];return r&&(n=":not("+n+")"),1===t.length&&1===u.nodeType?i.find.matchesSelector(u,n)?[u]:[]:i.find.matches(n,i.grep(t,function(n){return 1===n.nodeType}))};i.fn.extend({find:function(n){var t,r,u=this.length,f=this;if("string"!=typeof n)return this.pushStack(i(n).filter(function(){for(t=0;t<u;t++)if(i.contains(f[t],this))return!0}));for(r=this.pushStack([]),t=0;t<u;t++)i.find(n,f[t],r);return 1<u?i.uniqueSort(r):r},filter:function(n){return this.pushStack(bi(this,n||[],!1))},not:function(n){return this.pushStack(bi(this,n||[],!0))},is:function(n){return!!bi(this,"string"==typeof n&&gr.test(n)?i(n):n||[],!1).length}});tu=/^(?:\s*(<[\w\W]+>)[^>]*|#([\w-]+))$/;(i.fn.init=function(n,t,r){var e,o;if(!n)return this;if(r=r||nu,"string"==typeof n){if(!(e="<"===n[0]&&">"===n[n.length-1]&&3<=n.length?[null,n,null]:tu.exec(n))||!e[1]&&t)return!t||t.jquery?(t||r).find(n):this.constructor(t).find(n);if(e[1]){if(t=t instanceof i?t[0]:t,i.merge(this,i.parseHTML(e[1],t&&t.nodeType?t.ownerDocument||t:f,!0)),wi.test(e[1])&&i.isPlainObject(t))for(e in t)u(this[e])?this[e](t[e]):this.attr(e,t[e]);return this}return(o=f.getElementById(e[2]))&&(this[0]=o,this.length=1),this}return n.nodeType?(this[0]=n,this.length=1,this):u(n)?void 0!==r.ready?r.ready(n):n(i):i.makeArray(n,this)}).prototype=i.fn;nu=i(f);iu=/^(?:parents|prev(?:Until|All))/;ru={children:!0,contents:!0,next:!0,prev:!0};i.fn.extend({has:function(n){var t=i(n,this),r=t.length;return this.filter(function(){for(var n=0;n<r;n++)if(i.contains(this,t[n]))return!0})},closest:function(n,t){var r,f=0,o=this.length,u=[],e="string"!=typeof n&&i(n);if(!gr.test(n))for(;f<o;f++)for(r=this[f];r&&r!==t;r=r.parentNode)if(r.nodeType<11&&(e?-1<e.index(r):1===r.nodeType&&i.find.matchesSelector(r,n))){u.push(r);break}return this.pushStack(1<u.length?i.uniqueSort(u):u)},index:function(n){return n?"string"==typeof n?ii.call(i(n),this[0]):ii.call(this,n.jquery?n[0]:n):this[0]&&this[0].parentNode?this.first().prevAll().length:-1},add:function(n,t){return this.pushStack(i.uniqueSort(i.merge(this.get(),i(n,t))))},addBack:function(n){return this.add(null==n?this.prevObject:this.prevObject.filter(n))}});i.each({parent:function(n){var t=n.parentNode;return t&&11!==t.nodeType?t:null},parents:function(n){return ft(n,"parentNode")},parentsUntil:function(n,t,i){return ft(n,"parentNode",i)},next:function(n){return uu(n,"nextSibling")},prev:function(n){return uu(n,"previousSibling")},nextAll:function(n){return ft(n,"nextSibling")},prevAll:function(n){return ft(n,"previousSibling")},nextUntil:function(n,t,i){return ft(n,"nextSibling",i)},prevUntil:function(n,t,i){return ft(n,"previousSibling",i)},siblings:function(n){return dr((n.parentNode||{}).firstChild,n)},children:function(n){return dr(n.firstChild)},contents:function(n){return null!=n.contentDocument&&vr(n.contentDocument)?n.contentDocument:(c(n,"template")&&(n=n.content||n),i.merge([],n.childNodes))}},function(n,t){i.fn[n]=function(r,u){var f=i.map(this,t,r);return"Until"!==n.slice(-5)&&(u=r),u&&"string"==typeof u&&(f=i.filter(u,f)),1<this.length&&(ru[n]||i.uniqueSort(f),iu.test(n)&&f.reverse()),this.pushStack(f)}});l=/[^\x20\t\r\n\f]+/g;i.Callbacks=function(n){var a,h;n="string"==typeof n?(a=n,h={},i.each(a.match(l)||[],function(n,t){h[t]=!0}),h):i.extend({},n);var o,r,v,f,t=[],s=[],e=-1,y=function(){for(f=f||n.once,v=o=!0;s.length;e=-1)for(r=s.shift();++e<t.length;)!1===t[e].apply(r[0],r[1])&&n.stopOnFalse&&(e=t.length,r=!1);n.memory||(r=!1);o=!1;f&&(t=r?[]:"")},c={add:function(){return t&&(r&&!o&&(e=t.length-1,s.push(r)),function f(r){i.each(r,function(i,r){u(r)?n.unique&&c.has(r)||t.push(r):r&&r.length&&"string"!==ut(r)&&f(r)})}(arguments),r&&!o&&y()),this},remove:function(){return i.each(arguments,function(n,r){for(var u;-1<(u=i.inArray(r,t,u));)t.splice(u,1),u<=e&&e--}),this},has:function(n){return n?-1<i.inArray(n,t):0<t.length},empty:function(){return t&&(t=[]),this},disable:function(){return f=s=[],t=r="",this},disabled:function(){return!t},lock:function(){return f=s=[],r||o||(t=r=""),this},locked:function(){return!!f},fireWith:function(n,t){return f||(t=[n,(t=t||[]).slice?t.slice():t],s.push(t),o||y()),this},fire:function(){return c.fireWith(this,arguments),this},fired:function(){return!!v}};return c};i.extend({Deferred:function(t){var f=[["notify","progress",i.Callbacks("memory"),i.Callbacks("memory"),2],["resolve","done",i.Callbacks("once memory"),i.Callbacks("once memory"),0,"resolved"],["reject","fail",i.Callbacks("once memory"),i.Callbacks("once memory"),1,"rejected"]],o="pending",e={state:function(){return o},always:function(){return r.done(arguments).fail(arguments),this},"catch":function(n){return e.then(null,n)},pipe:function(){var n=arguments;return i.Deferred(function(t){i.each(f,function(i,f){var e=u(n[f[4]])&&n[f[4]];r[f[1]](function(){var n=e&&e.apply(this,arguments);n&&u(n.promise)?n.promise().progress(t.notify).done(t.resolve).fail(t.reject):t[f[0]+"With"](this,e?[n]:arguments)})});n=null}).promise()},then:function(t,r,e){function s(t,r,f,e){return function(){var h=this,c=arguments,l=function(){var n,i;if(!(t<o)){if((n=f.apply(h,c))===r.promise())throw new TypeError("Thenable self-resolution");i=n&&("object"==typeof n||"function"==typeof n)&&n.then;u(i)?e?i.call(n,s(o,r,et,e),s(o,r,fi,e)):(o++,i.call(n,s(o,r,et,e),s(o,r,fi,e),s(o,r,et,r.notifyWith))):(f!==et&&(h=void 0,c=[n]),(e||r.resolveWith)(h,c))}},a=e?l:function(){try{l()}catch(l){i.Deferred.exceptionHook&&i.Deferred.exceptionHook(l,a.stackTrace);o<=t+1&&(f!==fi&&(h=void 0,c=[l]),r.rejectWith(h,c))}};t?a():(i.Deferred.getStackHook&&(a.stackTrace=i.Deferred.getStackHook()),n.setTimeout(a))}}var o=0;return i.Deferred(function(n){f[0][3].add(s(0,n,u(e)?e:et,n.notifyWith));f[1][3].add(s(0,n,u(t)?t:et));f[2][3].add(s(0,n,u(r)?r:fi))}).promise()},promise:function(n){return null!=n?i.extend(n,e):e}},r={};return i.each(f,function(n,t){var i=t[2],u=t[5];e[t[1]]=i.add;u&&i.add(function(){o=u},f[3-n][2].disable,f[3-n][3].disable,f[0][2].lock,f[0][3].lock);i.add(t[3].fire);r[t[0]]=function(){return r[t[0]+"With"](this===r?void 0:this,arguments),this};r[t[0]+"With"]=i.fireWith}),e.promise(r),t&&t.call(r,r),r},when:function(n){var e=arguments.length,t=e,o=Array(t),f=k.call(arguments),r=i.Deferred(),s=function(n){return function(t){o[n]=this;f[n]=1<arguments.length?k.call(arguments):t;--e||r.resolveWith(o,f)}};if(e<=1&&(fu(n,r.done(s(t)).resolve,r.reject,!e),"pending"===r.state()||u(f[t]&&f[t].then)))return r.then();while(t--)fu(f[t],s(t),r.reject);return r.promise()}});eu=/^(Eval|Internal|Range|Reference|Syntax|Type|URI)Error$/;i.Deferred.exceptionHook=function(t,i){n.console&&n.console.warn&&t&&eu.test(t.name)&&n.console.warn("jQuery.Deferred exception: "+t.message,t.stack,i)};i.readyException=function(t){n.setTimeout(function(){throw t;})};ei=i.Deferred();i.fn.ready=function(n){return ei.then(n)["catch"](function(n){i.readyException(n)}),this};i.extend({isReady:!1,readyWait:1,ready:function(n){(!0===n?--i.readyWait:i.isReady)||(i.isReady=!0)!==n&&0<--i.readyWait||ei.resolveWith(f,[i])}});i.ready.then=ei.then;"complete"===f.readyState||"loading"!==f.readyState&&!f.documentElement.doScroll?n.setTimeout(i.ready):(f.addEventListener("DOMContentLoaded",oi),n.addEventListener("load",oi));var w=function(n,t,r,f,e,o,s){var h=0,l=n.length,c=null==r;if("object"===ut(r))for(h in e=!0,r)w(n,t,h,r[h],!0,o,s);else if(void 0!==f&&(e=!0,u(f)||(s=!0),c&&(s?(t.call(n,f),t=null):(c=t,t=function(n,t,r){return c.call(i(n),r)})),t))for(;h<l;h++)t(n[h],r,s?f:f.call(n[h],h,t(n[h],r)));return e?n:c?t.call(n):l?t(n[0],r):o},se=/^-ms-/,he=/-([a-z])/g;ot=function(n){return 1===n.nodeType||9===n.nodeType||!+n.nodeType};bt.uid=1;bt.prototype={cache:function(n){var t=n[this.expando];return t||(t={},ot(n)&&(n.nodeType?n[this.expando]=t:Object.defineProperty(n,this.expando,{value:t,configurable:!0}))),t},set:function(n,t,i){var r,u=this.cache(n);if("string"==typeof t)u[y(t)]=i;else for(r in t)u[y(r)]=t[r];return u},get:function(n,t){return void 0===t?this.cache(n):n[this.expando]&&n[this.expando][y(t)]},access:function(n,t,i){return void 0===t||t&&"string"==typeof t&&void 0===i?this.get(n,t):(this.set(n,t,i),void 0!==i?i:t)},remove:function(n,t){var u,r=n[this.expando];if(void 0!==r){if(void 0!==t)for(u=(t=Array.isArray(t)?t.map(y):(t=y(t))in r?[t]:t.match(l)||[]).length;u--;)delete r[t[u]];(void 0===t||i.isEmptyObject(r))&&(n.nodeType?n[this.expando]=void 0:delete n[this.expando])}},hasData:function(n){var t=n[this.expando];return void 0!==t&&!i.isEmptyObject(t)}};var r=new bt,o=new bt,le=/^(?:\{[\w\W]*\}|\[[\w\W]*\])$/,ae=/[A-Z]/g;i.extend({hasData:function(n){return o.hasData(n)||r.hasData(n)},data:function(n,t,i){return o.access(n,t,i)},removeData:function(n,t){o.remove(n,t)},_data:function(n,t,i){return r.access(n,t,i)},_removeData:function(n,t){r.remove(n,t)}});i.fn.extend({data:function(n,t){var f,u,e,i=this[0],s=i&&i.attributes;if(void 0===n){if(this.length&&(e=o.get(i),1===i.nodeType&&!r.get(i,"hasDataAttrs"))){for(f=s.length;f--;)s[f]&&0===(u=s[f].name).indexOf("data-")&&(u=y(u.slice(5)),ou(i,u,e[u]));r.set(i,"hasDataAttrs",!0)}return e}return"object"==typeof n?this.each(function(){o.set(this,n)}):w(this,function(t){var r;if(i&&void 0===t)return void 0!==(r=o.get(i,n))?r:void 0!==(r=ou(i,n))?r:void 0;this.each(function(){o.set(this,n,t)})},null,t,1<arguments.length,null,!0)},removeData:function(n){return this.each(function(){o.remove(this,n)})}});i.extend({queue:function(n,t,u){var f;if(n)return t=(t||"fx")+"queue",f=r.get(n,t),u&&(!f||Array.isArray(u)?f=r.access(n,t,i.makeArray(u)):f.push(u)),f||[]},dequeue:function(n,t){t=t||"fx";var r=i.queue(n,t),e=r.length,u=r.shift(),f=i._queueHooks(n,t);"inprogress"===u&&(u=r.shift(),e--);u&&("fx"===t&&r.unshift("inprogress"),delete f.stop,u.call(n,function(){i.dequeue(n,t)},f));!e&&f&&f.empty.fire()},_queueHooks:function(n,t){var u=t+"queueHooks";return r.get(n,u)||r.access(n,u,{empty:i.Callbacks("once memory").add(function(){r.remove(n,[t+"queue",u])})})}});i.fn.extend({queue:function(n,t){var r=2;return"string"!=typeof n&&(t=n,n="fx",r--),arguments.length<r?i.queue(this[0],n):void 0===t?this:this.each(function(){var r=i.queue(this,n,t);i._queueHooks(this,n);"fx"===n&&"inprogress"!==r[0]&&i.dequeue(this,n)})},dequeue:function(n){return this.each(function(){i.dequeue(this,n)})},clearQueue:function(n){return this.queue(n||"fx",[])},promise:function(n,t){var u,e=1,o=i.Deferred(),f=this,s=this.length,h=function(){--e||o.resolveWith(f,[f])};for("string"!=typeof n&&(t=n,n=void 0),n=n||"fx";s--;)(u=r.get(f[s],n+"queueHooks"))&&u.empty&&(e++,u.empty.add(h));return h(),o.promise(t)}});var su=/[+-]?(?:\d*\.|)\d+(?:[eE][+-]?\d+|)/.source,kt=new RegExp("^(?:([+-])=|)("+su+")([a-z%]*)$","i"),b=["Top","Right","Bottom","Left"],g=f.documentElement,st=function(n){return i.contains(n.ownerDocument,n)},ve={composed:!0};g.getRootNode&&(st=function(n){return i.contains(n.ownerDocument,n)||n.getRootNode(ve)===n.ownerDocument});dt=function(n,t){return"none"===(n=t||n).style.display||""===n.style.display&&st(n)&&"none"===i.css(n,"display")};ki={};i.fn.extend({show:function(){return ht(this,!0)},hide:function(){return ht(this)},toggle:function(n){return"boolean"==typeof n?n?this.show():this.hide():this.each(function(){dt(this)?i(this).show():i(this).hide()})}});var nt,si,gt=/^(?:checkbox|radio)$/i,cu=/<([a-z][^\/\0>\x20\t\r\n\f]*)/i,lu=/^$|^module$|\/(?:java|ecma)script/i;nt=f.createDocumentFragment().appendChild(f.createElement("div"));(si=f.createElement("input")).setAttribute("type","radio");si.setAttribute("checked","checked");si.setAttribute("name","t");nt.appendChild(si);e.checkClone=nt.cloneNode(!0).cloneNode(!0).lastChild.checked;nt.innerHTML="<textarea>x<\/textarea>";e.noCloneChecked=!!nt.cloneNode(!0).lastChild.defaultValue;nt.innerHTML="<option><\/option>";e.option=!!nt.lastChild;h={thead:[1,"<table>","<\/table>"],col:[2,"<table><colgroup>","<\/colgroup><\/table>"],tr:[2,"<table><tbody>","<\/tbody><\/table>"],td:[3,"<table><tbody><tr>","<\/tr><\/tbody><\/table>"],_default:[0,"",""]};h.tbody=h.tfoot=h.colgroup=h.caption=h.thead;h.th=h.td;e.option||(h.optgroup=h.option=[1,"<select multiple='multiple'>","<\/select>"]);au=/<|&#?\w+;/;var ye=/^key/,pe=/^(?:mouse|pointer|contextmenu|drag|drop)|click/,yu=/^([^.]*)(?:\.(.+)|)/;i.event={global:{},add:function(n,t,u,f,e){var p,a,k,v,w,h,s,c,o,b,d,y=r.get(n);if(ot(n))for(u.handler&&(u=(p=u).handler,e=p.selector),e&&i.find.matchesSelector(g,e),u.guid||(u.guid=i.guid++),(v=y.events)||(v=y.events=Object.create(null)),(a=y.handle)||(a=y.handle=function(t){if("undefined"!=typeof i&&i.event.triggered!==t.type)return i.event.dispatch.apply(n,arguments)}),w=(t=(t||"").match(l)||[""]).length;w--;)o=d=(k=yu.exec(t[w])||[])[1],b=(k[2]||"").split(".").sort(),o&&(s=i.event.special[o]||{},o=(e?s.delegateType:s.bindType)||o,s=i.event.special[o]||{},h=i.extend({type:o,origType:d,data:f,handler:u,guid:u.guid,selector:e,needsContext:e&&i.expr.match.needsContext.test(e),namespace:b.join(".")},p),(c=v[o])||((c=v[o]=[]).delegateCount=0,s.setup&&!1!==s.setup.call(n,f,b,a)||n.addEventListener&&n.addEventListener(o,a)),s.add&&(s.add.call(n,h),h.handler.guid||(h.handler.guid=u.guid)),e?c.splice(c.delegateCount++,0,h):c.push(h),i.event.global[o]=!0)},remove:function(n,t,u,f,e){var y,k,c,v,p,s,h,a,o,b,d,w=r.hasData(n)&&r.get(n);if(w&&(v=w.events)){for(p=(t=(t||"").match(l)||[""]).length;p--;)if(o=d=(c=yu.exec(t[p])||[])[1],b=(c[2]||"").split(".").sort(),o){for(h=i.event.special[o]||{},a=v[o=(f?h.delegateType:h.bindType)||o]||[],c=c[2]&&new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"),k=y=a.length;y--;)s=a[y],!e&&d!==s.origType||u&&u.guid!==s.guid||c&&!c.test(s.namespace)||f&&f!==s.selector&&("**"!==f||!s.selector)||(a.splice(y,1),s.selector&&a.delegateCount--,h.remove&&h.remove.call(n,s));k&&!a.length&&(h.teardown&&!1!==h.teardown.call(n,b,w.handle)||i.removeEvent(n,o,w.handle),delete v[o])}else for(o in v)i.event.remove(n,o+t[p],u,f,!0);i.isEmptyObject(v)&&r.remove(n,"handle events")}},dispatch:function(n){var u,h,c,e,f,l,s=new Array(arguments.length),t=i.event.fix(n),a=(r.get(this,"events")||Object.create(null))[t.type]||[],o=i.event.special[t.type]||{};for(s[0]=t,u=1;u<arguments.length;u++)s[u]=arguments[u];if(t.delegateTarget=this,!o.preDispatch||!1!==o.preDispatch.call(this,t)){for(l=i.event.handlers.call(this,t,a),u=0;(e=l[u++])&&!t.isPropagationStopped();)for(t.currentTarget=e.elem,h=0;(f=e.handlers[h++])&&!t.isImmediatePropagationStopped();)t.rnamespace&&!1!==f.namespace&&!t.rnamespace.test(f.namespace)||(t.handleObj=f,t.data=f.data,void 0!==(c=((i.event.special[f.origType]||{}).handle||f.handler).apply(e.elem,s))&&!1===(t.result=c)&&(t.preventDefault(),t.stopPropagation()));return o.postDispatch&&o.postDispatch.call(this,t),t.result}},handlers:function(n,t){var f,h,u,e,o,c=[],s=t.delegateCount,r=n.target;if(s&&r.nodeType&&!("click"===n.type&&1<=n.button))for(;r!==this;r=r.parentNode||this)if(1===r.nodeType&&("click"!==n.type||!0!==r.disabled)){for(e=[],o={},f=0;f<s;f++)void 0===o[u=(h=t[f]).selector+" "]&&(o[u]=h.needsContext?-1<i(u,this).index(r):i.find(u,this,null,[r]).length),o[u]&&e.push(h);e.length&&c.push({elem:r,handlers:e})}return r=this,s<t.length&&c.push({elem:r,handlers:t.slice(s)}),c},addProp:function(n,t){Object.defineProperty(i.Event.prototype,n,{enumerable:!0,configurable:!0,get:u(t)?function(){if(this.originalEvent)return t(this.originalEvent)}:function(){if(this.originalEvent)return this.originalEvent[n]},set:function(t){Object.defineProperty(this,n,{enumerable:!0,configurable:!0,writable:!0,value:t})}})},fix:function(n){return n[i.expando]?n:new i.Event(n)},special:{load:{noBubble:!0},click:{setup:function(n){var t=this||n;return gt.test(t.type)&&t.click&&c(t,"input")&&hi(t,"click",ct),!1},trigger:function(n){var t=this||n;return gt.test(t.type)&&t.click&&c(t,"input")&&hi(t,"click"),!0},_default:function(n){var t=n.target;return gt.test(t.type)&&t.click&&c(t,"input")&&r.get(t,"click")||c(t,"a")}},beforeunload:{postDispatch:function(n){void 0!==n.result&&n.originalEvent&&(n.originalEvent.returnValue=n.result)}}}};i.removeEvent=function(n,t,i){n.removeEventListener&&n.removeEventListener(t,i)};i.Event=function(n,t){if(!(this instanceof i.Event))return new i.Event(n,t);n&&n.type?(this.originalEvent=n,this.type=n.type,this.isDefaultPrevented=n.defaultPrevented||void 0===n.defaultPrevented&&!1===n.returnValue?ct:lt,this.target=n.target&&3===n.target.nodeType?n.target.parentNode:n.target,this.currentTarget=n.currentTarget,this.relatedTarget=n.relatedTarget):this.type=n;t&&i.extend(this,t);this.timeStamp=n&&n.timeStamp||Date.now();this[i.expando]=!0};i.Event.prototype={constructor:i.Event,isDefaultPrevented:lt,isPropagationStopped:lt,isImmediatePropagationStopped:lt,isSimulated:!1,preventDefault:function(){var n=this.originalEvent;this.isDefaultPrevented=ct;n&&!this.isSimulated&&n.preventDefault()},stopPropagation:function(){var n=this.originalEvent;this.isPropagationStopped=ct;n&&!this.isSimulated&&n.stopPropagation()},stopImmediatePropagation:function(){var n=this.originalEvent;this.isImmediatePropagationStopped=ct;n&&!this.isSimulated&&n.stopImmediatePropagation();this.stopPropagation()}};i.each({altKey:!0,bubbles:!0,cancelable:!0,changedTouches:!0,ctrlKey:!0,detail:!0,eventPhase:!0,metaKey:!0,pageX:!0,pageY:!0,shiftKey:!0,view:!0,char:!0,code:!0,charCode:!0,key:!0,keyCode:!0,button:!0,buttons:!0,clientX:!0,clientY:!0,offsetX:!0,offsetY:!0,pointerId:!0,pointerType:!0,screenX:!0,screenY:!0,targetTouches:!0,toElement:!0,touches:!0,which:function(n){var t=n.button;return null==n.which&&ye.test(n.type)?null!=n.charCode?n.charCode:n.keyCode:!n.which&&void 0!==t&&pe.test(n.type)?1&t?1:2&t?3:4&t?2:0:n.which}},i.event.addProp);i.each({focus:"focusin",blur:"focusout"},function(n,t){i.event.special[n]={setup:function(){return hi(this,n,we),!1},trigger:function(){return hi(this,n),!0},delegateType:t}});i.each({mouseenter:"mouseover",mouseleave:"mouseout",pointerenter:"pointerover",pointerleave:"pointerout"},function(n,t){i.event.special[n]={delegateType:t,bindType:t,handle:function(n){var u,r=n.relatedTarget,f=n.handleObj;return r&&(r===this||i.contains(this,r))||(n.type=f.origType,u=f.handler.apply(this,arguments),n.type=t),u}}});i.fn.extend({on:function(n,t,i,r){return gi(this,n,t,i,r)},one:function(n,t,i,r){return gi(this,n,t,i,r,1)},off:function(n,t,r){var u,f;if(n&&n.preventDefault&&n.handleObj)return u=n.handleObj,i(n.delegateTarget).off(u.namespace?u.origType+"."+u.namespace:u.origType,u.selector,u.handler),this;if("object"==typeof n){for(f in n)this.off(f,t,n[f]);return this}return!1!==t&&"function"!=typeof t||(r=t,t=void 0),!1===r&&(r=lt),this.each(function(){i.event.remove(this,n,r,t)})}});var be=/<script|<style|<link/i,ke=/checked\s*(?:[^=]|=\s*.checked.)/i,de=/^\s*<!(?:\[CDATA\[|--)|(?:\]\]|--)>\s*$/g;i.extend({htmlPrefilter:function(n){return n},clone:function(n,t,r){var u,c,o,f,l,a,v,h=n.cloneNode(!0),y=st(n);if(!(e.noCloneChecked||1!==n.nodeType&&11!==n.nodeType||i.isXMLDoc(n)))for(f=s(h),u=0,c=(o=s(n)).length;u<c;u++)l=o[u],a=f[u],void 0,"input"===(v=a.nodeName.toLowerCase())&&gt.test(l.type)?a.checked=l.checked:"input"!==v&&"textarea"!==v||(a.defaultValue=l.defaultValue);if(t)if(r)for(o=o||s(n),f=f||s(h),u=0,c=o.length;u<c;u++)wu(o[u],f[u]);else wu(n,h);return 0<(f=s(h,"script")).length&&di(f,!y&&s(n,"script")),h},cleanData:function(n){for(var u,t,f,s=i.event.special,e=0;void 0!==(t=n[e]);e++)if(ot(t)){if(u=t[r.expando]){if(u.events)for(f in u.events)s[f]?i.event.remove(t,f):i.removeEvent(t,f,u.handle);t[r.expando]=void 0}t[o.expando]&&(t[o.expando]=void 0)}}});i.fn.extend({detach:function(n){return bu(this,n,!0)},remove:function(n){return bu(this,n)},text:function(n){return w(this,function(n){return void 0===n?i.text(this):this.empty().each(function(){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||(this.textContent=n)})},null,n,arguments.length)},append:function(){return at(this,arguments,function(n){1!==this.nodeType&&11!==this.nodeType&&9!==this.nodeType||pu(this,n).appendChild(n)})},prepend:function(){return at(this,arguments,function(n){if(1===this.nodeType||11===this.nodeType||9===this.nodeType){var t=pu(this,n);t.insertBefore(n,t.firstChild)}})},before:function(){return at(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this)})},after:function(){return at(this,arguments,function(n){this.parentNode&&this.parentNode.insertBefore(n,this.nextSibling)})},empty:function(){for(var n,t=0;null!=(n=this[t]);t++)1===n.nodeType&&(i.cleanData(s(n,!1)),n.textContent="");return this},clone:function(n,t){return n=null!=n&&n,t=null==t?n:t,this.map(function(){return i.clone(this,n,t)})},html:function(n){return w(this,function(n){var t=this[0]||{},r=0,u=this.length;if(void 0===n&&1===t.nodeType)return t.innerHTML;if("string"==typeof n&&!be.test(n)&&!h[(cu.exec(n)||["",""])[1].toLowerCase()]){n=i.htmlPrefilter(n);try{for(;r<u;r++)1===(t=this[r]||{}).nodeType&&(i.cleanData(s(t,!1)),t.innerHTML=n);t=0}catch(n){}}t&&this.empty().append(n)},null,n,arguments.length)},replaceWith:function(){var n=[];return at(this,arguments,function(t){var r=this.parentNode;i.inArray(this,n)<0&&(i.cleanData(s(this)),r&&r.replaceChild(t,this))},n)}});i.each({appendTo:"append",prependTo:"prepend",insertBefore:"before",insertAfter:"after",replaceAll:"replaceWith"},function(n,t){i.fn[n]=function(n){for(var u,f=[],e=i(n),o=e.length-1,r=0;r<=o;r++)u=r===o?this:this.clone(!0),i(e[r])[t](u),yi.apply(f,u.get());return this.pushStack(f)}});var nr=new RegExp("^("+su+")(?!px)[a-z%]+$","i"),ci=function(t){var i=t.ownerDocument.defaultView;return i&&i.opener||(i=n),i.getComputedStyle(t)},ku=function(n,t,i){var u,r,f={};for(r in t)f[r]=n.style[r],n.style[r]=t[r];for(r in u=i.call(n),t)n.style[r]=f[r];return u},to=new RegExp(b.join("|"),"i");!function(){function r(){if(t){s.style.cssText="position:absolute;left:-11111px;width:60px;margin-top:1px;padding:0;border:0";t.style.cssText="position:relative;display:block;box-sizing:border-box;overflow:scroll;margin:auto;border:1px;padding:1px;width:60%;top:1%";g.appendChild(s).appendChild(t);var i=n.getComputedStyle(t);h="1%"!==i.top;v=12===u(i.marginLeft);t.style.right="60%";a=36===u(i.right);c=36===u(i.width);t.style.position="absolute";l=12===u(t.offsetWidth/3);g.removeChild(s);t=null}}function u(n){return Math.round(parseFloat(n))}var h,c,l,a,o,v,s=f.createElement("div"),t=f.createElement("div");t.style&&(t.style.backgroundClip="content-box",t.cloneNode(!0).style.backgroundClip="",e.clearCloneStyle="content-box"===t.style.backgroundClip,i.extend(e,{boxSizingReliable:function(){return r(),c},pixelBoxStyles:function(){return r(),a},pixelPosition:function(){return r(),h},reliableMarginLeft:function(){return r(),v},scrollboxSize:function(){return r(),l},reliableTrDimensions:function(){var t,i,r,u;return null==o&&(t=f.createElement("table"),i=f.createElement("tr"),r=f.createElement("div"),t.style.cssText="position:absolute;left:-11111px",i.style.height="1px",r.style.height="9px",g.appendChild(t).appendChild(i).appendChild(r),u=n.getComputedStyle(i),o=3<parseInt(u.height),g.removeChild(t)),o}}))}();var gu=["Webkit","Moz","ms"],nf=f.createElement("div").style,tf={};var io=/^(none|table(?!-c[ea]).+)/,rf=/^--/,ro={position:"absolute",visibility:"hidden",display:"block"},uf={letterSpacing:"0",fontWeight:"400"};i.extend({cssHooks:{opacity:{get:function(n,t){if(t){var i=ni(n,"opacity");return""===i?"1":i}}}},cssNumber:{animationIterationCount:!0,columnCount:!0,fillOpacity:!0,flexGrow:!0,flexShrink:!0,fontWeight:!0,gridArea:!0,gridColumn:!0,gridColumnEnd:!0,gridColumnStart:!0,gridRow:!0,gridRowEnd:!0,gridRowStart:!0,lineHeight:!0,opacity:!0,order:!0,orphans:!0,widows:!0,zIndex:!0,zoom:!0},cssProps:{},style:function(n,t,r,u){if(n&&3!==n.nodeType&&8!==n.nodeType&&n.style){var f,h,o,c=y(t),l=rf.test(t),s=n.style;if(l||(t=tr(c)),o=i.cssHooks[t]||i.cssHooks[c],void 0===r)return o&&"get"in o&&void 0!==(f=o.get(n,!1,u))?f:s[t];"string"==(h=typeof r)&&(f=kt.exec(r))&&f[1]&&(r=hu(n,t,f),h="number");null!=r&&r==r&&("number"!==h||l||(r+=f&&f[3]||(i.cssNumber[c]?"":"px")),e.clearCloneStyle||""!==r||0!==t.indexOf("background")||(s[t]="inherit"),o&&"set"in o&&void 0===(r=o.set(n,r,u))||(l?s.setProperty(t,r):s[t]=r))}},css:function(n,t,r,u){var f,e,o,s=y(t);return rf.test(t)||(t=tr(s)),(o=i.cssHooks[t]||i.cssHooks[s])&&"get"in o&&(f=o.get(n,!0,r)),void 0===f&&(f=ni(n,t,u)),"normal"===f&&t in uf&&(f=uf[t]),""===r||r?(e=parseFloat(f),!0===r||isFinite(e)?e||0:f):f}});i.each(["height","width"],function(n,t){i.cssHooks[t]={get:function(n,r,u){if(r)return!io.test(i.css(n,"display"))||n.getClientRects().length&&n.getBoundingClientRect().width?ef(n,t,u):ku(n,ro,function(){return ef(n,t,u)})},set:function(n,r,u){var s,f=ci(n),h=!e.scrollboxSize()&&"absolute"===f.position,c=(h||u)&&"border-box"===i.css(n,"boxSizing",!1,f),o=u?ir(n,t,u,c,f):0;return c&&h&&(o-=Math.ceil(n["offset"+t[0].toUpperCase()+t.slice(1)]-parseFloat(f[t])-ir(n,t,"border",!1,f)-.5)),o&&(s=kt.exec(r))&&"px"!==(s[3]||"px")&&(n.style[t]=r,r=i.css(n,t)),ff(0,r,o)}}});i.cssHooks.marginLeft=du(e.reliableMarginLeft,function(n,t){if(t)return(parseFloat(ni(n,"marginLeft"))||n.getBoundingClientRect().left-ku(n,{marginLeft:0},function(){return n.getBoundingClientRect().left}))+"px"});i.each({margin:"",padding:"",border:"Width"},function(n,t){i.cssHooks[n+t]={expand:function(i){for(var r=0,f={},u="string"==typeof i?i.split(" "):[i];r<4;r++)f[n+b[r]+t]=u[r]||u[r-2]||u[0];return f}};"margin"!==n&&(i.cssHooks[n+t].set=ff)});i.fn.extend({css:function(n,t){return w(this,function(n,t,r){var f,e,o={},u=0;if(Array.isArray(t)){for(f=ci(n),e=t.length;u<e;u++)o[t[u]]=i.css(n,t[u],!1,f);return o}return void 0!==r?i.style(n,t,r):i.css(n,t)},n,t,1<arguments.length)}});((i.Tween=a).prototype={constructor:a,init:function(n,t,r,u,f,e){this.elem=n;this.prop=r;this.easing=f||i.easing._default;this.options=t;this.start=this.now=this.cur();this.end=u;this.unit=e||(i.cssNumber[r]?"":"px")},cur:function(){var n=a.propHooks[this.prop];return n&&n.get?n.get(this):a.propHooks._default.get(this)},run:function(n){var t,r=a.propHooks[this.prop];return this.pos=this.options.duration?t=i.easing[this.easing](n,this.options.duration*n,0,1,this.options.duration):t=n,this.now=(this.end-this.start)*t+this.start,this.options.step&&this.options.step.call(this.elem,this.now,this),r&&r.set?r.set(this):a.propHooks._default.set(this),this}}).init.prototype=a.prototype;(a.propHooks={_default:{get:function(n){var t;return 1!==n.elem.nodeType||null!=n.elem[n.prop]&&null==n.elem.style[n.prop]?n.elem[n.prop]:(t=i.css(n.elem,n.prop,""))&&"auto"!==t?t:0},set:function(n){i.fx.step[n.prop]?i.fx.step[n.prop](n):1!==n.elem.nodeType||!i.cssHooks[n.prop]&&null==n.elem.style[tr(n.prop)]?n.elem[n.prop]=n.now:i.style(n.elem,n.prop,n.now+n.unit)}}}).scrollTop=a.propHooks.scrollLeft={set:function(n){n.elem.nodeType&&n.elem.parentNode&&(n.elem[n.prop]=n.now)}};i.easing={linear:function(n){return n},swing:function(n){return.5-Math.cos(n*Math.PI)/2},_default:"swing"};i.fx=a.prototype.init;i.fx.step={};sf=/^(?:toggle|show|hide)$/;hf=/queueHooks$/;i.Animation=i.extend(v,{tweeners:{"*":[function(n,t){var i=this.createTween(n,t);return hu(i.elem,n,kt.exec(t),i),i}]},tweener:function(n,t){u(n)?(t=n,n=["*"]):n=n.match(l);for(var i,r=0,f=n.length;r<f;r++)i=n[r],v.tweeners[i]=v.tweeners[i]||[],v.tweeners[i].unshift(t)},prefilters:[function(n,t,u){var f,y,w,c,b,h,o,l,k="width"in t||"height"in t,v=this,p={},s=n.style,a=n.nodeType&&dt(n),e=r.get(n,"fxshow");for(f in u.queue||(null==(c=i._queueHooks(n,"fx")).unqueued&&(c.unqueued=0,b=c.empty.fire,c.empty.fire=function(){c.unqueued||b()}),c.unqueued++,v.always(function(){v.always(function(){c.unqueued--;i.queue(n,"fx").length||c.empty.fire()})})),t)if(y=t[f],sf.test(y)){if(delete t[f],w=w||"toggle"===y,y===(a?"hide":"show")){if("show"!==y||!e||void 0===e[f])continue;a=!0}p[f]=e&&e[f]||i.style(n,f)}if((h=!i.isEmptyObject(t))||!i.isEmptyObject(p))for(f in k&&1===n.nodeType&&(u.overflow=[s.overflow,s.overflowX,s.overflowY],null==(o=e&&e.display)&&(o=r.get(n,"display")),"none"===(l=i.css(n,"display"))&&(o?l=o:(ht([n],!0),o=n.style.display||o,l=i.css(n,"display"),ht([n]))),("inline"===l||"inline-block"===l&&null!=o)&&"none"===i.css(n,"float")&&(h||(v.done(function(){s.display=o}),null==o&&(l=s.display,o="none"===l?"":l)),s.display="inline-block")),u.overflow&&(s.overflow="hidden",v.always(function(){s.overflow=u.overflow[0];s.overflowX=u.overflow[1];s.overflowY=u.overflow[2]})),h=!1,p)h||(e?"hidden"in e&&(a=e.hidden):e=r.access(n,"fxshow",{display:o}),w&&(e.hidden=!a),a&&ht([n],!0),v.done(function(){for(f in a||ht([n]),r.remove(n,"fxshow"),p)i.style(n,f,p[f])})),h=lf(a?e[f]:0,f,v),f in e||(e[f]=h.start,a&&(h.end=h.start,h.start=0))}],prefilter:function(n,t){t?v.prefilters.unshift(n):v.prefilters.push(n)}});i.speed=function(n,t,r){var f=n&&"object"==typeof n?i.extend({},n):{complete:r||!r&&t||u(n)&&n,duration:n,easing:r&&t||t&&!u(t)&&t};return i.fx.off?f.duration=0:"number"!=typeof f.duration&&(f.duration=f.duration in i.fx.speeds?i.fx.speeds[f.duration]:i.fx.speeds._default),null!=f.queue&&!0!==f.queue||(f.queue="fx"),f.old=f.complete,f.complete=function(){u(f.old)&&f.old.call(this);f.queue&&i.dequeue(this,f.queue)},f};i.fn.extend({fadeTo:function(n,t,i,r){return this.filter(dt).css("opacity",0).show().end().animate({opacity:t},n,i,r)},animate:function(n,t,u,f){var s=i.isEmptyObject(n),o=i.speed(t,u,f),e=function(){var t=v(this,i.extend({},n),o);(s||r.get(this,"finish"))&&t.stop(!0)};return e.finish=e,s||!1===o.queue?this.each(e):this.queue(o.queue,e)},stop:function(n,t,u){var f=function(n){var t=n.stop;delete n.stop;t(u)};return"string"!=typeof n&&(u=t,t=n,n=void 0),t&&this.queue(n||"fx",[]),this.each(function(){var s=!0,t=null!=n&&n+"queueHooks",o=i.timers,e=r.get(this);if(t)e[t]&&e[t].stop&&f(e[t]);else for(t in e)e[t]&&e[t].stop&&hf.test(t)&&f(e[t]);for(t=o.length;t--;)o[t].elem!==this||null!=n&&o[t].queue!==n||(o[t].anim.stop(u),s=!1,o.splice(t,1));!s&&u||i.dequeue(this,n)})},finish:function(n){return!1!==n&&(n=n||"fx"),this.each(function(){var t,e=r.get(this),u=e[n+"queue"],o=e[n+"queueHooks"],f=i.timers,s=u?u.length:0;for(e.finish=!0,i.queue(this,n,[]),o&&o.stop&&o.stop.call(this,!0),t=f.length;t--;)f[t].elem===this&&f[t].queue===n&&(f[t].anim.stop(!0),f.splice(t,1));for(t=0;t<s;t++)u[t]&&u[t].finish&&u[t].finish.call(this);delete e.finish})}});i.each(["toggle","show","hide"],function(n,t){var r=i.fn[t];i.fn[t]=function(n,i,u){return null==n||"boolean"==typeof n?r.apply(this,arguments):this.animate(ai(t,!0),n,i,u)}});i.each({slideDown:ai("show"),slideUp:ai("hide"),slideToggle:ai("toggle"),fadeIn:{opacity:"show"},fadeOut:{opacity:"hide"},fadeToggle:{opacity:"toggle"}},function(n,t){i.fn[n]=function(n,i,r){return this.animate(t,n,i,r)}});i.timers=[];i.fx.tick=function(){var r,n=0,t=i.timers;for(vt=Date.now();n<t.length;n++)(r=t[n])()||t[n]!==r||t.splice(n--,1);t.length||i.fx.stop();vt=void 0};i.fx.timer=function(n){i.timers.push(n);i.fx.start()};i.fx.interval=13;i.fx.start=function(){li||(li=!0,rr())};i.fx.stop=function(){li=null};i.fx.speeds={slow:600,fast:200,_default:400};i.fn.delay=function(t,r){return t=i.fx&&i.fx.speeds[t]||t,r=r||"fx",this.queue(r,function(i,r){var u=n.setTimeout(i,t);r.stop=function(){n.clearTimeout(u)}})};yt=f.createElement("input");of=f.createElement("select").appendChild(f.createElement("option"));yt.type="checkbox";e.checkOn=""!==yt.value;e.optSelected=of.selected;(yt=f.createElement("input")).value="t";yt.type="radio";e.radioValue="t"===yt.value;pt=i.expr.attrHandle;i.fn.extend({attr:function(n,t){return w(this,i.attr,n,t,1<arguments.length)},removeAttr:function(n){return this.each(function(){i.removeAttr(this,n)})}});i.extend({attr:function(n,t,r){var f,u,e=n.nodeType;if(3!==e&&8!==e&&2!==e)return"undefined"==typeof n.getAttribute?i.prop(n,t,r):(1===e&&i.isXMLDoc(n)||(u=i.attrHooks[t.toLowerCase()]||(i.expr.match.bool.test(t)?af:void 0)),void 0!==r?null===r?void i.removeAttr(n,t):u&&"set"in u&&void 0!==(f=u.set(n,r,t))?f:(n.setAttribute(t,r+""),r):u&&"get"in u&&null!==(f=u.get(n,t))?f:null==(f=i.find.attr(n,t))?void 0:f)},attrHooks:{type:{set:function(n,t){if(!e.radioValue&&"radio"===t&&c(n,"input")){var i=n.value;return n.setAttribute("type",t),i&&(n.value=i),t}}}},removeAttr:function(n,t){var i,u=0,r=t&&t.match(l);if(r&&1===n.nodeType)while(i=r[u++])n.removeAttribute(i)}});af={set:function(n,t,r){return!1===t?i.removeAttr(n,r):n.setAttribute(r,r),r}};i.each(i.expr.match.bool.source.match(/\w+/g),function(n,t){var r=pt[t]||i.find.attr;pt[t]=function(n,t,i){var f,e,u=t.toLowerCase();return i||(e=pt[u],pt[u]=f,f=null!=r(n,t,i)?u:null,pt[u]=e),f}});vf=/^(?:input|select|textarea|button)$/i;yf=/^(?:a|area)$/i;i.fn.extend({prop:function(n,t){return w(this,i.prop,n,t,1<arguments.length)},removeProp:function(n){return this.each(function(){delete this[i.propFix[n]||n]})}});i.extend({prop:function(n,t,r){var f,u,e=n.nodeType;if(3!==e&&8!==e&&2!==e)return 1===e&&i.isXMLDoc(n)||(t=i.propFix[t]||t,u=i.propHooks[t]),void 0!==r?u&&"set"in u&&void 0!==(f=u.set(n,r,t))?f:n[t]=r:u&&"get"in u&&null!==(f=u.get(n,t))?f:n[t]},propHooks:{tabIndex:{get:function(n){var t=i.find.attr(n,"tabindex");return t?parseInt(t,10):vf.test(n.nodeName)||yf.test(n.nodeName)&&n.href?0:-1}}},propFix:{"for":"htmlFor","class":"className"}});e.optSelected||(i.propHooks.selected={get:function(n){var t=n.parentNode;return t&&t.parentNode&&t.parentNode.selectedIndex,null},set:function(n){var t=n.parentNode;t&&(t.selectedIndex,t.parentNode&&t.parentNode.selectedIndex)}});i.each(["tabIndex","readOnly","maxLength","cellSpacing","cellPadding","rowSpan","colSpan","useMap","frameBorder","contentEditable"],function(){i.propFix[this.toLowerCase()]=this});i.fn.extend({addClass:function(n){var o,t,r,f,e,s,h,c=0;if(u(n))return this.each(function(t){i(this).addClass(n.call(this,t,it(this)))});if((o=ur(n)).length)while(t=this[c++])if(f=it(t),r=1===t.nodeType&&" "+tt(f)+" "){for(s=0;e=o[s++];)r.indexOf(" "+e+" ")<0&&(r+=e+" ");f!==(h=tt(r))&&t.setAttribute("class",h)}return this},removeClass:function(n){var o,r,t,f,e,s,h,c=0;if(u(n))return this.each(function(t){i(this).removeClass(n.call(this,t,it(this)))});if(!arguments.length)return this.attr("class","");if((o=ur(n)).length)while(r=this[c++])if(f=it(r),t=1===r.nodeType&&" "+tt(f)+" "){for(s=0;e=o[s++];)while(-1<t.indexOf(" "+e+" "))t=t.replace(" "+e+" "," ");f!==(h=tt(t))&&r.setAttribute("class",h)}return this},toggleClass:function(n,t){var f=typeof n,e="string"===f||Array.isArray(n);return"boolean"==typeof t&&e?t?this.addClass(n):this.removeClass(n):u(n)?this.each(function(r){i(this).toggleClass(n.call(this,r,it(this),t),t)}):this.each(function(){var t,o,u,s;if(e)for(o=0,u=i(this),s=ur(n);t=s[o++];)u.hasClass(t)?u.removeClass(t):u.addClass(t);else void 0!==n&&"boolean"!==f||((t=it(this))&&r.set(this,"__className__",t),this.setAttribute&&this.setAttribute("class",t||!1===n?"":r.get(this,"__className__")||""))})},hasClass:function(n){for(var t,r=0,i=" "+n+" ";t=this[r++];)if(1===t.nodeType&&-1<(" "+tt(it(t))+" ").indexOf(i))return!0;return!1}});pf=/\r/g;i.fn.extend({val:function(n){var t,r,e,f=this[0];return arguments.length?(e=u(n),this.each(function(r){var u;1===this.nodeType&&(null==(u=e?n.call(this,r,i(this).val()):n)?u="":"number"==typeof u?u+="":Array.isArray(u)&&(u=i.map(u,function(n){return null==n?"":n+""})),(t=i.valHooks[this.type]||i.valHooks[this.nodeName.toLowerCase()])&&"set"in t&&void 0!==t.set(this,u,"value")||(this.value=u))})):f?(t=i.valHooks[f.type]||i.valHooks[f.nodeName.toLowerCase()])&&"get"in t&&void 0!==(r=t.get(f,"value"))?r:"string"==typeof(r=f.value)?r.replace(pf,""):null==r?"":r:void 0}});i.extend({valHooks:{option:{get:function(n){var t=i.find.attr(n,"value");return null!=t?t:tt(i.text(n))}},select:{get:function(n){for(var e,t,o=n.options,u=n.selectedIndex,f="select-one"===n.type,s=f?null:[],h=f?u+1:o.length,r=u<0?h:f?u:0;r<h;r++)if(((t=o[r]).selected||r===u)&&!t.disabled&&(!t.parentNode.disabled||!c(t.parentNode,"optgroup"))){if(e=i(t).val(),f)return e;s.push(e)}return s},set:function(n,t){for(var r,u,f=n.options,e=i.makeArray(t),o=f.length;o--;)((u=f[o]).selected=-1<i.inArray(i.valHooks.option.get(u),e))&&(r=!0);return r||(n.selectedIndex=-1),e}}}});i.each(["radio","checkbox"],function(){i.valHooks[this]={set:function(n,t){if(Array.isArray(t))return n.checked=-1<i.inArray(i(n).val(),t)}};e.checkOn||(i.valHooks[this].get=function(n){return null===n.getAttribute("value")?"on":n.value})});e.focusin="onfocusin"in n;fr=/^(?:focusinfocus|focusoutblur)$/;er=function(n){n.stopPropagation()};i.extend(i.event,{trigger:function(t,e,o,s){var k,c,l,d,v,y,a,p,w=[o||f],h=ui.call(t,"type")?t.type:t,b=ui.call(t,"namespace")?t.namespace.split("."):[];if(c=p=l=o=o||f,3!==o.nodeType&&8!==o.nodeType&&!fr.test(h+i.event.triggered)&&(-1<h.indexOf(".")&&(h=(b=h.split(".")).shift(),b.sort()),v=h.indexOf(":")<0&&"on"+h,(t=t[i.expando]?t:new i.Event(h,"object"==typeof t&&t)).isTrigger=s?2:3,t.namespace=b.join("."),t.rnamespace=t.namespace?new RegExp("(^|\\.)"+b.join("\\.(?:.*\\.|)")+"(\\.|$)"):null,t.result=void 0,t.target||(t.target=o),e=null==e?[t]:i.makeArray(e,[t]),a=i.event.special[h]||{},s||!a.trigger||!1!==a.trigger.apply(o,e))){if(!s&&!a.noBubble&&!rt(o)){for(d=a.delegateType||h,fr.test(d+h)||(c=c.parentNode);c;c=c.parentNode)w.push(c),l=c;l===(o.ownerDocument||f)&&w.push(l.defaultView||l.parentWindow||n)}for(k=0;(c=w[k++])&&!t.isPropagationStopped();)p=c,t.type=1<k?d:a.bindType||h,(y=(r.get(c,"events")||Object.create(null))[t.type]&&r.get(c,"handle"))&&y.apply(c,e),(y=v&&c[v])&&y.apply&&ot(c)&&(t.result=y.apply(c,e),!1===t.result&&t.preventDefault());return t.type=h,s||t.isDefaultPrevented()||a._default&&!1!==a._default.apply(w.pop(),e)||!ot(o)||v&&u(o[h])&&!rt(o)&&((l=o[v])&&(o[v]=null),i.event.triggered=h,t.isPropagationStopped()&&p.addEventListener(h,er),o[h](),t.isPropagationStopped()&&p.removeEventListener(h,er),i.event.triggered=void 0,l&&(o[v]=l)),t.result}},simulate:function(n,t,r){var u=i.extend(new i.Event,r,{type:n,isSimulated:!0});i.event.trigger(u,null,t)}});i.fn.extend({trigger:function(n,t){return this.each(function(){i.event.trigger(n,t,this)})},triggerHandler:function(n,t){var r=this[0];if(r)return i.event.trigger(n,t,r,!0)}});e.focusin||i.each({focus:"focusin",blur:"focusout"},function(n,t){var u=function(n){i.event.simulate(t,n.target,i.event.fix(n))};i.event.special[t]={setup:function(){var i=this.ownerDocument||this.document||this,f=r.access(i,t);f||i.addEventListener(n,u,!0);r.access(i,t,(f||0)+1)},teardown:function(){var i=this.ownerDocument||this.document||this,f=r.access(i,t)-1;f?r.access(i,t,f):(i.removeEventListener(n,u,!0),r.remove(i,t))}}});var ti=n.location,wf={guid:Date.now()},or=/\?/;i.parseXML=function(t){var r;if(!t||"string"!=typeof t)return null;try{r=(new n.DOMParser).parseFromString(t,"text/xml")}catch(t){r=void 0}return r&&!r.getElementsByTagName("parsererror").length||i.error("Invalid XML: "+t),r};var uo=/\[\]$/,bf=/\r?\n/g,fo=/^(?:submit|button|image|reset|file)$/i,eo=/^(?:input|select|textarea|keygen)/i;i.param=function(n,t){var r,f=[],e=function(n,t){var i=u(t)?t():t;f[f.length]=encodeURIComponent(n)+"="+encodeURIComponent(null==i?"":i)};if(null==n)return"";if(Array.isArray(n)||n.jquery&&!i.isPlainObject(n))i.each(n,function(){e(this.name,this.value)});else for(r in n)sr(r,n[r],t,e);return f.join("&")};i.fn.extend({serialize:function(){return i.param(this.serializeArray())},serializeArray:function(){return this.map(function(){var n=i.prop(this,"elements");return n?i.makeArray(n):this}).filter(function(){var n=this.type;return this.name&&!i(this).is(":disabled")&&eo.test(this.nodeName)&&!fo.test(n)&&(this.checked||!gt.test(n))}).map(function(n,t){var r=i(this).val();return null==r?null:Array.isArray(r)?i.map(r,function(n){return{name:t.name,value:n.replace(bf,"\r\n")}}):{name:t.name,value:r.replace(bf,"\r\n")}}).get()}});var oo=/%20/g,so=/#.*$/,ho=/([?&])_=[^&]*/,co=/^(.*?):[ \t]*([^\r\n]*)$/gm,lo=/^(?:GET|HEAD)$/,ao=/^\/\//,kf={},hr={},df="*/".concat("*"),cr=f.createElement("a");return cr.href=ti.href,i.extend({active:0,lastModified:{},etag:{},ajaxSettings:{url:ti.href,type:"GET",isLocal:/^(?:about|app|app-storage|.+-extension|file|res|widget):$/.test(ti.protocol),global:!0,processData:!0,async:!0,contentType:"application/x-www-form-urlencoded; charset=UTF-8",accepts:{"*":df,text:"text/plain",html:"text/html",xml:"application/xml, text/xml",json:"application/json, text/javascript"},contents:{xml:/\bxml\b/,html:/\bhtml/,json:/\bjson\b/},responseFields:{xml:"responseXML",text:"responseText",json:"responseJSON"},converters:{"* text":String,"text html":!0,"text json":JSON.parse,"text xml":i.parseXML},flatOptions:{url:!0,context:!0}},ajaxSetup:function(n,t){return t?lr(lr(n,i.ajaxSettings),t):lr(i.ajaxSettings,n)},ajaxPrefilter:gf(kf),ajaxTransport:gf(hr),ajax:function(t,r){function b(t,r,f,c){var v,rt,b,p,g,l=r;s||(s=!0,d&&n.clearTimeout(d),a=void 0,k=c||"",e.readyState=0<t?4:0,v=200<=t&&t<300||304===t,f&&(p=function(n,t,i){for(var e,u,f,o,s=n.contents,r=n.dataTypes;"*"===r[0];)r.shift(),void 0===e&&(e=n.mimeType||t.getResponseHeader("Content-Type"));if(e)for(u in s)if(s[u]&&s[u].test(e)){r.unshift(u);break}if(r[0]in i)f=r[0];else{for(u in i){if(!r[0]||n.converters[u+" "+r[0]]){f=u;break}o||(o=u)}f=f||o}if(f)return f!==r[0]&&r.unshift(f),i[f]}(u,e,f)),!v&&-1<i.inArray("script",u.dataTypes)&&(u.converters["text script"]=function(){}),p=function(n,t,i,r){var h,u,f,s,e,o={},c=n.dataTypes.slice();if(c[1])for(f in n.converters)o[f.toLowerCase()]=n.converters[f];for(u=c.shift();u;)if(n.responseFields[u]&&(i[n.responseFields[u]]=t),!e&&r&&n.dataFilter&&(t=n.dataFilter(t,n.dataType)),e=u,u=c.shift())if("*"===u)u=e;else if("*"!==e&&e!==u){if(!(f=o[e+" "+u]||o["* "+u]))for(h in o)if((s=h.split(" "))[1]===u&&(f=o[e+" "+s[0]]||o["* "+s[0]])){!0===f?f=o[h]:!0!==o[h]&&(u=s[0],c.unshift(s[1]));break}if(!0!==f)if(f&&n.throws)t=f(t);else try{t=f(t)}catch(n){return{state:"parsererror",error:f?n:"No conversion from "+e+" to "+u}}}return{state:"success",data:t}}(u,p,e,v),v?(u.ifModified&&((g=e.getResponseHeader("Last-Modified"))&&(i.lastModified[o]=g),(g=e.getResponseHeader("etag"))&&(i.etag[o]=g)),204===t||"HEAD"===u.type?l="nocontent":304===t?l="notmodified":(l=p.state,rt=p.data,v=!(b=p.error))):(b=l,!t&&l||(l="error",t<0&&(t=0))),e.status=t,e.statusText=(r||l)+"",v?tt.resolveWith(h,[rt,l,e]):tt.rejectWith(h,[e,l,b]),e.statusCode(w),w=void 0,y&&nt.trigger(v?"ajaxSuccess":"ajaxError",[e,u,v?rt:b]),it.fireWith(h,[e,l]),y&&(nt.trigger("ajaxComplete",[e,u]),--i.active||i.event.trigger("ajaxStop")))}"object"==typeof t&&(r=t,t=void 0);r=r||{};var a,o,k,v,d,c,s,y,g,p,u=i.ajaxSetup({},r),h=u.context||u,nt=u.context&&(h.nodeType||h.jquery)?i(h):i.event,tt=i.Deferred(),it=i.Callbacks("once memory"),w=u.statusCode||{},rt={},ut={},ft="canceled",e={readyState:0,getResponseHeader:function(n){var t;if(s){if(!v)for(v={};t=co.exec(k);)v[t[1].toLowerCase()+" "]=(v[t[1].toLowerCase()+" "]||[]).concat(t[2]);t=v[n.toLowerCase()+" "]}return null==t?null:t.join(", ")},getAllResponseHeaders:function(){return s?k:null},setRequestHeader:function(n,t){return null==s&&(n=ut[n.toLowerCase()]=ut[n.toLowerCase()]||n,rt[n]=t),this},overrideMimeType:function(n){return null==s&&(u.mimeType=n),this},statusCode:function(n){var t;if(n)if(s)e.always(n[e.status]);else for(t in n)w[t]=[w[t],n[t]];return this},abort:function(n){var t=n||ft;return a&&a.abort(t),b(0,t),this}};if(tt.promise(e),u.url=((t||u.url||ti.href)+"").replace(ao,ti.protocol+"//"),u.type=r.method||r.type||u.method||u.type,u.dataTypes=(u.dataType||"*").toLowerCase().match(l)||[""],null==u.crossDomain){c=f.createElement("a");try{c.href=u.url;c.href=c.href;u.crossDomain=cr.protocol+"//"+cr.host!=c.protocol+"//"+c.host}catch(t){u.crossDomain=!0}}if(u.data&&u.processData&&"string"!=typeof u.data&&(u.data=i.param(u.data,u.traditional)),ne(kf,u,r,e),s)return e;for(g in(y=i.event&&u.global)&&0==i.active++&&i.event.trigger("ajaxStart"),u.type=u.type.toUpperCase(),u.hasContent=!lo.test(u.type),o=u.url.replace(so,""),u.hasContent?u.data&&u.processData&&0===(u.contentType||"").indexOf("application/x-www-form-urlencoded")&&(u.data=u.data.replace(oo,"+")):(p=u.url.slice(o.length),u.data&&(u.processData||"string"==typeof u.data)&&(o+=(or.test(o)?"&":"?")+u.data,delete u.data),!1===u.cache&&(o=o.replace(ho,"$1"),p=(or.test(o)?"&":"?")+"_="+wf.guid+++p),u.url=o+p),u.ifModified&&(i.lastModified[o]&&e.setRequestHeader("If-Modified-Since",i.lastModified[o]),i.etag[o]&&e.setRequestHeader("If-None-Match",i.etag[o])),(u.data&&u.hasContent&&!1!==u.contentType||r.contentType)&&e.setRequestHeader("Content-Type",u.contentType),e.setRequestHeader("Accept",u.dataTypes[0]&&u.accepts[u.dataTypes[0]]?u.accepts[u.dataTypes[0]]+("*"!==u.dataTypes[0]?", "+df+"; q=0.01":""):u.accepts["*"]),u.headers)e.setRequestHeader(g,u.headers[g]);if(u.beforeSend&&(!1===u.beforeSend.call(h,e,u)||s))return e.abort();if(ft="abort",it.add(u.complete),e.done(u.success),e.fail(u.error),a=ne(hr,u,r,e)){if(e.readyState=1,y&&nt.trigger("ajaxSend",[e,u]),s)return e;u.async&&0<u.timeout&&(d=n.setTimeout(function(){e.abort("timeout")},u.timeout));try{s=!1;a.send(rt,b)}catch(t){if(s)throw t;b(-1,t)}}else b(-1,"No Transport");return e},getJSON:function(n,t,r){return i.get(n,t,r,"json")},getScript:function(n,t){return i.get(n,void 0,t,"script")}}),i.each(["get","post"],function(n,t){i[t]=function(n,r,f,e){return u(r)&&(e=e||f,f=r,r=void 0),i.ajax(i.extend({url:n,type:t,dataType:e,data:r,success:f},i.isPlainObject(n)&&n))}}),i.ajaxPrefilter(function(n){for(var t in n.headers)"content-type"===t.toLowerCase()&&(n.contentType=n.headers[t]||"")}),i._evalUrl=function(n,t,r){return i.ajax({url:n,type:"GET",dataType:"script",cache:!0,async:!1,global:!1,converters:{"text script":function(){}},dataFilter:function(n){i.globalEval(n,t,r)}})},i.fn.extend({wrapAll:function(n){var t;return this[0]&&(u(n)&&(n=n.call(this[0])),t=i(n,this[0].ownerDocument).eq(0).clone(!0),this[0].parentNode&&t.insertBefore(this[0]),t.map(function(){for(var n=this;n.firstElementChild;)n=n.firstElementChild;return n}).append(this)),this},wrapInner:function(n){return u(n)?this.each(function(t){i(this).wrapInner(n.call(this,t))}):this.each(function(){var t=i(this),r=t.contents();r.length?r.wrapAll(n):t.append(n)})},wrap:function(n){var t=u(n);return this.each(function(r){i(this).wrapAll(t?n.call(this,r):n)})},unwrap:function(n){return this.parent(n).not("body").each(function(){i(this).replaceWith(this.childNodes)}),this}}),i.expr.pseudos.hidden=function(n){return!i.expr.pseudos.visible(n)},i.expr.pseudos.visible=function(n){return!!(n.offsetWidth||n.offsetHeight||n.getClientRects().length)},i.ajaxSettings.xhr=function(){try{return new n.XMLHttpRequest}catch(t){}},te={0:200,1223:204},wt=i.ajaxSettings.xhr(),e.cors=!!wt&&"withCredentials"in wt,e.ajax=wt=!!wt,i.ajaxTransport(function(t){var i,r;if(e.cors||wt&&!t.crossDomain)return{send:function(u,f){var o,e=t.xhr();if(e.open(t.type,t.url,t.async,t.username,t.password),t.xhrFields)for(o in t.xhrFields)e[o]=t.xhrFields[o];for(o in t.mimeType&&e.overrideMimeType&&e.overrideMimeType(t.mimeType),t.crossDomain||u["X-Requested-With"]||(u["X-Requested-With"]="XMLHttpRequest"),u)e.setRequestHeader(o,u[o]);i=function(n){return function(){i&&(i=r=e.onload=e.onerror=e.onabort=e.ontimeout=e.onreadystatechange=null,"abort"===n?e.abort():"error"===n?"number"!=typeof e.status?f(0,"error"):f(e.status,e.statusText):f(te[e.status]||e.status,e.statusText,"text"!==(e.responseType||"text")||"string"!=typeof e.responseText?{binary:e.response}:{text:e.responseText},e.getAllResponseHeaders()))}};e.onload=i();r=e.onerror=e.ontimeout=i("error");void 0!==e.onabort?e.onabort=r:e.onreadystatechange=function(){4===e.readyState&&n.setTimeout(function(){i&&r()})};i=i("abort");try{e.send(t.hasContent&&t.data||null)}catch(u){if(i)throw u;}},abort:function(){i&&i()}}}),i.ajaxPrefilter(function(n){n.crossDomain&&(n.contents.script=!1)}),i.ajaxSetup({accepts:{script:"text/javascript, application/javascript, application/ecmascript, application/x-ecmascript"},contents:{script:/\b(?:java|ecma)script\b/},converters:{"text script":function(n){return i.globalEval(n),n}}}),i.ajaxPrefilter("script",function(n){void 0===n.cache&&(n.cache=!1);n.crossDomain&&(n.type="GET")}),i.ajaxTransport("script",function(n){var r,t;if(n.crossDomain||n.scriptAttrs)return{send:function(u,e){r=i("<script>").attr(n.scriptAttrs||{}).prop({charset:n.scriptCharset,src:n.url}).on("load error",t=function(n){r.remove();t=null;n&&e("error"===n.type?404:200,n.type)});f.head.appendChild(r[0])},abort:function(){t&&t()}}}),ar=[],vi=/(=)\?(?=&|$)|\?\?/,i.ajaxSetup({jsonp:"callback",jsonpCallback:function(){var n=ar.pop()||i.expando+"_"+wf.guid++;return this[n]=!0,n}}),i.ajaxPrefilter("json jsonp",function(t,r,f){var e,o,s,h=!1!==t.jsonp&&(vi.test(t.url)?"url":"string"==typeof t.data&&0===(t.contentType||"").indexOf("application/x-www-form-urlencoded")&&vi.test(t.data)&&"data");if(h||"jsonp"===t.dataTypes[0])return e=t.jsonpCallback=u(t.jsonpCallback)?t.jsonpCallback():t.jsonpCallback,h?t[h]=t[h].replace(vi,"$1"+e):!1!==t.jsonp&&(t.url+=(or.test(t.url)?"&":"?")+t.jsonp+"="+e),t.converters["script json"]=function(){return s||i.error(e+" was not called"),s[0]},t.dataTypes[0]="json",o=n[e],n[e]=function(){s=arguments},f.always(function(){void 0===o?i(n).removeProp(e):n[e]=o;t[e]&&(t.jsonpCallback=r.jsonpCallback,ar.push(e));s&&u(o)&&o(s[0]);s=o=void 0}),"script"}),e.createHTMLDocument=((ie=f.implementation.createHTMLDocument("").body).innerHTML="<form><\/form><form><\/form>",2===ie.childNodes.length),i.parseHTML=function(n,t,r){return"string"!=typeof n?[]:("boolean"==typeof t&&(r=t,t=!1),t||(e.createHTMLDocument?((s=(t=f.implementation.createHTMLDocument("")).createElement("base")).href=f.location.href,t.head.appendChild(s)):t=f),u=!r&&[],(o=wi.exec(n))?[t.createElement(o[1])]:(o=vu([n],t,u),u&&u.length&&i(u).remove(),i.merge([],o.childNodes)));var s,o,u},i.fn.load=function(n,t,r){var f,s,h,e=this,o=n.indexOf(" ");return-1<o&&(f=tt(n.slice(o)),n=n.slice(0,o)),u(t)?(r=t,t=void 0):t&&"object"==typeof t&&(s="POST"),0<e.length&&i.ajax({url:n,type:s||"GET",dataType:"html",data:t}).done(function(n){h=arguments;e.html(f?i("<div>").append(i.parseHTML(n)).find(f):n)}).always(r&&function(n,t){e.each(function(){r.apply(this,h||[n.responseText,t,n])})}),this},i.expr.pseudos.animated=function(n){return i.grep(i.timers,function(t){return n===t.elem}).length},i.offset={setOffset:function(n,t,r){var v,o,s,h,e,c,l=i.css(n,"position"),a=i(n),f={};"static"===l&&(n.style.position="relative");e=a.offset();s=i.css(n,"top");c=i.css(n,"left");("absolute"===l||"fixed"===l)&&-1<(s+c).indexOf("auto")?(h=(v=a.position()).top,o=v.left):(h=parseFloat(s)||0,o=parseFloat(c)||0);u(t)&&(t=t.call(n,r,i.extend({},e)));null!=t.top&&(f.top=t.top-e.top+h);null!=t.left&&(f.left=t.left-e.left+o);"using"in t?t.using.call(n,f):("number"==typeof f.top&&(f.top+="px"),"number"==typeof f.left&&(f.left+="px"),a.css(f))}},i.fn.extend({offset:function(n){if(arguments.length)return void 0===n?this:this.each(function(t){i.offset.setOffset(this,n,t)});var r,u,t=this[0];if(t)return t.getClientRects().length?(r=t.getBoundingClientRect(),u=t.ownerDocument.defaultView,{top:r.top+u.pageYOffset,left:r.left+u.pageXOffset}):{top:0,left:0}},position:function(){if(this[0]){var n,r,u,t=this[0],f={top:0,left:0};if("fixed"===i.css(t,"position"))r=t.getBoundingClientRect();else{for(r=this.offset(),u=t.ownerDocument,n=t.offsetParent||u.documentElement;n&&(n===u.body||n===u.documentElement)&&"static"===i.css(n,"position");)n=n.parentNode;n&&n!==t&&1===n.nodeType&&((f=i(n).offset()).top+=i.css(n,"borderTopWidth",!0),f.left+=i.css(n,"borderLeftWidth",!0))}return{top:r.top-f.top-i.css(t,"marginTop",!0),left:r.left-f.left-i.css(t,"marginLeft",!0)}}},offsetParent:function(){return this.map(function(){for(var n=this.offsetParent;n&&"static"===i.css(n,"position");)n=n.offsetParent;return n||g})}}),i.each({scrollLeft:"pageXOffset",scrollTop:"pageYOffset"},function(n,t){var r="pageYOffset"===t;i.fn[n]=function(i){return w(this,function(n,i,u){var f;if(rt(n)?f=n:9===n.nodeType&&(f=n.defaultView),void 0===u)return f?f[t]:n[i];f?f.scrollTo(r?f.pageXOffset:u,r?u:f.pageYOffset):n[i]=u},n,i,arguments.length)}}),i.each(["top","left"],function(n,t){i.cssHooks[t]=du(e.pixelPosition,function(n,r){if(r)return r=ni(n,t),nr.test(r)?i(n).position()[t]+"px":r})}),i.each({Height:"height",Width:"width"},function(n,t){i.each({padding:"inner"+n,content:t,"":"outer"+n},function(r,u){i.fn[u]=function(f,e){var o=arguments.length&&(r||"boolean"!=typeof f),s=r||(!0===f||!0===e?"margin":"border");return w(this,function(t,r,f){var e;return rt(t)?0===u.indexOf("outer")?t["inner"+n]:t.document.documentElement["client"+n]:9===t.nodeType?(e=t.documentElement,Math.max(t.body["scroll"+n],e["scroll"+n],t.body["offset"+n],e["offset"+n],e["client"+n])):void 0===f?i.css(t,r,s):i.style(t,r,f,s)},t,o?f:void 0,o)}})}),i.each(["ajaxStart","ajaxStop","ajaxComplete","ajaxError","ajaxSuccess","ajaxSend"],function(n,t){i.fn[t]=function(n){return this.on(t,n)}}),i.fn.extend({bind:function(n,t,i){return this.on(n,null,t,i)},unbind:function(n,t){return this.off(n,null,t)},delegate:function(n,t,i,r){return this.on(t,n,i,r)},undelegate:function(n,t,i){return 1===arguments.length?this.off(n,"**"):this.off(t,n||"**",i)},hover:function(n,t){return this.mouseenter(n).mouseleave(t||n)}}),i.each("blur focus focusin focusout resize scroll click dblclick mousedown mouseup mousemove mouseover mouseout mouseenter mouseleave change select submit keydown keypress keyup contextmenu".split(" "),function(n,t){i.fn[t]=function(n,i){return 0<arguments.length?this.on(t,null,n,i):this.trigger(t)}}),re=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,i.proxy=function(n,t){var r,f,e;if("string"==typeof t&&(r=n[t],t=n,n=r),u(n))return f=k.call(arguments,2),(e=function(){return n.apply(t||this,f.concat(k.call(arguments)))}).guid=n.guid=n.guid||i.guid++,e},i.holdReady=function(n){n?i.readyWait++:i.ready(!0)},i.isArray=Array.isArray,i.parseJSON=JSON.parse,i.nodeName=c,i.isFunction=u,i.isWindow=rt,i.camelCase=y,i.type=ut,i.now=Date.now,i.isNumeric=function(n){var t=i.type(n);return("number"===t||"string"===t)&&!isNaN(n-parseFloat(n))},i.trim=function(n){return null==n?"":(n+"").replace(re,"")},"function"==typeof define&&define.amd&&define("jquery",[],function(){return i}),ue=n.jQuery,fe=n.$,i.noConflict=function(t){return n.$===i&&(n.$=fe),t&&n.jQuery===i&&(n.jQuery=ue),i},"undefined"==typeof t&&(n.jQuery=n.$=i),i}),PageLoadTime.prototype.getPerformanceData=function(){var n=window.performance||{},t={};return n.navigation&&(t.navigation=_getPerformanceObjectData(n.navigation)),n.timing&&(t.timing=_getPerformanceObjectData(n.timing)),n.getEntries&&(t.entries=_mapObject(n.getEntries(),_getPerformanceObjectData)),navigator.connection&&(t.connection=_getPerformanceObjectData(navigator.connection)),t},$trace=function(n,t){var i=n,r=(new Date).getTime(),f=t,u=0;return{toJson:function(){if(f){var n=new Date-r;u=n}return{ac:i,acST:Math.floor(r/1e3),acD:u}},append:function(n){i+=n}}},$diags=function(n,t){var i=[];return setTimeout(function(){var r,f;if(!window.$diags.initializationSuccessful){var t=new n("T000",!1),u="UX001",i="";window.jQuery&&typeof $element!="undefined"||(t=new n("T025",!1),r=window.jQuery?"element":"jQuery",f=document.querySelectorAll('[data-script="'+r+'"]'),i=f[0].src||r,t.append(" - "+i),u="UX002");window.$diags.trace(t);window.$diags.sendDiagnostics(u,i)}},window.initializationTimeout||7e3),{VERSION:"2.0.0",initializationSuccessful:!1,sendDiagnostics:function(n,t,i){window.location.replace(this.toDiagnosticsPath(n,t,i))},sendQuietDiagnostics:function(n,t,i){$.ajax({url:this.toDiagnosticsPath(n,t,i)})},toDiagnosticsPath:function(n,i,r){var f=this.toJson(n,r),u=t.hosts.tenant+"/api/"+t.api+"/error?code="+n+"&diags="+encodeURIComponent(f)+"&csrf_token="+t.csrf+"&tx="+t.transId+"&p="+t.hosts.policy;return i&&(u+="&desc="+encodeURIComponent(i)),u},toJson:function(n,r){for(var e=JSON.stringify(i),f=!1,u;i.length!==0&&e.length>window.maxTrace;)i.shift(),e=JSON.stringify(i),f=!0;return u={pageViewId:t.pageViewId,pageId:t.api,trace:i},f&&(u.truncated=f),n&&(u.code=n),r&&(u.info=r),JSON.stringify(u)},trace:function(n){i.push(n.toJson());return},toConsole:function(){for(var n=0,t=i.length;n<t;n++);return},traceStack:i}}($trace,SETTINGS),function(){var n=function(){var t=window.navigator!==undefined&&window.navigator.onLine!==undefined?window.navigator.onLine?"Online":"Offline":"NotSupported",n=new $trace("T030",!0);n.append(t);$diags.trace(n)};window.addEventListener("online",n);window.addEventListener("offline",n)}(),$santizer=function(n,t){var i=function(n){return t.pageMode===1?n:function(t){try{return sanitizeHtml(n,SETTINGS.sanitizerPolicy)}catch(i){t.append(" - err:"+i.message);$diags.trace(t);$diags.sendDiagnostics("UX017",SETTINGS.remoteResource)}finally{$diags.trace(t)}}(new $trace("T029",!0))};return{santize:i}}($santizer,SETTINGS),typeof navigator.cookieEnabled=="undefined"&&(document.cookie="probe",navigator.cookieEnabled=document.cookie.indexOf("probe")!==-1?!0:!1),window.contentReady,window.pageReady=!1,function(){document.querySelectorAll||(document.querySelectorAll=function(n){var i=i=document.createStyleSheet(),r=document.all,f=r.length,t,u=[];for(i.addRule(n,"k:v"),t=0;t<f;t+=1)if(r[t].currentStyle.k==="v"&&(u.push(r[t]),u.length>Infinity))break;return i.removeRule(0),u});String.prototype.trim||function(){var n=/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g;String.prototype.trim=function(){return this.replace(n,"")}}()}(),navigator.cookieEnabled){preloadCssLink=[],function(){for(var n,u=function(){},t=["assert","clear","count","debug","dir","dirxml","error","exception","group","groupCollapsed","groupEnd","info","log","markTimeline","profile","profileEnd","table","time","timeEnd","timeStamp","trace","warn"],i=t.length,r=window.console=window.console||{};i--;)n=t[i],r[n]||(r[n]=u)}();String.prototype.endsWith||(String.prototype.endsWith=function(n){var t=this.length-n.length;return t>=0&&this.lastIndexOf(n)===t});var checkCssPreloaded=function(){preloadCssLink=preloadCssLink.filter(function(n){for(var t=0;t<document.styleSheets.length;t++)try{if(document.styleSheets[t].href!=null&&document.styleSheets[t].href.endsWith(n)&&document.styleSheets[t].cssRules)return!1}catch(i){if(i.code!==DOMException.INVALID_ACCESS_ERR&&i.name!=="Error")throw i;else return!0}return!0});preloadCssLink.length===0?$("body").show():setTimeout(function(){checkCssPreloaded()},0)},preloadJavaScript=function(n){var r=function(t){var i=$(n[t]),r=i.attr("src");if(r)return $.ajax({method:"GET",url:r,dataType:"text"}).done(function(n){i.text(n);i.removeAttr("src")}).fail(function(){})},i=[],t;if(n)for(t=0;t<n.length;t++)i.push(r(t));return $.when.apply($,i)},$cors=function(){var n,t=function(t){(function(i){var e,o,f;try{e=document.createElement("html");e.innerHTML=$santizer.santize(n);var u=$(e).find("head:first"),r=$(e).find("body:first"),s=u.find("script"),h=[];for(o=0;o<s.length;o++)f=s[o],f.getAttribute("src")&&f.getAttribute("data-preload")&&f.getAttribute("data-preload").toLowerCase()==="true"&&h.push(f);preloadJavaScript(h).then(function(){var e,f,n;u.find("title").length>0&&$("title").remove();var o="link[rel$='icon']",i=u.find(o);for(i&&i[0]&&(i[0].getAttribute("href")===undefined&&i[0].setAttribute("href","data:;base64,iVBORw0KGgo="),$("head").find(o).remove()),e=u.find("link"),f=0;f<e.length;f++)n=e[f],n.getAttribute("rel")==="stylesheet"&&n.getAttribute("href")&&n.getAttribute("data-preload")&&n.getAttribute("data-preload").toLowerCase()==="true"&&(n.setAttribute("crossorigin","anonymous"),$("body").hide(),preloadCssLink.push(n.getAttribute("href")));$("head").prepend(u.html());applyTenantBranding(r),function(n){var t=r.find("#api"),i=$($element.getElementContent()).html(),u=t[0].attributes;t.replaceWith($(i));t=r.find("#api");$.each(u,function(){this.name!=="id"&&t.attr(this.name,this.value)});r=$(r.html().replace(/\x3Cscript>/g,"&lt;script>").replace(/\x3C\/script>/g,"&lt;/script>").replace(/\x3Ciframe>/g,"&lt;iframe>").replace(/\x3C\/iframe>/g,"&lt;/iframe>").replace(/\x3Cimg>/g,"&lt;img>").replace(/\x3C\/img>/g,"&lt;/img>"));$diags.trace(n)}(new $trace("T004",!0));setTimeout(function(){$("body").append(r);$.isFunction(t)&&t()},0);$("table[class=panel_layout]").attr({"aria-label":document.title})})}catch(c){i.append(" - err:"+c.message)}finally{$diags.trace(i)}})(new $trace("T019",!0))};return function i(){(function(t){var f=!window.XDomainRequest,r=f?new XMLHttpRequest:new XDomainRequest,u=SETTINGS.remoteResource,e;u.indexOf(window.staticHost)===0&&(e=u.indexOf("?")===-1?"?":"&",u+=e+"slice="+window.targetSlice,u+="&dc="+window.targetDc);r.open("GET",u);r.onload=function(){if(!f||f&&r.status===200){n=r.responseText;window.contentReady=!0;t.append(" - URL:"+u);$diags.trace(t);return}if(r.status===404){var i=new $trace("T028",!1);$diags.trace(i);$diags.trace(t);$diags.sendDiagnostics("UX005",SETTINGS.remoteResource)}return};r.onerror=function(){var i=0,n;f&&(i=r.status);n=new $trace("T027",!1);$diags.trace(n);$diags.trace(t);$diags.sendDiagnostics("UX004",SETTINGS.remoteResource);return};r.ontimeout=function(){if(this.tryCount++,this.tryCount<=this.retryLimit){i();return}var n=new $trace("T026",!1);$diags.trace(n);$diags.trace(t);$diags.sendDiagnostics("UX003",SETTINGS.remoteResource);return};r.onprogress=function(){};r.timeout=(window.initializationTimeout||7e3)-200;r.send()})(new $trace("T021",!0))}(),{process:t}}($cors,SETTINGS),applyTenantBranding=function(n){var f="tenantBranding",i,r,u;if(SETTINGS[f]){var t=SETTINGS[f],e="backgroundColor",o="backgroundImageUrl",s="bannerLogoUrl",h=n.find("div[data-tenant-branding-background-color=true]");t[e]&&h.length>0&&(i=document.createElement("style"),i.innerHTML="div[data-tenant-branding-background-color=true]#background_branding_container {background:"+t[e]+"}",$("head").append(i));r=n.find("img[data-tenant-branding-background=true]");t[o]&&r.length>0&&r.attr("src",t[o]);u=n.find("img[data-tenant-branding-logo=true]");t[s]&&u.length>0&&u.attr("src",t[s]).show()}},sendPageLoadTime=function(){var t=new PageLoadTime,i=JSON.stringify(t.getPerformanceData()),n=jQuery.extend(!0,{},window.SETTINGS);n.contentType="application/json; charset=utf-8";n.api="client";$i2e.sendDataWithRetry(i,function(){},function(){$diags.trace(new $trace("T035",!1))},function(){},"perftrace",n)};(function defer(){window.jQuery&&typeof $element!="undefined"?function n(){window.contentReady?$cors.process(function(){$element.initialize();$diags.initializationSuccessful=!0;window.pageReady=!0;setTimeout(sendPageLoadTime,0);checkCssPreloaded()}):setTimeout(n,0)}():setTimeout(function(){defer()},50)})()}
/**!
 @license
 handlebars v4.7.7
Copyright (C) 2011-2019 by Yehuda Katz
Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:
The above copyright notice and this permission notice shall be included in
all copies or substantial portions of the Software.
THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN
THE SOFTWARE.
*/
(function(n,t){typeof exports=="object"&&typeof module=="object"?module.exports=t():typeof define=="function"&&define.amd?define([],t):typeof exports=="object"?exports.Handlebars=t():n.Handlebars=t()})(this,function(){return function(n){function t(r){if(i[r])return i[r].exports;var u=i[r]={exports:{},id:r,loaded:!1};return n[r].call(u.exports,u,u.exports,t),u.loaded=!0,u.exports}var i={};return t.m=n,t.c=i,t.p="",t(0)}([function(n,t,i){"use strict";function h(){var n=new o.HandlebarsEnvironment;return e.extend(n,o),n.SafeString=a["default"],n.Exception=y["default"],n.Utils=e,n.escapeExpression=e.escapeExpression,n.VM=s,n.template=function(t){return s.template(t,n)},n}var u=i(1)["default"],f=i(2)["default"],r;t.__esModule=!0;var c=i(3),o=u(c),l=i(36),a=f(l),v=i(5),y=f(v),p=i(4),e=u(p),w=i(37),s=u(w),b=i(43),k=f(b);r=h();r.create=h;k["default"](r);r["default"]=r;t["default"]=r;n.exports=t["default"]},function(n,t){"use strict";t["default"]=function(n){var t,i;if(n&&n.__esModule)return n;if(t={},n!=null)for(i in n)Object.prototype.hasOwnProperty.call(n,i)&&(t[i]=n[i]);return t["default"]=n,t};t.__esModule=!0},function(n,t){"use strict";t["default"]=function(n){return n&&n.__esModule?n:{"default":n}};t.__esModule=!0},function(n,t,i){"use strict";function o(n,t,i){this.helpers=n||{};this.partials=t||{};this.decorators=i||{};y.registerDefaultHelpers(this);p.registerDefaultDecorators(this)}var s=i(2)["default"],h,c,l,f,a;t.__esModule=!0;t.HandlebarsEnvironment=o;var r=i(4),v=i(5),e=s(v),y=i(9),p=i(29),w=i(31),u=s(w),b=i(32);t.VERSION="4.7.7";h=8;t.COMPILER_REVISION=h;c=7;t.LAST_COMPATIBLE_COMPILER_REVISION=c;l={1:"<= 1.0.rc.2",2:"== 1.0.0-rc.3",3:"== 1.0.0-rc.4",4:"== 1.x.x",5:"== 2.0.0-alpha.x",6:">= 2.0.0-beta.1",7:">= 4.0.0 <4.3.0",8:">= 4.3.0"};t.REVISION_CHANGES=l;f="[object Object]";o.prototype={constructor:o,logger:u["default"],log:u["default"].log,registerHelper:function(n,t){if(r.toString.call(n)===f){if(t)throw new e["default"]("Arg not supported with multiple helpers");r.extend(this.helpers,n)}else this.helpers[n]=t},unregisterHelper:function(n){delete this.helpers[n]},registerPartial:function(n,t){if(r.toString.call(n)===f)r.extend(this.partials,n);else{if(typeof t=="undefined")throw new e["default"]('Attempting to register a partial called "'+n+'" as undefined');this.partials[n]=t}},unregisterPartial:function(n){delete this.partials[n]},registerDecorator:function(n,t){if(r.toString.call(n)===f){if(t)throw new e["default"]("Arg not supported with multiple decorators");r.extend(this.decorators,n)}else this.decorators[n]=t},unregisterDecorator:function(n){delete this.decorators[n]},resetLoggedPropertyAccesses:function(){b.resetLoggedProperties()}};a=u["default"].log;t.log=a;t.createFrame=r.createFrame;t.logger=u["default"]},function(n,t){"use strict";function h(n){return e[n]}function f(n){for(var i,t=1;t<arguments.length;t++)for(i in arguments[t])Object.prototype.hasOwnProperty.call(arguments[t],i)&&(n[i]=arguments[t][i]);return n}function c(n,t){for(var i=0,r=n.length;i<r;i++)if(n[i]===t)return i;return-1}function l(n){if(typeof n!="string"){if(n&&n.toHTML)return n.toHTML();if(n==null)return"";if(!n)return n+"";n=""+n}return s.test(n)?n.replace(o,h):n}function a(n){return n||n===0?u(n)&&n.length===0?!0:!1:!0}function v(n){var t=f({},n);return t._parent=n,t}function y(n,t){return n.path=t,n}function p(n,t){return(n?n+".":"")+t}var i,r,u;t.__esModule=!0;t.extend=f;t.indexOf=c;t.escapeExpression=l;t.isEmpty=a;t.createFrame=v;t.blockParams=y;t.appendContextPath=p;var e={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#x27;","`":"&#x60;","=":"&#x3D;"},o=/[&<>"'`=]/g,s=/[&<>"'`=]/;i=Object.prototype.toString;t.toString=i;r=function(n){return typeof n=="function"};r(/x/)&&(t.isFunction=r=function(n){return typeof n=="function"&&i.call(n)==="[object Function]"});t.isFunction=r;u=Array.isArray||function(n){return n&&typeof n=="object"?i.call(n)==="[object Array]":!1};t.isArray=u},function(n,t,i){"use strict";function u(n,t){var i=t&&t.loc,s=undefined,c=undefined,o=undefined,h=undefined,l,e;for(i&&(s=i.start.line,c=i.end.line,o=i.start.column,h=i.end.column,n+=" - "+s+":"+o),l=Error.prototype.constructor.call(this,n),e=0;e<r.length;e++)this[r[e]]=l[r[e]];Error.captureStackTrace&&Error.captureStackTrace(this,u);try{i&&(this.lineNumber=s,this.endLineNumber=c,f?(Object.defineProperty(this,"column",{value:o,enumerable:!0}),Object.defineProperty(this,"endColumn",{value:h,enumerable:!0})):(this.column=o,this.endColumn=h))}catch(a){}}var f=i(6)["default"],r;t.__esModule=!0;r=["description","fileName","lineNumber","endLineNumber","message","name","number","stack"];u.prototype=new Error;t["default"]=u;n.exports=t["default"]},function(n,t,i){n.exports={"default":i(7),__esModule:!0}},function(n,t,i){var r=i(8);n.exports=function(n,t,i){return r.setDesc(n,t,i)}},function(n){var t=Object;n.exports={create:t.create,getProto:t.getPrototypeOf,isEnum:{}.propertyIsEnumerable,getDesc:t.getOwnPropertyDescriptor,setDesc:t.defineProperty,setDescs:t.defineProperties,getKeys:t.keys,getNames:t.getOwnPropertyNames,getSymbols:t.getOwnPropertySymbols,each:[].forEach}},function(n,t,i){"use strict";function k(n){f["default"](n);o["default"](n);h["default"](n);l["default"](n);v["default"](n);p["default"](n);b["default"](n)}function d(n,t,i){n.helpers[t]&&(n.hooks[t]=n.helpers[t],i||delete n.helpers[t])}var r=i(2)["default"];t.__esModule=!0;t.registerDefaultHelpers=k;t.moveHelperToHooks=d;var u=i(10),f=r(u),e=i(11),o=r(e),s=i(24),h=r(s),c=i(25),l=r(c),a=i(26),v=r(a),y=i(27),p=r(y),w=i(28),b=r(w)},function(n,t,i){"use strict";t.__esModule=!0;var r=i(4);t["default"]=function(n){n.registerHelper("blockHelperMissing",function(t,i){var f=i.inverse,e=i.fn,u;return t===!0?e(this):t===!1||t==null?f(this):r.isArray(t)?t.length>0?(i.ids&&(i.ids=[i.name]),n.helpers.each(t,i)):f(this):(i.data&&i.ids&&(u=r.createFrame(i.data),u.contextPath=r.appendContextPath(i.data.contextPath,i.name),i={data:u}),e(t,i))})};n.exports=t["default"]},function(n,t,i){(function(r){"use strict";var f=i(12)["default"],e=i(2)["default"];t.__esModule=!0;var u=i(4),o=i(5),s=e(o);t["default"]=function(n){n.registerHelper("each",function(n,t){function c(t,i,r){e&&(e.key=t,e.index=i,e.first=i===0,e.last=!!r,h&&(e.contextPath=h+t));o=o+p(n[t],{data:e,blockParams:u.blockParams([n[t],t],[h+t,null])})}var v,y,a,l;if(!t)throw new s["default"]("Must pass iterator to #each");var p=t.fn,w=t.inverse,i=0,o="",e=undefined,h=undefined;if(t.data&&t.ids&&(h=u.appendContextPath(t.data.contextPath,t.ids[0])+"."),u.isFunction(n)&&(n=n.call(this)),t.data&&(e=u.createFrame(t.data)),n&&typeof n=="object")if(u.isArray(n))for(l=n.length;i<l;i++)i in n&&c(i,i,i===n.length-1);else if(r.Symbol&&n[r.Symbol.iterator]){for(v=[],y=n[r.Symbol.iterator](),a=y.next();!a.done;a=y.next())v.push(a.value);for(n=v,l=n.length;i<l;i++)c(i,i,i===n.length-1)}else(function(){var t=undefined;f(n).forEach(function(n){t!==undefined&&c(t,i-1);t=n;i++});t!==undefined&&c(t,i-1,!0)})();return i===0&&(o=w(this)),o})};n.exports=t["default"]}).call(t,function(){return this}())},function(n,t,i){n.exports={"default":i(13),__esModule:!0}},function(n,t,i){i(14);n.exports=i(20).Object.keys},function(n,t,i){var r=i(15);i(17)("keys",function(n){return function(t){return n(r(t))}})},function(n,t,i){var r=i(16);n.exports=function(n){return Object(r(n))}},function(n){n.exports=function(n){if(n==undefined)throw TypeError("Can't call method on  "+n);return n}},function(n,t,i){var r=i(18),u=i(20),f=i(23);n.exports=function(n,t){var i=(u.Object||{})[n]||Object[n],e={};e[n]=t(i);r(r.S+r.F*f(function(){i(1)}),"Object",e)}},function(n,t,i){var f=i(19),e=i(20),o=i(21),u="prototype",r=function(n,t,i){var p=n&r.F,l=n&r.G,w=n&r.S,y=n&r.P,b=n&r.B,k=n&r.W,a=l?e:e[t]||(e[t]={}),c=l?f:w?f[t]:(f[t]||{})[u],s,v,h;l&&(i=t);for(s in i)(v=!p&&c&&s in c,v&&s in a)||(h=v?c[s]:i[s],a[s]=l&&typeof c[s]!="function"?i[s]:b&&v?o(h,f):k&&c[s]==h?function(n){var t=function(t){return this instanceof n?new n(t):n(t)};return t[u]=n[u],t}(h):y&&typeof h=="function"?o(Function.call,h):h,y&&((a[u]||(a[u]={}))[s]=h))};r.F=1;r.G=2;r.S=4;r.P=8;r.B=16;r.W=32;n.exports=r},function(n){var t=n.exports=typeof window!="undefined"&&window.Math==Math?window:typeof self!="undefined"&&self.Math==Math?self:Function("return this")();typeof __g=="number"&&(__g=t)},function(n){var t=n.exports={version:"1.2.6"};typeof __e=="number"&&(__e=t)},function(n,t,i){var r=i(22);n.exports=function(n,t,i){if(r(n),t===undefined)return n;switch(i){case 1:return function(i){return n.call(t,i)};case 2:return function(i,r){return n.call(t,i,r)};case 3:return function(i,r,u){return n.call(t,i,r,u)}}return function(){return n.apply(t,arguments)}}},function(n){n.exports=function(n){if(typeof n!="function")throw TypeError(n+" is not a function!");return n}},function(n){n.exports=function(n){try{return!!n()}catch(t){return!0}}},function(n,t,i){"use strict";var f=i(2)["default"],r,u;t.__esModule=!0;r=i(5);u=f(r);t["default"]=function(n){n.registerHelper("helperMissing",function(){if(arguments.length===1)return undefined;throw new u["default"]('Missing helper: "'+arguments[arguments.length-1].name+'"');})};n.exports=t["default"]},function(n,t,i){"use strict";var f=i(2)["default"];t.__esModule=!0;var r=i(4),e=i(5),u=f(e);t["default"]=function(n){n.registerHelper("if",function(n,t){if(arguments.length!=2)throw new u["default"]("#if requires exactly one argument");return r.isFunction(n)&&(n=n.call(this)),(t.hash.includeZero||n)&&!r.isEmpty(n)?t.fn(this):t.inverse(this)});n.registerHelper("unless",function(t,i){if(arguments.length!=2)throw new u["default"]("#unless requires exactly one argument");return n.helpers["if"].call(this,t,{fn:i.inverse,inverse:i.fn,hash:i.hash})})};n.exports=t["default"]},function(n,t){"use strict";t.__esModule=!0;t["default"]=function(n){n.registerHelper("log",function(){for(var i,r=[undefined],t=arguments[arguments.length-1],u=0;u<arguments.length-1;u++)r.push(arguments[u]);i=1;t.hash.level!=null?i=t.hash.level:t.data&&t.data.level!=null&&(i=t.data.level);r[0]=i;n.log.apply(n,r)})};n.exports=t["default"]},function(n,t){"use strict";t.__esModule=!0;t["default"]=function(n){n.registerHelper("lookup",function(n,t,i){return n?i.lookupProperty(n,t):n})};n.exports=t["default"]},function(n,t,i){"use strict";var u=i(2)["default"];t.__esModule=!0;var r=i(4),f=i(5),e=u(f);t["default"]=function(n){n.registerHelper("with",function(n,t){var u,i;if(arguments.length!=2)throw new e["default"]("#with requires exactly one argument");return r.isFunction(n)&&(n=n.call(this)),u=t.fn,r.isEmpty(n)?t.inverse(this):(i=t.data,t.data&&t.ids&&(i=r.createFrame(t.data),i.contextPath=r.appendContextPath(t.data.contextPath,t.ids[0])),u(n,{data:i,blockParams:r.blockParams([n],[i&&i.contextPath])}))})};n.exports=t["default"]},function(n,t,i){"use strict";function e(n){u["default"](n)}var f=i(2)["default"],r,u;t.__esModule=!0;t.registerDefaultDecorators=e;r=i(30);u=f(r)},function(n,t,i){"use strict";t.__esModule=!0;var r=i(4);t["default"]=function(n){n.registerDecorator("inline",function(n,t,i,u){var f=n;return t.partials||(t.partials={},f=function(u,f){var e=i.partials,o;return i.partials=r.extend({},e,t.partials),o=n(u,f),i.partials=e,o}),t.partials[u.args[0]]=u.fn,f})};n.exports=t["default"]},function(n,t,i){"use strict";t.__esModule=!0;var u=i(4),r={methodMap:["debug","info","warn","error"],level:"info",lookupLevel:function(n){if(typeof n=="string"){var t=u.indexOf(r.methodMap,n.toLowerCase());n=t>=0?t:parseInt(n,10)}return n},log:function(n){var t;if(n=r.lookupLevel(n),typeof console!="undefined"&&r.lookupLevel(r.level)<=n){t=r.methodMap[n];console[t]||(t="log");for(var u=arguments.length,f=Array(u>1?u-1:0),i=1;i<u;i++)f[i-1]=arguments[i];console[t].apply(console,f)}}};t["default"]=r;n.exports=t["default"]},function(n,t,i){"use strict";function l(n){var t=u(null),i;return t.constructor=!1,t.__defineGetter__=!1,t.__defineSetter__=!1,t.__lookupGetter__=!1,i=u(null),i.__proto__=!1,{properties:{whitelist:f.createNewLookupObject(i,n.allowedProtoProperties),defaultValue:n.allowProtoPropertiesByDefault},methods:{whitelist:f.createNewLookupObject(t,n.allowedProtoMethods),defaultValue:n.allowProtoMethodsByDefault}}}function a(n,t,i){return typeof n=="function"?e(t.methods,i):e(t.properties,i)}function e(n,t){return n.whitelist[t]!==undefined?n.whitelist[t]===!0:n.defaultValue!==undefined?n.defaultValue:(v(t),!1)}function v(n){r[n]!==!0&&(r[n]=!0,c.log("error",'Handlebars: Access has been denied to resolve the property "'+n+'" because it is not an "own property" of its parent.\nYou can add a runtime option to disable the check or this warning:\nSee https://handlebarsjs.com/api-reference/runtime-options.html#options-to-control-prototype-access for details'))}function y(){o(r).forEach(function(n){delete r[n]})}var u=i(33)["default"],o=i(12)["default"],s=i(1)["default"];t.__esModule=!0;t.createProtoAccessControl=l;t.resultIsAllowed=a;t.resetLoggedProperties=y;var f=i(35),h=i(31),c=s(h),r=u(null)},function(n,t,i){n.exports={"default":i(34),__esModule:!0}},function(n,t,i){var r=i(8);n.exports=function(n,t){return r.create(n,t)}},function(n,t,i){"use strict";function f(){for(var t=arguments.length,i=Array(t),n=0;n<t;n++)i[n]=arguments[n];return r.extend.apply(undefined,[u(null)].concat(i))}var u=i(33)["default"],r;t.__esModule=!0;t.createNewLookupObject=f;r=i(4)},function(n,t){"use strict";function i(n){this.string=n}t.__esModule=!0;i.prototype.toString=i.prototype.toHTML=function(){return""+this.string};t["default"]=i;n.exports=t["default"]},function(n,t,i){"use strict";function k(n){var t=n&&n[0]||1,e=f.COMPILER_REVISION,i,r;if(!(t>=f.LAST_COMPATIBLE_COMPILER_REVISION)||!(t<=f.COMPILER_REVISION))if(t<f.LAST_COMPATIBLE_COMPILER_REVISION){i=f.REVISION_CHANGES[e];r=f.REVISION_CHANGES[t];throw new u["default"]("Template was precompiled with an older version of Handlebars than the current runtime. Please update your precompiler to a newer version ("+i+") or downgrade your runtime to an older version ("+r+").");}else throw new u["default"]("Template was precompiled with a newer version of Handlebars than the current runtime. Please update your runtime to a newer version ("+n[1]+").");}function d(n,t){function a(i,f,e){var c,o,h,s,l;if(e.hash&&(f=r.extend({},f,e.hash),e.ids&&(e.ids[0]=!0)),i=t.VM.resolvePartial.call(this,i,f,e),c=r.extend({},e,{hooks:this.hooks,protoAccessControl:this.protoAccessControl}),o=t.VM.invokePartial.call(this,i,f,c),o==null&&t.compile&&(e.partials[e.name]=t.compile(i,n.compilerOptions,t),o=e.partials[e.name](f,c)),o!=null){if(e.indent){for(h=o.split("\n"),s=0,l=h.length;s<l;s++){if(!h[s]&&s+1===l)break;h[s]=e.indent+h[s]}o=h.join("\n")}return o}throw new u["default"]("The partial "+e.name+" could not be compiled when running in runtime-only mode");}function f(t){function s(t){return""+n.main(i,t,i.helpers,i.partials,u,o,e)}var r=arguments.length<=1||arguments[1]===undefined?{}:arguments[1],u=r.data,e,o;return f._setup(r),!r.partial&&n.useData&&(u=tt(t,u)),e=undefined,o=n.useBlockParams?[]:undefined,n.useDepths&&(e=r.depths?t!=r.depths[0]?[t].concat(r.depths):r.depths:[t]),s=c(n.main,s,i,r.depths||[],u,o),s(t,r)}var h,i;if(!t)throw new u["default"]("No environment passed to template");if(!n||!n.main)throw new u["default"]("Unknown template object: "+typeof n);return n.main.decorator=n.main_d,t.VM.checkRevision(n.compiler),h=n.compiler&&n.compiler[0]===7,i={strict:function(n,t,r){if(!n||!(t in n))throw new u["default"]('"'+t+'" not defined in '+n,{loc:r});return i.lookupProperty(n,t)},lookupProperty:function(n,t){var r=n[t];return r==null?r:Object.prototype.hasOwnProperty.call(n,t)?r:s.resultIsAllowed(r,i.protoAccessControl,t)?r:undefined},lookup:function(n,t){for(var u,f=n.length,r=0;r<f;r++)if(u=n[r]&&i.lookupProperty(n[r],t),u!=null)return n[r][t]},lambda:function(n,t){return typeof n=="function"?n.call(t):n},escapeExpression:r.escapeExpression,invokePartial:a,fn:function(t){var i=n[t];return i.decorator=n[t+"_d"],i},programs:[],program:function(n,t,i,r,u){var f=this.programs[n],o=this.fn(n);return t||u||r||i?f=e(this,n,o,t,i,r,u):f||(f=this.programs[n]=e(this,n,o)),f},data:function(n,t){while(n&&t--)n=n._parent;return n},mergeIfNeeded:function(n,t){var i=n||t;return n&&t&&n!==t&&(i=r.extend({},t,n)),i},nullContext:l({}),noop:t.VM.noop,compilerInfo:n.compiler},f.isTop=!0,f._setup=function(u){var f,e;u.partial?(i.protoAccessControl=u.protoAccessControl,i.helpers=u.helpers,i.partials=u.partials,i.decorators=u.decorators,i.hooks=u.hooks):(f=r.extend({},t.helpers,u.helpers),it(f,i),i.helpers=f,n.usePartial&&(i.partials=i.mergeIfNeeded(u.partials,t.partials)),(n.usePartial||n.useDecorators)&&(i.decorators=r.extend({},t.decorators,u.decorators)),i.hooks={},i.protoAccessControl=s.createProtoAccessControl(u),e=u.allowCallsToHelperMissing||h,o.moveHelperToHooks(i,"helperMissing",e),o.moveHelperToHooks(i,"blockHelperMissing",e))},f._child=function(t,r,f,o){if(n.useBlockParams&&!f)throw new u["default"]("must pass block params");if(n.useDepths&&!o)throw new u["default"]("must pass parent depths");return e(i,t,n[t],r,0,f,o)},f}function e(n,t,i,r,u,f,e){function o(t){var u=arguments.length<=1||arguments[1]===undefined?{}:arguments[1],o=e;return!e||t==e[0]||t===n.nullContext&&e[0]===null||(o=[t].concat(e)),i(n,t,n.helpers,n.partials,u.data||r,f&&[u.blockParams].concat(f),o)}return o=c(i,o,n,e,r,f),o.program=t,o.depth=e?e.length:0,o.blockParams=u||0,o}function g(n,t,i){return n?n.call||i.name||(i.name=n,n=i.partials[n]):n=i.name==="@partial-block"?i.data["partial-block"]:i.partials[i.name],n}function nt(n,t,i){var o=i.data&&i.data["partial-block"],e;if(i.partial=!0,i.ids&&(i.data.contextPath=i.ids[0]||i.data.contextPath),e=undefined,i.fn&&i.fn!==h&&function(){i.data=f.createFrame(i.data);var n=i.fn;e=i.data["partial-block"]=function(t){var i=arguments.length<=1||arguments[1]===undefined?{}:arguments[1];return i.data=f.createFrame(i.data),i.data["partial-block"]=o,n(t,i)};n.partials&&(i.partials=r.extend({},i.partials,n.partials))}(),n===undefined&&e&&(n=e),n===undefined)throw new u["default"]("The partial "+i.name+" could not be found");else if(n instanceof Function)return n(t,i)}function h(){return""}function tt(n,t){return t&&"root"in t||(t=t?f.createFrame(t):{},t.root=n),t}function c(n,t,i,u,f,e){if(n.decorator){var o={};t=n.decorator(t,o,i,u&&u[0],f,e,u);r.extend(t,o)}return t}function it(n,t){a(n).forEach(function(i){var r=n[i];n[i]=rt(r,t)})}function rt(n,t){var i=t.lookupProperty;return b.wrapHelper(n,function(n){return r.extend({lookupProperty:i},n)})}var l=i(38)["default"],a=i(12)["default"],v=i(1)["default"],y=i(2)["default"];t.__esModule=!0;t.checkRevision=k;t.template=d;t.wrapProgram=e;t.resolvePartial=g;t.invokePartial=nt;t.noop=h;var p=i(4),r=v(p),w=i(5),u=y(w),f=i(3),o=i(9),b=i(42),s=i(32)},function(n,t,i){n.exports={"default":i(39),__esModule:!0}},function(n,t,i){i(40);n.exports=i(20).Object.seal},function(n,t,i){var r=i(41);i(17)("seal",function(n){return function(t){return n&&r(t)?n(t):t}})},function(n){n.exports=function(n){return typeof n=="object"?n!==null:typeof n=="function"}},function(n,t){"use strict";function i(n,t){if(typeof n!="function")return n;return function(){var i=arguments[arguments.length-1];return arguments[arguments.length-1]=t(i),n.apply(this,arguments)}}t.__esModule=!0;t.wrapHelper=i},function(n,t){(function(i){"use strict";t.__esModule=!0;t["default"]=function(n){var t=typeof i!="undefined"?i:window,r=t.Handlebars;n.noConflict=function(){return t.Handlebars===n&&(t.Handlebars=r),n}};n.exports=t["default"]}).call(t,function(){return this}())}])});$i2e=function(n,t){return{VERSION:"2.0.0",APPLE_IDP_PATH_REGEX:new RegExp(/\?claimsexchange=.*apple.*/i),redirectToServer:function(n,t){this.isAndroid()||this.APPLE_IDP_PATH_REGEX.test(n)||(this.redirectToServer=function(){});$diags.trace(new $trace("T002",!1));var i=this.getRedirectLink(n);$(document).ready(function(){window.diagsAlways&&(i+="&diags="+encodeURIComponent($diags.toJson()));t?window.location.assign(i):window.location.replace(i)})},getRedirectLink:function(n){return n=n+(n.indexOf("?")===-1?"?":"&")+"csrf_token="+t.csrf+"&tx="+t.transId,window.Metrics!==undefined&&(n+="&metrics="+encodeURIComponent(window.Metrics.serialize())),t.hosts.tenant+"/api/"+t.api+"/"+n+"&p="+t.hosts.policy},sendData:function(n,i,r){r=r!==undefined?r:t;var f=r.hosts.tenant+"/"+r.api+(i!==undefined?"/"+i:"")+"?tx="+r.transId+"&p="+r.hosts.policy,u={"X-CSRF-TOKEN":t.csrf};return t.isPageViewIdSentWithHeader&&(u["x-ms-cpim-pageviewid"]=t.pageViewId),$.ajax({type:"POST",dataType:"json",headers:u,timeout:r.config.timeout||9e4,url:f,cache:!1,data:n})},sendDataWithRetry:function(n,i,r,u,f,e,o){var c,h,s;return e=e!==undefined?e:t,o=o||"POST",c=e.hosts.tenant+"/"+e.api+(f!==undefined?"/"+f:"")+"?tx="+e.transId+"&p="+e.hosts.policy,h={"X-CSRF-TOKEN":t.csrf},t.isPageViewIdSentWithHeader&&(h["x-ms-cpim-pageviewid"]=t.pageViewId),s=e.xhrSettings,$.ajax({type:o,dataType:"json",headers:h,retryMaxAttempts:s&&s.hasOwnProperty("retryMaxAttempts")?s.retryMaxAttempts:3,retryDelay:s&&s.hasOwnProperty("retryDelay")?s.retryDelay:200,retryExponent:s&&s.hasOwnProperty("retryExponent")?s.retryExponent:2,retryEnabled:s&&s.hasOwnProperty("retryEnabled")?s.retryEnabled:!1,retryOn:s&&s.hasOwnProperty("retryOn")?s.retryOn:[],retryAttempt:0,timeout:e.config.timeout||9e4,url:c,cache:!1,contentType:e.contentType!==undefined?e.contentType:"application/x-www-form-urlencoded; charset=UTF-8",data:n,success:i,error:function(n,t,i){var u,f,e;(this.retryEnabled===!1||this.retryOn.indexOf(t)===-1)&&r(n,t,i);u=window.navigator!==undefined&&window.navigator.onLine!==undefined?window.navigator.onLine:!0;f=new $trace("T030",!1);f.append(u?"Online":"Offline");$diags.trace(f);u===!1?this.retryAttempt<this.retryMaxAttempts?(this.retryAttempt++,this.retryDelay=this.retryAttempt===1?this.retryDelay:this.retryDelay*this.retryExponent,e=new $trace("T031",!1),e.append(" '"+t+": ' - retryAttempt: "+this.retryAttempt+" - retryDelay: "+this.retryDelay+" - retryExponent:"+this.retryExponent),$diags.trace(e),setTimeout(function(n){return $.ajax(n)},this.retryDelay,this)):($diags.trace(new $trace("T032",!1)),r(n,t,i)):r(n,t,i)},complete:u})},generateServiceContent:function(n,t){var i=Handlebars.templates[n];return i(t)},insertServiceContent:function(n){var i=$(n),t=$("#api"),r=t[0].attributes;t.replaceWith(i.html());$.each(r,function(){this.name!=="id"&&$("#api").attr(this.name,this.value)})},lookupLanguage:function(t){if(n[t]){var i=document.createElement("div");return i.innerHTML=n[t],i.textContent}return n[t]},bindHost:function(n){return t.hosts.static+n},isAndroid:function(){var n=navigator.userAgent.toLowerCase();return n.indexOf("android")!==-1}}}(CONTENT,SETTINGS),function(n,t){Handlebars.registerHelper("getContent",function(t){return new Handlebars.SafeString(n[t])});Handlebars.registerHelper("getRawContent",function(t){return n[t]});Handlebars.registerHelper("getSettingAsBoolean",function(n){var i=t.config[n];return i&&i.toLowerCase()==="true"});Handlebars.registerHelper("getHostQualfiedUrl",function(n){return t.hosts.static+n});Handlebars.registerHelper("isSettingTrue",function(n,i){var r=t.config[n];return r&&r.toLowerCase()==="true"?i.fn(this):null});Handlebars.registerHelper("isSettingEqual",function(n,i,r,u){for(var o=t.config[n]||r,e=i.split(","),s=e.length,f=0;f<s;f++)if(e[f]&&e[f].toLowerCase()===o.toLowerCase())return u.fn(this);return null});Handlebars.registerHelper("doesSettingExist",function(n,i){var r=t.config[n];return r?i.fn(this):i.inverse(this)});Handlebars.registerHelper("doesSettingContain",function(n,i,r,u){for(var o=t.config[n]||r,e=o.split(","),s=e.length,f=0;f<s;f++)if(e[f]&&e[f].toLowerCase()===i.toLowerCase())return u.fn(this);return u.inverse(this)});Handlebars.registerHelper("doesSettingContainAny",function(n,i,r,u){for(var f,s=n.split(","),e=0;e<s.length;e++){var h=t.config[s[e]]||r,o=h.split(","),c=o.length;for(f=0;f<c;f++)if(o[f]&&o[f].toLowerCase()===i.toLowerCase())return u.fn(this)}return u.inverse(this)});Handlebars.registerHelper("isSingle",function(n,t){return n.length===1?t.fn(this):null});Handlebars.registerHelper("isMultiple",function(n,t){return n.length>1?t.fn(this):null});Handlebars.registerHelper("getCountryDialingCodeOptionList",function(){var f=document.createElement("div"),o=t.locale.country,e=document.createElement("textarea"),u,i,r,n;e.innerHTML=$i2e.lookupLanguage("countryList");u=JSON.parse(e.value);for(i in u)r=$isoData.countryByIso[i].dc,n=document.createElement("option"),n.setAttribute("value","+"+r),o===i&&n.setAttribute("selected",!0),n.innerText=u[i]+(r?" (+"+r+")":""),f.appendChild(n);return f.innerHTML})}(CONTENT,SETTINGS);$predicateValidation=function(){var n={UNKNOWN:0,INCLUDES_CHARACTERS:1,MATCHES_REGEX:2,IS_LENGTH_RANGE:3,IS_DATE_RANGE:4},t={MINIMUM:1,MAXIMUM:2,CHARACTER_SET:3,REGULAR_EXPRESSION:4},f=function(n){var t,i;if(!Array.isArray(SA_FIELDS.AttributeFields))return null;for(t=0;t<SA_FIELDS.AttributeFields.length;t++){if(SA_FIELDS.AttributeFields[t].ID===n)return SA_FIELDS.AttributeFields[t];if(SA_FIELDS.AttributeFields[t].USER_INPUT_TYPE==="DisplayControl"){if(!Array.isArray(SA_FIELDS.AttributeFields[t].DISPLAY_FIELDS))return null;for(i=0;i<SA_FIELDS.AttributeFields[t].DISPLAY_FIELDS.length;i++)if(SA_FIELDS.AttributeFields[t].DISPLAY_FIELDS[i].ID===n)return SA_FIELDS.AttributeFields[t].DISPLAY_FIELDS[i]}}return null},e=function(n,t,i){var r,u;if(!Array.isArray(n))return null;for(r=0;r<n.length;r++)if(u=n[r],u[t]!=null&&u[t]==i)return u;return null},i=function(n){return n.replace(/&amp;/g,"&").replace(/&lt;/g,"<").replace(/&gt;/g,">").replace(/&quot;/g,'"').replace(/&#039;/g,"'").replace(/&#39;/g,"'")},o=function(n){return n.replace(/([\/^$*+?.()|[\]{}])/g,"\\$1")},r=function(){var u=this,r=[];return r[n.UNKNOWN]=function(){return!0},r[n.INCLUDES_CHARACTERS]=function(n,t){var r=i(t[0].VALUE),u=new RegExp("["+o(r)+"]+");return u.test(n)},r[n.IS_LENGTH_RANGE]=function(n,i){var r=parseInt($predicateValidation.findBy(i,"NAME",t.MINIMUM).VALUE),u=parseInt($predicateValidation.findBy(i,"NAME",t.MAXIMUM).VALUE);return r<=n.length&&n.length<=u},r[n.IS_DATE_RANGE]=function(n,i){var r=new Date($predicateValidation.findBy(i,"NAME",t.MINIMUM).VALUE),u=new Date($predicateValidation.findBy(i,"NAME",t.MAXIMUM).VALUE),f=new Date(r.getUTCFullYear(),r.getUTCMonth(),r.getUTCDate()),e=new Date(u.getUTCFullYear(),u.getUTCMonth(),u.getUTCDate());return f<=n&&n<=e},r[n.MATCHES_REGEX]=function(n,r){var u=i($predicateValidation.findBy(r,"NAME",t.REGULAR_EXPRESSION).VALUE),f=new RegExp(u);return f.test(n)},r},u=function(n,t,i){for(var u=n!=""&&n!=null,e=u?i:"",f=u?n:"",r=0;r<t.length;r++)f+=e+t[r].HELP_TEXT;return f},s=function(n,t,i){var h=[],c={isValid:!0,errorMsgs:h},w=$predicateValidation.findAttributeFieldByClaimId(n),f,v,l,e,o,y,a,s,b,p;if(null==w||(f=w.INPUT_VALIDATION,null==f))return c;if(v=r(),l=!0,f.PREDICATES!=null)for(e=0;e<f.PREDICATES.length;e++)s=f.PREDICATES[e],v[s.METHOD](t,s.PARAMS)||(h.push(u("",[s],i)),l=!1);if(f.PREDICATE_GROUPS!=null)for(e=0;e<f.PREDICATE_GROUPS.length;e++){for(o=f.PREDICATE_GROUPS[e],y=0,a=0;a<o.PREDICATES.length;a++)s=o.PREDICATES[a],b=v[s.METHOD](t,s.PARAMS),b&&y++;p=y>=o.MATCH_AT_LEAST;(p&&o.REJECT===!0||!p&&o.REJECT!==!0)&&(h.push(u(o.HELP_TEXT,o.PREDICATES,i)),l=!1)}return c.isValid=l,c.errorMsgs=h,c};return{findAttributeFieldByClaimId:f,findBy:e,generateValidationDict:s,validationMethods:r,unescapeHtml:i,PREDICATE_METHODS:n,PREDICATE_PARAMETERS:t}}($diags,CONTENT,SETTINGS);this.Handlebars=this.Handlebars||{};this.Handlebars.templates=this.Handlebars.templates||{};this.Handlebars.templates.unifiedssp=Handlebars.template({1:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'      <div class="heading">\r\n        <h1 role="heading">'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"heading",{name:"getContent",hash:{},data:u,loc:{start:{line:5,column:27},end:{line:5,column:53}}}))?f:"")+"<\/h1>\r\n      <\/div>\r\n"},3:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=e(i,"if").call(null!=t?t:n.nullContext||{},null!=t?e(t,"buttonList"):t,{name:"if",hash:{},fn:n.program(4,u,0),inverse:n.noop,data:u,loc:{start:{line:9,column:6},end:{line:35,column:13}}}))?f:""},4:function(n,t,i,r,u){var f,o=null!=t?t:n.nullContext||{},s=n.hooks.helperMissing,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'        <div class="claims-provider-list-buttons social" aria-label="'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||s).call(o,"social_intro",{name:"getContent",hash:{},data:u,loc:{start:{line:10,column:69},end:{line:10,column:100}}}))?f:"")+'" role="form">\r\n          <div class="intro">\r\n            <h2>'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||s).call(o,"social_intro",{name:"getContent",hash:{},data:u,loc:{start:{line:12,column:16},end:{line:12,column:47}}}))?f:"")+'<\/h2>\r\n          <\/div>\r\n          <div class="options">\r\n'+(null!=(f=e(i,"each").call(o,null!=t?e(t,"buttonList"):t,{name:"each",hash:{},fn:n.program(5,u,0),inverse:n.noop,data:u,loc:{start:{line:15,column:12},end:{line:26,column:21}}}))?f:"")+"          <\/div>\r\n        <\/div>\r\n\r\n"+(null!=(f=e(i,"if").call(o,null!=t?e(t,"AttributeFields"):t,{name:"if",hash:{},fn:n.program(13,u,0),inverse:n.noop,data:u,loc:{start:{line:30,column:8},end:{line:34,column:15}}}))?f:"")},5:function(n,t,i,r,u){var e,f=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(e=(f(i,"doesSettingContainAny")||t&&f(t,"doesSettingContainAny")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotPasswordLinkOverride,bottomClaimsProviderSelections,bottomUnderFormClaimsProviderSelections",null!=t?f(t,"id"):t,"",{name:"doesSettingContainAny",hash:{},fn:n.program(6,u,0),inverse:n.program(8,u,0),data:u,loc:{start:{line:16,column:14},end:{line:25,column:40}}}))?e:""},6:function(){return""},8:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return"                <div>\r\n"+(null!=(f=e(i,"if").call(null!=t?t:n.nullContext||{},u&&e(u,"first"),{name:"if",hash:{},fn:n.program(9,u,0),inverse:n.program(11,u,0),data:u,loc:{start:{line:19,column:18},end:{line:23,column:25}}}))?f:"")+"                <\/div>\r\n"},9:function(n,t){var i,r=n.lambda,u=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                    <button class="accountButton firstButton claims-provider-selection" id="'+(null!=(i=r(null!=t?u(t,"id"):t,t))?i:"")+'" role="link" autofocus>'+(null!=(i=r(null!=t?u(t,"description"):t,t))?i:"")+"<\/button>\r\n"},11:function(n,t){var i,r=n.lambda,u=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                    <button class="accountButton claims-provider-selection" id="'+(null!=(i=r(null!=t?u(t,"id"):t,t))?i:"")+'" role="link">'+(null!=(i=r(null!=t?u(t,"description"):t,t))?i:"")+"<\/button>\r\n"},13:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'        <div class="divider">\r\n          <h2>'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"divider_title",{name:"getContent",hash:{},data:u,loc:{start:{line:32,column:14},end:{line:32,column:46}}}))?f:"")+"<\/h2>\r\n        <\/div>\r\n"},15:function(n,t,i,r,u){var f,o=null!=t?t:n.nullContext||{},s=n.hooks.helperMissing,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'      <form id="localAccountForm" class="localAccount" aria-label="'+(null!=(f=(e(i,"buildIntroString")||t&&e(t,"buildIntroString")||s).call(o,null!=(f=null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"0"):f)?e(f,"DN"):f,{name:"buildIntroString",hash:{},data:u,loc:{start:{line:38,column:67},end:{line:38,column:110}}}))?f:"")+'">\r\n        <div class="intro">\r\n          <h2>\r\n            '+(null!=(f=(e(i,"buildIntroString")||t&&e(t,"buildIntroString")||s).call(o,null!=(f=null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"0"):f)?e(f,"DN"):f,{name:"buildIntroString",hash:{},data:u,loc:{start:{line:41,column:12},end:{line:41,column:55}}}))?f:"")+'\r\n          <\/h2>\r\n        <\/div>\r\n        <div class="error pageLevel" aria-hidden="true" role="alert">\r\n          <p><\/p>\r\n        <\/div>\r\n        <div class="entry">\r\n          <div class="entry-item">\r\n'+(null!=(f=(e(i,"doesSettingContain")||t&&e(t,"doesSettingContain")||s).call(o,"pageFlavor","classic","None",{name:"doesSettingContain",hash:{},fn:n.program(16,u,0),inverse:n.program(18,u,0),data:u,loc:{start:{line:49,column:12},end:{line:53,column:35}}}))?f:"")+'            <div class="error itemLevel" aria-hidden="true" role="alert">\r\n              <p ><\/p>\r\n            <\/div>\r\n            '+n.escapeExpression((e(i,"buildInput")||t&&e(t,"buildInput")||s).call(o,null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"0"):f,null!=t?e(t,"buttonList"):t,{name:"buildInput",hash:{},data:u,loc:{start:{line:57,column:12},end:{line:57,column:57}}}))+"\r\n          <\/div>\r\n"+(null!=(f=e(i,"if").call(o,null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"1"):f,{name:"if",hash:{},fn:n.program(21,u,0),inverse:n.noop,data:u,loc:{start:{line:59,column:10},end:{line:95,column:17}}}))?f:"")+'\r\n          <div class="working"><\/div>\r\n'+(null!=(f=(e(i,"isSettingTrue")||t&&e(t,"isSettingTrue")||s).call(o,"enableRememberMe",{name:"isSettingTrue",hash:{},fn:n.program(41,u,0),inverse:n.noop,data:u,loc:{start:{line:98,column:10},end:{line:103,column:28}}}))?f:"")+"\r\n"+(null!=(f=(e(i,"isSettingTrue")||t&&e(t,"isSettingTrue")||s).call(o,"enableCaptchaChallenge",{name:"isSettingTrue",hash:{},fn:n.program(43,u,0),inverse:n.noop,data:u,loc:{start:{line:106,column:10},end:{line:108,column:28}}}))?f:"")+"\r\n"+(null!=(f=e(i,"if").call(o,null!=t?e(t,"textLinkList"):t,{name:"if",hash:{},fn:n.program(45,u,0),inverse:n.noop,data:u,loc:{start:{line:110,column:10},end:{line:122,column:17}}}))?f:"")+'\r\n          <div class="buttons">\r\n            <button id="next" type="submit" form="localAccountForm" >'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||s).call(o,"button_signin",{name:"getContent",hash:{},data:u,loc:{start:{line:125,column:69},end:{line:125,column:101}}}))?f:"")+"<\/button>\r\n          <\/div>\r\n"+(null!=(f=(e(i,"doesSettingExist")||t&&e(t,"doesSettingExist")||s).call(o,"forgotPasswordLinkLocation",{name:"doesSettingExist",hash:{},fn:n.program(50,u,0),inverse:n.noop,data:u,loc:{start:{line:127,column:10},end:{line:131,column:31}}}))?f:"")+"        <\/div>\r\n"+(null!=(f=(e(i,"ifSignUpLink")||t&&e(t,"ifSignUpLink")||s).call(o,null!=t?e(t,"textLinkList"):t,{name:"ifSignUpLink",hash:{},fn:n.program(53,u,0),inverse:n.program(55,u,0),data:u,loc:{start:{line:133,column:8},end:{line:153,column:25}}}))?f:"")+"      <\/form>\r\n"},16:function(n,t){var i,u=n.lambda,r=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'              <label for="'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"0"):i)?r(i,"ID"):i,t))?i:"")+'">'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"0"):i)?r(i,"DN"):i,t))?i:"")+"<\/label>\r\n"},18:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"doesSettingContain")||t&&e(t,"doesSettingContain")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"enableInputLabels","True","True",{name:"doesSettingContain",hash:{},fn:n.program(19,u,0),inverse:n.noop,data:u,loc:{start:{line:51,column:12},end:{line:53,column:12}}}))?f:""},19:function(n,t){var i,u=n.lambda,r=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'              <label for="'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"0"):i)?r(i,"ID"):i,t))?i:"")+'">'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"0"):i)?r(i,"DN"):i,t))?i:"")+"<\/label>\r\n            "},21:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"ifPasswordField")||t&&e(t,"ifPasswordField")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"1"):f,{name:"ifPasswordField",hash:{},fn:n.program(22,u,0),inverse:n.noop,data:u,loc:{start:{line:60,column:12},end:{line:94,column:32}}}))?f:""},22:function(n,t,i,r,u){var f,s=null!=t?t:n.nullContext||{},h=n.hooks.helperMissing,o=n.lambda,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'            <div class="entry-item">\r\n              <div class="password-label">\r\n'+(null!=(f=(e(i,"doesSettingContain")||t&&e(t,"doesSettingContain")||h).call(s,"pageFlavor","classic","None",{name:"doesSettingContain",hash:{},fn:n.program(23,u,0),inverse:n.program(25,u,0),data:u,loc:{start:{line:63,column:16},end:{line:67,column:39}}}))?f:"")+(null!=(f=(e(i,"doesSettingExist")||t&&e(t,"doesSettingExist")||h).call(s,"forgotPasswordLinkLocation",{name:"doesSettingExist",hash:{},fn:n.program(28,u,0),inverse:n.program(31,u,0),data:u,loc:{start:{line:68,column:16},end:{line:76,column:37}}}))?f:"")+'              <\/div>\r\n              <div class="error itemLevel" aria-hidden="true">\r\n                <p role="alert"><\/p>\r\n              <\/div>\r\n              <input type="password" id="'+(null!=(f=o(null!=(f=null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"1"):f)?e(f,"ID"):f,t))?f:"")+'" name="'+(null!=(f=o(null!=(f=null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"1"):f)?e(f,"DN"):f,t))?f:"")+'" placeholder="'+(null!=(f=o(null!=(f=null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"1"):f)?e(f,"DN"):f,t))?f:"")+'" aria-label="'+(null!=(f=o(null!=(f=null!=(f=null!=t?e(t,"AttributeFields"):t)?e(f,"1"):f)?e(f,"DN"):f,t))?f:"")+'" autocomplete="current-password" aria-required="true"/>\r\n'+(null!=(f=(e(i,"doesSettingExist")||t&&e(t,"doesSettingExist")||h).call(s,"forgotPasswordLinkLocation",{name:"doesSettingExist",hash:{},fn:n.program(34,u,0),inverse:n.program(37,u,0),data:u,loc:{start:{line:82,column:14},end:{line:92,column:35}}}))?f:"")+"            <\/div>\r\n"},23:function(n,t){var i,u=n.lambda,r=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                  <label for="'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"1"):i)?r(i,"ID"):i,t))?i:"")+'">'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"1"):i)?r(i,"DN"):i,t))?i:"")+"<\/label>\r\n"},25:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"doesSettingContain")||t&&e(t,"doesSettingContain")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"enableInputLabels","True","True",{name:"doesSettingContain",hash:{},fn:n.program(26,u,0),inverse:n.noop,data:u,loc:{start:{line:65,column:16},end:{line:67,column:16}}}))?f:""},26:function(n,t){var i,u=n.lambda,r=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                  <label for="'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"1"):i)?r(i,"ID"):i,t))?i:"")+'">'+(null!=(i=u(null!=(i=null!=(i=null!=t?r(t,"AttributeFields"):t)?r(i,"1"):i)?r(i,"DN"):i,t))?i:"")+"<\/label>\r\n                "},28:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"isSettingEqual")||t&&e(t,"isSettingEqual")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotPasswordLinkLocation","AfterLabel","None",{name:"isSettingEqual",hash:{},fn:n.program(29,u,0),inverse:n.noop,data:u,loc:{start:{line:69,column:18},end:{line:71,column:37}}}))?f:""},29:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                    <a id="forgotPassword" >'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotpassword_link",{name:"getContent",hash:{},data:u,loc:{start:{line:70,column:44},end:{line:70,column:82}}}))?f:"")+"<\/a>\r\n"},31:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"doesSettingExist")||t&&e(t,"doesSettingExist")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"pageFlavor",{name:"doesSettingExist",hash:{},fn:n.program(32,u,0),inverse:n.noop,data:u,loc:{start:{line:72,column:16},end:{line:76,column:16}}}))?f:""},32:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return(null!=(f=(e(i,"isSettingEqual")||t&&e(t,"isSettingEqual")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"pageFlavor","classic","None",{name:"isSettingEqual",hash:{},fn:n.program(29,u,0),inverse:n.noop,data:u,loc:{start:{line:73,column:18},end:{line:75,column:37}}}))?f:"")+"                "},34:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"isSettingEqual")||t&&e(t,"isSettingEqual")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotPasswordLinkLocation","AfterInput","None",{name:"isSettingEqual",hash:{},fn:n.program(35,u,0),inverse:n.noop,data:u,loc:{start:{line:83,column:16},end:{line:85,column:35}}}))?f:""},35:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                  <a id="forgotPassword" >'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotpassword_link",{name:"getContent",hash:{},data:u,loc:{start:{line:84,column:42},end:{line:84,column:80}}}))?f:"")+"<\/a>\r\n"},37:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"doesSettingExist")||t&&e(t,"doesSettingExist")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"pageFlavor",{name:"doesSettingExist",hash:{},fn:n.program(38,u,0),inverse:n.noop,data:u,loc:{start:{line:86,column:14},end:{line:92,column:14}}}))?f:""},38:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return(null!=(f=(e(i,"isSettingEqual")||t&&e(t,"isSettingEqual")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"pageFlavor","oceanBlue,slateGray","None",{name:"isSettingEqual",hash:{},fn:n.program(39,u,0),inverse:n.noop,data:u,loc:{start:{line:87,column:16},end:{line:91,column:35}}}))?f:"")+"              "},39:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                <div class="forgot-password center-height">\r\n                  <a id="forgotPassword" >'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotpassword_link",{name:"getContent",hash:{},data:u,loc:{start:{line:89,column:42},end:{line:89,column:80}}}))?f:"")+"<\/a>\r\n                <\/div>\r\n"},41:function(n,t,i,r,u){var e,o=null!=t?t:n.nullContext||{},s=n.hooks.helperMissing,f=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'              <div class="'+(null!=(e=(f(i,"getRememberMeClass")||t&&f(t,"getRememberMeClass")||s).call(o,null!=t?f(t,"AttributeFields"):t,{name:"getRememberMeClass",hash:{},data:u,loc:{start:{line:99,column:26},end:{line:99,column:66}}}))?e:"")+'">\r\n                <input id="rememberMe" type="checkbox" name="rememberMe" />\r\n                <label for="rememberMe">'+(null!=(e=(f(i,"getContent")||t&&f(t,"getContent")||s).call(o,"remember_me",{name:"getContent",hash:{},data:u,loc:{start:{line:101,column:40},end:{line:101,column:70}}}))?e:"")+"<\/label>\r\n              <\/div>\r\n"},43:function(n,t,i,r,u){var f=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return"              "+n.escapeExpression((f(i,"generateCaptchaControl")||t&&f(t,"generateCaptchaControl")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},null!=t?f(t,"AttributeFields"):t,{name:"generateCaptchaControl",hash:{},data:u,loc:{start:{line:107,column:14},end:{line:107,column:57}}}))+"\r\n"},45:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"doesSettingExist")||t&&e(t,"doesSettingExist")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"bottomClaimsProviderSelections",{name:"doesSettingExist",hash:{},fn:n.program(46,u,0),inverse:n.noop,data:u,loc:{start:{line:111,column:12},end:{line:121,column:33}}}))?f:""},46:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'              <div class="claims-provider-list-text-links-bottom">\r\n'+(null!=(f=e(i,"each").call(null!=t?t:n.nullContext||{},null!=t?e(t,"textLinkList"):t,{name:"each",hash:{},fn:n.program(47,u,0),inverse:n.noop,data:u,loc:{start:{line:113,column:16},end:{line:119,column:25}}}))?f:"")+"              <\/div>\r\n"},47:function(n,t,i,r,u){var e,f=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(e=(f(i,"doesSettingContain")||t&&f(t,"doesSettingContain")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"bottomClaimsProviderSelections",null!=t?f(t,"id"):t,"",{name:"doesSettingContain",hash:{},fn:n.program(48,u,0),inverse:n.noop,data:u,loc:{start:{line:114,column:16},end:{line:118,column:39}}}))?e:""},48:function(n,t){var i,r=n.lambda,u=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                  <div>\r\n                    <a id="'+(null!=(i=r(null!=t?u(t,"id"):t,t))?i:"")+'" class="text-link">'+(null!=(i=r(null!=t?u(t,"description"):t,t))?i:"")+"<\/a>\r\n                  <\/div>\r\n"},50:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"isSettingEqual")||t&&e(t,"isSettingEqual")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotPasswordLinkLocation","AfterButtons","None",{name:"isSettingEqual",hash:{},fn:n.program(51,u,0),inverse:n.noop,data:u,loc:{start:{line:128,column:12},end:{line:130,column:31}}}))?f:""},51:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'              <a id="forgotPassword" >'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotpassword_link",{name:"getContent",hash:{},data:u,loc:{start:{line:129,column:38},end:{line:129,column:76}}}))?f:"")+"<\/a>\r\n"},53:function(n,t,i,r,u){var e,o=null!=t?t:n.nullContext||{},s=n.hooks.helperMissing,f=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'          <div class="claims-provider-list-text-links">\r\n            <p>\r\n              '+(null!=(e=(f(i,"getContent")||t&&f(t,"getContent")||s).call(o,"createaccount_intro",{name:"getContent",hash:{},data:u,loc:{start:{line:136,column:14},end:{line:136,column:52}}}))?e:"")+"&nbsp;"+(null!=(e=(f(i,"buildTextLinks")||t&&f(t,"buildTextLinks")||s).call(o,null!=t?f(t,"textLinkList"):t,{name:"buildTextLinks",hash:{},data:u,loc:{start:{line:136,column:58},end:{line:136,column:91}}}))?e:"")+"\r\n            <\/p>\r\n          <\/div>\r\n"},55:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=e(i,"if").call(null!=t?t:n.nullContext||{},null!=t?e(t,"AttributeFields"):t,{name:"if",hash:{},fn:n.program(56,u,0),inverse:n.noop,data:u,loc:{start:{line:141,column:10},end:{line:152,column:17}}}))?f:""},56:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"isSettingTrue")||t&&e(t,"isSettingTrue")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"showSignupLink",{name:"isSettingTrue",hash:{},fn:n.program(57,u,0),inverse:n.noop,data:u,loc:{start:{line:142,column:12},end:{line:151,column:30}}}))?f:""},57:function(n,t,i,r,u){var f,o=null!=t?t:n.nullContext||{},s=n.hooks.helperMissing,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'              <div class="divider">\r\n                <h2>'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||s).call(o,"divider_title",{name:"getContent",hash:{},data:u,loc:{start:{line:144,column:20},end:{line:144,column:52}}}))?f:"")+'<\/h2>\r\n              <\/div>\r\n              <div class="create">\r\n                <p>\r\n                  '+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||s).call(o,"createaccount_intro",{name:"getContent",hash:{},data:u,loc:{start:{line:148,column:18},end:{line:148,column:56}}}))?f:"")+'<a id="createAccount" >'+(null!=(f=(e(i,"getContent")||t&&e(t,"getContent")||s).call(o,"createaccount_one_link",{name:"getContent",hash:{},data:u,loc:{start:{line:148,column:79},end:{line:148,column:120}}}))?f:"")+"<\/a>\r\n                <\/p>\r\n              <\/div>\r\n"},59:function(n,t,i,r,u,f,e){var o,h=null!=t?t:n.nullContext||{},s=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return(null!=(o=s(i,"if").call(h,null!=t?s(t,"AttributeFields"):t,{name:"if",hash:{},fn:n.program(13,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:157,column:6},end:{line:161,column:13}}}))?o:"")+(null!=(o=s(i,"if").call(h,null!=t?s(t,"textLinkList"):t,{name:"if",hash:{},fn:n.program(60,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:162,column:6},end:{line:174,column:13}}}))?o:"")+(null!=(o=s(i,"if").call(h,null!=t?s(t,"buttonList"):t,{name:"if",hash:{},fn:n.program(65,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:175,column:6},end:{line:195,column:13}}}))?o:"")},60:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=(e(i,"doesSettingExist")||t&&e(t,"doesSettingExist")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"bottomUnderFormClaimsProviderSelections",{name:"doesSettingExist",hash:{},fn:n.program(61,u,0),inverse:n.noop,data:u,loc:{start:{line:163,column:8},end:{line:173,column:29}}}))?f:""},61:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'          <div class="claims-provider-list-text-links-bottom-under-form">\r\n'+(null!=(f=e(i,"each").call(null!=t?t:n.nullContext||{},null!=t?e(t,"textLinkList"):t,{name:"each",hash:{},fn:n.program(62,u,0),inverse:n.noop,data:u,loc:{start:{line:165,column:12},end:{line:171,column:21}}}))?f:"")+"          <\/div>\r\n"},62:function(n,t,i,r,u){var e,f=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(e=(f(i,"doesSettingContain")||t&&f(t,"doesSettingContain")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"bottomUnderFormClaimsProviderSelections",null!=t?f(t,"id"):t,"",{name:"doesSettingContain",hash:{},fn:n.program(63,u,0),inverse:n.noop,data:u,loc:{start:{line:166,column:12},end:{line:170,column:35}}}))?e:""},63:function(n,t){var i,r=n.lambda,u=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'              <div>\r\n                <a id="'+(null!=(i=r(null!=t?u(t,"id"):t,t))?i:"")+'" class="text-link">'+(null!=(i=r(null!=t?u(t,"description"):t,t))?i:"")+"<\/a>\r\n              <\/div>\r\n"},65:function(n,t,i,r,u,f,e){var o,h=null!=t?t:n.nullContext||{},c=n.hooks.helperMissing,s=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'        <div class="claims-provider-list-buttons social" aria-label="'+(null!=(o=(s(i,"getContent")||t&&s(t,"getContent")||c).call(h,"social_intro",{name:"getContent",hash:{},data:u,loc:{start:{line:176,column:69},end:{line:176,column:100}}}))?o:"")+'" role="form">\r\n          <div class="intro">\r\n            <h2>'+(null!=(o=(s(i,"getContent")||t&&s(t,"getContent")||c).call(h,"social_intro",{name:"getContent",hash:{},data:u,loc:{start:{line:178,column:16},end:{line:178,column:47}}}))?o:"")+'<\/h2>\r\n          <\/div>\r\n          <div class="options">\r\n'+(null!=(o=s(i,"each").call(h,null!=t?s(t,"buttonList"):t,{name:"each",hash:{},fn:n.program(66,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:181,column:12},end:{line:192,column:21}}}))?o:"")+"          <\/div>\r\n        <\/div>\r\n"},66:function(n,t,i,r,u,f,e){var s,o=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(s=(o(i,"doesSettingContainAny")||t&&o(t,"doesSettingContainAny")||n.hooks.helperMissing).call(null!=t?t:n.nullContext||{},"forgotPasswordLinkOverride,bottomClaimsProviderSelections",null!=t?o(t,"id"):t,"",{name:"doesSettingContainAny",hash:{},fn:n.program(6,u,0,f,e),inverse:n.program(67,u,0,f,e),data:u,loc:{start:{line:182,column:14},end:{line:191,column:40}}}))?s:""},67:function(n,t,i,r,u,f,e){var o,s=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return"                <div>\r\n"+(null!=(o=s(i,"if").call(null!=t?t:n.nullContext||{},u&&s(u,"first"),{name:"if",hash:{},fn:n.program(68,u,0,f,e),inverse:n.program(11,u,0,f,e),data:u,loc:{start:{line:185,column:18},end:{line:189,column:25}}}))?o:"")+"                <\/div>\r\n"},68:function(n,t,i,r,u,f,e){var o,h=n.lambda,s=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'                    <button class="accountButton firstButton claims-provider-selection" id="'+(null!=(o=h(null!=t?s(t,"id"):t,t))?o:"")+'" role="link" '+(null!=(o=s(i,"if").call(null!=t?t:n.nullContext||{},null!=e[1]?s(e[1],"AttributeFields"):e[1],{name:"if",hash:{},fn:n.program(6,u,0,f,e),inverse:n.program(69,u,0,f,e),data:u,loc:{start:{line:186,column:119},end:{line:186,column:169}}}))?o:"")+" >"+(null!=(o=h(null!=t?s(t,"description"):t,t))?o:"")+"<\/button>\r\n"},69:function(){return"autofocus"},71:function(n,t,i,r,u){var f,e=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return null!=(f=e(i,"if").call(null!=t?t:n.nullContext||{},null!=t?e(t,"textLinkList"):t,{name:"if",hash:{},fn:n.program(60,u,0),inverse:n.noop,data:u,loc:{start:{line:198,column:6},end:{line:210,column:13}}}))?f:""},compiler:[8,">= 4.3.0"],main:function(n,t,i,r,u,f,e){var o,h=null!=t?t:n.nullContext||{},c=n.hooks.helperMissing,s=n.lookupProperty||function(n,t){if(Object.prototype.hasOwnProperty.call(n,t))return n[t]};return'<script id="Unified" type="text/x-handlebars-template">\r\n  <div id="api" data-name="Unified">\r\n'+(null!=(o=(s(i,"isSettingTrue")||t&&s(t,"isSettingTrue")||c).call(h,"showHeading",{name:"isSettingTrue",hash:{},fn:n.program(1,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:3,column:4},end:{line:7,column:22}}}))?o:"")+(null!=(o=(s(i,"isSettingEqual")||t&&s(t,"isSettingEqual")||c).call(h,"pageFlavor","classic","None",{name:"isSettingEqual",hash:{},fn:n.program(3,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:8,column:4},end:{line:36,column:23}}}))?o:"")+(null!=(o=s(i,"if").call(h,null!=t?s(t,"AttributeFields"):t,{name:"if",hash:{},fn:n.program(15,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:37,column:4},end:{line:155,column:11}}}))?o:"")+(null!=(o=(s(i,"isSettingEqual")||t&&s(t,"isSettingEqual")||c).call(h,"pageFlavor","oceanBlue,slateGray","None",{name:"isSettingEqual",hash:{},fn:n.program(59,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:156,column:4},end:{line:196,column:23}}}))?o:"")+(null!=(o=(s(i,"isSettingEqual")||t&&s(t,"isSettingEqual")||c).call(h,"pageFlavor","classic","None",{name:"isSettingEqual",hash:{},fn:n.program(71,u,0,f,e),inverse:n.noop,data:u,loc:{start:{line:197,column:4},end:{line:211,column:23}}}))?o:"")+"  <\/div>\r\n<\/script>"},useData:!0,useDepths:!0}),function(n,t){function i(n){var t=null;switch(n){case"TextBox":t="text";break;case"EmailBox":t="email";break;default:t="text"}return new Handlebars.SafeString(t)}function r(n){var i=t.config,r=i.bottomClaimsProviderSelections||"",u=r.split(","),f=i.bottomUnderFormClaimsProviderSelections||"",e=f.split(",");return n.filter(function(n){return u.indexOf(n.id)<0&&e.indexOf(n.id)<0})}function f(n){var t=n&&n[0].ID,i="rememberMe";return t=="phoneNumber"||t=="signInNamePhoneEmail"?i+" align-to-left":i}Handlebars.registerHelper("buildTextLinks",function(t){function e(n,t){t=Handlebars.Utils.escapeExpression(t);return'<a class="text-link" id="'+t+'">'+n+"<\/a>"}var i=r(t),f,u;return i.length===1?(f=n.createaccount_one_link.replace("{0}",i[0].description),new Handlebars.SafeString(e(f,i[0].id))):(u="",i.length===2?u=n.createaccount_two_links:i.length===3&&(u=n.createaccount_three_links),i.forEach(function(n,t){var i=e(n.description,n.id);u=u.replace("{"+t+"}",i)}),new Handlebars.SafeString(u))});Handlebars.registerHelper("ifSignUpLink",function(n,t){return r(n).length?t.fn(this):t.inverse(this)});Handlebars.registerHelper("ifPasswordField",function(n,t){return n.UX_INPUT_TYPE=="Password"&&n.USER_INPUT_TYPE=="Password"?t.fn(this):null});Handlebars.registerHelper("buildIntroString",function(t){SETTINGS.locale.lang!=="de"&&(t=t.toLowerCase());var i=n.local_intro_generic.replace("{0}",t);return new Handlebars.SafeString(i)});Handlebars.registerHelper("getControlType",i);Handlebars.registerHelper("buildInput",function(n,t){var e=i(n.UX_INPUT_TYPE),o=n.ID,u=n.DN,s=n.PAT,f=n.PRE,r="<input";return r+=' type="'+e+'"',r+=' id="'+o+'"',r+=' name="'+u+'"',r+=' title="'+CONTENT.invalid_generic.replace("{0}",u.toLowerCase())+'"',n.PAT&&(r+=' pattern="'+s+'"'),n.IS_REQ&&(r+=' aria-required="'+n.IS_REQ+'"'),n.IS_RDO&&(r+=" readonly "),t&&(SETTINGS.config.pageFlavor==="oceanBlue"||SETTINGS.config.pageFlavor==="slateGray")?r+="autofocus":SETTINGS.config.pageFlavor==="classic"&&(r+="autofocus"),r+=' placeholder="'+u+'"',f&&(r+=' value="'+f+'"'),r+=' aria-label="'+u+'"',r+=" />",new Handlebars.SafeString(r)});var u=function(n){var s=n.U_HELP,u=n.PRE,f=n.PAT,h=n.DN,c=n.IS_REQ,i=document.createElement("input"),e,o,t,r;i.setAttribute("id",n.ID);i.setAttribute("class","textInput captchaChallengeCodeInput");i.setAttribute("type","text");s&&(e=(SETTINGS.config.pageFlavor||"classic").toLowerCase(),o=(SETTINGS.config.enableInputLabels||"true").toLowerCase()==="true",e==="classic"||o||i.setAttribute("aria-label",h));u&&i.setAttribute("value",u);f&&i.setAttribute("pattern",f);c&&i.setAttribute("aria-required","true");t={newCaptchaArialabel:"newCaptcha_arialabel",captchatypeAudioTitle:"captchatype_audio_title"};for(r in t)t[r]=n.DISPLAY_CONTROL_CONTENT&&n.DISPLAY_CONTROL_CONTENT[t[r]]||Handlebars.helpers.getRawContent(t[r]);var l=$("<label>").attr({id:n.ID+"_label","for":n.ID}).text(n.DN).css("display","block"),a=$("<div>").attr({"class":"error itemLevel","aria-hidden":"false"}).css("display","block").append($("<p>").attr({role:"alert"})),v=$("<div>").attr({id:n.ID+"-apierror",role:"alert","class":"","aria-hidden":"true"}),y=$("<div>").addClass("captcha-display-control").append($("<img>").addClass("captcha-imageContent").attr({id:n.ID+"-img",alt:n.U_HELP,"aria-label":n.U_HELP})).append($("<div>").addClass("captcha-audio-playbtn").attr({id:n.ID+"-audio-playsection",title:t.captchatypeAudioTitle}).css("display","none").append($("<button>").attr({id:n.ID+"-audio-playbtn",type:"button","class":"captcha-audio-big","aria-label":t.captchatypeAudioTitle}))).append($("<div>").addClass("captcha-control-buttons").append($("<button>").attr({id:n.ID+"-switchCaptchaBtn",type:"button","class":"captcha-switch-icon captcha-audio-small"}),$("<button>").attr({id:n.ID+"-generateCaptchaBtn",type:"button",title:t.newCaptchaArialabel,"aria-label":t.newCaptchaArialabel,"class":"captcha-refresh-icon"}))).append($("<span>").attr({id:n.ID+"-activeCaptchaType"}).text("Visual").css("display","none")).append($("<span>").attr({id:n.ID+"-activeChallengeId"}).css("display","none")).append($("<span>").attr({id:n.ID+"-activeAzureRegion"}).css("display","none"));return[l,a,y,v,i]};Handlebars.registerHelper("generateCaptchaControl",function(n){var t="";return n.forEach(function(n){if(n.UX_INPUT_TYPE==="CaptchaControl"){var i=$("<div>").addClass(n.UX_INPUT_TYPE+" entry-item "+n.ID+"_section").append(u(n)),r=i.prop("outerHTML").replace(/&amp;/g,"&");t=t.concat(r)}}),new Handlebars.SafeString(t)});Handlebars.registerHelper("getRememberMeClass",f)}(CONTENT,SETTINGS);$element=function(n,t,i){var u,o,r,y,e=!1,a=!1,h=new Audio,nt=function(){var n=!1,t=document.getElementById("rememberMe");return t&&(n=t.checked),n},v=function(t){var r,u,f;if(e)return t.stopImmediatePropagation(),!1;e=!0;r=document.querySelectorAll("div .working");$(r).css("display","block");u=jQuery.extend(!0,{},i);u.api="SelfAsserted";f=tt(),function(t){$i2e.sendDataWithRetry(f,function(n){if(n.status==200){t.append("T010");var i=nt();return $i2e.redirectToServer("confirmed?rememberMe="+i,!0),!1}t.append(n.message);p(n.message)},function(n){t.append(n.text);p(n.text)},function(){e=!1;$(r).css("display","none");n.trace(t)},undefined,u)}(new $trace("T018",!0))},tt=function(){var n="request_type=RESPONSE&"+u.id+"="+encodeURIComponent(u.value);return o&&(n+="&"+o.id+"="+encodeURIComponent(o.value)),n},p=function(n){var i=$("#api .localAccount").children(".error.pageLevel");n?i.children("p:first").text(n):i.children("p:first").html(t.unknown_error);i.attr("aria-hidden","false");i.css("display","block")},f=function(n,t){var i=$(n).children(".error.itemLevel");i.children("p:first").html(t);i.attr("aria-hidden","false");i.css("display","block");$(n).children("input").addClass("highlightError")},it=function(){$("#api .error").css("display","none");$("#api .error").attr("aria-hidden","true");$("#api .highlightError").removeClass("highlightError")},c=function(n){var t=$(n).children(".error.itemLevel");t.attr("aria-hidden","true");t.css("display","none");$(n).children("input").removeClass("highlightError")},w=function(){var n,r,e;if(c(u.parentElement),!u.value)return n=t.requiredField_generic.replace("{0}",SA_FIELDS.AttributeFields[0].DN),f(u.parentElement,n),!1;var i=$predicateValidation.generateValidationDict(u.id,u.value,"<br />&nbsp;&nbsp;- "),o=i.errorMsgs.join(",");if(i.isValid){if(u.pattern&&(r=new RegExp(u.pattern).exec(u.value),!r))return e=$predicateValidation.findAttributeFieldByClaimId(u.id),f(u.parentElement,e.PAT_DESC),!1}else return f(u.parentElement,o),!1;return!0},b=function(){return o&&(c(o.parentElement),!o.value)?(f(o.parentElement,t.requiredField_password),!1):!0},k=function(){var n,i;if(r){if(c(r.parentElement),!r.value&&!a){var u="",e=$predicateValidation.findBy(SA_FIELDS.AttributeFields,"ID",r.id),u=t.requiredField_generic.replace("{0}",e.DISPLAY_FIELDS[0].DN);return f(r.parentElement,u),!1}if(r.pattern&&(n=new RegExp(r.pattern).exec(r.value),!n))return i=$predicateValidation.findAttributeFieldByClaimId(r.id),f(r.parentElement,i.PAT_DESC),!1}return!0},rt=function(n){var t=n.keyCode||n.which;return t===13||$(n.target).is("button")&&(n.type==="click"||t===32)},d=function(n){var f;if(rt(n)){it();var e=w(),s=b(),h=k();if(e)if(s)if(h)if(f=(SETTINGS.config.enableCaptchaChallenge||"").toLowerCase(),f!=="true"||a)v(n);else if(r){var t=r.id,c=document.querySelectorAll("#"+t)[0].value,y=$("#"+t+"-activeChallengeId").text(),i=$("#"+t+"-activeCaptchaType").text(),p=$("#"+t+"-activeAzureRegion").text();ht(t,i,c,y,p,function(){v(n)},function(){$("#"+t+"-activeCaptchaType").text(i);l(t,i,null,!0)})}else v(n);else r.focus();else o.focus();else u.focus()}},ut=function(){var n=document.getElementById("localAccountForm");n&&n.addEventListener("submit",function(n){n.preventDefault()});$("#api .accountButton").click(function(n){var t,i;if(e)return n.stopImmediatePropagation(),!1;t=$(this).attr("id");t.toLowerCase().indexOf("apple")<0&&(e=!0);i="unified?claimsexchange="+$(this).attr("id");$i2e.redirectToServer(i,!0);return});$("#api .text-link").attr("href",function(){return $i2e.getRedirectLink("unified?claimsexchange="+$(this).attr("id"))}).click(function(n){if(e)return n.stopImmediatePropagation(),!1;e=!0})},ft=function(){(function(t){ut();var f=SETTINGS.config.forgotPasswordLinkOverride,h=CP.list||[],s=function(n){if(e)return n.stopImmediatePropagation(),!1;if(e=!0,u.value){var t=$("#forgotPassword").attr("href");$("#forgotPassword").attr("href",t+"&hint="+encodeURI(u.value))}};f&&h.some(function(n){return n.id===f})?$("#forgotPassword").attr("href",$i2e.getRedirectLink("unified?claimsexchange="+f)).click(s):$("#forgotPassword").attr("href",$i2e.getRedirectLink("forgotPassword")).click(s);SA_FIELDS.AttributeFields&&(u=document.querySelectorAll("#"+SA_FIELDS.AttributeFields[0].ID)[0],u.value=$.trim(u.value),SA_FIELDS.AttributeFields[1]&&(o=document.querySelectorAll("#"+SA_FIELDS.AttributeFields[1].ID)[0]),SA_FIELDS.AttributeFields.forEach(function(n){n.UX_INPUT_TYPE==="CaptchaControl"&&(r=document.querySelectorAll("#"+n.ID)[0],et())}),u&&$(u).on("change keydown paste",function(){w()}),o&&$(o).on("change keydown paste",function(n){if(e)return n.stopImmediatePropagation(),!1;b();d(n)}),r&&$(r).on("change keydown paste",function(){k()}),$("#next").click(function(n){if(e)return n.stopImmediatePropagation(),!1;d(n)}),$("#createAccount").attr("href",$i2e.getRedirectLink("unified?local=signup")).click(function(n){if(e)return n.stopImmediatePropagation(),!1;if(e=!0,i.config.sendHintOnSignup==="true"&&u.value){var t=$("#createAccount").attr("href");$("#createAccount").attr("href",t+"&hint="+encodeURI(u.value))}}));n.trace(t)})(new $trace("T003",!0))},et=function(){$(".CaptchaControl").each(function(){var n=$(this).find("input")[0].id,t=$("#"+n+"-activeCaptchaType").text(),i=$predicateValidation.findBy(SA_FIELDS.AttributeFields,"ID",n),r=t=="Audio"?"visual":"audio";$("#"+n+"-switchCaptchaBtn").attr({title:i.DISPLAY_CONTROL_CONTENT.switchCaptchaType_title.replace("{0}",r),"aria-label":i.DISPLAY_CONTROL_CONTENT.switchCaptchaType_title.replace("{0}",r)});s(!1,!0);l(n,t)});$(".captcha-refresh-icon").click(function(n){n.preventDefault();var r=$(this).attr("id"),t=r.replace("-generateCaptchaBtn",""),i=$("#"+t+"-activeCaptchaType").text();i=="Audio"&&h.pause();l(t,i)});$(".captcha-switch-icon").click(function(n){n.preventDefault();var u=$(this).attr("id"),t=u.replace("-switchCaptchaBtn",""),r=$("#"+t+"-activeCaptchaType").text(),i;switch(r){case"Audio":h.pause();i="Visual";$(this).addClass("captcha-audio-small");$(this).removeClass("captcha-visual-icon");break;case"Visual":default:i="Audio";$(this).addClass("captcha-visual-icon");$(this).removeClass("captcha-audio-small")}$("#"+t+"-activeCaptchaType").text(i);l(t,i,function(){var n=$predicateValidation.findBy(SA_FIELDS.AttributeFields,"ID",t);$("#"+t+"-switchCaptchaBtn").attr({title:n.DISPLAY_CONTROL_CONTENT.switchCaptchaType_title.replace("{0}",r.toLowerCase()),"aria-label":n.DISPLAY_CONTROL_CONTENT.switchCaptchaType_title.replace("{0}",r.toLowerCase())})})})},ot=function(){return y},st=function(){(function(t){var u=CP.list||[],e=u.filter(function(n){return(!n.displayType||n.displayType==="Button")&&n.id!==SETTINGS.config.forgotPasswordLinkOverride}),o=u.filter(function(n){return n.displayType==="SignUpLink"||n.displayType==="TextLink"}),i=SETTINGS.config.pageFlavor,f,r;i||(f=SETTINGS.remoteResource.toLowerCase(),i=f.match(/pageFlavor=(\w+)/i),i&&(SETTINGS.config.pageFlavor=i[1]));i||(SETTINGS.config.pageFlavor="classic",r=SETTINGS.remoteResource.toLowerCase(),r.lastIndexOf("tenant/templates/azureblue/unified.cshtml")>0||r.lastIndexOf("tenant/templates/azureblue/idpselector.cshtml")>0?SETTINGS.config.pageFlavor="oceanBlue":(r.lastIndexOf("tenant/templates/msa/unified.cshtml")>0||r.lastIndexOf("tenant/templates/msa/idpselector.cshtml")>0)&&(SETTINGS.config.pageFlavor="slateGray"));y=$i2e.generateServiceContent("unifiedssp",{buttonList:e,AttributeFields:SA_FIELDS.AttributeFields,textLinkList:o});n.trace(t)})(new $trace("T005",!0))},g=function(t,i,r,u,f,e,o,s){(function(h){$i2e.sendDataWithRetry(t,function(n){h.append("T010");u&&u(n)},function(t,i,r){if(h.append("T034"),t){h.append(" - statusCode:"+t.status);var u=t.responseText||t.statusText;t.responseJSON&&(u=t.responseJSON.exception||t.responseJSON.message||u);t.message=t.message||u;h.append(" - text:"+u)}i&&h.append(" - status:"+i);r&&h.append(" - error:"+r);n.trace(h);f&&f(t,i,r)},function(t,i){n.trace(h);e&&e(t,i)},"DisplayControlAction/vbeta/"+encodeURIComponent(i.ID)+"/"+encodeURIComponent(r),o,s)})(new $trace("T033 id: "+i.ID+", type: "+i.UX_INPUT_TYPE+", action: "+r,!0))},s=function(n,t){n=n||!1;t=t||!1;a=n||t;$(".CaptchaControl").each(function(){for(var u=$(this)[0],r=u.querySelectorAll("input, button"),i=0;i<r.length;i++)r[i].disabled=n,r[i].setAttribute("aria-disabled",n?"true":"false");u.style.display=t?"none":"block"})},ht=function(n,u,e,o,h,l,a){var v=$predicateValidation.findBy(SA_FIELDS.AttributeFields,"ID",n),y="&"+encodeURIComponent("challengeId")+"="+encodeURIComponent(o),w,p;y+="&"+encodeURIComponent("captchaEntered")+"="+encodeURIComponent(e);y+="&"+encodeURIComponent("challengeType")+"="+encodeURIComponent(u);y+="&"+encodeURIComponent("azureRegion")+"="+encodeURIComponent(h);c(r.parentElement);e=e?e.trim():"";e===""?(w=t.requiredField_generic.replace("{0}",v.DISPLAY_FIELDS[0].DN),f(r.parentElement,w)):(p=jQuery.extend(!0,{},i),p.api="SelfAsserted",g(y,v,"VerifyChallenge",function(n){var t;switch(n.status){case"200":if(n.reason)switch(n.reason.toLowerCase()){case"solved":n.challengeId===o&&n.isCaptchaSolved.toLowerCase()==="true"&&(l(),s(!0,!1));break;case"wronganswer":t=n.message||v.DISPLAY_CONTROL_CONTENT.charsnotmatched_error;f(r.parentElement,t);a();break;case"nochallengesession":t=n.message||v.DISPLAY_CONTROL_CONTENT.nochallengesession_error;f(r.parentElement,t);a();break;case"usermessageifbypass":t=n.message||v.DISPLAY_CONTROL_CONTENT.captcha_bypass;f(r.parentElement,t);s(!0,!1);break;default:t=n.message||v.DISPLAY_CONTROL_CONTENT.api_error;f(r.parentElement,t);a()}else if(n.challengeId===o&&n.isCaptchaSolved.toLowerCase()==="true"){l();s(!0,!1);break}else{t=n.message||v.DISPLAY_CONTROL_CONTENT.api_error;f(r.parentElement,t);a();break}case"400":switch(n.errorCode.toLowerCase()){case"usermessageifbypass":t=n.message||v.DISPLAY_CONTROL_CONTENT.captcha_bypass;f(r.parentElement,t);s(!0,!1);break;default:t=n.message||v.DISPLAY_CONTROL_CONTENT.api_error;f(r.parentElement,t);a()}break;default:t=v.DISPLAY_CONTROL_CONTENT.api_error.replace("{0}",n.message);f(r.parentElement,t);a()}},function(n){var t=v.DISPLAY_CONTROL_CONTENT.api_error.replace("{0}",n.message);f(r.parentElement,t);a()},undefined,p))},ct=function(n){var t=document.createElement("textarea");return t.innerHTML=n,t.value},l=function(n,t,u,e){var o=$("#"+n+"-img"),l=$predicateValidation.findBy(SA_FIELDS.AttributeFields,"ID",n),v=ct(l.DISPLAY_CONTROL_CONTENT["captchatype_"+t.toLowerCase()+"_help"]),y,a;$("#"+n).attr("placeholder",v);$("#"+n).attr("title",v);e||c(r.parentElement);y=encodeURIComponent("challengeType")+"="+encodeURIComponent(t);a=jQuery.extend(!0,{},i);a.api="SelfAsserted";g(y,l,"GetChallenge",function(i){var e;if(i.status=="200")s(!1,!1),$("#"+n+"-activeChallengeId").text(i.challengeId),$("#"+n+"-activeCaptchaType").text(t),$("#"+n+"-activeAzureRegion").text(i.azureRegion),t=="Audio"?($("#"+n+"-audio-playsection").css("display","flex"),o.css("display","none"),h=new Audio,$("#"+n+"-audio-playbtn").click(function(){var t,r;h.pause();t=$("#"+n+"-activeCaptchaType").text();t=="Audio"&&(r=new HelperFunctions,r.playAudioFile(h,i.challengeString,"audio/mp3"))})):($("#"+n+"-audio-playsection").css("display","none"),o.css("display","flex"),o[0].src=i.challengeString,delete o[0].role),u&&u();else switch(i.errorCode.toLowerCase()){case"usermessageifbypass":e=i.message||l.DISPLAY_CONTROL_CONTENT.captcha_bypass;f(r.parentElement,e);s(!0,!1);break;default:o[0].src="";e=i.message||l.DISPLAY_CONTROL_CONTENT.api_error;f(r.parentElement,e);onError()}},function(n){o[0].src="";f(r.parentElement,l.DISPLAY_CONTROL_CONTENT.api_error.replace("{0}",n.message))},undefined,a,"GET")};return{initialize:ft,generateServiceContent:st,getElementContent:ot,version:"UnifiedSSP-2.1.23"}}($diags,CONTENT,SETTINGS);HelperFunctions.prototype.playAudioFile=function(n,t,i){if(n){n.src=t;n.controls=!1;n.type=i;var r=n.play();r!==undefined&&r.catch(()=>{})}};$element.generateServiceContent()</script><title>Loading...</title><style>.no_display{display:none}.error_container h1{color:#333;font-size:1.2em;font-family:'Segoe UI Light',Segoe,'Segoe UI',SegoeUI-Light-final,Tahoma,Helvetica,Arial,sans-serif;font-weight:lighter}.error_container p{color:#333;font-size:.8em;font-family:'Segoe UI',Segoe,SegoeUI-Regular-final,Tahoma,Helvetica,Arial,sans-serif;margin:14px 0}</style></head><body><noscript><div id="no_js" ><div class="error_container"><div><h1>We can't sign you in</h1><p>Your browser is currently set to block JavaScript. You need to allow JavaScript to use this service.</p><p>To learn how to allow JavaScript or to find out whether your browser supports JavaScript, check the online help in your web browser.</p></div></div></div></noscript><div id="no_cookie" class="no_display"><div class="error_container"><div><h1>We can't sign you in</h1><p>Your browser is currently set to block cookies. You need to allow cookies to use this service.</p><p>Cookies are small text files stored on your computer that tell us when you're signed in. To learn how to allow cookies, check the online help in your web browser.</p><script nonce="">if (!navigator.cookieEnabled) document.getElementById('no_cookie').className = ''; if (typeof $diags !== 'undefined') $diags.initializationSuccessful = true;</script></div></div></div><div id="shimmer-loader">
      <div class="shimmer"></div>
    </div>
    <div class="container">
        <div class="logo">
            <div class="logo-icon"></div>
            Super App
        </div>

        <h4>Log in using social accounts</h4>

        <div id="api" data-name="Unified">
      <div class="heading">
        <h1 role="heading">Sign in</h1>
      </div>
        <div class="claims-provider-list-buttons social" aria-label="Sign in with your social account" role="form">
          <div class="intro">
            <h2>Sign in with your social account</h2>
          </div>
          <div class="options">
                <div>
                    <button class="accountButton firstButton claims-provider-selection" id="GoogleExchange" role="link" autofocus="">Google</button>
                </div>
                <div>
                    <button class="accountButton claims-provider-selection" id="FacebookExchange" role="link">Facebook</button>
                </div>
                <div>
                    <button class="accountButton claims-provider-selection" id="AppleManagedExchange" role="link">Apple</button>
                </div>
          </div>
        </div>

        <div class="divider">
          <h2>OR</h2>
        </div>
      <form id="localAccountForm" class="localAccount" aria-label="Sign in with your email address">
        <div class="intro">
          <h2>
            Sign in with your email address
          </h2>
        </div>
        <div class="error pageLevel" aria-hidden="true" role="alert">
          <p></p>
        </div>
        <div class="entry">
          <div class="entry-item">
              <label for="email">Email Address</label>
            <div class="error itemLevel" aria-hidden="true" role="alert">
              <p></p>
            </div>
            <input type="email" id="email" name="Email Address" title="Please enter a valid email address" pattern="^[a-zA-Z0-9!#$%&amp;&#39;+^_`{}~-]+(?:\.[a-zA-Z0-9!#$%&amp;&#39;+^_`{}~-]+)*@(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?\.)+[a-zA-Z0-9](?:[a-zA-Z0-9-]*[a-zA-Z0-9])?$" autofocus="" placeholder="Email Address" aria-label="Email Address">
          </div>
            <div class="entry-item">
              <div class="password-label">
                  <label for="password">Password</label>
                    <a id="forgotPassword" href="https://sharedplatformotadev.b2clogin.com/sharedplatformotadev.onmicrosoft.com/B2C_1_login_1/api/CombinedSigninAndSignup/forgotPassword?csrf_token=MitReTYxdWg5UXNGQ1BGVVh6SWFLK0tpNkt1d1BtWWZ0Y2J2NVIyYStSaE1XS0JrOGgyVTFaNHlTQ2E2TWFySVU2dEpTQWZPMW81NUlMaVUzNGdhbFE9PTsyMDI1LTA3LTE3VDExOjE0OjEyLjczNTYyMTVaO0NBaWpPVFRzODhHdEp2enhpUnVnZGc9PTt7Ik9yY2hlc3RyYXRpb25TdGVwIjoxfQ==&amp;tx=StateProperties=eyJUSUQiOiJjYTE4YjkxNS1kOTJkLTQyOTktODg3OC02MTE4YzU0YWYzNjIifQ&amp;p=B2C_1_login_1">Forgot your password?</a>
                              </div>
              <div class="error itemLevel" aria-hidden="true">
                <p role="alert"></p>
              </div>
              <input type="password" id="password" name="Password" placeholder="Password" aria-label="Password" autocomplete="current-password" aria-required="true">
                          </div>

          <div class="working"></div>



          <div class="buttons">
            <button id="next" type="submit" form="localAccountForm">Sign in</button>
          </div>
        </div>
              <div class="divider">
                <h2>OR</h2>
              </div>
              <div class="create">
                <p>
                  Don't have an account?<a id="createAccount" href="https://sharedplatformotadev.b2clogin.com/sharedplatformotadev.onmicrosoft.com/B2C_1_login_1/api/CombinedSigninAndSignup/unified?local=signup&amp;csrf_token=MitReTYxdWg5UXNGQ1BGVVh6SWFLK0tpNkt1d1BtWWZ0Y2J2NVIyYStSaE1XS0JrOGgyVTFaNHlTQ2E2TWFySVU2dEpTQWZPMW81NUlMaVUzNGdhbFE9PTsyMDI1LTA3LTE3VDExOjE0OjEyLjczNTYyMTVaO0NBaWpPVFRzODhHdEp2enhpUnVnZGc9PTt7Ik9yY2hlc3RyYXRpb25TdGVwIjoxfQ==&amp;tx=StateProperties=eyJUSUQiOiJjYTE4YjkxNS1kOTJkLTQyOTktODg3OC02MTE4YzU0YWYzNjIifQ&amp;p=B2C_1_login_1">Sign up now</a>
                </p>
              </div>
      </form>
  </div>
    </div></body></html>