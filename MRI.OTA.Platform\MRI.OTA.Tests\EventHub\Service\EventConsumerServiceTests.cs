﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Application.Services;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.UnitTestCases.EventHub.Service
{
    public class EventConsumerServiceTests
    {
        private readonly Mock<IEventConsumerRepository> _mockRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<ILogger<EventConsumerService>> _mockLogger;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly EventConsumerService _service;

        public EventConsumerServiceTests()
        {
            _mockRepository = new Mock<IEventConsumerRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockLogger = new Mock<ILogger<EventConsumerService>>();
            _mockConfiguration = new Mock<IConfiguration>();

            _service = new EventConsumerService(
                _mockLogger.Object,
                _mockRepository.Object,
                _mockMapper.Object,
                _mockConfiguration.Object
            );
        }

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllEventConsumers()
        {
            // Arrange  
            var entities = new List<EventConsumer>
           {
               new EventConsumer { EventId = 1, EventConsumerId = "EC1" },
               new EventConsumer { EventId = 2, EventConsumerId = "EC2" }
           };
            var models = new List<EventConsumerModel>
           {
               new EventConsumerModel { EventConsumerId = "EC1" },
               new EventConsumerModel { EventConsumerId = "EC2" }
           };

            _mockRepository.Setup(repo => repo.GetAllAsync()).ReturnsAsync(entities);
            _mockMapper.Setup(mapper => mapper.Map<IEnumerable<EventConsumerModel>>(entities)).Returns(models);

            // Act  
            var result = await _service.GetAllAsync();

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(2, result.Count()); // Fix: Use Count() method instead of Count property  
        }

        [Fact]
        public async Task GetByIdAsync_ShouldReturnEventConsumerModel_WhenIdIsValid()
        {
            // Arrange  
            var entity = new EventConsumer { EventId = 1, EventConsumerId = "EC1" };
            var model = new EventConsumerModel { EventConsumerId = "EC1" };

            _mockRepository.Setup(repo => repo.GetByIdAsync(1, "EventId")).ReturnsAsync(entity);
            _mockMapper.Setup(mapper => mapper.Map<EventConsumerModel>(entity)).Returns(model);

            // Act  
            var result = await _service.GetByIdAsync(1, "EventId");

            // Assert  
            Assert.NotNull(result);
            Assert.Equal("EC1", result.EventConsumerId);
        }

        [Fact]
        public async Task AddAsync_ShouldReturnNewEntityId()
        {
            // Arrange  
            var model = new EventConsumerModel { EventConsumerId = "EC1" };
            var entity = new EventConsumer { EventConsumerId = "EC1" };

            _mockMapper.Setup(mapper => mapper.Map<EventConsumer>(model)).Returns(entity);
            _mockRepository.Setup(repo => repo.AddAsync(entity)).ReturnsAsync(1);

            // Act  
            var result = await _service.AddAsync(model);

            // Assert  
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task UpdateAsync_ShouldReturnNumberOfRowsAffected()
        {
            // Arrange  
            var model = new EventConsumerModel { EventConsumerId = "EC1" };
            var entity = new EventConsumer { EventConsumerId = "EC1" };

            _mockMapper.Setup(mapper => mapper.Map<EventConsumer>(model)).Returns(entity);
            _mockRepository.Setup(repo => repo.UpdateAsync(1, "EventId", entity)).ReturnsAsync(1);

            // Act  
            var result = await _service.UpdateAsync(1, "EventId", model);

            // Assert  
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task DeleteAsync_ShouldReturnNumberOfRowsAffected()
        {
            // Arrange  
            _mockRepository.Setup(repo => repo.DeleteAsync(1, "EventId")).ReturnsAsync(1);

            // Act  
            var result = await _service.DeleteAsync(1, "EventId");

            // Assert  
            Assert.Equal(1, result);
        }
    }
}
