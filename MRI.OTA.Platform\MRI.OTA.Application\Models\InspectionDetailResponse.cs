﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.Application.Models
{
    public class InspectionDetailResponse
    {
        public string ManagementId { get; set; }
        public string TenancyId { get; set; }
        public string PropertyId { get; set; }
        public List<InspectionsListResponse> Inspections { get; set; }
    }
    public class InspectionsListResponse
    {
        public string InspectionId { get; set; }
        public string Status { get; set; }
        public DateTime InspectionDate { get; set; }
        public DateTime InspectionStartTime { get; set; }
        public DateTime InspectionEndTime { get; set; }
        public string Summary { get; set; }
    }
}
