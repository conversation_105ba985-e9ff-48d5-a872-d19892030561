﻿using MRI.OTA.Common.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.DBCore.Entities
{
    public class UserNotificationDetails
    {
        [ExcludeColumn]
        public int UserNotificationDetailId { get; set; }
        public int NotificationId { get; set; }
        public int UserId { get; set; }
        public string Title { get; set; }
        public string Body { get; set; }
        public bool IsRead { get; set; } = false;
    }
}
