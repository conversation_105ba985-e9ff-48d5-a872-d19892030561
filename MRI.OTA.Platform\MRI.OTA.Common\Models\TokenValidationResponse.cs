using System.Text.Json.Serialization;

namespace MRI.OTA.Common.Models
{
    /// <summary>
    /// Response model for token validation
    /// </summary>
    public class TokenValidationResponse
    {
        /// <summary>
        /// Indicates if the token is valid
        /// </summary>
        [JsonPropertyName("is_valid")]
        public bool IsValid { get; set; }

        /// <summary>
        /// User email from token claims
        /// </summary>
        [JsonPropertyName("user_email")]
        public string? UserEmail { get; set; }

        /// <summary>
        /// User ID from token claims
        /// </summary>
        [JsonPropertyName("user_id")]
        public string? UserId { get; set; }

        /// <summary>
        /// Token expiration time
        /// </summary>
        [JsonPropertyName("expires_at")]
        public DateTime? ExpiresAt { get; set; }

        /// <summary>
        /// Token source (e.g., custom_enhanced)
        /// </summary>
        [JsonPropertyName("token_source")]
        public string? TokenSource { get; set; }

        /// <summary>
        /// Token issuer
        /// </summary>
        [JsonPropertyName("issuer")]
        public string? Issuer { get; set; }

        /// <summary>
        /// Token audience
        /// </summary>
        [JsonPropertyName("audience")]
        public string? Audience { get; set; }

        /// <summary>
        /// Error message if validation fails
        /// </summary>
        [JsonPropertyName("error_message")]
        public string? ErrorMessage { get; set; }
    }
} 