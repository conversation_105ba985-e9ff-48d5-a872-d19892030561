﻿using AutoMapper;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.Application.Services
{
    public class EventConsumerService : BaseService<EventConsumer, EventConsumerModel, int>, IEventConsumerService
    {
        private IEventConsumerRepository _eventConsumerRepository { get; set; }

        private IMapper _mapper { get; set; }

        private readonly IConfiguration _configuration;

        private ILogger _logger { get; set; }
        /// <summary>
        /// EventConsumer service constructor
        /// </summary>
        /// <param name="repository"></param>
        /// <param name="mapper"></param>
        public EventConsumerService(ILogger<EventConsumerService> logger, IEventConsumerRepository repository, IMapper mapper, IConfiguration configuration)
        : base(logger, repository, mapper)
        {
            _eventConsumerRepository = repository;
            _mapper = mapper;
            _configuration = configuration;
            _logger = logger;
        }
    }
}
