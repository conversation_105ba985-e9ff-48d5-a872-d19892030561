﻿using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Protocols;
using Microsoft.IdentityModel.Protocols.OpenIdConnect;
using Microsoft.IdentityModel.Tokens;
using MRI.OTA.Common.Models;
using MRI.OTA.Core.Repositories;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Infrastructure.Authentication.Interfaces;
using MRI.OTA.Infrastructure.Caching.Interfaces;

namespace MRI.OTA.Infrastructure.Authentication
{
    /// <summary>
    /// Token service
    /// </summary>
    public class JwtTokenValidator : IJwtTokenValidator
    {
        private readonly IConfiguration _configuration;
        private readonly ILogger<JwtTokenValidator> _logger;
        private readonly OpenIdConnectConfiguration _pkceOpenIdConfig;
        private readonly OpenIdConnectConfiguration _clientCredentialsOpenIdConfig;
        private readonly IServiceProvider _serviceProvider;
        private readonly ICacheService _cacheService;

        /// <summary>
        /// Constructor
        /// </summary>
        public JwtTokenValidator(
            IConfiguration configuration,
            ILogger<JwtTokenValidator> logger,
            IServiceProvider serviceProvider,
            ICacheService cacheService)
        {
            _logger = logger;
            _configuration = configuration;
            _serviceProvider = serviceProvider;
            _cacheService = cacheService;

            // Initialize configurations at startup
            _pkceOpenIdConfig = GetOpenIdConfiguration(false).GetAwaiter().GetResult();
            _clientCredentialsOpenIdConfig = GetOpenIdConfiguration(true).GetAwaiter().GetResult();
        }

        private async Task<OpenIdConnectConfiguration> GetOpenIdConfiguration(bool isClientCredentials)
        {
            var tenantName = _configuration["Authentication:TenantName"];
            var tenantId = _configuration["Authentication:TenantId"];
            var policyId = _configuration["Authentication:SignUpSignInPolicyId"];

            string configUrl = isClientCredentials
                ? $"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/{policyId}/v2.0/.well-known/openid-configuration"
                : $"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/v2.0/.well-known/openid-configuration?p={policyId}";

            var configurationManager = new ConfigurationManager<OpenIdConnectConfiguration>(
                configUrl,
                new OpenIdConnectConfigurationRetriever());

            return await configurationManager.GetConfigurationAsync();
        }

        /// <summary>
        /// Validate token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public Task<ClaimsPrincipal> ValidateToken(string token, bool isClientCredentials = false)
        {
            var tenantName = _configuration["Authentication:TenantName"];
            var tenantId = _configuration["Authentication:TenantId"];
            var clientId = _configuration["Authentication:ClientId"];
            var policyId = _configuration["Authentication:SignUpSignInPolicyId"];

            var tokenHandler = new JwtSecurityTokenHandler();

            // Use the pre-loaded configuration
            var openIdConfig = isClientCredentials ? _clientCredentialsOpenIdConfig : _pkceOpenIdConfig;

            var validationParameters = new TokenValidationParameters
            {
                ValidateIssuer = true,
                ValidIssuers = GetValidIssuers(tenantName, tenantId, policyId, isClientCredentials),
                ValidateAudience = false,
                ValidAudience = clientId,
                ValidateLifetime = true,
                ClockSkew = TimeSpan.FromMinutes(5),
                IssuerSigningKeys = openIdConfig.SigningKeys
            };

            try
            {
                var principal = tokenHandler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);
                return Task.FromResult(principal); // Wrap the result in a Task
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Token validation failed for {AuthType}",
                    isClientCredentials ? "ClientCredentials" : "PKCE");
                throw;
            }
        }

        /// <summary>
        /// Read JWT token
        /// </summary>
        /// <param name="token"></param>
        /// <returns></returns>
        public Task<List<Claim>> GetClaims(string token)
        {
            var tokenHandler = new JwtSecurityTokenHandler();

            // Check if the token can be read
            if (!tokenHandler.CanReadToken(token))
            {
                return Task.FromResult(new List<Claim>());
            }

            // Decode the token
            var jwtToken = tokenHandler.ReadJwtToken(token);
            return Task.FromResult(jwtToken.Claims.ToList());
        }

        public async Task<bool> ValidateAccessToken(string accessKey, string accessToken)
        {
            // Create a cache key based on the access key
            string cacheKey = $"access_key:{accessKey}";

            // Try to check the validation result from cache first
            var cachedResult = await _cacheService.ExistsAsync(cacheKey);

            // If found in cache, return the cached result
            if (cachedResult) return true;


            // Not found in cache, query the database
            using (var scope = _serviceProvider.CreateScope())
            {
                var dataSourceRepository = scope.ServiceProvider.GetRequiredService<IDataSourceRepository>();
                var result = await dataSourceRepository.GetUserDataSource(accessKey, accessToken);
                var isValid = result != null;

                // Store the result in cache for future requests
                await _cacheService.SetAsync(cacheKey, accessToken, expirationMinutes: 30);

                return isValid;
            }
        }

        private string[] GetValidIssuers(string tenantName, string tenantId, string policyId, bool isClientCredentials)
        {
            if (isClientCredentials)
            {
                return new[]
                {
                $"https://login.microsoftonline.com/{tenantId}/v2.0",
                $"https://sts.windows.net/{tenantId}/",
                $"https://{tenantName}.b2clogin.com/tfp/{tenantId}/{policyId}/v2.0/",
                $"https://{tenantName}.b2clogin.com/{tenantId}/v2.0/",
            };
            }

            return new[]
            {
            $"https://{tenantName}.b2clogin.com/tfp/{tenantId}/{policyId}/v2.0/",
            $"https://{tenantName}.b2clogin.com/{tenantName}.onmicrosoft.com/{policyId}/v2.0/",
            $"https://{tenantName}.b2clogin.com/{tenantId}/{policyId}/v2.0/",
            $"https://{tenantName}.b2clogin.com/{tenantId}/v2.0/"
        };
        }

        /// <summary>
        /// Extract claims from the validated principal to create response
        /// </summary>
        /// <param name="principal">The validated claims principal</param>
        /// <param name="token">The original token</param>
        /// <returns>Token validation response with extracted claims</returns>
        public TokenValidationResponse ExtractClaimsFromPrincipal(ClaimsPrincipal principal, string token)
        {
            var response = new TokenValidationResponse();

            try
            {
                // Log available claims for debugging
                var allClaims = principal.Claims.Select(c => $"{c.Type}: {c.Value}").ToList();
                _logger.LogDebug("Available claims in principal: {Claims}", string.Join(", ", allClaims));

                // Extract standard claims
                response.UserEmail = principal.FindFirst("user_email")?.Value ?? 
                                   principal.FindFirst("emails")?.Value ?? 
                                   principal.FindFirst(ClaimTypes.Email)?.Value ??
                                   principal.FindFirst("email")?.Value;

                response.UserId = principal.FindFirst("sub")?.Value ?? 
                                principal.FindFirst(ClaimTypes.NameIdentifier)?.Value ??
                                principal.FindFirst("http://schemas.xmlsoap.org/ws/2005/05/identity/claims/nameidentifier")?.Value ??
                                principal.FindFirst("oid")?.Value;

                _logger.LogDebug("Extracted UserId: {UserId}, UserEmail: {UserEmail}", response.UserId, response.UserEmail);

                response.TokenSource = principal.FindFirst("token_source")?.Value;

                // Read token directly to get issuer and audience
                var tokenHandler = new JwtSecurityTokenHandler();
                if (tokenHandler.CanReadToken(token))
                {
                    var jwtToken = tokenHandler.ReadJwtToken(token);
                    response.Issuer = jwtToken.Issuer;
                    response.Audience = jwtToken.Audiences?.FirstOrDefault();
                    response.ExpiresAt = jwtToken.ValidTo;
                }
            }
            catch (Exception ex)
            {
                _logger.LogWarning(ex, "Failed to extract some claims from token");
            }

            return response;
        }
    }
}


