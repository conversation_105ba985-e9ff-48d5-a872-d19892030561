using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities
{
    /// <summary>
    /// Entity for AgencyPartners
    /// </summary>
    public class AgencyPartners
    {
        /// <summary>
        /// AgencyPartnersId - Primary key
        /// </summary>
        [ExcludeColumn]
        public int AgencyPartnersId { get; set; }

        /// <summary>
        /// AgencyId
        /// </summary>
        public string? AgencyId { get; set; }

        /// <summary>
        /// PartnerAgencyId
        /// </summary>
        public string? PartnerId { get; set; }

        /// <summary>
        /// PartnerAgencyName
        /// </summary>
        public string? PartnerName { get; set; }
    }
} 