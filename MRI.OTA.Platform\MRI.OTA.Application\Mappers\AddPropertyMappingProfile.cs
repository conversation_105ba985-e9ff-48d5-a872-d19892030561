﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    /// <summary>
    /// Class for property mapper
    /// </summary>
    public class AddPropertyMappingProfile : Profile
    {
        /// <summary>
        /// Constructor for property mapper
        /// </summary>
        public AddPropertyMappingProfile() : base("AddPropertyMappingProfile")
        {
            CreateMap<UserPropertiesModel, UserProperties>().ReverseMap();

            CreateMap<UserDataSourceModel, UserDataSource>().ReverseMap();

            CreateMap<PropertyManagerInformationModel, PropertyManagerInformation>().ReverseMap();

            CreateMap<PropertyFinancialInformationModel, PropertyFinancialInformation>().ReverseMap();

            // Mapping for Property Manager API View Model from Entity with Agency Details
            CreateMap<PropertyManagerWithAgencyDetails, PropertyManagerViewModel>()
                .ForMember(dest => dest.PropertyManagerInformationId, opt => opt.MapFrom(src => src.PropertyManagerInformationId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.SRCAgencyId, opt => opt.MapFrom(src => src.SRCAgencyId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.SRCPropertyId))
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.SRCManagementId))
                .ForMember(dest => dest.ManagementType, opt => opt.MapFrom(src => src.ManagementType))
                .ForMember(dest => dest.AgencyName, opt => opt.MapFrom(src => src.AgencyName))
                .ForMember(dest => dest.PropertyManagerName, opt => opt.MapFrom(src => src.PropertyManagerName))
                .ForMember(dest => dest.PropertyManagerMobile, opt => opt.MapFrom(src => src.PropertyManagerMobile))
                .ForMember(dest => dest.PropertyManagerPhone, opt => opt.MapFrom(src => src.PropertyManagerPhone))
                .ForMember(dest => dest.PropertyManagerEmail, opt => opt.MapFrom(src => src.PropertyManagerEmail))
                .ForMember(dest => dest.AuthorityStartDate, opt => opt.MapFrom(src => src.AuthorityStartDate))
                .ForMember(dest => dest.AuthorityEndDate, opt => opt.MapFrom(src => src.AuthorityEndDate))
                .ForMember(dest => dest.Ownership, opt => opt.MapFrom(src => src.Ownership))
                .ForMember(dest => dest.ExpenditureLimit, opt => opt.MapFrom(src => src.ExpenditureLimit))
                .ForMember(dest => dest.ExpenditureNotes, opt => opt.MapFrom(src => src.ExpenditureNotes))
                .ForMember(dest => dest.BusinessName, opt => opt.MapFrom(src => src.BusinessName))
                .ForMember(dest => dest.BusinessRegisteredName, opt => opt.MapFrom(src => src.BusinessRegisteredName))
                .ForMember(dest => dest.ContactRole, opt => opt.MapFrom(src => src.ContactRole));

            // Legacy mapping for Property Manager API View Model from Entity (for backward compatibility)
            CreateMap<PropertyManagerInformation, PropertyManagerViewModel>()
                .ForMember(dest => dest.PropertyManagerInformationId, opt => opt.MapFrom(src => src.PropertyManagerInformationId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.SRCAgencyId, opt => opt.MapFrom(src => src.SRCAgencyId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.SRCPropertyId))
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.SRCManagementId))
                .ForMember(dest => dest.ManagementType, opt => opt.MapFrom(src => src.ManagementType))
                .ForMember(dest => dest.AgencyName, opt => opt.MapFrom(src => src.AgencyName))
                .ForMember(dest => dest.PropertyManagerName, opt => opt.MapFrom(src => src.PropertyManagerName))
                .ForMember(dest => dest.PropertyManagerMobile, opt => opt.MapFrom(src => src.PropertyManagerMobile))
                .ForMember(dest => dest.PropertyManagerPhone, opt => opt.MapFrom(src => src.PropertyManagerPhone))
                .ForMember(dest => dest.PropertyManagerEmail, opt => opt.MapFrom(src => src.PropertyManagerEmail))
                .ForMember(dest => dest.ContactRole, opt => opt.MapFrom(src => src.ContactRole))
                .ForMember(dest => dest.AuthorityStartDate, opt => opt.MapFrom(src => src.AuthorityStartDate))
                .ForMember(dest => dest.AuthorityEndDate, opt => opt.MapFrom(src => src.AuthorityEndDate))
                .ForMember(dest => dest.Ownership, opt => opt.MapFrom(src => src.Ownership))
                .ForMember(dest => dest.ExpenditureLimit, opt => opt.MapFrom(src => src.ExpenditureLimit))
                .ForMember(dest => dest.ExpenditureNotes, opt => opt.MapFrom(src => src.ExpenditureNotes))
                .ForMember(dest => dest.BusinessRegisteredName, opt => opt.Ignore())
                .ForMember(dest => dest.BusinessName, opt => opt.Ignore());

            // Reverse mapping from ViewModel to Entity (if needed)
            CreateMap<PropertyManagerViewModel, PropertyManagerInformation>();

            // Mapping from Model to ViewModel
            CreateMap<PropertyManagerInformationModel, PropertyManagerViewModel>()
                .ForMember(dest => dest.BusinessName, opt => opt.Ignore());

            // Mapping for Property Financial Information API View Model from Entity with Agency Details
            CreateMap<PropertyFinancialWithAgencyDetails, PropertyFinancialViewModel>()
                .ForMember(dest => dest.PropertyFinancialInformationId, opt => opt.MapFrom(src => src.PropertyFinancialInformationId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.TenancyName, opt => opt.MapFrom(src => src.TenancyName))
                .ForMember(dest => dest.LeaseStart, opt => opt.MapFrom(src => src.LeaseStart))
                .ForMember(dest => dest.LeaseEnd, opt => opt.MapFrom(src => src.LeaseEnd))
                .ForMember(dest => dest.VacateDate, opt => opt.MapFrom(src => src.VacateDate))
                .ForMember(dest => dest.Rent, opt => opt.MapFrom(src => src.Rent))
                .ForMember(dest => dest.IncreaseRent, opt => opt.MapFrom(src => src.IncreaseRent))
                .ForMember(dest => dest.IncreaseDate, opt => opt.MapFrom(src => src.IncreaseDate))
                .ForMember(dest => dest.OptionsDate, opt => opt.MapFrom(src => src.OptionsDate))
                .ForMember(dest => dest.OptionsDetail, opt => opt.MapFrom(src => src.OptionsDetail))
                .ForMember(dest => dest.Arrears, opt => opt.MapFrom(src => src.Arrears))
                .ForMember(dest => dest.PayToDate, opt => opt.MapFrom(src => src.PayToDate))
                .ForMember(dest => dest.AmountToVacate, opt => opt.MapFrom(src => src.AmountToVacate))
                .ForMember(dest => dest.OutstandingInvoices, opt => opt.MapFrom(src => src.OutstandingInvoices))
                .ForMember(dest => dest.InvoiceFeesArrears, opt => opt.MapFrom(src => src.InvoiceFeesArrears))
                .ForMember(dest => dest.RentCharge, opt => opt.MapFrom(src => src.RentCharge))
                .ForMember(dest => dest.WeeklyRent, opt => opt.MapFrom(src => src.WeeklyRent))
                .ForMember(dest => dest.LastPaid, opt => opt.MapFrom(src => src.LastPaid))
                .ForMember(dest => dest.HeldFunds, opt => opt.MapFrom(src => src.HeldFunds))
                .ForMember(dest => dest.SRCAgencyId, opt => opt.MapFrom(src => src.SRCAgencyId))
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.SRCManagementId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.SRCPropertyId))
                .ForMember(dest => dest.AgencyName, opt => opt.MapFrom(src => src.AgencyName))
                .ForMember(dest => dest.BusinessRegisteredName, opt => opt.MapFrom(src => src.BusinessRegisteredName))
                .ForMember(dest => dest.BusinessName, opt => opt.MapFrom(src => src.BusinessName))
                .ForMember(dest => dest.OwnershipTotalAvailableBalance, opt => opt.MapFrom(src => src.OwnershipTotalAvailableBalance))
                .ForMember(dest => dest.PropertyOutstandingFees, opt => opt.MapFrom(src => src.PropertyOutstandingFees))
                .ForMember(dest => dest.PropertyOutstandingInvoices, opt => opt.MapFrom(src => src.PropertyOutstandingInvoices))
                .ForMember(dest => dest.PropertyOverdueInvoices, opt => opt.MapFrom(src => src.PropertyOverdueInvoices))
                .ForMember(dest => dest.LastPaymentAmount, opt => opt.MapFrom(src => src.LastPaymentAmount))
                .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency))
                .ForMember(dest => dest.LastStatementDate, opt => opt.MapFrom(src => src.LastStatementDate));

            // Legacy mapping for Property Financial Information API View Model from Entity (for backward compatibility)
            CreateMap<PropertyFinancialInformation, PropertyFinancialViewModel>();
        }
    }
}
