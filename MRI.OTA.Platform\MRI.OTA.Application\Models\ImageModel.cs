﻿using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace MRI.OTA.Application.Models
{
    public class ImageModel
    {
        /// <summary>
        /// List of base64 encoded image strings
        /// </summary>
        [Required]
        public List<Base64ImageFile> Files { get; set; }

        /// <summary>
        /// Property ID associated with the images
        /// </summary>
        [Required]
        public int PropertyId { get; set; }


        /// <summary>
        /// Index of the image in the Files list that should be set as the default image
        /// </summary>
        public int? DefaultImageIndex { get; set; }
    }

    public class Base64ImageFile
    {
        /// <summary>
        /// Base64 encoded string of the image content
        /// </summary>
        [Required]
        public string Base64Content { get; set; }

        /// <summary>
        /// Filename with extension (e.g., "image.jpg")
        /// </summary>
        [Required]
        public string FileName { get; set; }

        ///// <summary>
        ///// Content type of the image (e.g., "image/jpeg", "image/png")
        ///// </summary>
        //[Required]
        //public string ContentType { get; set; }
    }
}
