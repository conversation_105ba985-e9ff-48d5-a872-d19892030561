﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\MRI.OTA.Common\MRI.OTA.Common.csproj" />
    <ProjectReference Include="..\MRI.OTA.Email\MRI.OTA.Email.csproj" />
    <ProjectReference Include="..\MRI.OTA.Entities\MRI.OTA.DBCore.csproj" />
    <ProjectReference Include="..\MRI.OTA.Infrastructure\MRI.OTA.Infrastructure.csproj" />
    <ProjectReference Include="..\MRI.OTA.Integration\MRI.OTA.Integration.csproj" />
  </ItemGroup>
	<ItemGroup>
		<!-- Core ASP.NET and Dependency Injection -->
		<PackageReference Include="AutoMapper" />
		<PackageReference Include="Azure.Storage.Blobs" />
		<PackageReference Include="FirebaseAdmin" />
		<PackageReference Include="FluentValidation" />
		<PackageReference Include="System.IdentityModel.Tokens.Jwt" />
	</ItemGroup>
</Project>
