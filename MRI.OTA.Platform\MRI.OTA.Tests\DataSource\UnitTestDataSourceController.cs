﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using MRI.OTA.API.Controllers.DataSource.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.UnitTestCases.DataSource
{
    public class UnitTestDataSourceController
    {
        private readonly Mock<IDataSourceService> _mockDataSourceService;
        private readonly DataSourceController _controller;
        private readonly Mock<HttpContext> _httpContextMock;
        private readonly Mock<HttpRequest> _httpRequestMock;
        private readonly Mock<IHeaderDictionary> _headerDictionaryMock;


        public UnitTestDataSourceController()
        {
            _mockDataSourceService = new Mock<IDataSourceService>();
            _controller = new DataSourceController(_mockDataSourceService.Object);

            // Setup HTTP context for headers
            _httpContextMock = new Mock<HttpContext>();
            _httpRequestMock = new Mock<HttpRequest>();
            _headerDictionaryMock = new Mock<IHeaderDictionary>();

            _httpRequestMock.Setup(x => x.Headers).Returns(_headerDictionaryMock.Object);
            _httpContextMock.Setup(x => x.Request).Returns(_httpRequestMock.Object);
            _controller.ControllerContext = new ControllerContext
            {
                HttpContext = _httpContextMock.Object
            };
        }

        [Fact]
        public async Task GetUserDataSource_ReturnsOkResult_WhenDataSourceExists()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";
            var dataSource = new DataSourceModel { DataSourceId = 1, Name = "Test Source" };

            _headerDictionaryMock.Setup(h => h["AccessKey"]).Returns(accessKey);
            _headerDictionaryMock.Setup(h => h["AccessSecret"]).Returns(accessSecret);

            _mockDataSourceService.Setup(service =>
                service.GetUserDataSource(accessKey, accessSecret))
                .ReturnsAsync(dataSource);

            // Act
            var result = await _controller.GetUserDataSource();

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(dataSource, apiResponse.Data);
        }

        [Fact]
        public async Task GetUserDataSource_ReturnsBadRequest_WhenHeadersAreMissing()
        {
            // Arrange
            _headerDictionaryMock.Setup(h => h["AccessKey"]).Returns(string.Empty);
            _headerDictionaryMock.Setup(h => h["AccessSecret"]).Returns(string.Empty);

            // Act
            var result = await _controller.GetUserDataSource();

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetUserDataSource_ReturnsNotFound_WhenDataSourceDoesNotExist()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";

            _headerDictionaryMock.Setup(h => h["AccessKey"]).Returns(accessKey);
            _headerDictionaryMock.Setup(h => h["AccessSecret"]).Returns(accessSecret);

            _mockDataSourceService.Setup(service =>
                service.GetUserDataSource(accessKey, accessSecret))
                .ReturnsAsync((DataSourceModel)null);

            // Act
            var result = await _controller.GetUserDataSource();

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task UpdateDataSource_ReturnsOkResult_WhenUpdateIsSuccessful()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";
            var dataSource = new DataSourceUpdateModel { ManifestJson = "Updated Source" };

            _headerDictionaryMock.Setup(h => h["AccessKey"]).Returns(accessKey);
            _headerDictionaryMock.Setup(h => h["AccessSecret"]).Returns(accessSecret);

            _mockDataSourceService.Setup(service =>
                service.UpdateDataSource(accessKey, accessSecret, dataSource))
                .ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.UpdateDataSource(dataSource);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
        }

        [Fact]
        public async Task UpdateDataSource_ReturnsBadRequest_WhenUpdateFails()
        {
            // Arrange
            var accessKey = "test-key";
            var accessSecret = "test-secret";
            var dataSource = new DataSourceUpdateModel { ManifestJson = "Updated Source" };

            _headerDictionaryMock.Setup(h => h["AccessKey"]).Returns(accessKey);
            _headerDictionaryMock.Setup(h => h["AccessSecret"]).Returns(accessSecret);

            _mockDataSourceService.Setup(service =>
                service.UpdateDataSource(accessKey, accessSecret, dataSource))
                .ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.UpdateDataSource(dataSource);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

    }
}
