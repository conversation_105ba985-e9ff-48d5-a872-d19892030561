﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class TenanciesTenantMappingProfile : Profile
    {
        public TenanciesTenantMappingProfile() : base("TenanciesTenantMappingProfile")
        {
            CreateMap<TenanciesTenantResponse, TenanciesTenant>()
            .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
            .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
            .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1))
            .ForMember(dest => dest.SRCTenancyId, opt => opt.MapFrom(src => src.TenancyId))
            .ForMember(dest => dest.TenancyName, opt => opt.MapFrom(src => src.TenancyName))
            .ForMember(dest => dest.IsActive, opt => opt.MapFrom(src =>
                string.Equals(src.TenancyStatus, "Active", StringComparison.OrdinalIgnoreCase)))
            .ForMember(dest => dest.LeaseStart, opt => opt.MapFrom(src => src.TenancyStartDate))
            .ForMember(dest => dest.LeaseEnd, opt => opt.MapFrom(src => src.TenancyEndDate))
            .ForMember(dest => dest.VacateDate, opt => opt.MapFrom(src => src.VacateDate))
            .ForMember(dest => dest.AmountToVacate, opt => opt.MapFrom(src => src.AmountToVacate))
            .ForMember(dest => dest.PayToDate, opt => opt.MapFrom(src => src.PayToDate))
            .ForMember(dest => dest.Rent, opt => opt.MapFrom(src => src.Rent))
            .ForMember(dest => dest.Arrears, opt => opt.MapFrom(src => src.Arrears))
            .ForMember(dest => dest.OutstandingInvoices, opt => opt.MapFrom(src => src.OutstandingInvoicesAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency))
            .ForMember(dest => dest.RentPeriod, opt => opt.MapFrom(src => src.RentPeriod))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyId, opt => opt.MapFrom(src => -1))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyManagerName, opt => opt.MapFrom(src => src.TenancyContactName))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyManagerMobile, opt => opt.MapFrom(src => src.TenancyContactNumber))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.PropertyManagerEmail, opt => opt.MapFrom(src => src.TenancyContactEmail))
            .ForPath(dest => dest.TenanciesPropertyManagerDetails.ContactRole, opt => opt.MapFrom(src => src.TenancyContactRole))
        .ReverseMap();

        }
    }
}
