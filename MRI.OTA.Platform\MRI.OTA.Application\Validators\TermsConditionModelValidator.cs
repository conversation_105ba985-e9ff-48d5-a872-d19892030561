using FluentValidation;
using MRI.OTA.Application.Models;

namespace MRI.OTA.Application.Validators
{
    /// <summary>
    /// Validator for TermsConditionModel
    /// </summary>
    public class TermsConditionModelValidator : AbstractValidator<TermsConditionModel>
    {
        public TermsConditionModelValidator()
        {
            RuleFor(x => x.UserId)
                .GreaterThan(0)
                .WithMessage("User ID must be greater than 0");

        }
    }
} 