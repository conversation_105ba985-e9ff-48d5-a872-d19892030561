using FluentValidation;

namespace MRI.OTA.Application.Validators
{
    /// <summary>
    /// Validator for User ID route parameter
    /// </summary>
    public class UserIdValidator : AbstractValidator<int>
    {
        public UserIdValidator()
        {
            RuleFor(x => x)
                .GreaterThan(0)
                .WithMessage("User ID must be greater than 0");
        }
    }

    /// <summary>
    /// Model for validating delete account parameters
    /// </summary>
    public class DeleteAccountParameters
    {
        public int UserId { get; set; }
        public string ProviderId { get; set; } = string.Empty;
    }

    /// <summary>
    /// Validator for delete account parameters
    /// </summary>
    public class DeleteAccountParametersValidator : AbstractValidator<DeleteAccountParameters>
    {
        public DeleteAccountParametersValidator()
        {
            RuleFor(x => x.UserId)
                .GreaterThan(0)
                .WithMessage("User ID must be greater than 0");

            RuleFor(x => x.ProviderId)
                .NotEmpty()
                .WithMessage("Provider ID is required")
                .MaximumLength(255)
                .WithMessage("Provider ID cannot exceed 255 characters");
        }
    }
} 