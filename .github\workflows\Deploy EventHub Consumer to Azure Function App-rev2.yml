name: Build and Deploy EventHub Consumer

on:
  push:
    branches:
      - develop
  workflow_dispatch:

env:
  IMAGE_NAME: apacsharedplatform/mriota/eventhub-consumer
  BUILD_CONFIGURATION: Release

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up .NET
        uses: actions/setup-dotnet@v4
        with:
          dotnet-version: '8.0.x'

      - name: Restore dependencies
        run: dotnet restore MRI.OTA.EventHub.Consumer/MRI.OTA.EventHub.Consumer.csproj

      - name: Build
        run: dotnet build MRI.OTA.EventHub.Consumer/MRI.OTA.EventHub.Consumer.csproj -c ${{ env.BUILD_CONFIGURATION }} --no-restore

      - name: Publish
        run: dotnet publish MRI.OTA.EventHub.Consumer/MRI.OTA.EventHub.Consumer.csproj -c ${{ env.BUILD_CONFIGURATION }} -o ./publish --no-build /p:UseAppHost=false

      - name: Log in to Azure Container Registry
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_LOGIN_SERVER }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: Build and Push Docker Image
        uses: docker/build-push-action@v3
        with:
          context: .
          file: MRI.OTA.EventHub.Consumer/Dockerfile
          push: true
          tags: |
            ${{ secrets.ACR_LOGIN_SERVER }}/${{ env.IMAGE_NAME }}:latest
            ${{ secrets.ACR_LOGIN_SERVER }}/${{ env.IMAGE_NAME }}:revision-${{ github.run_id }}

      - name: Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Deploy to Azure Function
        run: |
          az functionapp config container set \
            --name funcapp-nw02-shrdplt-dev \
            --resource-group ${{ secrets.RESOURCE_GROUP }} \
            --image "${{ secrets.ACR_LOGIN_SERVER }}/${{ env.IMAGE_NAME }}:revision-${{ github.run_id }}" \
            --registry-server ${{ secrets.ACR_LOGIN_SERVER }}
