﻿namespace MRI.OTA.Email
{
    public class EmailAttachment
    {
        public string FileName { get; set; }
        public byte[] Content { get; set; }
        public string ContentType { get; set; }

        public EmailAttachment(string fileName, byte[] content, string contentType)
        {
            FileName = fileName;
            Content = content;
            ContentType = contentType;
        }
    }
}
