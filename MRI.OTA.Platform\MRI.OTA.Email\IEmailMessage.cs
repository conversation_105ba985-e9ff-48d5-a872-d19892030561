﻿using SendGrid.Helpers.Mail;

namespace MRI.OTA.Email
{
    public interface IEmailMessage
    {
        string Subject { get; set; }
        string HtmlContent { get; set; }
        string PlainTextContent { get; set; }

        IList<EmailAddress> ToAddresses { get; }
        IList<EmailAddress> CcAddresses { get; }
        IList<EmailAddress> BccAddresses { get; }
        EmailAddress FromAddress { get; set; }
        EmailAddress ReplyToAddress { get; set; }

        IList<EmailAttachment> Attachments { get; }
    }
}
