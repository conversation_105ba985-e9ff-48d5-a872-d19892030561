name: funcapp-deploy-apac-shared-platform-mri-ota-worker

on:
  workflow_dispatch:
    inputs:
      environment:
        description: 'Target environment (dev, qa, uat, prod)'
        required: true
        default: 'qa'
      imageTagOverride:
        description: 'Optional image tag override'
        required: false
        default: ''

env:
  IMAGE_NAME: 'apacsharedplatform/mriota/worker'

jobs:
  build-and-push:
    name: Build & Push Docker Image
    runs-on: ubuntu-latest

    outputs:
      image_tag: ${{ steps.set-tag.outputs.image_tag }}

    steps:
      - name: 🛎️ Checkout code
        uses: actions/checkout@v4

      - name: 🔧 Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Set Image Tag
        id: set-tag
        run: |
          if [ -n "${{ github.event.inputs.imageTagOverride }}" ]; then
            echo "image_tag=${{ github.event.inputs.imageTagOverride }}" >> $GITHUB_OUTPUT
          else
            echo "image_tag=revision-${{ github.run_id }}-${{ github.event.inputs.environment }}" >> $GITHUB_OUTPUT
          fi

      - name: 🔐 Docker Login to ACR
        uses: azure/docker-login@v1
        with:
          login-server: ${{ secrets.ACR_ENDPOINT }}
          username: ${{ secrets.ACR_USERNAME }}
          password: ${{ secrets.ACR_PASSWORD }}

      - name: 📦 Build and Push Docker Image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: Dockerfile
          push: true
          tags: |
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:latest
            ${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ steps.set-tag.outputs.image_tag }}

  deploy-function-app:
    name: 🚀 Deploy to Azure Function App
    runs-on: ubuntu-latest
    needs: build-and-push
    environment: ${{ github.event.inputs.environment }}

    steps:
      - name: 🔐 Azure Login
        uses: azure/login@v1
        with:
          creds: ${{ secrets.AZURE_CREDENTIALS }}

      - name: Set Environment Variables
        id: set-env
        run: |
          ENV="${{ github.event.inputs.environment }}"
          echo "ENVIRONMENT=$ENV" >> $GITHUB_ENV

          case "$ENV" in
            dev)
              echo "APP_NAME=${{ secrets.DEV_FUNCTION_APP_NAME }}" >> $GITHUB_ENV
              echo "APP_RG=${{ secrets.DEV_FUNCTION_RESOURCE_GROUP }}" >> $GITHUB_ENV
              echo "CLIENT_SECRET=${{ secrets.DEV_B2C_CLIENT_SECRET }}" >> $GITHUB_ENV
              ;;
            qa)
              echo "APP_NAME=${{ secrets.QA_FUNCTION_APP_NAME }}" >> $GITHUB_ENV
              echo "APP_RG=${{ secrets.QA_FUNCTION_RESOURCE_GROUP }}" >> $GITHUB_ENV
              echo "CLIENT_SECRET=${{ secrets.QA_B2C_CLIENT_SECRET }}" >> $GITHUB_ENV
              ;;
            uat)
              echo "APP_NAME=${{ secrets.UAT_FUNCTION_APP_NAME }}" >> $GITHUB_ENV
              echo "APP_RG=${{ secrets.UAT_FUNCTION_RESOURCE_GROUP }}" >> $GITHUB_ENV
              echo "CLIENT_SECRET=${{ secrets.UAT_B2C_CLIENT_SECRET }}" >> $GITHUB_ENV
              ;;
            prod)
              echo "APP_NAME=${{ secrets.PROD_FUNCTION_APP_NAME }}" >> $GITHUB_ENV
              echo "APP_RG=${{ secrets.PROD_FUNCTION_RESOURCE_GROUP }}" >> $GITHUB_ENV
              echo "CLIENT_SECRET=${{ secrets.PROD_B2C_CLIENT_SECRET }}" >> $GITHUB_ENV
              ;;
            *)
              echo "❌ Invalid environment selected: $ENV"
              exit 1
              ;;
          esac

      - name: ✅ Validate Required Secrets
        run: |
          echo "📋 APP_NAME: $APP_NAME"
          echo "📋 APP_RG: $APP_RG"
          echo "📋 CLIENT_SECRET: ${CLIENT_SECRET:0:4}******"

          if [ -z "$APP_NAME" ] || [ -z "$APP_RG" ] || [ -z "$CLIENT_SECRET" ]; then
            echo "❌ Missing one or more required secrets for $ENVIRONMENT environment."
            exit 1
          fi

      - name: 🚀 Deploy Container to Azure Function App
        run: |
          IMAGE="${{ secrets.ACR_ENDPOINT }}/${{ env.IMAGE_NAME }}:${{ needs.build-and-push.outputs.image_tag }}"
          echo "📦 Image: $IMAGE"
          echo "🔧 Deploying to Function App: $APP_NAME"
          echo "📁 Resource Group: $APP_RG"

          echo "🛠️ Setting container config..."
          az functionapp config container set \
            --name "$APP_NAME" \
            --resource-group "$APP_RG" \
            --image "$IMAGE"

          echo "🔧 Updating app settings..."
          az functionapp config appsettings set \
            --name "$APP_NAME" \
            --resource-group "$APP_RG" \
            --settings \
              AzureWebJobsStorage="UseDevelopmentStorage=true" \
              FUNCTIONS_WORKER_RUNTIME="dotnet-isolated" \
              Timers__PropertyData="0 */5 * * * *" \
              Timers__AgencyData="0 * * * * *" \
              BaseUrl="https://localhost:7009/" \
              WorkerTimeOut="30" \
              TenantName="sharedplatformotadev" \
              ClientId="39856524-a4fa-4b2f-911c-3885a8dba92b" \
              ClientSecret="$CLIENT_SECRET" \
              SignUpSignInPolicyId="b2c_1_login" \
              ApplicationInsights__ConnectionString="InstrumentationKey=97a032a8-0a71-473e-aa7e-40b0354b8ed4;IngestionEndpoint=https://australiaeast-1.in.applicationinsights.azure.com/;LiveEndpoint=https://australiaeast.livediagnostics.monitor.azure.com/;ApplicationId=4e527d7b-92a9-4026-bf5d-aaccf198de30"
