FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src

# First, copy the entire solution to preserve directory structure
COPY . .

# List the project files for debugging
RUN find . -name "*.csproj" | sort

# Check if the API project exists
RUN ls -la MRI.OTA.Platform/MRI.OTA.API/ || echo "API directory not found"

# Try restore with direct path
RUN dotnet restore "MRI.OTA.Platform/MRI.OTA.API/MRI.OTA.API.csproj" || echo "Restore failed with direct path"

# Build the project with direct path
WORKDIR /src
RUN dotnet build "MRI.OTA.Platform/MRI.OTA.API/MRI.OTA.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "MRI.OTA.Platform/MRI.OTA.API/MRI.OTA.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .
ENTRYPOINT ["dotnet", "MRI.OTA.API.dll"]
