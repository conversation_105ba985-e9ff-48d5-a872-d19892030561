﻿using System.Text;
using Microsoft.Extensions.Logging;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.DBCore.Repositories
{
    public class MasterRepository : BaseRepository<Users, int>, IMasterRepository
    {
        protected readonly IDapperWrapper _dapperWrapper;

        /// <summary>
        /// Contructor for master repository
        /// </summary>
        /// <param name="dbConnection"></param>
        public MasterRepository(IDbConnectionFactory dbConnection, ILogger<MasterRepository> logger, IDapperWrapper dapperWrapper) 
            : base(dbConnection, logger, dapperWrapper)
        {
            _dapperWrapper = dapperWrapper;
        }

        /// <summary>
        /// Get module list. If propertyRelationshipId is null, returns all modules with relationships.
        /// </summary>
        /// <param name="propertyRelationshipId">Property relationship ID. If null, returns all modules.</param>
        /// <returns></returns>
        public async Task<List<ViewModuleRelationship>> GetModulesList(int? propertyRelationshipId)
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT m.ModuleId,m.ModuleName,m.Description,mpr.ModuleOrder,pr.PropertyRelationshipId,pr.PropertyRelationshipName ");
            query.Append($"FROM {Constants.ModulesTableName} m INNER JOIN {Constants.ModulePropertyRelationshipTableName} mpr ON m.ModuleID = mpr.ModuleID ");
            query.Append($"INNER JOIN {Constants.PropertyRelationshipTableName} pr ON mpr.PropertyRelationshipId = pr.PropertyRelationshipId");
            
            // If propertyRelationshipId is provided and not null/0, add WHERE clause
            if (propertyRelationshipId.HasValue && propertyRelationshipId.Value > 0)
            {
                query.Append($" WHERE pr.PropertyRelationshipId = @PropertyRelationshipId");
                var parameters = new { PropertyRelationshipId = propertyRelationshipId.Value };
                return await GetAllAsync<ViewModuleRelationship>(query.ToString(), parameters);
            }
            else
            {
                // Return all modules with relationships (no WHERE clause)
                return await GetAllAsync<ViewModuleRelationship>(query.ToString());
            }
        }

        public async Task<List<PropertyRelationship>> GetPropertyRelationship()
        {
            StringBuilder query = new StringBuilder();
            query.Append($"SELECT PropertyRelationshipId,PropertyRelationshipName,Description,IsActive ");
            query.Append($"FROM {Constants.PropertyRelationshipTableName} WHERE IsActive = 1");
            return await GetAllAsync<PropertyRelationship>(query.ToString());
        }
    }
}
