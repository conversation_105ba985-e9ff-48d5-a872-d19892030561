using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities
{
    /// <summary>
    /// Entity for AgencyDetails
    /// </summary>
    public class AgencyDetails
    {
        /// <summary>
        /// AgencyDetailsId - Primary key
        /// </summary>
        [ExcludeColumn]
        public int AgencyDetailsId { get; set; }

        /// <summary>
        /// AgencyId
        /// </summary>
        public string? AgencyId { get; set; }

        /// <summary>
        /// MriId
        /// </summary>
        public string? MriId { get; set; }

        /// <summary>
        /// DataSourceId
        /// </summary>
        public int? DataSourceId { get; set; }

        /// <summary>
        /// BusinessRegisteredName
        /// </summary>
        public string? BusinessRegisteredName { get; set; }

        /// <summary>
        /// BusinessName
        /// </summary>
        public string? BusinessName { get; set; }

        /// <summary>
        /// BusinessRegistrationNumber
        /// </summary>
        public string? BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// Phone
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// Email
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// DarkLogoLink
        /// </summary>
        public string? DarkLogoLink { get; set; }

        /// <summary>
        /// LightLogoLink
        /// </summary>
        public string? LightLogoLink { get; set; }

        /// <summary>
        /// BrandingBackgroundColor
        /// </summary>
        public string? BrandingBackgroundColor { get; set; }

        /// <summary>
        /// CountryCode
        /// </summary>
        public string? CountryCode { get; set; }

        /// <summary>
        /// CountryName
        /// </summary>
        public string? CountryName { get; set; }

        /// <summary>
        /// StateCode
        /// </summary>
        public string? StateCode { get; set; }

        /// <summary>
        /// StateName
        /// </summary>
        public string? StateName { get; set; }

        /// <summary>
        /// Suburb
        /// </summary>
        public string? Suburb { get; set; }

        /// <summary>
        /// PostalCode
        /// </summary>
        public string? PostalCode { get; set; }

        /// <summary>
        /// AdministrativeArea
        /// </summary>
        public string? AdministrativeArea { get; set; }

        /// <summary>
        /// BuildingNumber
        /// </summary>
        public string? BuildingNumber { get; set; }

        /// <summary>
        /// LotNumber
        /// </summary>
        public string? LotNumber { get; set; }

        /// <summary>
        /// StreetAddress
        /// </summary>
        public string? StreetAddress { get; set; }

        /// <summary>
        /// City
        /// </summary>
        public string? City { get; set; }

        /// <summary>
        /// Locale
        /// </summary>
        public string? Locale { get; set; }

        /// <summary>
        /// RuralDelivery
        /// </summary>
        public string? RuralDelivery { get; set; }

        /// <summary>
        /// PostOfficeName
        /// </summary>
        public string? PostOfficeName { get; set; }
    }
} 