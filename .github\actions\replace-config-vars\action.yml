name: Replace config placeholders with environment variables

description: Replaces placeholders in appsettings with values from GitHub environment variables and secrets

inputs:
  ota-db-connection-string:
    description: 'OTA Database Connection String'
    required: true
  azure-tenant-id:
    description: 'Azure Tenant ID'
    required: true
  ota-tenant-name:
    description: 'OTA Tenant Name'
    required: true
  ota-client-id:
    description: 'OTA Client ID'
    required: true
  ota-client-secret:
    description: 'OTA Client Secret'
    required: true
  ota-b2c-instance:
    description: 'OTA B2C Instance'
    required: true
  ota-b2c-domain:
    description: 'OTA B2C Domain'
    required: true
  ota-signup-signin-policyid:
    description: 'OTA Signup Signin Policy ID'
    required: true
  ota-api-allowed-origins:
    description: 'OTA API Allowed Origins'
    required: true
  ota-app-insight-connection-string:
    description: 'OTA App Insight Connection String'
    required: true
  ota-storage-connection-string:
    description: 'OTA Storage Connection String'
    required: true
  ota-storage-container-name:
    description: 'OTA Storage Container Name'
    required: true
  ota-azure-communication-connection-string:
    description: 'OTA Azure Communication Connection String'
    required: true
  ota-azure-communication-from-email:
    description: 'OTA Azure Communication From Email'
    required: true
  ota-redis-connection-string:
    description: 'OTA Redis Connection String'
    required: true
  ota-redis-default-expiration-minutes:
    description: 'OTA Redis Default Expiration Minutes'
    required: true
  ota-base-url:
    description: 'OTA Base URL'
    required: true
  ota-default-image-url:
    description: 'OTA Default Image URL'
    required: true
  ota-batch-size:
    description: 'OTA Batch Size'
    required: true
  ota-firebase-type:
    description: 'OTA Firebase Type'
    required: true
  ota-firebase-project-id:
    description: 'OTA Firebase Project ID'
    required: true
  ota-firebase-private-key-id:
    description: 'OTA Firebase Private Key ID'
    required: true
  ota-firebase-private-key:
    description: 'OTA Firebase Private Key'
    required: true
  ota-firebase-client-email:
    description: 'OTA Firebase Client Email'
    required: true
  ota-firebase-client-id:
    description: 'OTA Firebase Client ID'
    required: true
  ota-firebase-auth-uri:
    description: 'OTA Firebase Auth URI'
    required: true
  ota-firebase-token-uri:
    description: 'OTA Firebase Token URI'
    required: true
  ota-firebase-auth-provider-x509-cert-url:
    description: 'OTA Firebase Auth Provider X509 Cert URL'
    required: true
  ota-firebase-client-x509-cert-url:
    description: 'OTA Firebase Client X509 Cert URL'
    required: true
  ota-firebase-universe-domain:
    description: 'OTA Firebase Universe Domain'
    required: true

runs:
  using: "composite"
  steps:
    - name: Replace placeholders in appsettings
      shell: bash
      run: |
        echo "Replacing placeholders with:"
        echo "DB Connection: ${{ inputs.ota-db-connection-string }}"
        echo "Tenant ID: ${{ inputs.azure-tenant-id }}"
        
        for FILE in MRI.OTA.Platform/MRI.OTA.API/appsettings.json MRI.OTA.Platform/MRI.OTA.API/appsettings.Production.json; do
          if [ -f "$FILE" ]; then
            sed -i "s|#{ota-db-connection-string}|${{ inputs.ota-db-connection-string }}|g" $FILE
            sed -i "s|#{ota-tenant-id}|${{ inputs.azure-tenant-id }}|g" $FILE
            sed -i "s|#{ota-tenant-name}|${{ inputs.ota-tenant-name }}|g" $FILE
            sed -i "s|#{ota-client-id}|${{ inputs.ota-client-id }}|g" $FILE
            sed -i "s|#{ota-client-secret}|${{ inputs.ota-client-secret }}|g" $FILE
            sed -i "s|#{ota-b2c-instance}|${{ inputs.ota-b2c-instance }}|g" $FILE
            sed -i "s|#{ota-b2c-domain}|${{ inputs.ota-b2c-domain }}|g" $FILE
            sed -i "s|#{ota-signup-signin-policyid}|${{ inputs.ota-signup-signin-policyid }}|g" $FILE
            sed -i "s|#{ota-api-allowed-origins}|${{ inputs.ota-api-allowed-origins }}|g" $FILE
            sed -i "s|#{ota-app-insight-connection-string}|${{ inputs.ota-app-insight-connection-string }}|g" $FILE
            sed -i "s|#{ota-storage-connection-string}|${{ inputs.ota-storage-connection-string }}|g" $FILE
            sed -i "s|#{ota-storage-container-name}|${{ inputs.ota-storage-container-name }}|g" $FILE
            sed -i "s|#{ota-azure-communication-connection-string}|${{ inputs.ota-azure-communication-connection-string }}|g" $FILE
            sed -i "s|#{ota-azure-communication-from-email}|${{ inputs.ota-azure-communication-from-email }}|g" $FILE
            sed -i "s|#{ota-redis-connection-string}|${{ inputs.ota-redis-connection-string }}|g" $FILE
            sed -i "s|#{ota-redis-default-expiration-minutes}|${{ inputs.ota-redis-default-expiration-minutes }}|g" $FILE
            sed -i "s|#{ota-base-url}|${{ inputs.ota-base-url }}|g" $FILE
            sed -i "s|#{ota-default-image-url}|${{ inputs.ota-default-image-url }}|g" $FILE
            sed -i "s|#{ota-batch-size}|${{ inputs.ota-batch-size }}|g" $FILE
            sed -i "s|#{ota-firebase-type}|${{ inputs.ota-firebase-type }}|g" $FILE
            sed -i "s|#{ota-firebase-project-id}|${{ inputs.ota-firebase-project-id }}|g" $FILE
            sed -i "s|#{ota-firebase-private-key-id}|${{ inputs.ota-firebase-private-key-id }}|g" $FILE
            sed -i "s|#{ota-firebase-private-key}|${{ inputs.ota-firebase-private-key }}|g" $FILE
            sed -i "s|#{ota-firebase-client-email}|${{ inputs.ota-firebase-client-email }}|g" $FILE
            sed -i "s|#{ota-firebase-client-id}|${{ inputs.ota-firebase-client-id }}|g" $FILE
            echo "Updated $FILE"
          else
            echo "File $FILE not found"
          fi
        done 