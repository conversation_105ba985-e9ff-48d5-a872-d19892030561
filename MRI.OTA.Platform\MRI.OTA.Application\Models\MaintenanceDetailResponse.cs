﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.Application.Models
{
    /// <summary>
    /// Internal response model for maintenance details (used for database mapping)
    /// </summary>
    public class MaintenanceDetailResponse
    {
        public string ManagementId { get; set; }
        public string TenancyId { get; set; }
        public string PropertyId { get; set; }
        public List<MaintenanceJobResponse> MaintenanceJobs { get; set; }
    }

    /// <summary>
    /// Internal maintenance job model (used for database mapping)
    /// </summary>
    public class MaintenanceJobResponse
    {
        public string JobId { get; set; }
        public string JobSummary { get; set; }
        public string JobStatus { get; set; }
        public string RequestId { get; set; }
        public string RequestSummary { get; set; }
        public string RequestStatus { get; set; }
        public string RequestRaisedBy { get; set; }
        public DateTime RequestRaisedDate { get; set; }
        public string ImageLink { get; set; }
    }
    
}
