﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Moq;
using MRI.OTA.API.Controllers.Property.v1;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.Common.Models.Request;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Tests.Property.Controller
{
    public class UnitTestPropertyController
    {
        private readonly Mock<IPropertyService> _mockPropertyService;
        private readonly PropertyController _controller;

        public UnitTestPropertyController()
        {
            _mockPropertyService = new Mock<IPropertyService>();
            _controller = new PropertyController(_mockPropertyService.Object);
        }

        [Fact]
        public async Task GetAllProperties_ReturnsOkResult_WhenPropertiesExist()
        {
            // Arrange
            var searchCriteria = new SearchCriteriaModel();
            var properties = new List<ViewUserProperties> { new ViewUserProperties() };
            _mockPropertyService.Setup(service => service.GetAllProperties(searchCriteria)).ReturnsAsync(properties);

            // Act
            var result = await _controller.GetAllProperties(searchCriteria);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetAllProperties_ReturnsNotFoundResult_WhenPropertiesDoNotExist()
        {
            // Arrange
            var searchCriteria = new SearchCriteriaModel();
            _mockPropertyService.Setup(service => service.GetAllProperties(searchCriteria)).ReturnsAsync((List<ViewUserProperties>)null);

            // Act
            var result = await _controller.GetAllProperties(searchCriteria);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task AddProperty_ReturnsOkResult_WhenPropertyIsAddedSuccessfully()
        {
            // Arrange
            var userProperties = new UserPropertiesModel();
            _mockPropertyService.Setup(service => service.AddProperty(userProperties)).ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.AddProperty(userProperties);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
        }

        [Fact]
        public async Task AddProperty_ReturnsBadRequestResult_WhenPropertyIsNotAdded()
        {
            // Arrange
            var userProperties = new UserPropertiesModel();
            _mockPropertyService.Setup(service => service.AddProperty(userProperties)).ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.AddProperty(userProperties);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task UpdateProperty_ReturnsOkResult_WhenPropertyIsUpdatedSuccessfully()
        {
            // Arrange
            var userProperties = new UserPropertiesModel();
            _mockPropertyService.Setup(service => service.UpdateProperty(userProperties)).ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.UpdateProperty(1, userProperties);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
        }

        [Fact]
        public async Task UpdateProperty_ReturnsBadRequestResult_WhenPropertyIsNotUpdated()
        {
            // Arrange
            var userProperties = new UserPropertiesModel();
            _mockPropertyService.Setup(service => service.UpdateProperty(userProperties)).ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.UpdateProperty(1, userProperties);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyById_ReturnsOkResult_WhenPropertyExists()
        {
            // Arrange
            var property = new ViewUserProperties { PropertyId = 1 };
            _mockPropertyService.Setup(service => service.GetPropertyById(1)).ReturnsAsync(property);

            // Act
            var result = await _controller.GetPropertyById(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(property, apiResponse.Data);
        }

        [Fact]
        public async Task GetPropertyById_ReturnsNotFoundResult_WhenPropertyDoesNotExist()
        {
            // Arrange
            _mockPropertyService.Setup(service => service.GetPropertyById(1)).ReturnsAsync((ViewUserProperties)null);

            // Act
            var result = await _controller.GetPropertyById(1);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyRelations_ReturnsOkResult_WhenRelationsExist()
        {
            // Arrange
            var relations = new List<ViewUserPropertiesNickName> { new ViewUserPropertiesNickName() };
            _mockPropertyService.Setup(service => service.GetPropertyRelations(1)).ReturnsAsync(relations);

            // Act
            var result = await _controller.GetPropertyRelations(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(relations, apiResponse.Data);
        }

        [Fact]
        public async Task GetPropertyRelations_ReturnsNotFoundResult_WhenRelationsDoNotExist()
        {
            // Arrange
            _mockPropertyService.Setup(service => service.GetPropertyRelations(1)).ReturnsAsync((List<ViewUserPropertiesNickName>)null);

            // Act
            var result = await _controller.GetPropertyRelations(1);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyNickNames_ReturnsOkResult_WhenNicknamesExist()
        {
            // Arrange
            var nicknames = new List<ViewUserPropertiesNickName> { new ViewUserPropertiesNickName() };
            _mockPropertyService.Setup(service => service.GetPropertyNickNames(1)).ReturnsAsync(nicknames);

            // Act
            var result = await _controller.GetPropertyNickNames(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(nicknames, apiResponse.Data);
        }

        [Fact]
        public async Task GetPropertyNickNames_ReturnsNotFoundResult_WhenNicknamesDoNotExist()
        {
            // Arrange
            _mockPropertyService.Setup(service => service.GetPropertyNickNames(1)).ReturnsAsync((List<ViewUserPropertiesNickName>)null);

            // Act
            var result = await _controller.GetPropertyNickNames(1);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task DeleteProperty_ReturnsOkResult_WhenPropertyIsDeletedSuccessfully()
        {
            // Arrange
            _mockPropertyService.Setup(service => service.DeleteProperty(1)).ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.DeleteProperty(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
        }

        [Fact]
        public async Task DeleteProperty_ReturnsBadRequestResult_WhenPropertyIsNotDeleted()
        {
            // Arrange
            _mockPropertyService.Setup(service => service.DeleteProperty(1)).ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.DeleteProperty(1);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_ReturnsOkResult_WithPropertyCountsBySource()
        {
            // Arrange
            var agencyCounts = new List<ViewAgencyPropertyCount>
            {
                new ViewAgencyPropertyCount
                {
                    AgencyId = "AGENCY1",
                    BusinessRegisteredName = "SelfSource Agency",
                    ActiveCount = 5,
                    InactiveCount = 3
                },
                new ViewAgencyPropertyCount
                {
                    AgencyId = "AGENCY2",
                    BusinessRegisteredName = "PropertyTree Agency",
                    ActiveCount = 7,
                    InactiveCount = 2
                },
                new ViewAgencyPropertyCount
                {
                    AgencyId = "SELF_SOURCE",
                    ActiveCount = 3,
                    InactiveCount = 1
                }
            };

            _mockPropertyService.Setup(service => service.GetPropertyCountsByAgency(1)).ReturnsAsync(agencyCounts);

            // Act
            var result = await _controller.GetPropertyCountsByAgency(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(agencyCounts, apiResponse.Data);
            Assert.Equal("Property counts by data source retrieved successfully", apiResponse.Message);
        }

        [Fact]
        public async Task GetPropertyCountsByDataSource_ReturnsOkResult_WithEmptyPropertyCounts_WhenNoDataFound()
        {
            // Arrange
            var emptyAgencyCounts = new List<ViewAgencyPropertyCount>();
            _mockPropertyService.Setup(service => service.GetPropertyCountsByAgency(1)).ReturnsAsync(emptyAgencyCounts);

            // Act
            var result = await _controller.GetPropertyCountsByAgency(1);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(emptyAgencyCounts, apiResponse.Data);
            Assert.Equal("Property counts by data source retrieved successfully", apiResponse.Message);
        }

        #region UpdatePropertyStatus Tests

        [Fact]
        public async Task UpdatePropertyStatus_ReturnsOkResult_WhenPropertyStatusIsUpdatedSuccessfully()
        {
            // Arrange
            var propertyStatus = new PropertyStatusModel { UserId = 1, PropertyId = 1, IsActive = true };
            _mockPropertyService.Setup(service => service.UpdatePropertyStatus(propertyStatus)).ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.UpdatePropertyStatus(propertyStatus);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemUpdatedSucess, apiResponse.Message);
        }

        [Fact]
        public async Task UpdatePropertyStatus_ReturnsBadRequestResult_WhenPropertyStatusIsNotUpdated()
        {
            // Arrange
            var propertyStatus = new PropertyStatusModel { UserId = 1, PropertyId = 1, IsActive = true };
            _mockPropertyService.Setup(service => service.UpdatePropertyStatus(propertyStatus)).ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.UpdatePropertyStatus(propertyStatus);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotUpdated, apiResponse.Message);
        }

        #endregion

        #region UpdatePropertyPortfolio Tests

        [Fact]
        public async Task UpdatePropertyPortfolio_ReturnsOkResult_WhenPortfolioIsUpdatedSuccessfully()
        {
            // Arrange
            var portfolioRequest = new PropertyPortfolioModel
            {
                UserId = 1,
                PropertyId = 1,
                NicknameId = 1,
                Nickname = "Test Nickname"
            };
            _mockPropertyService.Setup(service => service.UpdatePropertyPortfolio(portfolioRequest)).ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.UpdatePropertyPortfolio(portfolioRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Property portfolio updated successfully", apiResponse.Message);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_ReturnsBadRequestResult_WhenPortfolioIsNotUpdated()
        {
            // Arrange
            var portfolioRequest = new PropertyPortfolioModel
            {
                UserId = 1,
                PropertyId = 1,
                NicknameId = 1,
                Nickname = "Test Nickname"
            };
            _mockPropertyService.Setup(service => service.UpdatePropertyPortfolio(portfolioRequest)).ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.UpdatePropertyPortfolio(portfolioRequest);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("Property portfolio could not be updated", apiResponse.Message);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_ReturnsOkResult_WhenCreatingNewNickname()
        {
            // Arrange
            var portfolioRequest = new PropertyPortfolioModel
            {
                UserId = 1,
                PropertyId = 1,
                NicknameId = 0, // 0 indicates creating new nickname
                Nickname = "New Nickname"
            };
            _mockPropertyService.Setup(service => service.UpdatePropertyPortfolio(portfolioRequest)).ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.UpdatePropertyPortfolio(portfolioRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
        }

        [Fact]
        public async Task UpdatePropertyPortfolio_ReturnsOkResult_WhenDuplicateNickname()
        {
            // Arrange
            var portfolioRequest = new PropertyPortfolioModel
            {
                PropertyId = 1
            };
            _mockPropertyService
                .Setup(service => service.UpdatePropertyPortfolio(portfolioRequest))
                .ReturnsAsync(Constants.DuplicateError);

            // Act
            var result = await _controller.UpdatePropertyPortfolio(portfolioRequest);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Please enter a unique nickname, or select an existing property.", apiResponse.Message);
            Assert.Contains("Please enter a unique nickname, or select an existing property.", apiResponse.Errors);
        }
        #endregion

        #region GetMaintenanceDetails Tests

        [Fact]
        public async Task GetMaintenanceDetails_ReturnsOkResult_WhenMaintenanceDetailsExist()
        {
            // Arrange
            var managementId = "MGT123";
            var propertyId = 1;
            var maintenanceDetails = new List<MaintenanceDetailModel>
            {
                new MaintenanceDetailModel
                {
                    ManagementId = managementId,
                    PropertyId = propertyId.ToString(),
                    JobId = "JOB123",
                    JobSummary = "Test maintenance job",
                    RequestId = "REQ123",
                    RequestSummary = "Test maintenance request"
                }
            };
            _mockPropertyService.Setup(service => service.GetMaintenanceDetails(propertyId, managementId, null)).ReturnsAsync(maintenanceDetails);

            // Act
            var result = await _controller.GetMaintenanceDetails(propertyId, managementId, null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Maintenance details retrieved successfully", apiResponse.Message);
            Assert.Equal(maintenanceDetails, apiResponse.Data);
        }

        [Fact]
        public async Task GetMaintenanceDetails_ReturnsNotFoundResult_WhenMaintenanceDetailsDoNotExist()
        {
            // Arrange
            var managementId = "MGT123";
            var propertyId = 1;
            _mockPropertyService.Setup(service => service.GetMaintenanceDetails(propertyId, managementId, null)).ReturnsAsync((List<MaintenanceDetailModel>)null);

            // Act
            var result = await _controller.GetMaintenanceDetails(propertyId, managementId, null);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotFound, apiResponse.Message);
        }

        [Fact]
        public async Task GetMaintenanceDetails_ValidatesParameters()
        {
            // Arrange
            var managementId = "";
            var propertyId = 0;
            var maintenanceDetails = new List<MaintenanceDetailModel> { new MaintenanceDetailModel() };
            _mockPropertyService.Setup(service => service.GetMaintenanceDetails(propertyId, managementId, null)).ReturnsAsync(maintenanceDetails);

            // Act
            var result = await _controller.GetMaintenanceDetails(propertyId, managementId, null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            _mockPropertyService.Verify(service => service.GetMaintenanceDetails(propertyId, managementId, null), Times.Once);
        }

        #endregion

        #region GetComplianceDetails Tests

        [Fact]
        public async Task GetComplianceDetails_ReturnsOkResult_WhenComplianceDetailsExist()
        {
            // Arrange
            var managementId = "MGT123";
            var propertyId = 1;
            var complianceDetails = new List<ComplianceDetailModel>
            {
                new ComplianceDetailModel
                {
                    ManagementId = managementId,
                    PropertyId = propertyId.ToString(),
                    ComplianceId = "COMP123",
                    ComplianceName = "Fire Safety",
                    ExpiryDate = DateTime.Now.AddDays(30),
                    ServicedBy = "Test Company"
                }
            };
            _mockPropertyService.Setup(service => service.GetCompliance(managementId, propertyId)).ReturnsAsync(complianceDetails);

            // Act
            var result = await _controller.GetComplianceDetails(managementId, propertyId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Compliance details retrieved successfully", apiResponse.Message);
            Assert.Equal(complianceDetails, apiResponse.Data);
        }

        [Fact]
        public async Task GetComplianceDetails_ReturnsNotFoundResult_WhenComplianceDetailsDoNotExist()
        {
            // Arrange
            var managementId = "MGT123";
            var propertyId = 1;
            _mockPropertyService.Setup(service => service.GetCompliance(managementId, propertyId)).ReturnsAsync((List<ComplianceDetailModel>)null);

            // Act
            var result = await _controller.GetComplianceDetails(managementId, propertyId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotFound, apiResponse.Message);
        }

        [Fact]
        public async Task GetComplianceDetails_ValidatesParameters()
        {
            // Arrange
            var managementId = "MGT456";
            var propertyId = 2;
            var complianceDetails = new List<ComplianceDetailModel> { new ComplianceDetailModel() };
            _mockPropertyService.Setup(service => service.GetCompliance(managementId, propertyId)).ReturnsAsync(complianceDetails);

            // Act
            var result = await _controller.GetComplianceDetails(managementId, propertyId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            _mockPropertyService.Verify(service => service.GetCompliance(managementId, propertyId), Times.Once);
        }

        [Fact]
        public async Task GetInspections_ReturnsOkResult_WhenInspectionsExist()
        {
            // Arrange
            var tenancyId = "TEN123";
            var propertyId = 1;
            var inspections = new List<InspectionDetailModel>
            {
                new InspectionDetailModel { InspectionsDetailId = 1, PropertyId = propertyId, SRCTenancyId = tenancyId }
            };
            _mockPropertyService.Setup(service => service.GetInspections(propertyId, null, tenancyId)).ReturnsAsync(inspections);

            // Act
            var result = await _controller.GetInspections(propertyId, null, tenancyId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Inspections list retrieved successfully", apiResponse.Message);
            Assert.Equal(inspections, apiResponse.Data);
        }

        [Fact]
        public async Task GetInspections_ReturnsNotFoundResult_WhenNoInspections()
        {
            // Arrange
            var tenancyId = "TEN123";
            var propertyId = 1;
            _mockPropertyService.Setup(service => service.GetInspections(propertyId, null, tenancyId)).ReturnsAsync((List<InspectionDetailModel>)null);

            // Act
            var result = await _controller.GetInspections(propertyId, null, tenancyId);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
            Assert.Equal(MessagesConstants.ItemNotFound, apiResponse.Message);
        }

        [Fact]
        public async Task GetDocuments_ReturnsOkResult_WhenDocumentsExist()
        {
            // Arrange
            var requestModel = new GetDocumentRequestModel { ManagementId = "MGT123" };
            var documents = new List<UserPropertyDocumentDetail> { new UserPropertyDocumentDetail { PropertyId = 1 } };
            _mockPropertyService.Setup(s => s.GetDocuments(requestModel)).ReturnsAsync(documents);

            // Act
            var result = await _controller.GetDocuments(requestModel);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(documents, apiResponse.Data);
        }

        [Fact]
        public async Task GetDocuments_ReturnsNotFoundResult_WhenNoDocuments()
        {
            // Arrange
            var requestModel = new GetDocumentRequestModel { ManagementId = "MGT123" };
            _mockPropertyService.Setup(s => s.GetDocuments(requestModel)).ReturnsAsync((List<UserPropertyDocumentDetail>)null);

            // Act
            var result = await _controller.GetDocuments(requestModel);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetDocuments_ReturnsNotFoundResult_WhenDocumentsListIsEmpty()
        {
            // Arrange
            var requestModel = new GetDocumentRequestModel { ManagementId = "MGT123" };
            _mockPropertyService.Setup(s => s.GetDocuments(requestModel)).ReturnsAsync(new List<UserPropertyDocumentDetail>());

            // Act
            var result = await _controller.GetDocuments(requestModel);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_ReturnsBadRequest_WhenNoParameterProvided()
        {
            // Act
            var result = await _controller.GetPropertyManagerInformation(null, null, null);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_ReturnsBadRequest_WhenManagementIdWithoutSrcPropertyId()
        {
            // Act
            var result = await _controller.GetPropertyManagerInformation("MGT123", null, null);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_ReturnsNotFound_WhenNoData()
        {
            // Arrange
            _mockPropertyService.Setup(s => s.GetPropertyManagerInformation(null, 1, null)).ReturnsAsync((PropertyManagerViewModel)null);

            // Act
            var result = await _controller.GetPropertyManagerInformation(null, 1, null);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyManagerInformation_ReturnsOkResult_WhenDataExists()
        {
            // Arrange
            var managerInfo = new PropertyManagerViewModel { PropertyManagerInformationId = 1, PropertyId = 1 };
            _mockPropertyService.Setup(s => s.GetPropertyManagerInformation(null, 1, null)).ReturnsAsync(managerInfo);

            // Act
            var result = await _controller.GetPropertyManagerInformation(null, 1, null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<PropertyManagerViewModel>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(managerInfo, apiResponse.Data);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_ReturnsBadRequest_WhenNoParameterProvided()
        {
            // Act
            var result = await _controller.GetPropertyFinancialInformation(null, null, null);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_ReturnsBadRequest_WhenManagementIdWithoutSrcPropertyId()
        {
            // Act
            var result = await _controller.GetPropertyFinancialInformation("MGT123", null, null);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_ReturnsNotFound_WhenNoData()
        {
            // Arrange
            _mockPropertyService.Setup(s => s.GetPropertyFinancialInformation(null, 1, null)).ReturnsAsync((PropertyFinancialViewModel)null);

            // Act
            var result = await _controller.GetPropertyFinancialInformation(null, 1, null);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetPropertyFinancialInformation_ReturnsOkResult_WhenDataExists()
        {
            // Arrange
            var financialInfo = new PropertyFinancialViewModel { PropertyFinancialInformationId = 1, PropertyId = 1 };
            _mockPropertyService.Setup(s => s.GetPropertyFinancialInformation(null, 1, null)).ReturnsAsync(financialInfo);

            // Act
            var result = await _controller.GetPropertyFinancialInformation(null, 1, null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<PropertyFinancialViewModel>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(financialInfo, apiResponse.Data);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_ReturnsBadRequest_WhenNoParameterProvided()
        {
            // Act
            var result = await _controller.GetTenantOwnerDetail(null, null, null);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_ReturnsBadRequest_WhenTenancyIdWithoutSrcPropertyId()
        {
            // Act
            var result = await _controller.GetTenantOwnerDetail(null, "TEN123", null);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_ReturnsNotFound_WhenNoData()
        {
            // Arrange
            _mockPropertyService.Setup(s => s.GetTenantOwnerDetail(null, null, 1)).ReturnsAsync((TenanciesTenantDetailResponse)null);

            // Act
            var result = await _controller.GetTenantOwnerDetail(1, null, null);

            // Assert
            var notFoundResult = Assert.IsType<NotFoundObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(notFoundResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status404NotFound, apiResponse.StatusCode);
        }

        [Fact]
        public async Task GetTenantOwnerDetail_ReturnsOkResult_WhenDataExists()
        {
            // Arrange
            var tenantDetail = new TenanciesTenantDetailResponse { PropertyId = 1 };
            _mockPropertyService.Setup(s => s.GetTenantOwnerDetail(null, null, 1)).ReturnsAsync(tenantDetail);

            // Act
            var result = await _controller.GetTenantOwnerDetail(1, null, null);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result.Result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal(tenantDetail, apiResponse.Data);
        }
        #endregion
    }
}
