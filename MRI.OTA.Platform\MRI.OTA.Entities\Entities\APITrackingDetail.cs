﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities
{
    public class APITrackingDetail
    {
        public int APIDetailId { get; set; }
        public int? UserId { get; set; }
        public string InviteCode { get; set; }
        public bool IsCompleted { get; set; }
        public string? ErrorInfo { get; set; }
        public string? AdditionalErrorInfo { get; set; }
        public bool IsDataSync { get; set; } = false;
    }
}
