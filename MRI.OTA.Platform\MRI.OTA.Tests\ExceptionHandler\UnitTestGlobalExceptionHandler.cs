﻿using System.Text.Json;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.ExceptionHandler;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;

namespace MRI.OTA.UnitTestCases.ExceptionHandler
{
    public class UnitTestGlobalExceptionHandler
    {
        [Fact]
        public async Task TryHandleAsync_LogsErrorAndReturnsInternalServerErrorResponse()
        {
            // Arrange
            var loggerMock = new Mock<ILogger<GlobalExceptionHandler>>();
            var envMock = new Mock<IHostEnvironment>();
            var handler = new GlobalExceptionHandler(loggerMock.Object, envMock.Object);

            var context = new DefaultHttpContext();
            var responseStream = new MemoryStream();
            context.Response.Body = responseStream;

            var exception = new Exception("Test exception");
            var cancellationToken = CancellationToken.None;

            // Act
            var result = await handler.TryHandleAsync(context, exception, cancellationToken);

            // Assert
            Assert.True(result);
            Assert.Equal(StatusCodes.Status500InternalServerError, context.Response.StatusCode);
            Assert.Equal("application/json; charset=utf-8", context.Response.ContentType);

            responseStream.Seek(0, SeekOrigin.Begin);
            var responseBody = await new StreamReader(responseStream).ReadToEndAsync();
            var deserialized = JsonSerializer.Deserialize<string>(responseBody);
            var apiResponse = JsonSerializer.Deserialize<ApiResponse<object>>(deserialized!);

            Assert.NotNull(apiResponse);
            Assert.False(apiResponse!.Success);
            Assert.Equal(MessagesConstants.InternalServerErrorMessage, apiResponse.Message);
            Assert.Equal(StatusCodes.Status500InternalServerError, apiResponse.StatusCode);
            Assert.Contains(MessagesConstants.InternalServerErrorMessage, apiResponse.Errors[0]);

            loggerMock.Verify(
                l => l.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Exception occurred:")),
                    exception,
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()
                ),
                Times.Once
            );
        }
    }
}
