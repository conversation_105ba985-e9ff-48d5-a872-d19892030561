<!DOCTYPE html>
<html lang="en">
<style>
body {
    background: #343a40;
    font-family: 'Segoe UI', Arial, sans-serif;
    margin: 0;
    padding: 0;
}

.top-label {
    position: absolute;
    top: 32px;
    left: 32px;
    color: #6ec1e4;
    font-size: 1.05em;
    font-weight: 500;
    letter-spacing: 0.02em;
    z-index: 2;
}
#api {
    display: none !important;
}
.container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
}

.phone-frame {
    background: #fff;
    border-radius: 32px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.15);
    width: 350px;
    padding: 32px 24px 24px 24px;
    display: flex;
    flex-direction: column;
    align-items: stretch;
    position: relative;
}

.header {
    display: flex;
    align-items: center;
    margin-bottom: 16px;
}

.logo {
    font-weight: bold;
    font-size: 1.1em;
    color: #0078d4;
}

h2 {
    margin: 0 0 8px 0;
    font-size: 1.5em;
    font-weight: 700;
    color: #222;
}

.subtitle {
    color: #888;
    font-size: 0.95em;
    margin-bottom: 20px;
}

.social-buttons {
    display: flex;
    flex-direction: column;
    gap: 12px;
    margin-bottom: 18px;
}

.social-buttons button {
    display: flex;
    align-items: center;
    gap: 8px;
    border: 1px solid #e0e0e0;
    border-radius: 24px;
    padding: 10px 0;
    background: #fff;
    font-size: 1em;
    cursor: pointer;
    transition: background 0.2s;
    justify-content: flex-start;
    font-weight: 500;
}

.social-buttons button img {
    margin-left: 16px;
}

.social-buttons button span {
    margin-left: 8px;
}

.social-buttons button:active {
    background: #f5f5f5;
}

.social-buttons .google { color: #4285f4; }
.social-buttons .facebook { color: #1877f3; }
.social-buttons .apple { color: #222; }

.divider {
    text-align: center;
    color: #bbb;
    margin: 18px 0 12px 0;
    position: relative;
}
.divider span {
    background: #fff;
    padding: 0 12px;
    position: relative;
    z-index: 1;
}
.divider:before {
    content: "";
    display: block;
    border-top: 1px solid #eee;
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    z-index: 0;
}

form {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

label {
    font-size: 0.95em;
    color: #888;
    margin-bottom: 2px;
}

input[type="email"], input[type="password"] {
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    padding: 10px;
    font-size: 1em;
    margin-bottom: 4px;
    outline: none;
    transition: border 0.2s;
}

input[type="email"]:focus, input[type="password"]:focus {
    border: 1.5px solid #0078d4;
}

.password-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.toggle-password {
    position: absolute;
    right: 12px;
    cursor: pointer;
    color: #bbb;
    font-size: 1.1em;
}

.forgot {
    align-self: flex-end;
    font-size: 0.95em;
    color: #0078d4;
    text-decoration: none;
    margin-bottom: 8px;
    margin-top: 2px;
}

.login-btn {
    background: #e0e0e0;
    color: #888;
    border: none;
    border-radius: 24px;
    padding: 12px 0;
    font-size: 1.1em;
    font-weight: 600;
    margin-top: 8px;
    cursor: not-allowed;
    width: 100%;
}

.signup {
    text-align: center;
    margin-top: 18px;
    font-size: 0.97em;
    color: #888;
}

.signup a {
    color: #0078d4;
    text-decoration: none;
    font-weight: 500;
}
</style>
<head>
    <meta charset="UTF-8">
    <title>Super App - Login</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
</head>
<body>
    <div class="top-label">Login Screen</div>
    <div class="container">
        <div id="api" role="main"></div>
        <div class="phone-frame">
            <div style="height: 18px;"></div>
            <div class="header">
                <span class="logo">🔒 Super App</span>
            </div>
            <h2>Log in to your Account</h2>
            <p class="subtitle">Enter your email and password to log in</p>
            <div class="social-buttons">
                <button class="google"><img src="https://img.icons8.com/color/16/000000/google-logo.png"/> <span>Continue with Google</span></button>
                <button class="facebook"><img src="https://img.icons8.com/color/16/000000/facebook-new.png"/> <span>Continue with Facebook</span></button>
                <button class="apple"><img src="https://img.icons8.com/ios-filled/16/000000/mac-os.png"/> <span>Continue with Apple</span></button>
            </div>
            <div class="divider"><span>Or</span></div>
            <form>
                <label for="email">Email</label>
                <input type="email" id="email" placeholder="Enter your email" required value="<EMAIL>">
                <label for="password">Password</label>
                <div class="password-wrapper">
                    <input type="password" id="password" placeholder="Enter your password" required value="*********">
                    <span class="toggle-password">👁️</span>
                </div>
                <a href="#" class="forgot">Forgot Password ?</a>
                <button type="submit" class="login-btn" disabled>Log In</button>
            </form>
            <div class="signup">
                Don't have an account? <a href="#">Sign Up</a>
            </div>
        </div>
    </div>
</body>
</html>