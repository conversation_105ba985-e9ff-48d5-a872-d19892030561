﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Application.Models.Master;
using MRI.OTA.Application.Services;
using MRI.OTA.Common.Constants;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.Tests.Master.Service
{
    public class UnitTestMasterService
    {
        private readonly Mock<IMasterRepository> _masterRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger<MasterService>> _loggerMock;
        private readonly MasterService _service;

        public UnitTestMasterService()
        {
            _masterRepositoryMock = new Mock<IMasterRepository>();
            _mapperMock = new Mock<IMapper>();
            _loggerMock = new Mock<ILogger<MasterService>>();
            _service = new MasterService(_loggerMock.Object, _masterRepositoryMock.Object, _mapperMock.Object);
        }

        [Fact]
        public async Task GetDataByTableName_ReturnsData()
        {
            var expected = new List<CountryModel> { new CountryModel { CountryCode = "US" } };
            _masterRepositoryMock.Setup(r => r.GetDataByTableName<CountryModel>("Countries")).ReturnsAsync(expected);

            var result = await _service.GetDataByTableName<CountryModel>("Countries");

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetDataByTableName_LogsErrorAndReturnsEmptyList_OnException()
        {
            _masterRepositoryMock.Setup(r => r.GetDataByTableName<CountryModel>("Countries")).ThrowsAsync(new Exception("db error"));

            var result = await _service.GetDataByTableName<CountryModel>("Countries");

            Assert.Empty(result);
        }

        [Fact]
        public async Task GetModulesList_ReturnsModules()
        {
            var expected = new List<ViewModuleRelationship> { new ViewModuleRelationship { ModuleId = 1, PropertyRelationshipName = "Owner" } };
            _masterRepositoryMock.Setup(r => r.GetModulesList(1)).ReturnsAsync(expected);

            var result = await _service.GetModulesList(1);

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetModulesList_ReturnsEmptyList_OnException()
        {
            _masterRepositoryMock.Setup(r => r.GetModulesList(1)).ThrowsAsync(new Exception("error"));

            var result = await _service.GetModulesList(1);

            Assert.Empty(result);
        }

        [Fact]
        public async Task GetAllCountriesAsync_ReturnsCountries()
        {
            var expected = new List<CountryModel> { new CountryModel { CountryCode = "US" } };
            _masterRepositoryMock.Setup(r => r.GetDataByTableName<CountryModel>(Constants.CountriesTableName)).ReturnsAsync(expected);

            var result = await _service.GetAllCountriesAsync();

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetAllStatesAsync_ReturnsStates()
        {
            var expected = new List<StateModel> { new StateModel { StateId = 1 } };
            _masterRepositoryMock.Setup(r => r.GetDataByTableName<StateModel>(Constants.StateTableName)).ReturnsAsync(expected);

            var result = await _service.GetAllStatesAsync();

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetAllAdminAreas_ReturnsAdminAreas()
        {
            var expected = new List<AdminAreaModel> { new AdminAreaModel { AreaCode = "A1" } };
            _masterRepositoryMock.Setup(r => r.GetDataByTableName<AdminAreaModel>(Constants.AdministrativeAreasTableName)).ReturnsAsync(expected);

            var result = await _service.GetAllAdminAreas();

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetAllOccupancyTypes_ReturnsOccupancyTypes()
        {
            var expected = new List<OccupancyTypesModel> { new OccupancyTypesModel { OccupancyTypesId = 1 } };
            _masterRepositoryMock.Setup(r => r.GetDataByTableName<OccupancyTypesModel>(Constants.OccupancyTypesTableName)).ReturnsAsync(expected);

            var result = await _service.GetAllOccupancyTypes();

            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task GetPropertyRelationships_ReturnsMappedRelationships()
        {
            var repoResult = new List<PropertyRelationship> { new PropertyRelationship { PropertyRelationshipId = 1 } };
            var mapped = new List<PropertyRelationshipsModel> { new PropertyRelationshipsModel { PropertyRelationshipId = 1 } };
            _masterRepositoryMock.Setup(r => r.GetPropertyRelationship()).ReturnsAsync(repoResult);
            _mapperMock.Setup(m => m.Map<List<PropertyRelationshipsModel>>(repoResult)).Returns(mapped);

            var result = await _service.GetPropertyRelationships();

            Assert.Equal(mapped, result);
        }

        [Fact]
        public async Task GetOccupancyStatusType_ReturnsStatusTypes()
        {
            var expected = new List<OccupancyStatusTypeModel> { new OccupancyStatusTypeModel { OccupancyStatusTypeId = 1 } };
            _masterRepositoryMock.Setup(r => r.GetDataByTableName<OccupancyStatusTypeModel>(Constants.OccupancyStatusTypeTableName)).ReturnsAsync(expected);

            var result = await _service.GetOccupancyStatusType();

            Assert.Equal(expected, result);
        }
    }
}
