using MRI.Integration.Consumer.SDK;
using MRI.Integration.Consumer.SDK.Models;
using MRI.OTA.EventHub.Consumer;
using Serilog;

var builder = Host.CreateApplicationBuilder(args);

// Manually build configuration
var configuration = new ConfigurationBuilder()
    .AddJsonFile("appsettings.json", optional: true, reloadOnChange: true)
    .AddJsonFile($"appsettings.{Environment.GetEnvironmentVariable("DOTNET_ENVIRONMENT") ?? "Production"}.json", optional: true, reloadOnChange: true)
    .AddEnvironmentVariables()
    .Build();

// Setup Serilog manually
Log.Logger = new LoggerConfiguration()
    .ReadFrom.Configuration(configuration)
    .CreateLogger();

// Register Serilog as the logging provider
builder.Services.AddLogging(loggingBuilder =>
{
    loggingBuilder.AddSerilog();
});

// Configure Services
builder.Services.Configure<ConsumerConfig>(configuration.GetSection(nameof(ConsumerConfig)));
builder.Services.AddScoped<IConsumerService, ConsumerService>();
builder.Services.AddScoped<IConsumerEventService, ConsumerEventService>();
builder.Services.AddScoped<INotificationService, NotificationService>();


builder.Services.AddIntegrationConsumerServices(options =>
{
    options.AuthSettings.AuthClientID = configuration.GetValue<string>("ConsumerConfig:ClientID");
    options.AuthSettings.AuthClientSecret = configuration.GetValue<string>("ConsumerConfig:ClientSecret");
    options.AuthSettings.AuthTokenEndpoint = configuration.GetValue<string>("ConsumerConfig:TokenEndpointUrl");
    options.ConsumerId = configuration.GetValue<string>("ConsumerConfig:ConsumerID");
    options.ConsumerApiBaseUrl = configuration.GetValue<string>("ConsumerConfig:ApiBaseUrl");
    options.ProtocolVersion = configuration.GetValue<int>("ConsumerConfig:ProtocolVersion");
    options.WaitTimeForAbandoningEvents = TimeSpan.FromSeconds(configuration.GetValue<int>("ConsumerConfig:WaitTimeForAbandoningEventsInSeconds"));
    options.MinimumSeverityToTriggerFaultDelegate = configuration.GetValue<Severity>("ConsumerConfig:MinimumSeverityToTriggerFaultDelegate");
});

builder.Services.AddHostedService<ConsumerHostedService>();

var host = builder.Build();

host.Run();

// Ensure Serilog flushes logs on shutdown
Log.CloseAndFlush();

