﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Http;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Infrastructure.Caching.Interfaces;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication
{
    public class ApiKeyAuthorizationHandler : AuthorizationHandler<ApiKeyRequirement>
    {
        private readonly IDataSourceRepository _dataSourceRepository;
        private readonly ICacheService _cacheService;

        public ApiKeyAuthorizationHandler(IDataSourceRepository dataSourceRepository, ICacheService cacheService)
        {
            _dataSourceRepository = dataSourceRepository;
            _cacheService = cacheService;
        }
        protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, ApiKeyRequirement requirement)
        {
            if (context.Resource is HttpContext httpContext)
            {
                var accessKey = httpContext.Request.Headers["AccessKey"].FirstOrDefault();
                var accessSecret = httpContext.Request.Headers["AccessSecret"].FirstOrDefault();

                if (!string.IsNullOrEmpty(accessKey) && !string.IsNullOrEmpty(accessSecret) && await IsValidApiKey(accessKey, accessSecret))
                {
                    context.Succeed(requirement);
                }
                else
                {
                    var failureReasons = context.FailureReasons.ToList();
                    failureReasons.Add(new AuthorizationFailureReason(this, "Invalid API Key or User ID."));
                    context.Fail(new AuthorizationFailureReason(this, "Invalid API Key or User ID."));
                }
            }
            return;
        }

        private async Task<bool> IsValidApiKey(string accessKey, string accessSecret)
        {
            // Create a cache key based on the access key
            string cacheKey = $"access_key:{accessKey}";

            // Try to check the validation result from cache first
            var cachedResult =  await _cacheService.ExistsAsync(cacheKey);

            // If found in cache, return the cached result
            if (cachedResult) return true;

            var result = await _dataSourceRepository.GetUserDataSource(accessKey, accessSecret);

            // Store the result in cache for future requests
            if(result != null) _cacheService.SetAsync(cacheKey, accessSecret, expirationMinutes: 30).GetAwaiter();
            return result != null;
        }
    }
}
