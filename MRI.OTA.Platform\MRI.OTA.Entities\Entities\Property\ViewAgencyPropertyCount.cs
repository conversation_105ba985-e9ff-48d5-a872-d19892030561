using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    /// <summary>
    /// Entity for Property Counts by Agency with comprehensive Agency Details
    /// </summary>
    public class ViewAgencyPropertyCount
    {
        /// <summary>
        /// Agency ID
        /// </summary>
        public string AgencyId { get; set; }

        /// <summary>
        /// Agency name (mapped from BusinessRegisteredName)
        /// </summary>
        public string? AgencyName { get; set; }

        /// <summary>
        /// Business registered name
        /// </summary>
        public string? BusinessRegisteredName { get; set; }

        /// <summary>
        /// Business name
        /// </summary>
        public string? BusinessName { get; set; }

        /// <summary>
        /// Business registration number
        /// </summary>
        public string? BusinessRegistrationNumber { get; set; }

        /// <summary>
        /// Agency phone number
        /// </summary>
        public string? Phone { get; set; }

        /// <summary>
        /// Agency email
        /// </summary>
        public string? Email { get; set; }

        /// <summary>
        /// MRI ID
        /// </summary>
        public string? MriId { get; set; }

        /// <summary>
        /// Dark logo link
        /// </summary>
        public string? DarkLogoLink { get; set; }

        /// <summary>
        /// Light logo link
        /// </summary>
        public string? LightLogoLink { get; set; }

        /// <summary>
        /// Branding background color
        /// </summary>
        public string? BrandingBackgroundColor { get; set; }

        /// <summary>
        /// Country code
        /// </summary>
        public string? CountryCode { get; set; }

        /// <summary>
        /// Country name
        /// </summary>
        public string? CountryName { get; set; }

        /// <summary>
        /// State code
        /// </summary>
        public string? StateCode { get; set; }

        /// <summary>
        /// State name
        /// </summary>
        public string? StateName { get; set; }

        /// <summary>
        /// Suburb
        /// </summary>
        public string? Suburb { get; set; }

        /// <summary>
        /// Postal code
        /// </summary>
        public string? PostalCode { get; set; }

        /// <summary>
        /// Administrative area
        /// </summary>
        public string? AdministrativeArea { get; set; }

        /// <summary>
        /// Building number
        /// </summary>
        public string? BuildingNumber { get; set; }

        /// <summary>
        /// Lot number
        /// </summary>
        public string? LotNumber { get; set; }

        /// <summary>
        /// Street address
        /// </summary>
        public string? StreetAddress { get; set; }

        /// <summary>
        /// City
        /// </summary>
        public string? City { get; set; }

        /// <summary>
        /// Locale
        /// </summary>
        public string? Locale { get; set; }

        /// <summary>
        /// Rural delivery
        /// </summary>
        public string? RuralDelivery { get; set; }

        /// <summary>
        /// Post office name
        /// </summary>
        public string? PostOfficeName { get; set; }

        /// <summary>
        /// Agency data source ID
        /// </summary>
        public int? DataSourceId { get; set; }

        /// <summary>
        /// Agency data source name
        /// </summary>
        public string? DataSourceName { get; set; }

        /// <summary>
        /// Count of active properties
        /// </summary>
        public int ActiveCount { get; set; }

        /// <summary>
        /// Count of inactive properties
        /// </summary>
        public int InactiveCount { get; set; }

        /// <summary>
        /// Total count of properties
        /// </summary>
        [ExcludeColumn]
        public int TotalCount => ActiveCount + InactiveCount;

        /// <summary>
        /// Display name for the agency
        /// </summary>
        [ExcludeColumn]
        public string DisplayName => !string.IsNullOrEmpty(AgencyName) 
            ? AgencyName 
            : !string.IsNullOrEmpty(BusinessName) 
                ? BusinessName 
                : AgencyId == "SELF_SOURCE" 
                    ? "Self Source Property" 
                    : AgencyId ?? "Unknown Agency";
    }
} 