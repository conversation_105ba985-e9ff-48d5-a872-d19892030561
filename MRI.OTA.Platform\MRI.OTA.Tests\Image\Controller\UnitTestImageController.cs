﻿using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.API.Controllers.Image;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;

namespace MRI.OTA.UnitTestCases.Image.Controller
{
    public class UnitTestImageController
    {
        private readonly Mock<IImageStorageService> _imageStorageServiceMock;
        private readonly Mock<ILogger<ImageController>> _loggerMock;
        private readonly ImageController _controller;

        public UnitTestImageController()
        {
            _imageStorageServiceMock = new Mock<IImageStorageService>();
            _loggerMock = new Mock<ILogger<ImageController>>();
            // Create actual IConfiguration using ConfigurationBuilder
            var inMemorySettings = new Dictionary<string, string>
                {
                    {"ImageUploadSettings:AllowedExtensions:0", ".jpg"},
                    {"ImageUploadSettings:AllowedExtensions:1", ".png"},
                    {"ImageUploadSettings:MaxFileSizeInBytes", "5"}
                };

            IConfiguration _configurationMock = new ConfigurationBuilder().AddInMemoryCollection(inMemorySettings).Build();

            _controller = new ImageController(_imageStorageServiceMock.Object, _loggerMock.Object, _configurationMock);
        }

        #region UploadImages Tests

        [Fact]
        public async Task UploadImages_ShouldReturnOk_WhenUploadSucceeds()
        {
            // Arrange
            var images = new ImageModel
            {
                PropertyId = 1,
                Files = new List<Base64ImageFile>
                {
                    new Base64ImageFile
                    {
                        FileName = "test.jpg",
                        Base64Content = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD"
                    }
                }
            };

            var expectedResult = new List<(int PropertyId, string ImageBlobUrl)>
            {
                (1, "https://storage.blob.core.windows.net/images/test.jpg")
            };

            _imageStorageServiceMock.Setup(s => s.UploadImages(It.IsAny<ImageModel>()))
                .ReturnsAsync(expectedResult);

            // Act
            var result = await _controller.UploadImages(images);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Item added successfully", apiResponse.Message);
            Assert.Equal(expectedResult, apiResponse.Data);
        }

        [Fact]
        public async Task UploadImages_ShouldReturnBadRequest_WhenNoImagesProvided()
        {
            // Arrange
            var images = new ImageModel
            {
                PropertyId = 1,
                Files = new List<Base64ImageFile>()
            };

            // Act
            var result = await _controller.UploadImages(images);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("No images uploaded", badRequestResult.Value);
        }

        [Fact]
        public async Task UploadImages_ShouldReturnBadRequest_WhenImageIsEmpty()
        {
            // Arrange
            var images = new ImageModel
            {
                PropertyId = 1,
                Files = new List<Base64ImageFile>
                {
                    new Base64ImageFile
                    {
                        FileName = "test.jpg",
                        Base64Content = ""
                    }
                }
            };

            // Act
            var result = await _controller.UploadImages(images);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("One or more images are empty", badRequestResult.Value);
        }

        [Fact]
        public async Task UploadImages_ShouldReturnBadRequest_WhenFileExtensionIsInvalid()
        {
            // Arrange
            var images = new ImageModel
            {
                PropertyId = 1,
                Files = new List<Base64ImageFile>
                {
                    new Base64ImageFile
                    {
                        FileName = "test.exe",
                        Base64Content = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD"
                    }
                }
            };

            // Act
            var result = await _controller.UploadImages(images);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("Invalid file type: test.exe", badRequestResult.Value);
        }

        [Fact]
        public async Task UploadImages_ShouldReturnBadRequest_WhenFileSizeExceedsLimit()
        {
            // Arrange
            // Create a large base64 string (more than 5MB after decoding)
            var largeBase64 = "data:image/jpeg;base64," + new string('A', 7 * 1024 * 1024); // Approximately 7MB

            var images = new ImageModel
            {
                PropertyId = 1,
                Files = new List<Base64ImageFile>
                {
                    new Base64ImageFile
                    {
                        FileName = "test.jpg",
                        Base64Content = largeBase64
                    }
                }
            };

            // Act
            var result = await _controller.UploadImages(images);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            Assert.Equal("File size exceeds maximum limit of 5MB: test.jpg", badRequestResult.Value);
        }

        [Fact]
        public async Task UploadImages_ShouldReturnBadRequest_WhenServiceReturnsEmptyResult()
        {
            // Arrange
            var images = new ImageModel
            {
                PropertyId = 1,
                Files = new List<Base64ImageFile>
                {
                    new Base64ImageFile
                    {
                        FileName = "test.jpg",
                        Base64Content = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD"
                    }
                }
            };

            _imageStorageServiceMock.Setup(s => s.UploadImages(It.IsAny<ImageModel>()))
                .ReturnsAsync(new List<(int PropertyId, string ImageBlobUrl)>());

            // Act
            var result = await _controller.UploadImages(images);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("Item not added", apiResponse.Message);
        }

        [Fact]
        public async Task UploadImages_ShouldReturnStatusCode500_WhenExceptionOccurs()
        {
            // Arrange
            var images = new ImageModel
            {
                PropertyId = 1,
                Files = new List<Base64ImageFile>
                {
                    new Base64ImageFile
                    {
                        FileName = "test.jpg",
                        Base64Content = "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD"
                    }
                }
            };

            _imageStorageServiceMock.Setup(s => s.UploadImages(It.IsAny<ImageModel>()))
                .ThrowsAsync(new Exception("Test exception"));

            // Act
            var result = await _controller.UploadImages(images);

            // Assert
            var statusCodeResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(500, statusCodeResult.StatusCode);
            Assert.Equal("An error occurred while uploading the images", statusCodeResult.Value);
        }

        #endregion

        #region UpdateDefaultImages Tests

        [Fact]
        public async Task UpdateDefaultImages_ShouldReturnOk_WhenUpdateSucceeds()
        {
            // Arrange
            var model = new UpdateDefaultImageModel
            {
                PropertyId = 1,
                PropertyImagesId = 2,
                UserId = 3
            };

            _imageStorageServiceMock.Setup(s => s.UpdateDefaultImage(It.IsAny<UpdateDefaultImageModel>()))
                .ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.UpdateDefaultImages(model);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Item updated successfully", apiResponse.Message);
        }

        [Fact]
        public async Task UpdateDefaultImages_ShouldReturnBadRequest_WhenUpdateFails()
        {
            // Arrange
            var model = new UpdateDefaultImageModel
            {
                PropertyId = 1,
                PropertyImagesId = 2,
                UserId = 3
            };

            _imageStorageServiceMock.Setup(s => s.UpdateDefaultImage(It.IsAny<UpdateDefaultImageModel>()))
                .ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.UpdateDefaultImages(model);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("The item could not be updated.", apiResponse.Message);
        }

        #endregion

        #region DeleteProperty Tests

        [Fact]
        public async Task DeleteProperty_ShouldReturnOk_WhenDeletionSucceeds()
        {
            // Arrange
            var propertyImagesId = 1;
            _imageStorageServiceMock.Setup(s => s.DeletePropertyImage(propertyImagesId))
                .ReturnsAsync(Constants.Success);

            // Act
            var result = await _controller.DeleteProperty(propertyImagesId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.True(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("Item deleted successfully", apiResponse.Message);
        }

        [Fact]
        public async Task DeleteProperty_ShouldReturnOkWithWarning_WhenDefaultImageExists()
        {
            // Arrange
            var propertyImagesId = 1;
            _imageStorageServiceMock.Setup(s => s.DeletePropertyImage(propertyImagesId))
                .ReturnsAsync(Constants.DefaultImageExists);

            // Act
            var result = await _controller.DeleteProperty(propertyImagesId);

            // Assert
            var okResult = Assert.IsType<OkObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(okResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status200OK, apiResponse.StatusCode);
            Assert.Equal("The default image could not be deleted.", apiResponse.Message);
        }

        [Fact]
        public async Task DeleteProperty_ShouldReturnBadRequest_WhenDeletionFails()
        {
            // Arrange
            var propertyImagesId = 1;
            _imageStorageServiceMock.Setup(s => s.DeletePropertyImage(propertyImagesId))
                .ReturnsAsync(Constants.Error);

            // Act
            var result = await _controller.DeleteProperty(propertyImagesId);

            // Assert
            var badRequestResult = Assert.IsType<BadRequestObjectResult>(result);
            var apiResponse = Assert.IsType<ApiResponse<object>>(badRequestResult.Value);
            Assert.False(apiResponse.Success);
            Assert.Equal(StatusCodes.Status400BadRequest, apiResponse.StatusCode);
            Assert.Equal("The item could not be deleted.", apiResponse.Message);
        }

        #endregion
    }
}
