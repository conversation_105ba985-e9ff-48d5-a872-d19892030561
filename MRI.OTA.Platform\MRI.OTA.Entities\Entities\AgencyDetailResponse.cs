﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities
{
    public class AgencyDetailResponse
    {
        [ExcludeColumn]
        public int ArchivePropertyAgencyDetailId { get; set; }
        public int? PropertyId { get; set; }
        public string? SRCPropertyId { get; set; }
        public int AgencyDetailsId { get; set; }
        public string? AgencyId { get; set; }
        public string? MriId { get; set; }
        public int? DataSourceId { get; set; }
        public string? BusinessRegisteredName { get; set; }
        public string? BusinessName { get; set; }
        public string? BusinessRegistrationNumber { get; set; }
        public string? Phone { get; set; }
        public string? Email { get; set; }
        public string? DarkLogoLink { get; set; }
        public string? LightLogoLink { get; set; }
        public string? BrandingBackgroundColor { get; set; }
        public string? CountryCode { get; set; }
        public string? CountryName { get; set; }
        public string? StateCode { get; set; }
        public string? StateName { get; set; }
        public string? Suburb { get; set; }
        public string? PostalCode { get; set; }
        public string? AdministrativeArea { get; set; }
        public string? BuildingNumber { get; set; }
        public string? LotNumber { get; set; }
        public string? StreetAddress { get; set; }
        public string? City { get; set; }
        public string? Locale { get; set; }
        public string? RuralDelivery { get; set; }
        public string? PostOfficeName { get; set; }
       
    }
}
