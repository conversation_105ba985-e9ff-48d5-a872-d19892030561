﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.Application.Services
{
    public class APITrackingService : BaseService<APITrackingDetail, APITrackingDetail, int>, IAPITrackingService
    {
        private readonly ILogger<APITrackingService> _logger;
        private readonly IAPITrackingRepository _apiTrackingRepository;
        private IMapper _mapper { get; set; }

        public APITrackingService(ILogger<APITrackingService> logger, IAPITrackingRepository repository, IMapper mapper) : base(logger, repository, mapper)
        {
            _logger = logger;
            _apiTrackingRepository = repository;
            _mapper = mapper;
        }
        public async Task LogAPITrackingDetails(APITrackingDetail apiTrackingDetail)
        {
            await _apiTrackingRepository.LogAPITrackingDetails(apiTrackingDetail);
        }
    }
}
