﻿using MRI.OTA.Common.Interfaces;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;

namespace MRI.OTA.DBCore.Interfaces
{
    /// <summary>
    /// Interface for userRepository
    /// </summary>
    public interface IUserRepository : IBaseRepository<Users, int> 
    {
        public Task<List<Users>> GetAllUsers();

        public Task<ViewUserProfileModel> GetUserDetails(ViewUserProfileModel userProfileModel);

        public Task<int> UpdateUserEmail(Users user);

        public Task<int> UpdateUserProviderType(Users user);

        public Task<int> UpdateUserTermsAndCondition(Users user);

        public Task<int> UpdateUserProfileSettings(Users user);

        public Task<bool> DeleteAccount(int userId, string providerId);

        public Task<List<DataSource>> GetDataSourcesByUserId(int userId);

        public Task<int> GetUserCount();

        public Task<List<Users>> GetUsersBatch(int skip, int take);
        public Task<int> AddUpdateUserDevice(UserDeviceDetail deviceDetail, int userId);
        public Task<bool> DeleteUserDeviceInfo(int userId, string deviceId);
    }
}
