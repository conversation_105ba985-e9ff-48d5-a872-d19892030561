FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Debug
WORKDIR /src

COPY MRI.OTA.Platform/MRI.OTA.API/*.csproj ./MRI.OTA.API/
RUN dotnet restore ./MRI.OTA.API/MRI.OTA.API.csproj

COPY . .
WORKDIR /src/MRI.OTA.API

EXPOSE 8080
EXPOSE 8081

# RUN apt-get update && apt-get install -y --no-install-recommends openssl ca-certificates
# RUN mkdir -p /app
# RUN dotnet dev-certs https -ep /app/aspnetapp.pfx -p "YourPfxPassword"
# RUN openssl pkcs12 -in /app/aspnetapp.pfx -nokeys -passin pass:YourPfxPassword -out /usr/local/share/ca-certificates/aspnetapp.crt && \
#     update-ca-certificates
# RUN chown app:app /app/aspnetapp.pfx && chmod 644 /app/aspnetapp.pfx

# # Configure <PERSON><PERSON><PERSON> to use the certificate
# ENV ASPNETCORE_Kestrel__Certificates__Default__Path="/app/aspnetapp.pfx" \
#     ASPNETCORE_Kestrel__Certificates__Default__Password="YourPfxPassword"

USER app

# Use dotnet watch to run the app
CMD ["dotnet", "watch", "run"]
