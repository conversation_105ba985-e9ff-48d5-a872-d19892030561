﻿using Microsoft.AspNetCore.Mvc;

namespace MRI.OTA.Common.Helper
{
    public static class ResultHelper
    {
        public static bool IsSuccessResult(IActionResult result)
        {
            if (result is ObjectResult objectResult)
            {
                return objectResult.StatusCode.HasValue && objectResult.StatusCode.Value >= 200 && objectResult.StatusCode.Value < 300;
            }
            if (result is StatusCodeResult statusCodeResult)
            {
                return statusCodeResult.StatusCode >= 200 && statusCodeResult.StatusCode < 300;
            }
            return false;
        }
    }
}
