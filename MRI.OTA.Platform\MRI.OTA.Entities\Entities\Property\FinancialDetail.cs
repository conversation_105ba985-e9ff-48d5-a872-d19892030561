﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class FinancialDetail
    {
        public string SRCAgencyId { get; set; }
        [ExcludeColumn]
        public string SRCUserId { get; set; }
        public string SRCManagementId { get; set; }
        public string SRCPropertyId { get; set; }
        public int PropertyId { get; set; }
        public decimal? OwnershipTotalAvailableBalance { get; set; }
        public decimal? PropertyOutstandingFees { get; set; }
        public decimal? PropertyOutstandingInvoices { get; set; }
        public decimal? PropertyOverdueInvoices { get; set; }
        public decimal? LastPaymentAmount { get; set; }
        public DateTime? LastStatementDate { get; set; }

        public string? Currency { get; set; }
    }
}
