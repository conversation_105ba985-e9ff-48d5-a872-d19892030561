﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class FinancialDetailMappingProfile : Profile
    {
        public FinancialDetailMappingProfile() : base("FinancialDetailMappingProfile")
        {
            CreateMap<FinancialDetailResponse, FinancialDetail>()
            .ForMember(dest => dest.SRCAgencyId, opt => opt.MapFrom(src => src.AgencyId))
            .ForMember(dest => dest.SRCUserId, opt => opt.MapFrom(src => src.UserId))
            .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
            .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
            .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1))
            .ForMember(dest => dest.OwnershipTotalAvailableBalance, opt => opt.MapFrom(src => src.OwnershipTotalAvailableBalance))
            .ForMember(dest => dest.PropertyOutstandingFees, opt => opt.MapFrom(src => src.PropertyOutstandingFees))
            .ForMember(dest => dest.PropertyOutstandingInvoices, opt => opt.MapFrom(src => src.PropertyOutstandingInvoices))
            .ForMember(dest => dest.PropertyOverdueInvoices, opt => opt.MapFrom(src => src.PropertyOverdueInvoices))
            .ForMember(dest => dest.LastPaymentAmount, opt => opt.MapFrom(src => src.LastPaymentAmount))
            .ForMember(dest => dest.Currency, opt => opt.MapFrom(src => src.Currency))
            .ForMember(dest => dest.LastStatementDate, opt => opt.MapFrom(src => src.LastStatementDate));
        }
    }
}
