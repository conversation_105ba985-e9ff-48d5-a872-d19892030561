using Microsoft.ApplicationInsights;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using Microsoft.IdentityModel.Tokens;
using MRI.OTA.Common.Constants;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication
{
    /// <summary>
    /// Handler for authentication exceptions
    /// </summary>
    public class AuthenticationExceptionHandler
    {
        private readonly ILogger _logger;
        private readonly TelemetryClient _telemetryClient;
        private readonly IResponseGenerator _responseGenerator;
        private readonly AuthenticationLogger _authLogger;

        /// <summary>
        /// Constructor for AuthenticationExceptionHandler
        /// </summary>
        /// <param name="logger">The logger</param>
        /// <param name="telemetryClient">The telemetry client</param>
        /// <param name="responseGenerator">The response generator</param>
        /// <param name="authLogger">The authentication logger</param>
        public AuthenticationExceptionHandler(
            ILogger logger,
            TelemetryClient telemetryClient,
            IResponseGenerator responseGenerator,
            AuthenticationLogger authLogger)
        {
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _telemetryClient = telemetryClient ?? throw new ArgumentNullException(nameof(telemetryClient));
            _responseGenerator = responseGenerator ?? throw new ArgumentNullException(nameof(responseGenerator));
            _authLogger = authLogger ?? throw new ArgumentNullException(nameof(authLogger));
        }

        /// <summary>
        /// Handles an authentication exception
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="ex">The exception</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task HandleAuthenticationException(HttpContext context, Exception ex)
        {
            var properties = new Dictionary<string, string>
            {
                ["Path"] = context.Request.Path,
                ["ExceptionType"] = ex.GetType().Name,
                ["CorrelationId"] = _authLogger.GetOrGenerateCorrelationId(context)
            };

            // Track authentication failure
            _telemetryClient.TrackException(ex, properties);

            var (message, statusCode) = GetExceptionDetails(ex);

            _logger.LogError(ex, "Authentication error occurred: {Message}", message);
            await _responseGenerator.WriteUnauthorizedResponse(context, message, statusCode);
        }

        /// <summary>
        /// Gets the details of an exception
        /// </summary>
        /// <param name="ex">The exception</param>
        /// <returns>A tuple containing the message and status code</returns>
        private (string Message, int StatusCode) GetExceptionDetails(Exception ex)
        {
            return ex switch
            {
                SecurityTokenExpiredException _ =>
                    (MessagesConstants.TokenExpiredMessage, StatusCodes.Status401Unauthorized),
                SecurityTokenInvalidSignatureException _ =>
                    (MessagesConstants.TokenInvalidSignatureMessage, StatusCodes.Status401Unauthorized),
                SecurityTokenInvalidIssuerException _ =>
                    (MessagesConstants.TokenInvalidIssuerMessage, StatusCodes.Status401Unauthorized),
                SecurityTokenException _ =>
                    (MessagesConstants.TokenValidationFailedMessage, StatusCodes.Status401Unauthorized),
                _ => ("Internal Server Error", StatusCodes.Status500InternalServerError)
            };
        }
    }
}
