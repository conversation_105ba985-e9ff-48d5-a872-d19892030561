﻿namespace MRI.OTA.EventHub.Consumer
{
    public class EventData
    {
        public string? SourceServiceId { get; set; }

        public string? EntityId { get; set; }

        public string? InstanceId { get; set; }

        public string? UserId { get; set; }

        public string? TimeStamp { get; set; }

        public string? CollationId { get; set; }

        public string? EventType { get; set; }
    }
}
