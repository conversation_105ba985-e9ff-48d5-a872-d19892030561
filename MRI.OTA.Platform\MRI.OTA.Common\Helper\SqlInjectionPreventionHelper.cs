﻿using Microsoft.Extensions.Logging;
using System.Text.RegularExpressions;

namespace MRI.OTA.Common.Helper
{
    public static class SqlInjectionPreventionHelper
    {
        private static readonly Regex SqlInjectionPattern = new Regex(@"[;'\-\-]", RegexOptions.Compiled);
        public static bool ContainsSqlInjection(string input)
        {
            if (string.IsNullOrWhiteSpace(input)) return false;

            string[] blacklist = {
            "--", ";--", ";", "/*", "*/", "@@", "@",
            "char", "nchar", "varchar", "nvarchar",
            "alter", "begin", "cast", "create", "cursor",
            "declare", "delete", "drop", "exec", "execute",
            "fetch", "insert", "kill", "select", "sys",
            "table", "update", "union"
            };

            string lower = input.ToLowerInvariant();
            foreach (var keyword in blacklist)
            {
                if (lower.Contains(keyword)) return true;
            }

            return false;
        }
      
        public static bool ContainsSqlInjectionInArray(string[] inputs)
        {
            if (inputs == null || inputs.Length == 0)
                return false;

            return inputs.Any(input => ContainsSqlInjection(input));
        }

        public static void LogPotentialSqlInjection(string input, string methodName, ILogger logger)
        {
            if (logger == null)
                throw new ArgumentNullException(nameof(logger));

            logger.LogWarning("Potential SQL injection detected in method {MethodName}. Input: {Input}", methodName, input);
        }
    }
}
