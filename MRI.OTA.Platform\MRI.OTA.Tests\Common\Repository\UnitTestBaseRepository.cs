using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Interfaces;
using System.Data;

namespace MRI.OTA.UnitTestCases.Common.Repository
{
    public class UnitTestBaseRepository
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<UnitTestBaseRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<IDbTransaction> _mockTransaction;

        public UnitTestBaseRepository()
        {
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<UnitTestBaseRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();
            _mockTransaction = new Mock<IDbTransaction>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);
            _mockConnection.Setup(c => c.BeginTransaction()).Returns(_mockTransaction.Object);
        }

        private TestRepository CreateRepository()
        {
            return new TestRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object);
        }

        #region GetAllAsync Tests

        [Fact]
        public async Task GetAllAsync_ShouldReturnAllEntities()
        {
            // Arrange
            var expectedEntities = new List<TestEntity>
            {
                new TestEntity { Id = 1, Name = "Test1" },
                new TestEntity { Id = 2, Name = "Test2" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestEntity>(_mockConnection.Object, It.IsAny<string>(), null, null, null, null))
                .ReturnsAsync(expectedEntities);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetAllAsync();

            // Assert
            Assert.Equal(expectedEntities, result);
            _mockDapperWrapper.Verify(d => d.QueryAsync<TestEntity>(
                _mockConnection.Object,
                "SELECT * FROM TestEntity",
                null, null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task GetAllAsync_Generic_ShouldReturnAllEntities()
        {
            // Arrange
            var expectedEntities = new List<TestDto>
            {
                new TestDto { Id = 1, Name = "Test1" },
                new TestDto { Id = 2, Name = "Test2" }
            };

            var query = "SELECT Id, Name FROM TestTable";
            var parameters = new { Param = "Value" };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ReturnsAsync(expectedEntities);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetAllAsync<TestDto>(query, parameters);

            // Assert
            Assert.Equal(expectedEntities, result);
            _mockDapperWrapper.Verify(d => d.QueryAsync<TestDto>(
                _mockConnection.Object,
                query,
                parameters, null, null, null),
                Times.Once);
        }

        #endregion

        #region GetByIdAsync Tests

        [Fact]
        public async Task GetByIdAsync_ShouldReturnEntityById()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var expectedEntity = new TestEntity { Id = id, Name = "Test" };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<TestEntity>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedEntity);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetByIdAsync(id, idColumnName);

            // Assert
            Assert.Equal(expectedEntity, result);
            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<TestEntity>(
                _mockConnection.Object,
                "SELECT * FROM TestEntity WHERE Id = @Id",
                It.Is<object>(o => o.GetType().GetProperty("Id").GetValue(o).Equals(id)),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task GetByIdAsync_Generic_ShouldReturnEntityById()
        {
            // Arrange
            var expectedDto = new TestDto { Id = 1, Name = "Test" };
            var query = "SELECT * FROM TestTable WHERE Id = @Id";
            var parameters = new { Id = 1 };

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ReturnsAsync(expectedDto);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetByIdAsync<TestDto>(query, parameters);

            // Assert
            Assert.Equal(expectedDto, result);
            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<TestDto>(
                _mockConnection.Object,
                query,
                parameters,
                null, null, null),
                Times.Once);
        }

        #endregion

        #region GetByIdListAsync Tests

        [Fact]
        public async Task GetByIdListAsync_ShouldReturnEntitiesByIds()
        {
            // Arrange
            var expectedDtos = new List<TestDto>
            {
                new TestDto { Id = 1, Name = "Test1" },
                new TestDto { Id = 2, Name = "Test2" }
            };

            var query = "SELECT * FROM TestTable WHERE Id IN (@Ids)";
            var parameters = new { Ids = new[] { 1, 2 } };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ReturnsAsync(expectedDtos);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetByIdListAsync<TestDto>(query, parameters);

            // Assert
            Assert.Equal(expectedDtos, result);
            _mockDapperWrapper.Verify(d => d.QueryAsync<TestDto>(
                _mockConnection.Object,
                query,
                parameters,
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task GetByIdListAsync_ShouldHandleExceptions()
        {
            // Arrange
            var query = "SELECT * FROM TestTable WHERE Id IN (@Ids)";
            var parameters = new { Ids = new[] { 1, 2 } };
            var expectedException = new Exception("Test exception");

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => repository.GetByIdListAsync<TestDto>(query, parameters));
            Assert.Same(expectedException, exception);
        }

        #endregion

        #region GetDataByTableName Tests

        [Fact]
        public async Task GetDataByTableName_ShouldReturnEntitiesFromTable()
        {
            // Arrange
            var tableName = "TestTable";
            var expectedDtos = new List<TestDto>
            {
                new TestDto { Id = 1, Name = "Test1" },
                new TestDto { Id = 2, Name = "Test2" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(_mockConnection.Object, $"SELECT * FROM {tableName}", null, null, null, null))
                .ReturnsAsync(expectedDtos);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetDataByTableName<TestDto>(tableName);

            // Assert
            Assert.Equal(expectedDtos, result);
            _mockDapperWrapper.Verify(d => d.QueryAsync<TestDto>(
                _mockConnection.Object,
                $"SELECT * FROM {tableName}",
                null, null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task GetDataByTableName_ShouldHandleExceptions()
        {
            // Arrange
            var tableName = "TestTable";
            var expectedException = new Exception("Test exception");

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(_mockConnection.Object, It.IsAny<string>(), null, null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => repository.GetDataByTableName<TestDto>(tableName));
            Assert.Same(expectedException, exception);
        }

        #endregion

        #region AddAsync Tests

        [Fact]
        public async Task AddAsync_ShouldInsertEntityAndReturnId()
        {
            // Arrange
            var entity = new TestEntity { Id = 0, Name = "Test" };
            var expectedId = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), entity, null, null, null))
                .ReturnsAsync(expectedId);

            var repository = CreateRepository();

            // Act
            var result = await repository.AddAsync(entity);

            // Assert
            Assert.Equal(expectedId, result);
            _mockDapperWrapper.Verify(d => d.ExecuteScalarAsync<int>(
                _mockConnection.Object,
                It.Is<string>(s => s.StartsWith("INSERT INTO TestEntity")),
                entity,
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task AddAsync_WithTableNameAndIdColumn_ShouldInsertAndReturnId()
        {
            // Arrange
            var tableName = "TestTable";
            var idColumnName = "Id";
            var keyValuePairs = new Dictionary<string, object>
            {
                { "Name", "Test" },
                { "Value", 100 }
            };
            var expectedId = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedId);

            var repository = CreateRepository();

            // Act
            var result = await repository.AddAsync(tableName, idColumnName, keyValuePairs);

            // Assert
            Assert.Equal(expectedId, result);
            _mockDapperWrapper.Verify(d => d.ExecuteScalarAsync<int>(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains($"INSERT INTO {tableName}") && s.Contains($"OUTPUT INSERTED.{idColumnName}")),
                It.IsAny<object>(),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task AddAsync_WithTransaction_ShouldUseProvidedTransaction()
        {
            // Arrange
            var tableName = "TestTable";
            var idColumnName = "Id";
            var keyValuePairs = new Dictionary<string, object>
            {
                { "Name", "Test" },
                { "Value", 100 }
            };
            var expectedId = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(
                    _mockConnection.Object,
                    It.IsAny<string>(),
                    It.IsAny<object>(),
                    _mockTransaction.Object,
                    null, null))
                .ReturnsAsync(expectedId);

            var repository = CreateRepository();

            // Act
            var result = await repository.AddAsync(tableName, idColumnName, keyValuePairs, _mockTransaction.Object, _mockConnection.Object);

            // Assert
            Assert.Equal(expectedId, result);
            _mockDapperWrapper.Verify(d => d.ExecuteScalarAsync<int>(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains($"INSERT INTO {tableName}") && s.Contains($"OUTPUT INSERTED.{idColumnName}")),
                It.IsAny<object>(),
                _mockTransaction.Object,
                null, null),
                Times.Once);
        }

        [Fact]
        public async Task AddAsync_WithStringDictionary_ShouldInsertAndReturnRowCount()
        {
            // Arrange
            var tableName = "TestTable";
            var keyValuePairs = new Dictionary<string, string>
            {
                { "Name", "Test" },
                { "Value", "100" }
            };
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.AddAsync(tableName, keyValuePairs);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains($"INSERT INTO {tableName}")),
                It.IsAny<object>(),
                null, null, null),
                Times.Once);
        }

        #endregion

        #region UpdateAsync Tests

        [Fact]
        public async Task UpdateAsync_ShouldUpdateEntityAndReturnRowCount()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var entity = new TestEntity { Id = id, Name = "Updated" };
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateAsync(id, idColumnName, entity);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                It.Is<string>(s => s.StartsWith("UPDATE TestEntity SET") && s.Contains($"WHERE {idColumnName} = @Id")),
                It.IsAny<object>(),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithTableNameAndIdColumn_ShouldUpdateAndReturnRowCount()
        {
            // Arrange
            var tableName = "TestTable";
            var idColumnName = "Id";
            var idValue = 1;
            var keyValuePairs = new Dictionary<string, object>
            {
                { "Name", "Updated" },
                { "Value", 200 }
            };
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateAsync(tableName, idColumnName, idValue, keyValuePairs);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains($"UPDATE {tableName} SET") && s.Contains($"WHERE {idColumnName} = @{idColumnName}")),
                It.IsAny<object>(),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithQuery_ShouldExecuteQueryAndReturnRowCount()
        {
            // Arrange
            var query = "UPDATE TestTable SET Name = 'Updated' WHERE Id = 1";
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, query, null, null, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateAsync(query);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                query,
                null, null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithQueryAndParameters_ShouldExecuteQueryAndReturnRowCount()
        {
            // Arrange
            var query = "UPDATE TestTable SET Name = @Name WHERE Id = @Id";
            var parameters = new { Id = 1, Name = "Updated" };
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, query, parameters, null, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateAsync(query, parameters);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                query,
                parameters,
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task UpdateAsync_WithTransaction_ShouldUseProvidedTransaction()
        {
            // Arrange
            var query = "UPDATE TestTable SET Name = @Name WHERE Id = @Id";
            var parameters = new { Id = 1, Name = "Updated" };
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, query, parameters, _mockTransaction.Object, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateAsync(query, parameters, _mockTransaction.Object, _mockConnection.Object);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                query,
                parameters,
                _mockTransaction.Object, null, null),
                Times.Once);
        }

        #endregion

        #region DeleteAsync Tests

        [Fact]
        public async Task DeleteAsync_ShouldDeleteEntityAndReturnRowCount()
        {
            // Arrange
            var id = 1;
            var idColumnName = "Id";
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.DeleteAsync(id, idColumnName);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                $"DELETE FROM TestEntity WHERE {idColumnName} = @Id",
                It.Is<object>(o => o.GetType().GetProperty("Id").GetValue(o).Equals(id)),
                null, null, null),
                Times.Once);
        }

        #endregion

        #region QueryAsync Tests

        [Fact]
        public async Task QueryAsync_ShouldReturnResults()
        {
            // Arrange
            var query = "SELECT * FROM TestTable";
            var parameters = new { Id = 1 };
            var expectedResults = new List<TestDto>
            {
                new TestDto { Id = 1, Name = "Test" },
                new TestDto { Id = 2, Name = "Test2" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ReturnsAsync(expectedResults);

            var repository = CreateRepository();

            // Act
            var result = await repository.QueryAsync<TestDto>(query, parameters);

            // Assert
            Assert.Equal(expectedResults, result);
            _mockDapperWrapper.Verify(d => d.QueryAsync<TestDto>(
                _mockConnection.Object,
                query,
                parameters, null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task QueryAsync_ShouldHandleExceptions()
        {
            // Arrange
            var query = "SELECT * FROM TestTable";
            var parameters = new { Id = 1 };
            var expectedException = new Exception("Test exception");

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => repository.QueryAsync<TestDto>(query, parameters));
            Assert.Same(expectedException, exception);
        }

        #endregion

        #region QueryFirstOrDefaultAsync Tests        
        [Fact]
        public async Task QueryFirstOrDefaultAsync_ShouldReturnFirstResult()
        {
            // Arrange
            var query = "SELECT * FROM TestTable WHERE Id = @Id";
            var parameters = new { Id = 1 };
            var expectedResult = new TestDto { Id = 1, Name = "Test" };

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ReturnsAsync(expectedResult);

            var repository = CreateRepository();

            // Act
            var result = await repository.QueryFirstOrDefaultAsync<TestDto>(query, parameters);

            // Assert
            Assert.Equal(expectedResult, result);
            _mockDapperWrapper.Verify(d => d.QueryFirstOrDefaultAsync<TestDto>(
                _mockConnection.Object,
                query,
                parameters,
                null, null, null),
                Times.Once);
        }
        [Fact]
        public async Task QueryFirstOrDefaultAsync_ShouldHandleExceptions()
        {
            // Arrange
            var query = "SELECT * FROM TestTable WHERE Id = @Id";
            var parameters = new { Id = 1 };
            var expectedException = new Exception("Test exception");

            _mockDapperWrapper
                .Setup(d => d.QueryFirstOrDefaultAsync<TestDto>(_mockConnection.Object, query, parameters, null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() => repository.QueryFirstOrDefaultAsync<TestDto>(query, parameters));
            Assert.Same(expectedException, exception);
        }

        #endregion

        #region ConvertToDictionary Tests

        [Fact]
        public void ConvertToDictionary_ShouldConvertEntityToDictionary()
        {
            // Arrange
            var entity = new TestEntity { Id = 1, Name = "Test" };
            var repository = CreateRepository();

            // Act
            var result = repository.ConvertToDictionary(entity);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal(1, result["Id"]);
            Assert.Equal("Test", result["Name"]);
        }

        [Fact]
        public void ConvertToDictionary_ShouldHandleDateTimeValues()
        {
            // Arrange
            var now = DateTime.UtcNow;
            var entity = new TestEntityWithDateTime { Id = 1, CreatedDate = now };
            var repository = CreateRepository();

            // Act
            var result = repository.ConvertToDictionary(entity);

            // Assert
            Assert.Equal(2, result.Count);
            Assert.Equal(1, result["Id"]);
            Assert.NotNull(result["CreatedDate"]);
        }

        #endregion

        #region GenerateUpdateStatement Tests

        [Fact]
        public async Task GenerateUpdateStatement_ShouldGenerateValidSqlStatement()
        {
            // Arrange
            var tableName = "TestTable";
            var values = new Dictionary<string, object>
            {
                { "Name", "Test" },
                { "Value", 100 }
            };
            var whereClause = "Id = 1";
            var repository = CreateRepository();

            // Act
            var result = await repository.GenerateUpdateStatement(tableName, values, whereClause);

            // Assert
            Assert.Contains($"UPDATE {tableName} SET", result);
            Assert.Contains("Name = @Name", result);
            Assert.Contains("Value = @Value", result);
            Assert.Contains($"WHERE {whereClause}", result);
        }

        [Fact]
        public async Task GenerateUpdateStatement_WithEmptyValues_ShouldThrowArgumentException()
        {
            // Arrange
            var tableName = "TestTable";
            var values = new Dictionary<string, object>();
            var whereClause = "Id = 1";
            var repository = CreateRepository();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => repository.GenerateUpdateStatement(tableName, values, whereClause));
        }

        [Fact]
        public async Task GenerateUpdateStatement_WithEmptyWhereClause_ShouldThrowArgumentException()
        {
            // Arrange
            var tableName = "TestTable";
            var values = new Dictionary<string, object> { { "Name", "Test" } };
            var whereClause = "";
            var repository = CreateRepository();

            // Act & Assert
            await Assert.ThrowsAsync<ArgumentException>(() => repository.GenerateUpdateStatement(tableName, values, whereClause));
        }

        #endregion

        #region ExecuteWithTransactionAsync Tests

        [Fact]
        public async Task ExecuteWithTransactionAsync_ShouldManageTransaction()
        {
            // Arrange
            var expectedResult = 1;
            var repository = CreateRepository();

            // Act
            var result = await repository.TestExecuteWithTransactionAsync<int>((conn, trans) =>
                Task.FromResult(expectedResult));

            // Assert
            Assert.Equal(expectedResult, result);
            _mockTransaction.Verify(t => t.Commit(), Times.Once);
            _mockTransaction.Verify(t => t.Rollback(), Times.Never);
        }

        [Fact]
        public async Task ExecuteWithTransactionAsync_OnException_ShouldRollbackTransaction()
        {
            // Arrange
            var repository = CreateRepository();
            var expectedException = new Exception("Test exception");

            // Act & Assert
            var exception = await Assert.ThrowsAsync<Exception>(() =>
                repository.TestExecuteWithTransactionAsync<int>((conn, trans) =>
                    throw expectedException));

            Assert.Same(expectedException, exception);
            _mockTransaction.Verify(t => t.Commit(), Times.Never);
            _mockTransaction.Verify(t => t.Rollback(), Times.Once);
        }

        #endregion

        [Fact]
        public async Task DeleteAsync_ShouldDeleteEntity_UsingDefaultConnection()
        {
            // Arrange
            var tableName = "TestTable";
            var id = "123";
            var idColumnName = "Id";
            var expectedRowsAffected = 1;
            var expectedQuery = $"DELETE FROM {tableName} WHERE {idColumnName} = @Id";

            _mockDapperWrapper
                 .Setup(d => d.ExecuteAsync(
                     _mockConnection.Object,
                     expectedQuery,
                     It.Is<object>(o =>
                         o != null &&
                         o.GetType().GetProperty("Id") != null &&
                         o.GetType().GetProperty("Id").GetValue(o) != null &&
                         o.GetType().GetProperty("Id").GetValue(o).ToString() == id
                     ),
                     null, null, null))
                 .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.DeleteAsync(tableName, id, idColumnName);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
        }

        [Fact]
        public async Task DeleteAsync_ShouldThrowException_AndLog_WhenDapperThrows()
        {
            // Arrange
            var tableName = "TestTable";
            var id = "789";
            var idColumnName = "Id";
            var expectedQuery = $"DELETE FROM {tableName} WHERE {idColumnName} = @Id";
            var expectedException = new System.Exception("Delete failed");

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, expectedQuery, It.IsAny<object>(), null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var ex = await Assert.ThrowsAsync<System.Exception>(() => repository.DeleteAsync(tableName, id, idColumnName));
            Assert.Equal(expectedException, ex);
        }

        [Fact]
        public async Task QueryAsync_WithTransactionAndConnection_ShouldReturnResults()
        {
            // Arrange
            var query = "SELECT * FROM TestTable";
            var parameters = new { Id = 1 };
            var expectedResults = new List<TestDto>
            {
                new TestDto { Id = 1, Name = "Test" },
                new TestDto { Id = 2, Name = "Test2" }
            };

            _mockDapperWrapper
                .Setup(d => d.QueryAsync<TestDto>(
                    _mockConnection.Object,
                    query,
                    parameters,
                    _mockTransaction.Object,
                    null,
                    null
                ))
                .ReturnsAsync(expectedResults);

            var repository = CreateRepository();

            // Act
            var result = await repository.QueryAsync<TestDto>(query, parameters, _mockTransaction.Object, _mockConnection.Object);

            // Assert
            Assert.Equal(expectedResults, result);
        }

        [Fact]
        public async Task QueryAsync_WithTransactionAndConnection_ShouldLogAndThrow_OnDapperException()
        {
            // Arrange
            var query = "SELECT * FROM TestTable";
            var parameters = new { Id = 1 };
            var expectedException = new Exception("Dapper error");

            _mockDapperWrapper
                 .Setup(d => d.QueryAsync<TestDto>(
                     _mockConnection.Object,
                     query,
                     parameters,
                     _mockTransaction.Object,
                     null,
                     null
                 ))
                 .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var ex = await Assert.ThrowsAsync<Exception>(() =>
                repository.QueryAsync<TestDto>(query, parameters, _mockTransaction.Object, _mockConnection.Object));
            Assert.Equal(expectedException, ex);
        }

        [Fact]
        public async Task QueryAsync_WithTransactionAndConnection_ShouldLogAndThrow_OnOuterException()
        {
            // Arrange
            var query = "SELECT * FROM TestTable";
            var parameters = new { Id = 1 };
            var expectedException = new InvalidOperationException("Outer error");

            var repository = CreateRepository();

            // Simulate an exception in the outer try/catch by throwing from the wrapper method
            _mockDapperWrapper
               .Setup(d => d.QueryAsync<TestDto>(
                   _mockConnection.Object,
                   query,
                   parameters,
                   _mockTransaction.Object,
                   null,
                   null
               ))
               .ThrowsAsync(expectedException);

            // Act & Assert
            var ex = await Assert.ThrowsAsync<InvalidOperationException>(() =>
                repository.QueryAsync<TestDto>(query, parameters, _mockTransaction.Object, _mockConnection.Object));
            Assert.Equal(expectedException, ex);
        }
    }
}

#region Helper Classes

// Helper classes for testing
public class TestEntity
{
    public int Id { get; set; }
    public string Name { get; set; }
}

public class TestEntityWithDateTime
{
    public int Id { get; set; }
    public DateTime CreatedDate { get; set; }
}

public class TestDto
{
    public int Id { get; set; }
    public string Name { get; set; }
}

// Concrete implementation of BaseRepository for testing
public class TestRepository : BaseRepository<TestEntity, int>
{
    public TestRepository(
        IDbConnectionFactory connectionFactory,
        ILogger logger,
        IDapperWrapper dapperWrapper)
        : base(connectionFactory, logger, dapperWrapper)
    {
    }

    // Expose protected method for testing
    public Task<T> TestExecuteWithTransactionAsync<T>(Func<IDbConnection, IDbTransaction, Task<T>> func)
    {
        return ExecuteWithTransactionAsync(func);
    }
}

#endregion
