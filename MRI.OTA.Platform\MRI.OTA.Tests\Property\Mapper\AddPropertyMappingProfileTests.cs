﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.UnitTestCases.Property.Mapper
{
    public class AddPropertyMappingProfileTests
    {
        private readonly IMapper _mapper;

        public AddPropertyMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<AddPropertyMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_UserPropertiesModel_To_UserProperties()
        {
            var model = new UserPropertiesModel { /* Initialize properties */ };

            var entity = _mapper.Map<UserProperties>(model);

            Assert.NotNull(entity);
            // Add assertions for property mappings  
        }

        [Fact]
        public void Should_Map_UserProperties_To_UserPropertiesModel()
        {
            var entity = new UserProperties { /* Initialize properties */ };

            var model = _mapper.Map<UserPropertiesModel>(entity);

            Assert.NotNull(model);
            // Add assertions for property mappings  
        }

        [Fact]
        public void Should_Map_UserDataSourceModel_To_UserDataSource()
        {
            var model = new UserDataSourceModel { /* Initialize properties */ };

            var entity = _mapper.Map<UserDataSource>(model);

            Assert.NotNull(entity);
            // Add assertions for property mappings  
        }

        [Fact]
        public void Should_Map_UserDataSource_To_UserDataSourceModel()
        {
            var entity = new UserDataSource { /* Initialize properties */ };

            var model = _mapper.Map<UserDataSourceModel>(entity);

            Assert.NotNull(model);
            // Add assertions for property mappings  
        }

        [Fact]
        public void Should_Map_PropertyManagerInformationModel_To_PropertyManagerInformation()
        {
            var model = new PropertyManagerInformationModel { /* Initialize properties */ };

            var entity = _mapper.Map<PropertyManagerInformation>(model);

            Assert.NotNull(entity);
            // Add assertions for property mappings  
        }

        [Fact]
        public void Should_Map_PropertyManagerInformation_To_PropertyManagerInformationModel()
        {
            var entity = new PropertyManagerInformation { /* Initialize properties */ };

            var model = _mapper.Map<PropertyManagerInformationModel>(entity);

            Assert.NotNull(model);
            // Add assertions for property mappings  
        }

        [Fact]
        public void Should_Map_PropertyFinancialInformationModel_To_PropertyFinancialInformation()
        {
            var model = new PropertyFinancialInformationModel { /* Initialize properties */ };

            var entity = _mapper.Map<PropertyFinancialInformation>(model);

            Assert.NotNull(entity);
            // Add assertions for property mappings  
        }

        [Fact]
        public void Should_Map_PropertyFinancialInformation_To_PropertyFinancialInformationModel()
        {
            var entity = new PropertyFinancialInformation { /* Initialize properties */ };

            var model = _mapper.Map<PropertyFinancialInformationModel>(entity);

            Assert.NotNull(model);
            // Add assertions for property mappings  
        }
    }
}
