using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.DBCore.Interfaces
{
    /// <summary>
    /// Interface for Integration Repository - handles agency and other integration data
    /// </summary>
    public interface IIntegrationRepository : IBaseRepository<AgencyDetails, int>
    {
        /// <summary>
        /// Get unique agencies from properties for a specific data source
        /// </summary>
        /// <param name="dataSourceId">The data source ID to filter agencies</param>
        /// <param name="batchSize">Number of agencies to return per batch</param>
        /// <param name="skip">Number of agencies to skip for pagination</param>
        /// <returns>List of unique agencies with their details</returns>
        public Task<List<(string AgencyId, string AgencyName, int DataSourceId)>> GetUniqueAgencies(int dataSourceId, int batchSize = 50, int skip = 0);
        public Task<List<(string Id, int DataSourceId)>> GetUniqueIdsFromUserProperty(string columnName, int dataSourceId, int batchSize = 50, int skip = 0, int? propertyRelationshipId = null);

        /// <summary>
        /// Get total count of unique agencies for a specific data source
        /// </summary>
        /// <param name="dataSourceId">The data source ID to filter agencies</param>
        /// <returns>Total count of unique agencies</returns>
        public Task<int> GetUniqueAgenciesCount(int dataSourceId);
        public Task<int> GetUniqueManagementOrTenancyCount(int dataSourceId, string columnName, int? propertyRelationshipId = null);

        /// <summary>
        /// Bulk upsert agency details using MERGE statement based on AgencyId - insert if not exists, update if exists
        /// </summary>
        /// <param name="agencyDetailsList">List of agency details to upsert</param>
        /// <param name="dataSourceId">The data source ID</param>
        /// <returns>Number of rows affected</returns>
        public Task<List<SQLQueryMergeResult>> BulkUpsertAgencyDetails(List<AgencyDetails> agencyDetailsList, int dataSourceId);

        /// <summary>
        /// Bulk upsert agency partners using MERGE statement based on PartnerAgencyId - insert if not exists, update if exists
        /// </summary>
        /// <param name="agencyPartnersList">List of agency partners to upsert</param>
        /// <param name="dataSourceId">The data source ID</param>
        /// <returns>Number of rows affected</returns>
        public Task<List<SQLQueryMergeResult>> BulkUpsertAgencyPartners(List<AgencyPartners> agencyPartnersList, int dataSourceId);
        public Task<List<SQLQueryMergeResult>> BulkUpsertDocumentDetails(List<DocumentDetail> list, int dataSourceId, string columnName);

        /// <summary>
        /// Bulk upsert property manager information using MERGE statement based on SRCAgencyId and SRCManagementId - insert if not exists, update if exists
        /// </summary>
        /// <param name="propertyManagerInfoList">List of property manager information to upsert</param>
        /// <param name="dataSourceId">The data source ID</param>
        /// <returns>Number of rows affected</returns>
        public Task<List<SQLQueryMergeResult>> BulkUpsertPropertyManagerInformation(List<PropertyManagerInformation> propertyManagerInfoList, int dataSourceId);
        public Task<List<SQLQueryMergeResult>> BulkUpsertComplianceDetails(List<ComplianceDetail> list, int dataSourceId);

        public Task<List<SQLQueryMergeResult>> BulkUpsertTenanciesTenants(List<TenanciesTenant> tenanciesTenantList, int dataSourceId);
        public Task<List<SQLQueryMergeResult>> BulkUpsertInspectionsDetails(List<InspectionDetail> list, int dataSourceId);
        public Task<List<SQLQueryMergeResult>> BulkUpsertMaintenanceDetails(List<MaintenanceDetail> list, int dataSourceId);
        public Task<List<SQLQueryMergeResult>> BulkUpsertFinancialsDetails(List<FinancialDetail> list, int dataSourceId);
        public Task<List<SQLQueryMergeResult>> BulkUpsertTenanciesOwners(List<TenanciesOwner> tenanciesOwnerList, int dataSourceId);
    }
} 