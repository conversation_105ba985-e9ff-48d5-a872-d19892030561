﻿﻿using Microsoft.Extensions.Caching.Distributed;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Moq;
using MRI.OTA.Infrastructure.Caching;
using MRI.OTA.Infrastructure.Caching.Configuration;
using MRI.OTA.Infrastructure.Caching.Interfaces;

namespace MRI.OTA.UnitTestCases.Infrastructure.Caching
{
    public class UnitTestCacheModule
    {
        [Fact]
        public void ConfigureRedisCache_RegistersServices_WhenConnectionStringProvided()
        {
            // Arrange
            var services = new ServiceCollection();

            // Add mock IDistributedCache to avoid actual Redis connection
            services.AddSingleton<IDistributedCache>(Mock.Of<IDistributedCache>());

            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:ConnectionString", "test-connection-string"},
                {"RedisConfig:DefaultExpirationMinutes", "30"}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            // Verify that ICacheService is registered
            var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ICacheService));
            Assert.NotNull(descriptor);
            Assert.Equal(ServiceLifetime.Singleton, descriptor.Lifetime);
            Assert.Equal(typeof(RedisCacheService), descriptor.ImplementationType);

            // Verify that Redis options are registered
            var optionsDescriptor = services.FirstOrDefault(d =>
                d.ServiceType == typeof(Microsoft.Extensions.Options.IConfigureOptions<RedisConfiguration>));
            Assert.NotNull(optionsDescriptor);
        }

        [Fact]
        public void ConfigureRedisCache_DoesNotRegisterRedisCacheService_WhenConnectionStringEmpty()
        {
            // Arrange
            var services = new ServiceCollection();

            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:ConnectionString", ""},
                {"RedisConfig:InstanceName", "test-instance"},
                {"RedisConfig:DefaultExpirationMinutes", "30"}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            // Verify that ICacheService is not registered
            var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ICacheService));
            Assert.Null(descriptor); // Service should not be registered

            // Verify that Redis options are still registered
            var optionsDescriptor = services.FirstOrDefault(d =>
                d.ServiceType == typeof(Microsoft.Extensions.Options.IConfigureOptions<RedisConfiguration>));
            Assert.NotNull(optionsDescriptor);
        }

        [Fact]
        public void ConfigureRedisCache_DoesNotRegisterRedisCacheService_WhenRedisConfigSectionMissing()
        {
            // Arrange
            var services = new ServiceCollection();

            // Create configuration with no RedisConfig section
            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(new Dictionary<string, string>())
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            // Verify that ICacheService is not registered
            var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ICacheService));
            Assert.Null(descriptor); // Service should not be registered

            // Verify that Redis options are still registered
            var optionsDescriptor = services.FirstOrDefault(d =>
                d.ServiceType == typeof(Microsoft.Extensions.Options.IConfigureOptions<RedisConfiguration>));
            Assert.NotNull(optionsDescriptor);
        }

        [Fact]
        public void ConfigureRedisCache_DoesNotRegisterRedisCacheService_WhenConnectionStringNull()
        {
            // Arrange
            var services = new ServiceCollection();

            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:DefaultExpirationMinutes", "30"}
                // ConnectionString is missing (null)
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ICacheService));
            Assert.Null(descriptor);
        }

        [Fact]
        public void ConfigureRedisCache_ReturnsServiceCollection_ForChaining()
        {
            // Arrange
            var services = new ServiceCollection();
            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:ConnectionString", "test-connection-string"},
                {"RedisConfig:DefaultExpirationMinutes", "30"}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            var result = services.ConfigureRedisCache(configuration);

            // Assert
            Assert.Same(services, result);
        }

        [Fact]
        public void ConfigureRedisCache_ConfiguresRedisOptions_WithCorrectValues()
        {
            // Arrange
            var services = new ServiceCollection();
            var connectionString = "localhost:6379";
            var expirationMinutes = "45";

            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:ConnectionString", connectionString},
                {"RedisConfig:DefaultExpirationMinutes", expirationMinutes}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            var serviceProvider = services.BuildServiceProvider();
            var options = serviceProvider.GetService<Microsoft.Extensions.Options.IOptions<RedisConfiguration>>();

            Assert.NotNull(options);
            Assert.Equal(connectionString, options.Value.ConnectionString);
            Assert.Equal(45, options.Value.DefaultExpirationMinutes);
        }

        [Fact]
        public void ConfigureRedisCache_RegistersStackExchangeRedisCache_WhenConnectionStringProvided()
        {
            // Arrange
            var services = new ServiceCollection();
            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:ConnectionString", "localhost:6379"},
                {"RedisConfig:DefaultExpirationMinutes", "30"}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            // Check that StackExchange Redis cache is registered
            var distributedCacheDescriptor = services.FirstOrDefault(d =>
                d.ServiceType == typeof(IDistributedCache) &&
                d.ImplementationType?.Name.Contains("Redis") == true);

            Assert.NotNull(distributedCacheDescriptor);
        }

        [Fact]
        public void ConfigureRedisCache_HandlesWhitespaceConnectionString()
        {
            // Arrange
            var services = new ServiceCollection();
            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:ConnectionString", "   "},
                {"RedisConfig:DefaultExpirationMinutes", "30"}
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ICacheService));
            Assert.NotNull(descriptor); // Current implementation registers service even with whitespace-only connection string
        }

        [Fact]
        public void ConfigureRedisCache_WorksWithMinimalConfiguration()
        {
            // Arrange
            var services = new ServiceCollection();
            var configValues = new Dictionary<string, string>
            {
                {"RedisConfig:ConnectionString", "localhost"}
                // Only connection string provided, no other settings
            };

            var configuration = new ConfigurationBuilder()
                .AddInMemoryCollection(configValues)
                .Build();

            // Act
            services.ConfigureRedisCache(configuration);

            // Assert
            var descriptor = services.FirstOrDefault(d => d.ServiceType == typeof(ICacheService));
            Assert.NotNull(descriptor);
            Assert.Equal(typeof(RedisCacheService), descriptor.ImplementationType);
        }
    }
}
