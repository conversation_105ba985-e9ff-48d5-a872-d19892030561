
using MRI.OTA.Common.Models;

namespace MRI.OTA.Core.Entities
{
    public class DataSource
    {
        [ExcludeColumn]
        public int DataSourceId { get; set; }
        public string? Name { get; set; }
        public string? Description { get; set; }
        public string? ManifestJson { get; set; }
        public string? AccessKey { get; set; }
        public string? AccessSecret { get; set; }
        public string? ApiKey { get; set; }

        public string? Scope { get; set; }
    }
}
