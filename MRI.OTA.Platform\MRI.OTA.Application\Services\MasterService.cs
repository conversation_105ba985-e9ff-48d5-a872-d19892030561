﻿using AutoMapper;
using Microsoft.Extensions.Logging;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.Master;
using MRI.OTA.Common.Constants;
using MRI.OTA.Core.Entities;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Interfaces;
using static Microsoft.ApplicationInsights.MetricDimensionNames.TelemetryContext;

namespace MRI.OTA.Application.Services
{
    public class MasterService : IMasterService
    {
        private IMasterRepository _masterRepository { get; set; }

        private IMapper _mapper { get; set; }

        private ILogger _logger { get; set; }


        /// <summary>
        /// User service constructor
        /// </summary>
        /// <param name="repository"></param>
        /// <param name="mapper"></param>
        public MasterService(ILogger<MasterService> logger, IMasterRepository repository, IMapper mapper)
        {
            _masterRepository = repository;
            _mapper = mapper;
            _logger = logger;
        }

        public async Task<List<T>> GetDataByTableName<T>(string tableName)
        {
            try
            {
                return await _masterRepository.GetDataByTableName<T>(tableName);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting master data for table: {TableName}", tableName);
                return new List<T>();
            }
        }

        /// <summary>
        /// Get modules list. If propertyRelationshipId is null, returns all modules with relationships.
        /// </summary>
        /// <param name="propertyRelationshipId">Property relationship ID. If null, returns all modules.</param>
        /// <returns></returns>
        public async Task<List<ViewModuleRelationship>> GetModulesList(int? propertyRelationshipId)
        {
            try
            {
                // Fetch the list of modules from the repository  
                var modules = await _masterRepository.GetModulesList(propertyRelationshipId);
                return modules;
            }
            catch (Exception ex)
            {
                _logger.LogError($"Error getting module data : {ex.Message}");
            }

            // Return an empty list in case of an error  
            return new List<ViewModuleRelationship>();
        }

        /// <summary>
        /// Function used to get list of countries from the database
        /// </summary>
        /// <returns></returns>
        public async Task<List<CountryModel>> GetAllCountriesAsync()
        {
            // Fetch data from the repository
            var countries = await _masterRepository.GetDataByTableName<CountryModel>(Constants.CountriesTableName);
            return countries;
        }

        /// <summary>
        /// Function used to get list of states from the database
        /// </summary>
        /// <returns></returns>
        public async Task<List<StateModel>> GetAllStatesAsync()
        {
            var states = await _masterRepository.GetDataByTableName<StateModel>(Constants.StateTableName);
            return states; 
        }

        /// <summary>
        /// Function used to get list of administrative areas from the database
        /// </summary>
        /// <returns></returns>
        public async Task<List<AdminAreaModel>> GetAllAdminAreas()
        {
           var adminAreas = await _masterRepository.GetDataByTableName<AdminAreaModel>(Constants.AdministrativeAreasTableName);
           return adminAreas;
        }

        /// <summary>
        /// Function used to get list of occupancy types from the database
        /// </summary>
        /// <returns></returns>
        public async Task<List<OccupancyTypesModel>> GetAllOccupancyTypes()
        {
            var occupancyTypes = await _masterRepository.GetDataByTableName<OccupancyTypesModel>(Constants.OccupancyTypesTableName);
            return occupancyTypes;
        }

        /// <summary>  
        /// Function used to get list of property relationships from the database  
        /// </summary>  
        /// <returns></returns>  
        public async Task<List<PropertyRelationshipsModel>> GetPropertyRelationships()
        {
            var result = await _masterRepository.GetPropertyRelationship();
            var propRelations = _mapper.Map<List<PropertyRelationshipsModel>>(result);
            return propRelations;
        }

        /// <summary>
        /// Function used to get list of occupancy status types from the database
        /// </summary>
        /// <returns></returns>
        public async Task<List<OccupancyStatusTypeModel>> GetOccupancyStatusType()
        {
            var occupancyStatusTypes = await _masterRepository.GetDataByTableName<OccupancyStatusTypeModel>(Constants.OccupancyStatusTypeTableName);
            return occupancyStatusTypes;
        }
    }
}
