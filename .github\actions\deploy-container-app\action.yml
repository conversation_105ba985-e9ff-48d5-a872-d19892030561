name: Deploy to Azure Container App

description: Deploys Docker image to Azure Container App

inputs:
  image-tag:
    description: 'Docker image tag to deploy'
    required: true
  acr-endpoint:
    description: 'Azure Container Registry endpoint'
    required: true
  image-name:
    description: 'Docker image name'
    required: true

runs:
  using: "composite"
  steps:
    - name: Deploy to Azure Container App
      shell: bash
      run: |
        IMAGE="${{ inputs.acr-endpoint }}/${{ inputs.image-name }}:${{ inputs.image-tag }}"
        echo "🚀 Deploying image: $IMAGE to $CONTAINER_APP in $RG_NAME"
        az containerapp update \
          --name "$CONTAINER_APP" \
          --resource-group "$RG_NAME" \
          --image "$IMAGE" 