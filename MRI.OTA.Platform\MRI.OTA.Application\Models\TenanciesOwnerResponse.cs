﻿

namespace MRI.OTA.Application.Models
{
    public class TenanciesOwnerResponse
    {
        public string ManagementId { get; set; }
        public string PropertyId { get; set; }
        public string TenancyId { get; set; }
        public string? TenancyName { get; set; }
        public string? TenancyStatus { get; set; }
        public DateTime? TenancyStartDate { get; set; }
        public DateTime? TenancyEndDate { get; set; }
        public decimal? Rent { get; set; }
        public string? IsActive { get; set; }
        public int? RentPeriod { get; set; }

        public string? Currency { get; set; }
        public ManagementResponse? ownershipResponse { get; set; }
    }
}
