﻿using AutoMapper;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Application.Models;
using MRI.OTA.Application.Services;
using MRI.OTA.Common.Constants;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;

namespace MRI.OTA.UnitTestCases.Image.Service
{
    public class UnitTestImageStorageService
    {
        private readonly Mock<IImageRepository> _imageRepositoryMock;
        private readonly Mock<IMapper> _mapperMock;
        private readonly Mock<ILogger<MasterService>> _loggerMock;
        private readonly Mock<IConfiguration> _configurationMock;
        private readonly ImageStorageService _service;

        public UnitTestImageStorageService()
        {
            _imageRepositoryMock = new Mock<IImageRepository>();
            _mapperMock = new Mock<IMapper>();
            _loggerMock = new Mock<ILogger<MasterService>>();
            _configurationMock = new Mock<IConfiguration>();
            _configurationMock.Setup(c => c["AzureStorage:ConnectionString"]).Returns("DefaultEndpointsProtocol=https;AccountName=stgnw02shrdpltdev;AccountKey=****************************************************************************************;EndpointSuffix=core.windows.net");
            _configurationMock.Setup(c => c["AzureStorage:ContainerName"]).Returns("azure-ota-images");

            _service = new ImageStorageService(
                _loggerMock.Object,
                _imageRepositoryMock.Object,
                _mapperMock.Object,
                _configurationMock.Object
            );
        }

        [Fact(Skip = "we are using actual connection so every time when we run test cases dummy image is getting uploaded on azure")]
        public async Task UploadImages_ShouldUploadAndReturnUrls_WhenSuccess()
        {
            // Arrange
            var propertyId = 1;
            var files = new List<IFormFile> { CreateMockFormFile("test1.jpg"), CreateMockFormFile("test2.jpg") };
            var base64Files = files.Select(file => new Base64ImageFile
            {
                FileName = file.FileName,
                Base64Content = ConvertToBase64(file)
            }).ToList();
            var model = new ImageModel { PropertyId = propertyId, Files = base64Files, DefaultImageIndex = 1 };
            var uploadedImages = new List<(int, string)>
           {
               (propertyId, "https://blob/1.jpg"),
               (propertyId, "https://blob/2.jpg")
           };
            var propertyImages = new List<PropertyImages>
           {
               new PropertyImages { PropertyId = propertyId, ImageBlobUrl = "https://blob/1.jpg" },
               new PropertyImages { PropertyId = propertyId, ImageBlobUrl = "https://blob/2.jpg" }
           };

            _mapperMock.Setup(m => m.Map<List<PropertyImages>>(It.IsAny<List<(int, string)>>()))
                .Returns(propertyImages);
            _imageRepositoryMock.Setup(r => r.AddPropertyImages(It.IsAny<List<PropertyImages>>()))
                .ReturnsAsync(propertyImages.Count);
            _imageRepositoryMock.Setup(r => r.UpdateDefaultImage(propertyId, It.IsAny<string>()))
                .ReturnsAsync(true);

            // Act
            var result = await _service.UploadImages(model);

            // Assert
            Assert.Equal(files.Count, result.Count);
        }

        private string ConvertToBase64(IFormFile file)
        {
            using var memoryStream = new MemoryStream();
            file.CopyTo(memoryStream);
            return Convert.ToBase64String(memoryStream.ToArray());
        }

        [Fact]
        public async Task UploadImages_ShouldReturnEmptyList_WhenDbInsertFails()
        {
            // Arrange
            var propertyId = 1;
            var files = new List<Base64ImageFile>
           {
               new Base64ImageFile
               {
                   FileName = "test1.jpg",
                   Base64Content = ConvertToBase64(CreateMockFormFile("test1.jpg"))
               }
           };
            var model = new ImageModel { PropertyId = propertyId, Files = files };
            var propertyImages = new List<PropertyImages>
           {
               new PropertyImages { PropertyId = propertyId, ImageBlobUrl = "https://blob/1.jpg" }
           };

            _mapperMock.Setup(m => m.Map<List<PropertyImages>>(It.IsAny<List<(int, string)>>()))
                .Returns(propertyImages);
            _imageRepositoryMock.Setup(r => r.AddPropertyImages(It.IsAny<List<PropertyImages>>()))
                .ReturnsAsync(0);

            // Act
            var result = await _service.UploadImages(model);

            // Assert
            Assert.Empty(result);
        }

        [Fact]
        public async Task DeletePropertyImage_ShouldReturnSuccess_WhenImageExists()
        {
            // Arrange
            var propertyImagesId = 1;
            var image = new PropertyImages { PropertyImagesId = propertyImagesId, ImageBlobUrl = "https://blob/1.jpg" };
            _imageRepositoryMock.Setup(r => r.GetPropertyImage(propertyImagesId)).ReturnsAsync(image);
            _imageRepositoryMock.Setup(r => r.DeleteImage(propertyImagesId)).ReturnsAsync(1);

            // Act
            var result = await _service.DeletePropertyImage(propertyImagesId);

            // Assert
            Assert.Equal(Constants.Success, result);
        }

        [Fact]
        public async Task DeletePropertyImage_ShouldReturnMinusOne_WhenImageNotFound()
        {
            // Arrange
            var propertyImagesId = 1;
            _imageRepositoryMock.Setup(r => r.GetPropertyImage(propertyImagesId)).ReturnsAsync((PropertyImages?)null);

            // Act
            var result = await _service.DeletePropertyImage(propertyImagesId);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task DeletePropertyImage_ShouldReturnMinusOne_OnException()
        {
            // Arrange
            var propertyImagesId = 1;
            _imageRepositoryMock.Setup(r => r.GetPropertyImage(propertyImagesId)).ThrowsAsync(new Exception("db error"));

            // Act
            var result = await _service.DeletePropertyImage(propertyImagesId);

            // Assert
            Assert.Equal(-1, result);
        }

        private IFormFile CreateMockFormFile(string fileName)
        {
            var content = "Fake file content";
            var stream = new MemoryStream(System.Text.Encoding.UTF8.GetBytes(content));
            return new FormFile(stream, 0, stream.Length, "file", fileName);
        }

        [Fact]
        public async Task UpdateDefaultImage_ShouldReturnSuccess_WhenRepositoryReturnsTrue()
        {
            // Arrange
            var model = new UpdateDefaultImageModel { PropertyId = 1, PropertyImagesId = 2, UserId = 3 };
            _imageRepositoryMock
                .Setup(r => r.UpdateDefaultImage(model.PropertyId, model.PropertyImagesId, model.UserId))
                .ReturnsAsync(true);

            // Act
            var result = await _service.UpdateDefaultImage(model);

            // Assert
            Assert.Equal(Constants.Success, result);
        }

        [Fact]
        public async Task UpdateDefaultImage_ShouldReturnError_WhenRepositoryReturnsFalse()
        {
            // Arrange
            var model = new UpdateDefaultImageModel { PropertyId = 1, PropertyImagesId = 2, UserId = 3 };
            _imageRepositoryMock
                .Setup(r => r.UpdateDefaultImage(model.PropertyId, model.PropertyImagesId, model.UserId))
                .ReturnsAsync(false);

            // Act
            var result = await _service.UpdateDefaultImage(model);

            // Assert
            Assert.Equal(Constants.Error, result);
        }

        [Fact]
        public async Task UpdateDefaultImage_ShouldReturnMinusOne_WhenExceptionThrown()
        {
            // Arrange
            var model = new UpdateDefaultImageModel { PropertyId = 1, PropertyImagesId = 2, UserId = 3 };
            _imageRepositoryMock
                .Setup(r => r.UpdateDefaultImage(model.PropertyId, model.PropertyImagesId, model.UserId))
                .ThrowsAsync(new Exception("db error"));

            // Act
            var result = await _service.UpdateDefaultImage(model);

            // Assert
            Assert.Equal(-1, result);
        }

        [Fact]
        public async Task DeletePropertyImagesByUserId_ShouldReturnSuccess_WhenNoImagesFound()
        {
            // Arrange
            int userId = 1;
            _imageRepositoryMock.Setup(r => r.GetPropertyImagesByUserId(userId))
                .ReturnsAsync((List<PropertyImages>?)null);

            // Act
            var result = await _service.DeletePropertyImagesByUserId(userId);

            // Assert
            Assert.Equal(Constants.Success, result);
        }

        [Fact]
        public async Task DeletePropertyImagesByUserId_ShouldReturnSuccess_WhenImagesDeleted()
        {
            // Arrange
            int userId = 1;
            var images = new List<PropertyImages>
            {
                new PropertyImages { PropertyImagesId = 1, PropertyId = userId, ImageBlobUrl = "https://blob/1.jpg" },
                new PropertyImages { PropertyImagesId = 2, PropertyId = userId, ImageBlobUrl = "https://blob/2.jpg" }
            };
            _imageRepositoryMock.Setup(r => r.GetPropertyImagesByUserId(userId))
                .ReturnsAsync(images);

            // Act
            var result = await _service.DeletePropertyImagesByUserId(userId);

            // Assert
            Assert.Equal(Constants.Success, result);
        }

        [Fact]
        public async Task DeletePropertyImagesByUserId_ShouldReturnError_WhenExceptionThrown()
        {
            // Arrange
            int userId = 1;
            _imageRepositoryMock.Setup(r => r.GetPropertyImagesByUserId(userId))
                .ThrowsAsync(new Exception("db error"));

            // Act
            var result = await _service.DeletePropertyImagesByUserId(userId);

            // Assert
            Assert.Equal(Constants.Error, result);
        }
    }
}