﻿using System.Data;
using System.Reflection;

namespace MRI.OTA.Common.Helper
{
    public static class DataTableConverter
    {
        public static DataTable ToDataTable<T>(this List<T> items)
        {
            DataTable table = new DataTable(typeof(T).Name);
            PropertyInfo[] props = typeof(T).GetProperties(BindingFlags.Public | BindingFlags.Instance);

            foreach (PropertyInfo prop in props)
            {
                Type propType = Nullable.GetUnderlyingType(prop.PropertyType) ?? prop.PropertyType;
                table.Columns.Add(prop.Name, propType);
            }

            foreach (T item in items)
            {
                var values = new object[props.Length];
                for (int i = 0; i < props.Length; i++)
                {
                    values[i] = props[i].GetValue(item, null) ?? DBNull.Value;
                }
                table.Rows.Add(values);
            }

            return table;
        }
    }
}
