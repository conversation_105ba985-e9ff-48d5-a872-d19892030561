﻿using MRI.OTA.Application.Models;
using MRI.OTA.Application.Validators;

namespace MRI.OTA.UnitTestCases.Validators
{
    public class TermsConditionModelValidatorTest
    {
        private readonly TermsConditionModelValidator _validator = new TermsConditionModelValidator();

        [Fact]
        public void Should_Have_Error_When_UserId_Is_Zero()
        {
            var model = new TermsConditionModel { UserId = 0, TermsAndConditions = true };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "UserId" &&
                e.ErrorMessage == "User ID must be greater than 0"
            );
        }

        [Fact]
        public void Should_Have_Error_When_UserId_Is_Negative()
        {
            var model = new TermsConditionModel { UserId = -5, TermsAndConditions = false };
            var result = _validator.Validate(model);
            Assert.Contains(result.Errors, e =>
                e.PropertyName == "UserId" &&
                e.ErrorMessage == "User ID must be greater than 0"
            );
        }

        [Fact]
        public void Should_Not_Have_Error_When_UserId_Is_Positive()
        {
            var model = new TermsConditionModel { UserId = 10, TermsAndConditions = true };
            var result = _validator.Validate(model);
            Assert.DoesNotContain(result.Errors, e => e.PropertyName == "UserId");
        }
    }
}
