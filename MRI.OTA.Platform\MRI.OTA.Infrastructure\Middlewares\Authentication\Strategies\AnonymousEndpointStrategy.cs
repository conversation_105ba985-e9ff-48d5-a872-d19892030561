using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication.Strategies
{
    /// <summary>
    /// Strategy for handling anonymous endpoints
    /// </summary>
    public class AnonymousEndpointStrategy : IAuthenticationStrategy
    {
        private readonly RequestDelegate _next;
        private readonly ILogger _logger;

        /// <summary>
        /// Constructor for AnonymousEndpointStrategy
        /// </summary>
        /// <param name="next">The next middleware in the pipeline</param>
        /// <param name="logger">The logger</param>
        public AnonymousEndpointStrategy(RequestDelegate next, ILogger logger)
        {
            _next = next ?? throw new ArgumentNullException(nameof(next));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        /// <summary>
        /// Determines if this strategy can handle the current request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>True if this strategy can handle the request, false otherwise</returns>
        public bool CanHandle(HttpContext context)
        {
            var endpoint = context.GetEndpoint();
            return endpoint?.Metadata?.GetMetadata<Microsoft.AspNetCore.Authorization.AllowAnonymousAttribute>() != null;
        }

        /// <summary>
        /// Authenticates the request
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <returns>A tuple indicating if the request was handled and authenticated</returns>
        public async Task<(bool Handled, bool Authenticated)> AuthenticateAsync(HttpContext context)
        {
            _logger.LogDebug("Anonymous access allowed for endpoint: {Endpoint}", context.Request.Path);
            await _next(context);
            return (true, true);
        }
    }
}
