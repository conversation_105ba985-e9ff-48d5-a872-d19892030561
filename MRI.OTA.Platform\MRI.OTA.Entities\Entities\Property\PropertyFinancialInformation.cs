﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class PropertyFinancialInformation
    {
        [ExcludeColumn]
        public int PropertyFinancialInformationId { get; set; }
        [ExcludeColumn]
        public int PropertyId { get; set; }
        public string? TenancyName { get; set; }
        public DateTime? LeaseStart { get; set; }
        public DateTime? LeaseEnd { get; set; }
        public DateTime? VacateDate { get; set; }
        public decimal? Rent { get; set; }
        public decimal? IncreaseRent { get; set; }
        public DateTime? IncreaseDate { get; set; }
        public DateTime? OptionsDate { get; set; }
        public string? OptionsDetail { get; set; }
        public decimal? Arrears { get; set; }
        public DateTime? PayToDate { get; set; }
        public decimal? AmountToVacate { get; set; }
        public decimal? OutstandingInvoices { get; set; }
        public decimal? InvoiceFeesArrears { get; set; }
        public decimal? RentCharge { get; set; }
        public decimal? WeeklyRent { get; set; }
        public DateTime? LastPaid { get; set; }
        public decimal? HeldFunds { get; set; }
    }
}
