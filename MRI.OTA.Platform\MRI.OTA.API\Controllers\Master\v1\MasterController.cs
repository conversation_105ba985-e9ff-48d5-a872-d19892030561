﻿using System.Net.Mime;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Models.Master;
using MRI.OTA.Common.Constants;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;
using Swashbuckle.AspNetCore.Annotations;

namespace MRI.OTA.API.Controllers.Master.v1
{
    /// <summary>
    /// To perform operations related to masters
    /// </summary>
    [Authorize]
    [ApiVersion("1.0")]
    [Route("api/v{version:apiVersion}/masters")]
    [ApiController]
    [Consumes(MediaTypeNames.Application.Json)]
    [Produces("application/json")]
    public class MasterController : ControllerBase
    {
        private readonly IMasterService _masterService;

        /// <summary>
        /// Constructor for master controller
        /// </summary>
        /// <param name="masterService"></param>
        public MasterController(IMasterService masterService)
        {
            _masterService = masterService;
        }

        /// <summary>
        /// API to get all countries data
        /// </summary>
        /// <returns></returns>
        [HttpGet("countries")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<CountryModel>), description: "Countries data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<CountryModel>>> GetAllCountries()
        {
            var countries = await _masterService.GetAllCountriesAsync();
            if (countries == null || !countries.Any())
            {
                return NotFound(new ApiResponse<object>(false, "No countries found", data: null!, StatusCodes.Status404NotFound, new List<string>()));
            }
            return Ok(new ApiResponse<List<CountryModel>>(true, StatusCodes.Status200OK, "Countries retrieved successfully", countries));
        }

        /// <summary>
        /// API to get all states data
        /// </summary>
        /// <returns></returns>
        [HttpGet("states")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<StateModel>), description: "States data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<StateModel>>> GetAllStates()
        {
            var states = await _masterService.GetAllStatesAsync();
            if (states == null || !states.Any())
            {
                return NotFound(new ApiResponse<object>(false, "Item not found", data: null!, StatusCodes.Status404NotFound, new List<string> { "The item with the specified ID does not exist." }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", states));
        }

        /// <summary>
        /// API to get all administrative areas data
        /// </summary>
        /// <returns></returns>
        [HttpGet("admin-areas")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<AdminAreaModel>), description: "administrative areas data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<AdminAreaModel>>> GetAllAdminAreas()
        {
            var states = await _masterService.GetDataByTableName<AdminAreaModel>(Constants.AdministrativeAreasTableName);
            if (states == null)
            {
                return NotFound(new ApiResponse<object>(false, "Item not found", data: null!, StatusCodes.Status404NotFound, new List<string> { "The item with the specified ID does not exist." }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", states));
        }

        /// <summary>
        /// API to get all occupancy types data
        /// </summary>
        /// <returns></returns>
        [HttpGet("occupancy-types")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<OccupancyTypesModel>), description: "Occupancy types data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<OccupancyTypesModel>>> GetAllOccupancyTypes()
        {
            var occupancyTypes = await _masterService.GetAllOccupancyTypes();
            if (occupancyTypes == null || !occupancyTypes.Any())
            {
                return NotFound(new ApiResponse<object>(false, "Item not found", data: null!, StatusCodes.Status404NotFound, new List<string> { "The item with the specified ID does not exist." }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", occupancyTypes));
        }

        /// <summary>
        /// API to get all property relationships data
        /// </summary>
        /// <returns></returns>
        [HttpGet("property-relationships")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<PropertyRelationshipsModel>), description: "Property relationships data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<PropertyRelationshipsModel>>> GetPropertyRelationships()
        {
            var propertyRelationships = await _masterService.GetPropertyRelationships();
            if (propertyRelationships == null || !propertyRelationships.Any())
            {
                return NotFound(new ApiResponse<object>(false, "Item not found", data: null!, StatusCodes.Status404NotFound, new List<string> { "The item with the specified ID does not exist." }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", propertyRelationships));
        }

        /// <summary>
        /// API to get all occupancy status types data
        /// </summary>
        /// <returns></returns>
        [HttpGet("occupancy-status-types")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<OccupancyStatusTypeModel>), description: "Occupancy status types data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<OccupancyStatusTypeModel>>> GetOccupancyStatusType()
        {
            var occupancyStatusTypes = await _masterService.GetOccupancyStatusType();
            if (occupancyStatusTypes == null || !occupancyStatusTypes.Any())
            {
                return NotFound(new ApiResponse<object>(false, "Item not found", data: null!, StatusCodes.Status404NotFound, new List<string> { "The item with the specified ID does not exist." }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", occupancyStatusTypes));
        }

        /// <summary>
        /// Get modules based on relationship. If propertyRelationshipId is null or 0, returns all modules with relationships.
        /// </summary>
        /// <param name="propertyRelationshipId">Property relationship ID. If null or 0, returns all modules.</param>
        /// <returns></returns>
        [HttpGet("module-category")]
        [SwaggerResponse(statusCode: StatusCodes.Status200OK, type: typeof(List<ViewModuleRelationship>), description: "Modules data retrieved successfully.")]
        [SwaggerResponse(statusCode: StatusCodes.Status400BadRequest, type: typeof(ProblemDetailsModel), description: "Bad Request")]
        [SwaggerResponse(statusCode: StatusCodes.Status401Unauthorized, type: typeof(ProblemDetailsModel), description: "Unauthorized")]
        [SwaggerResponse(statusCode: StatusCodes.Status403Forbidden, type: typeof(ProblemDetailsModel), description: "Forbidden")]
        [SwaggerResponse(statusCode: StatusCodes.Status404NotFound, type: typeof(ProblemDetailsModel), description: "Not Found")]
        public async Task<ActionResult<List<ViewModuleRelationship>>> GetModulesList(int? propertyRelationshipId = null)
        {
            List<ViewModuleRelationship> modules;
            
            // If propertyRelationshipId is null or 0, get all modules with relationships
            if (propertyRelationshipId == null || propertyRelationshipId == 0)
            {
                modules = await _masterService.GetModulesList(null);
            }
            else
            {
                modules = await _masterService.GetModulesList(propertyRelationshipId.Value);
            }
            
            if (modules == null || !modules.Any())
            {
                return NotFound(new ApiResponse<object>(false, "Item not found", data: null!, StatusCodes.Status404NotFound, new List<string> { "The item with the specified ID does not exist." }));
            }
            return Ok(new ApiResponse<object>(true, StatusCodes.Status200OK, "Item retrieved successfully", modules));
        }
    }
}
