using Microsoft.AspNetCore.Http;
using MRI.OTA.Common.Models;
using MRI.OTA.Infrastructure.Middlewares.Authentication.Interfaces;

namespace MRI.OTA.Infrastructure.Middlewares.Authentication
{
    /// <summary>
    /// Implementation of IResponseGenerator for generating HTTP responses
    /// </summary>
    public class ResponseGenerator : IResponseGenerator
    {
        /// <summary>
        /// Writes an unauthorized response to the HTTP context
        /// </summary>
        /// <param name="context">The HTTP context</param>
        /// <param name="message">The error message</param>
        /// <param name="statusCode">The HTTP status code</param>
        /// <returns>A task representing the asynchronous operation</returns>
        public async Task WriteUnauthorizedResponse(HttpContext context, string message, int statusCode)
        {
            context.Response.StatusCode = statusCode;
            context.Response.ContentType = "application/json";

            var response = new ApiResponse<object>(
                success: false,
                message: message,
                data: null!,
                statusCode: statusCode,
                errors: new List<string> { message }
            );

            await context.Response.WriteAsJsonAsync(response);
        }
    }
}
