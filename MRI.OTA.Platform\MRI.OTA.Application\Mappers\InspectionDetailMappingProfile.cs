﻿using AutoMapper;
using MRI.OTA.Application.Models;
using MRI.OTA.Common.Constants;
using MRI.OTA.DBCore.Entities.Property;

namespace MRI.OTA.Application.Mappers
{
    public class InspectionDetailMappingProfile : Profile
    {
        public InspectionDetailMappingProfile() : base("InspectionDetailMappingProfile")
        {
            // Map from MaintenanceJobs to MaintenanceDetail
            CreateMap<InspectionsListResponse, InspectionDetail>()
                .ForMember(dest => dest.InspectionsDetailId, opt => opt.Ignore()) // Auto-generated
                .ForMember(dest => dest.SRCManagementId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.SRCTenancyId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1)) // Default value, will be updated
                .ForMember(dest => dest.SRCPropertyId, opt => opt.Ignore()) // Will be set from parent
                .ForMember(dest => dest.SRCInspectionId, opt => opt.MapFrom(src => src.InspectionId))
                .ForMember(dest => dest.InspectionStatus, opt => opt.MapFrom(src => src.Status))
                .ForMember(dest => dest.InspectionDate, opt => opt.MapFrom(src => src.InspectionDate))
                .ForMember(dest => dest.InspectionStartTime, opt => opt.MapFrom(src => src.InspectionStartTime))
                .ForMember(dest => dest.InspectionEndTime, opt => opt.MapFrom(src => src.InspectionEndTime))
                .ForMember(dest => dest.Summary, opt => opt.MapFrom(src => src.Summary));

            // Map from InspectionDetailResponse to List<InspectionDetail>
            // This handles the flattening of the response structure
            CreateMap<InspectionDetailResponse, List<InspectionDetail>>()
                .ConvertUsing((src, dest, context) =>
                {
                    if (src?.Inspections == null || src.Inspections.Count == 0)
                        return [];

                    var details = new List<InspectionDetail>();

                    foreach (var data in src.Inspections)
                    {
                        var detail = context.Mapper.Map<InspectionDetail>(data);

                        // Set the parent-level properties
                        detail.SRCManagementId = src.ManagementId;
                        detail.SRCTenancyId = src.TenancyId;
                        detail.SRCPropertyId = src.PropertyId;

                        details.Add(detail);
                    }

                    return details;
                });

            // Map from List<InspectionDetailResponse> to List<InspectionDetail>
            // This handles the flattening of multiple responses, each containing multiple jobs
            CreateMap<List<InspectionDetailResponse>, List<InspectionDetail>>()
                .ConvertUsing((src, dest, context) =>
                {
                    if (src == null || src.Count == 0)
                        return [];

                    var allDetails = new List<InspectionDetail>();

                    foreach (var response in src)
                    {
                        // Use the existing single response mapping to flatten each response
                        var responseDetails = context.Mapper.Map<List<InspectionDetail>>(response);
                        allDetails.AddRange(responseDetails);
                    }

                    return allDetails;
                });

            // Legacy mapping for backward compatibility
            CreateMap<InspectionDetailResponse, InspectionDetail>()
                .ForMember(dest => dest.SRCManagementId, opt => opt.MapFrom(src => src.ManagementId))
                .ForMember(dest => dest.SRCTenancyId, opt => opt.MapFrom(src => src.TenancyId))
                .ForMember(dest => dest.SRCPropertyId, opt => opt.MapFrom(src => src.PropertyId))
                .ForMember(dest => dest.PropertyId, opt => opt.MapFrom(src => -1))
                .ForMember(dest => dest.SRCInspectionId, opt => opt.MapFrom(src =>
                    src.Inspections != null && src.Inspections.Count > 0
                        ? src.Inspections.First().InspectionId
                        : null))
                .ForMember(dest => dest.InspectionStatus, opt => opt.MapFrom(src =>
                    src.Inspections != null && src.Inspections.Count > 0
                        ? src.Inspections.First().Status
                        : null))
                .ForMember(dest => dest.InspectionDate, opt => opt.MapFrom(src =>
                    src.Inspections != null && src.Inspections.Count > 0
                        ? src.Inspections.First().InspectionDate
                        : default))
                .ForMember(dest => dest.InspectionStartTime, opt => opt.MapFrom(src =>
                    src.Inspections != null && src.Inspections.Count > 0
                        ? src.Inspections.First().InspectionStartTime
                        : default))
                .ForMember(dest => dest.InspectionEndTime, opt => opt.MapFrom(src =>
                    src.Inspections != null && src.Inspections.Count > 0
                        ? src.Inspections.First().InspectionEndTime
                        : default))
                .ForMember(dest => dest.Summary, opt => opt.MapFrom(src =>
                    src.Inspections != null && src.Inspections.Count > 0
                        ? src.Inspections.First().Summary
                        : null))
                .ReverseMap();
        }
    }
}
