﻿using AutoMapper;
using MRI.OTA.Application.Mappers;
using MRI.OTA.Application.Models;
using MRI.OTA.Core.Entities;

namespace MRI.OTA.UnitTestCases.User.Mapper
{
    public class UpdateUsersMappingProfileTests
    {
        private readonly IMapper _mapper;

        public UpdateUsersMappingProfileTests()
        {
            var config = new MapperConfiguration(cfg =>
            {
                cfg.AddProfile<UpdateUsersMappingProfile>();
            });
            _mapper = config.CreateMapper();
        }

        [Fact]
        public void Should_Map_Users_To_ViewUserProfileModel()
        {
            // Arrange  
            var user = new Users { UserId = 1, DisplayName = "John Doe" };

            // Act  
            var result = _mapper.Map<ViewUserProfileModel>(user);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(user.DisplayName, result.DisplayName);
        }

        [Fact]
        public void Should_Map_ViewUserProfileModel_To_Users()
        {
            // Arrange  
            var model = new ViewUserProfileModel { UserId = 1, DisplayName = "John Doe" };

            // Act  
            var result = _mapper.Map<Users>(model);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(model.UserId, result.UserId);
            Assert.Equal(model.DisplayName, result.DisplayName);
        }

        [Fact]
        public void Should_Map_Users_To_TermsConditionModel()
        {
            // Arrange  
            var user = new Users { UserId = 1, TermsAndConditions = true };

            // Act  
            var result = _mapper.Map<TermsConditionModel>(user);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(user.TermsAndConditions, result.TermsAndConditions);
        }

        [Fact]
        public void Should_Map_TermsConditionModel_To_Users()
        {
            // Arrange  
            var model = new TermsConditionModel { UserId = 1, TermsAndConditions = true };

            // Act  
            var result = _mapper.Map<Users>(model);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(model.UserId, result.UserId);
            Assert.Equal(model.TermsAndConditions, result.TermsAndConditions);
        }

        [Fact]
        public void Should_Map_Users_To_UserProfileSettingsModel()
        {
            // Arrange  
            var user = new Users { UserId = 1, PushNotificationEnabled = true };

            // Act  
            var result = _mapper.Map<UserProfileSettingsModel>(user);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(user.UserId, result.UserId);
            Assert.Equal(user.PushNotificationEnabled, result.PushNotificationEnabled);
        }

        [Fact]
        public void Should_Map_UserProfileSettingsModel_To_Users()
        {
            // Arrange  
            var model = new UserProfileSettingsModel { UserId = 1, EmailNotificationEnabled = true };

            // Act  
            var result = _mapper.Map<Users>(model);

            // Assert  
            Assert.NotNull(result);
            Assert.Equal(model.UserId, result.UserId);
            Assert.Equal(model.EmailNotificationEnabled, result.EmailNotificationEnabled);
        }
    }
}
