using Microsoft.Extensions.Logging;
using Moq;
using MRI.OTA.Common.Interfaces;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.DBCore.Repositories;
using System.Data;
using Xunit;

namespace MRI.OTA.UnitTestCases.Image.Repository
{
    public class UnitTestImageRepository
    {
        private readonly Mock<IDbConnectionFactory> _mockConnectionFactory;
        private readonly Mock<ILogger<ImageRepository>> _mockLogger;
        private readonly Mock<IDapperWrapper> _mockDapperWrapper;
        private readonly Mock<IDbConnection> _mockConnection;
        private readonly Mock<IDbTransaction> _mockTransaction;

        public UnitTestImageRepository()
        {
            _mockConnectionFactory = new Mock<IDbConnectionFactory>();
            _mockLogger = new Mock<ILogger<ImageRepository>>();
            _mockDapperWrapper = new Mock<IDapperWrapper>();
            _mockConnection = new Mock<IDbConnection>();
            _mockTransaction = new Mock<IDbTransaction>();

            _mockConnectionFactory.Setup(cf => cf.CreateConnection()).Returns(_mockConnection.Object);
            _mockConnection.Setup(c => c.BeginTransaction()).Returns(_mockTransaction.Object);
        }

        private ImageRepository CreateRepository()
        {
            return new ImageRepository(
                _mockConnectionFactory.Object,
                _mockLogger.Object,
                _mockDapperWrapper.Object);
        }

        private PropertyImages CreateTestPropertyImage(int propertyImagesId = 1, int propertyId = 100, string imageUrl = "https://example.com/image.jpg")
        {
            return new PropertyImages
            {
                PropertyImagesId = propertyImagesId,
                PropertyId = propertyId,
                ImageBlobUrl = imageUrl
            };
        }

        private ViewPropertyImages CreateTestViewPropertyImage(int propertyImagesId = 1, int propertyId = 100, int defaultImageId = 1)
        {
            return new ViewPropertyImages
            {
                PropertyImagesId = propertyImagesId,
                PropertyId = propertyId,
                DefaultImageId = defaultImageId,
                ImageBlobUrl = "https://example.com/image.jpg"
            };
        }

        #region Constructor Tests

        [Fact]
        public void Constructor_ShouldInitializeCorrectly()
        {
            // Arrange & Act
            var repository = CreateRepository();

            // Assert
            Assert.NotNull(repository);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullDbConnectionFactory()
        {
            // Arrange & Act
            var repository = new ImageRepository(null!, _mockLogger.Object, _mockDapperWrapper.Object);

            // Assert
            Assert.NotNull(repository);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullLogger()
        {
            // Arrange & Act
            var repository = new ImageRepository(_mockConnectionFactory.Object, null!, _mockDapperWrapper.Object);

            // Assert
            Assert.NotNull(repository);
        }

        [Fact]
        public void Constructor_ShouldAcceptNullDapperWrapper()
        {
            // Arrange & Act
            var repository = new ImageRepository(_mockConnectionFactory.Object, _mockLogger.Object, null!);

            // Assert
            Assert.NotNull(repository);
        }

        #endregion

        #region Interface Implementation Tests

        [Fact]
        public void ImageRepository_ShouldImplementIImageRepository()
        {
            // Arrange & Act
            var repository = CreateRepository();

            // Assert
            Assert.IsAssignableFrom<IImageRepository>(repository);
        }

        [Fact]
        public void ImageRepository_ShouldInheritFromBaseRepository()
        {
            // Arrange & Act
            var repositoryType = typeof(ImageRepository);

            // Assert
            Assert.True(repositoryType.BaseType?.IsGenericType == true);
            Assert.Equal("BaseRepository`2", repositoryType.BaseType?.Name);
        }

        #endregion

        #region AddPropertyImages Tests

        [Fact]
        public async Task AddPropertyImages_ShouldReturnCorrectCount_WhenAllImagesAddedSuccessfully()
        {
            // Arrange
            var imagesList = new List<PropertyImages>
            {
                CreateTestPropertyImage(1, 100, "https://example.com/image1.jpg"),
                CreateTestPropertyImage(2, 101, "https://example.com/image2.jpg"),
                CreateTestPropertyImage(3, 102, "https://example.com/image3.jpg")
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<PropertyImages>(), null, null, null))
                .ReturnsAsync(1); // Simulate successful insert returning ID

            var repository = CreateRepository();

            // Act
            var result = await repository.AddPropertyImages(imagesList);

            // Assert
            Assert.Equal(3, result);
            _mockDapperWrapper.Verify(d => d.ExecuteScalarAsync<int>(
                _mockConnection.Object,
                It.IsAny<string>(),
                It.IsAny<PropertyImages>(),
                null, null, null),
                Times.Exactly(3));
        }

        [Fact]
        public async Task AddPropertyImages_ShouldReturnPartialCount_WhenSomeImagesFail()
        {
            // Arrange
            var imagesList = new List<PropertyImages>
            {
                CreateTestPropertyImage(1, 100, "https://example.com/image1.jpg"),
                CreateTestPropertyImage(2, 101, "https://example.com/image2.jpg"),
                CreateTestPropertyImage(3, 102, "https://example.com/image3.jpg")
            };

            _mockDapperWrapper
                .SetupSequence(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<PropertyImages>(), null, null, null))
                .ReturnsAsync(1)  // First image succeeds
                .ReturnsAsync(0)  // Second image fails
                .ReturnsAsync(1); // Third image succeeds

            var repository = CreateRepository();

            // Act
            var result = await repository.AddPropertyImages(imagesList);

            // Assert
            Assert.Equal(2, result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to add image with ID: 101")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task AddPropertyImages_ShouldReturnZero_WhenEmptyList()
        {
            // Arrange
            var imagesList = new List<PropertyImages>();
            var repository = CreateRepository();

            // Act
            var result = await repository.AddPropertyImages(imagesList);

            // Assert
            Assert.Equal(0, result);
            _mockDapperWrapper.Verify(d => d.ExecuteScalarAsync<int>(
                It.IsAny<IDbConnection>(),
                It.IsAny<string>(),
                It.IsAny<PropertyImages>(),
                It.IsAny<IDbTransaction>(),
                It.IsAny<int?>(),
                It.IsAny<CommandType?>()), Times.Never);
        }

        [Fact]
        public async Task AddPropertyImages_ShouldLogError_WhenAddFails()
        {
            // Arrange
            var imagesList = new List<PropertyImages>
            {
                CreateTestPropertyImage(1, 100, "https://example.com/image1.jpg")
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<PropertyImages>(), null, null, null))
                .ReturnsAsync(0); // Simulate failed insert

            var repository = CreateRepository();

            // Act
            var result = await repository.AddPropertyImages(imagesList);

            // Assert
            Assert.Equal(0, result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to add image with ID: 100")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion

        #region GetPropertyImage Tests

        [Fact]
        public async Task GetPropertyImage_ShouldReturnImage_WhenImageExists()
        {
            // Arrange
            var propertyImagesId = 1;
            var expectedImage = CreateTestPropertyImage(propertyImagesId, 100, "https://example.com/image.jpg");

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyImages>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedImage);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetPropertyImage(propertyImagesId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedImage.PropertyImagesId, result.PropertyImagesId);
            Assert.Equal(expectedImage.PropertyId, result.PropertyId);
            Assert.Equal(expectedImage.ImageBlobUrl, result.ImageBlobUrl);
            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<PropertyImages>(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains("PropertyImagesId")),
                It.Is<object>(o => HasProperty(o, "Id", propertyImagesId)),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task GetPropertyImage_ShouldReturnNull_WhenImageNotFound()
        {
            // Arrange
            var propertyImagesId = 999;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyImages>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync((PropertyImages?)null);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetPropertyImage(propertyImagesId);

            // Assert
            Assert.Null(result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to get image with ID: 999")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion

        #region GetPropertyDefaultImage Tests

        [Fact]
        public async Task GetPropertyDefaultImage_ShouldReturnViewPropertyImages_WhenFound()
        {
            // Arrange
            var propertyImagesId = 1;
            var defaultImageId = 1;
            var expectedResult = CreateTestViewPropertyImage(propertyImagesId, 100, defaultImageId);

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<ViewPropertyImages>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedResult);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetPropertyDefaultImage(propertyImagesId, defaultImageId);

            // Assert
            Assert.NotNull(result);
            Assert.Equal(expectedResult.PropertyImagesId, result.PropertyImagesId);
            Assert.Equal(expectedResult.DefaultImageId, result.DefaultImageId);
            _mockDapperWrapper.Verify(d => d.QuerySingleOrDefaultAsync<ViewPropertyImages>(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains("SELECT DefaultImageId FROM UserProperties") &&
                                   s.Contains("WHERE PropertyId = @PropertyId") &&
                                   s.Contains("AND DefaultImageId = @DefaultImageId")),
                It.Is<object>(o => HasProperty(o, "PropertyId", propertyImagesId) && HasProperty(o, "DefaultImageId", defaultImageId)),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task GetPropertyDefaultImage_ShouldReturnNull_WhenNotFound()
        {
            // Arrange
            var propertyImagesId = 999;
            var defaultImageId = 999;

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<ViewPropertyImages>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync((ViewPropertyImages?)null);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetPropertyDefaultImage(propertyImagesId, defaultImageId);

            // Assert
            Assert.Null(result);
        }

        #endregion

        #region UpdateDefaultImage Tests (String overload)

        [Fact]
        public async Task UpdateDefaultImage_WithImageUri_ShouldReturnTrue_WhenUpdateSuccessful()
        {
            // Arrange
            var propertyId = 100;
            var defaultImageUri = "https://example.com/default-image.jpg";

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(1); // Simulate successful update

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateDefaultImage(propertyId, defaultImageUri);

            // Assert
            Assert.True(result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains("UPDATE UserProperties") &&
                                   s.Contains("SET DefaultImageId = (SELECT PropertyImagesId FROM PropertyImages WHERE ImageBlobUrl = @ImageBlobUrl)") &&
                                   s.Contains("WHERE PropertyId = @PropertyId")),
                It.Is<object>(o => HasProperty(o, "ImageBlobUrl", defaultImageUri) && HasProperty(o, "PropertyId", propertyId)),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task UpdateDefaultImage_WithImageUri_ShouldReturnFalse_WhenUpdateFails()
        {
            // Arrange
            var propertyId = 100;
            var defaultImageUri = "https://example.com/nonexistent-image.jpg";

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0); // Simulate failed update

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateDefaultImage(propertyId, defaultImageUri);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region UpdateDefaultImage Tests (Int overload)

        [Fact]
        public async Task UpdateDefaultImage_WithPropertyImagesId_ShouldReturnTrue_WhenUpdateSuccessful()
        {
            // Arrange
            var propertyId = 100;
            var propertyImagesId = 1;
            var userId = 50;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(1); // Simulate successful update

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateDefaultImage(propertyId, propertyImagesId, userId);

            // Assert
            Assert.True(result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains("UPDATE UserProperties SET DefaultImageId = @PropertyImagesId") &&
                                   s.Contains("WHERE PropertyId = @PropertyId AND UserId = @UserId")),
                It.Is<object>(o => HasProperty(o, "PropertyImagesId", propertyImagesId) &&
                                   HasProperty(o, "PropertyId", propertyId) &&
                                   HasProperty(o, "UserId", userId)),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task UpdateDefaultImage_WithPropertyImagesId_ShouldReturnFalse_WhenUpdateFails()
        {
            // Arrange
            var propertyId = 999;
            var propertyImagesId = 999;
            var userId = 999;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0); // Simulate failed update

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateDefaultImage(propertyId, propertyImagesId, userId);

            // Assert
            Assert.False(result);
        }

        #endregion

        #region DeleteImage Tests

        [Fact]
        public async Task DeleteImage_ShouldReturnRowsAffected_WhenDeleteSuccessful()
        {
            // Arrange
            var propertyImagesId = 1;
            var expectedRowsAffected = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(expectedRowsAffected);

            var repository = CreateRepository();

            // Act
            var result = await repository.DeleteImage(propertyImagesId);

            // Assert
            Assert.Equal(expectedRowsAffected, result);
            _mockDapperWrapper.Verify(d => d.ExecuteAsync(
                _mockConnection.Object,
                It.Is<string>(s => s.Contains("DELETE FROM PropertyImages WHERE PropertyImagesId = @Id")),
                It.Is<object>(o => HasProperty(o, "Id", propertyImagesId)),
                null, null, null),
                Times.Once);
        }

        [Fact]
        public async Task DeleteImage_ShouldReturnZero_WhenDeleteFails()
        {
            // Arrange
            var propertyImagesId = 999;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0); // Simulate failed delete

            var repository = CreateRepository();

            // Act
            var result = await repository.DeleteImage(propertyImagesId);

            // Assert
            Assert.Equal(0, result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to delete image with ID: 999")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        [Fact]
        public async Task DeleteImage_ShouldLogError_WhenDeleteFails()
        {
            // Arrange
            var propertyImagesId = 1;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0); // Simulate failed delete

            var repository = CreateRepository();

            // Act
            var result = await repository.DeleteImage(propertyImagesId);

            // Assert
            Assert.Equal(0, result);
            _mockLogger.Verify(
                x => x.Log(
                    LogLevel.Error,
                    It.IsAny<EventId>(),
                    It.Is<It.IsAnyType>((v, t) => v.ToString()!.Contains("Failed to delete image with ID: 1")),
                    It.IsAny<Exception>(),
                    It.IsAny<Func<It.IsAnyType, Exception?, string>>()),
                Times.Once);
        }

        #endregion

        #region Exception Handling Tests

        [Fact]
        public async Task AddPropertyImages_ShouldPropagateException_WhenDapperThrows()
        {
            // Arrange
            var imagesList = new List<PropertyImages>
            {
                CreateTestPropertyImage(1, 100, "https://example.com/image1.jpg")
            };

            var expectedException = new InvalidOperationException("Database connection failed");
            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<PropertyImages>(), null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => repository.AddPropertyImages(imagesList));
            Assert.Same(expectedException, exception);
        }

        [Fact]
        public async Task GetPropertyImage_ShouldPropagateException_WhenDapperThrows()
        {
            // Arrange
            var propertyImagesId = 1;
            var expectedException = new InvalidOperationException("Database connection failed");

            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyImages>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => repository.GetPropertyImage(propertyImagesId));
            Assert.Same(expectedException, exception);
        }

        [Fact]
        public async Task UpdateDefaultImage_ShouldPropagateException_WhenDapperThrows()
        {
            // Arrange
            var propertyId = 100;
            var defaultImageUri = "https://example.com/image.jpg";
            var expectedException = new InvalidOperationException("Database connection failed");

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => repository.UpdateDefaultImage(propertyId, defaultImageUri));
            Assert.Same(expectedException, exception);
        }

        [Fact]
        public async Task DeleteImage_ShouldPropagateException_WhenDapperThrows()
        {
            // Arrange
            var propertyImagesId = 1;
            var expectedException = new InvalidOperationException("Database connection failed");

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ThrowsAsync(expectedException);

            var repository = CreateRepository();

            // Act & Assert
            var exception = await Assert.ThrowsAsync<InvalidOperationException>(() => repository.DeleteImage(propertyImagesId));
            Assert.Same(expectedException, exception);
        }

        #endregion

        #region Edge Case Tests

        [Fact]
        public async Task AddPropertyImages_ShouldHandleNullImageUrl()
        {
            // Arrange
            var imagesList = new List<PropertyImages>
            {
                CreateTestPropertyImage(1, 100, null!)
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<PropertyImages>(), null, null, null))
                .ReturnsAsync(1);

            var repository = CreateRepository();

            // Act
            var result = await repository.AddPropertyImages(imagesList);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task AddPropertyImages_ShouldHandleEmptyImageUrl()
        {
            // Arrange
            var imagesList = new List<PropertyImages>
            {
                CreateTestPropertyImage(1, 100, "")
            };

            _mockDapperWrapper
                .Setup(d => d.ExecuteScalarAsync<int>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<PropertyImages>(), null, null, null))
                .ReturnsAsync(1);

            var repository = CreateRepository();

            // Act
            var result = await repository.AddPropertyImages(imagesList);

            // Assert
            Assert.Equal(1, result);
        }

        [Fact]
        public async Task UpdateDefaultImage_ShouldHandleNullImageUri()
        {
            // Arrange
            var propertyId = 100;
            string? defaultImageUri = null;

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0); // Likely to fail with null URI

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateDefaultImage(propertyId, defaultImageUri!);

            // Assert
            Assert.False(result);
        }

        [Fact]
        public async Task UpdateDefaultImage_ShouldHandleEmptyImageUri()
        {
            // Arrange
            var propertyId = 100;
            var defaultImageUri = "";

            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0); // Likely to fail with empty URI

            var repository = CreateRepository();

            // Act
            var result = await repository.UpdateDefaultImage(propertyId, defaultImageUri);

            // Assert
            Assert.False(result);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(int.MinValue)]
        public async Task GetPropertyImage_ShouldHandleInvalidIds(int invalidId)
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.QuerySingleOrDefaultAsync<PropertyImages>(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync((PropertyImages?)null);

            var repository = CreateRepository();

            // Act
            var result = await repository.GetPropertyImage(invalidId);

            // Assert
            Assert.Null(result);
        }

        [Theory]
        [InlineData(0)]
        [InlineData(-1)]
        [InlineData(int.MinValue)]
        public async Task DeleteImage_ShouldHandleInvalidIds(int invalidId)
        {
            // Arrange
            _mockDapperWrapper
                .Setup(d => d.ExecuteAsync(_mockConnection.Object, It.IsAny<string>(), It.IsAny<object>(), null, null, null))
                .ReturnsAsync(0);

            var repository = CreateRepository();

            // Act
            var result = await repository.DeleteImage(invalidId);

            // Assert
            Assert.Equal(0, result);
        }

        #endregion

        #region Entity Validation Tests

        [Fact]
        public void PropertyImages_ShouldHaveCorrectProperties()
        {
            // Arrange & Act
            var image = CreateTestPropertyImage();

            // Assert
            Assert.True(image.PropertyImagesId > 0);
            Assert.True(image.PropertyId > 0);
            Assert.NotNull(image.ImageBlobUrl);
        }

        [Fact]
        public void ViewPropertyImages_ShouldHaveCorrectProperties()
        {
            // Arrange & Act
            var viewImage = CreateTestViewPropertyImage();

            // Assert
            Assert.True(viewImage.PropertyImagesId > 0);
            Assert.True(viewImage.PropertyId > 0);
            Assert.True(viewImage.DefaultImageId > 0);
            Assert.NotNull(viewImage.ImageBlobUrl);
        }

        #endregion

        // Helper method to check if an object has a property with a specific value
        private static bool HasProperty(object obj, string propertyName, object expectedValue)
        {
            var property = obj.GetType().GetProperty(propertyName);
            if (property == null) return false;
            var value = property.GetValue(obj);
            return Equals(value, expectedValue);
        }
    }
}
