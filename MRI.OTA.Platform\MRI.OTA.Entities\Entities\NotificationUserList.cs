﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MRI.OTA.DBCore.Entities
{
    public class NotificationUserList
    {
        public int UserId { get; set; }
        public int DeviceType { get; set; }
        public string DeviceToken { get; set; }
        public string DeviceId { get; set; }
        public string? PropertyNickName { get; set; }
        public DateTime? InspectionDate { get; set; }
        public string? DocumentName { get; set; }
        public string? DocumentLink { get; set; }
    }
}
