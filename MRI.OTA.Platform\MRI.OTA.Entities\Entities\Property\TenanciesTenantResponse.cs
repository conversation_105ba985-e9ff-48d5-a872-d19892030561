﻿using MRI.OTA.Common.Models;

namespace MRI.OTA.DBCore.Entities.Property
{
    public class TenanciesTenantDetailResponse
    {
        // UserProperties fields
        public int PropertyId { get; set; }
        public string? SRCPropertyId { get; set; }
        public string? SRCAgencyId { get; set; }
        public string? SRCManagementId { get; set; }
        public string? SRCTenancyId { get; set; }

        // PropertyFinancialInformation fields
        public string? TenancyName { get; set; }
        public DateTime? LeaseStart { get; set; }
        public DateTime? LeaseEnd { get; set; }
        public DateTime? VacateDate { get; set; }
        public decimal? Rent { get; set; }
        public decimal? IncreaseRent { get; set; }
        public DateTime? IncreaseDate { get; set; }
        public decimal? AmountToVacate { get; set; }
        public DateTime? PayToDate { get; set; }
        public decimal? RentPeriod { get; set; }
        public string? Currency { get; set; }
        public string? CurrencySymbol { get; set; }
        public decimal? Arrears { get; set; }
        public decimal? OutstandingInvoices { get; set; }

        // PropertyManagerInformation fields
        public string? PropertyManagerName { get; set; }
        public string? PropertyManagerMobile { get; set; }
        public string? PropertyManagerEmail { get; set; }
        public DateTime? AuthorityStartDate { get; set; }
        public DateTime? AuthorityEndDate { get; set; }

        // AgencyDetails fields
        public string? MriId { get; set; }
        public string? AgencyName { get; set; }
        public string? BusinessName { get; set; }
        public string? BusinessRegisteredName { get; set; }
        public string? BusinessRegistrationNumber { get; set; }
        public string? AgencyPhone { get; set; }
        public string? AgencyEmail { get; set; }
        [ExcludeColumn]
        public string? ContactRole { get; set; }
    }
}
