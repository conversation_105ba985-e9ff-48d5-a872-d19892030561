﻿﻿using MRI.OTA.Infrastructure.Caching.Configuration;

namespace MRI.OTA.UnitTestCases.Infrastructure.Caching
{
    public class UnitTestRedisConfiguration
    {
        [Fact]
        public void RedisConfiguration_HasCorrectDefaultValues()
        {
            // Arrange & Act
            var config = new RedisConfiguration();

            // Assert
            Assert.Equal(string.Empty, config.ConnectionString);
            Assert.Equal(60, config.DefaultExpirationMinutes);
        }

        [Fact]
        public void RedisConfiguration_PropertiesSetAndGetCorrectly()
        {
            // Arrange
            var config = new RedisConfiguration();
            var connectionString = "test-connection-string";
            var defaultExpirationMinutes = 30;

            // Act
            config.ConnectionString = connectionString;
            config.DefaultExpirationMinutes = defaultExpirationMinutes;

            // Assert
            Assert.Equal(connectionString, config.ConnectionString);
            Assert.Equal(defaultExpirationMinutes, config.DefaultExpirationMinutes);
        }
    }
}
