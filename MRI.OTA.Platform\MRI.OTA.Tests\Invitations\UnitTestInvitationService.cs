﻿using AutoMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Moq;
using Moq.Protected;
using MRI.OTA.Application.Interfaces;
using MRI.OTA.Application.Interfaces.Integration;
using MRI.OTA.Application.Models.Integration;
using MRI.OTA.Application.Services;
using MRI.OTA.Application.Services.Integration;
using MRI.OTA.Common.Models;
using MRI.OTA.DBCore.Entities;
using MRI.OTA.DBCore.Entities.Property;
using MRI.OTA.DBCore.Interfaces;
using MRI.OTA.Email;
using MRI.OTA.Integration.Http;
using MRI.OTA.Integration.Models;
using MRI.OTA.Integration.Services;
using System.Net;
using System.Reflection;
using System.Text;
using System.Text.Json;
using static MRI.OTA.Common.Constants.Constants;
using DataSourceEntity = MRI.OTA.Core.Entities.DataSource;

namespace MRI.OTA.UnitTestCases.Invitations
{
    public class UnitTestInvitationService
    {
        private readonly Mock<ILogger<InvitationService>> _mockLogger;
        private readonly Mock<IInvitationRepository> _mockInvitationRepository;
        private readonly Mock<IMapper> _mockMapper;
        private readonly Mock<IConfiguration> _mockConfiguration;
        private readonly Mock<IEmailServiceFactory> _mockEmailServiceFactory;
        private readonly Mock<IDataSourceRepository> _mockDataSourceRepository;
        private readonly Mock<IProxyService> _mockProxyService;
        private readonly Mock<IPropertyRepository> _mockPropertyRepository;
        private readonly Mock<IEmailService> _mockEmailService;
        private readonly TaskContext _taskContext;
        private readonly InvitationService _invitationService;
        private readonly Mock<IPropertTreeService> _mockPTService;
        private readonly Mock<IAPITrackingService> _mockAPITracking;

        public UnitTestInvitationService()
        {
            _mockLogger = new Mock<ILogger<InvitationService>>();
            _mockInvitationRepository = new Mock<IInvitationRepository>();
            _mockMapper = new Mock<IMapper>();
            _mockConfiguration = new Mock<IConfiguration>();
            _mockEmailServiceFactory = new Mock<IEmailServiceFactory>();
            _mockDataSourceRepository = new Mock<IDataSourceRepository>();
            _mockProxyService = new Mock<IProxyService>();
            _mockPropertyRepository = new Mock<IPropertyRepository>();
            _mockEmailService = new Mock<IEmailService>();
            _mockPTService = new Mock<IPropertTreeService>();
            _mockAPITracking = new Mock<IAPITrackingService>();
            _taskContext = new TaskContext
            {
                UserId = 1,
                Email = "<EMAIL>",
                AccessToken = "test-token",
                AccessKey = "test-key"
            };

            // Setup configuration
            _mockConfiguration.Setup(c => c["ApplicationOption:BaseUrl"]).Returns("https://example.com/");

            // Setup email service factory
            _mockEmailServiceFactory.Setup(f => f.GetEmailService(It.IsAny<int>(), It.IsAny<int>()))
                .Returns(_mockEmailService.Object);

            // Setup email service
            _mockEmailService.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
                .ReturnsAsync(true);

            // Setup API tracking service
            _mockAPITracking.Setup(a => a.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()))
                .Returns(Task.CompletedTask);

            _invitationService = new InvitationService(
                _mockLogger.Object,
                _mockInvitationRepository.Object,
                _mockMapper.Object,
                _mockConfiguration.Object,
                _mockEmailServiceFactory.Object,
                _mockDataSourceRepository.Object,
                _taskContext,
                _mockProxyService.Object,
                _mockPTService.Object,
                _mockAPITracking.Object
            );
        }

        [Fact]
        public void GetEndpointFromDictionary_Should_Throw_ArgumentException_For_UnknownType()
        {
            // Arrange
            var invalidType = (IntegrationEndPointsType)999;

            // Act & Assert
            var ex = Assert.Throws<TargetInvocationException>(() =>
                typeof(PropertTreeService)
                    .GetMethod("GetEndpointFromDictionary", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                    .Invoke(null, new object[] { invalidType })
            );
            Assert.IsType<ArgumentException>(ex.InnerException);
        }

        [Theory]
        [InlineData(IntegrationEndPointsType.OwnerPropertyList, true)]
        [InlineData(IntegrationEndPointsType.TenanciesTenantList, true)]
        [InlineData((IntegrationEndPointsType)999, false)]
        public void IsEndpointSupported_ReturnsExpectedResult(IntegrationEndPointsType sectionType, bool expected)
        {
            // Act
            var result = typeof(PropertTreeService)
                .GetMethod("IsEndpointSupported", System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Static)
                .Invoke(null, new object[] { sectionType });

            // Assert
            Assert.Equal(expected, result);
        }

        [Fact]
        public async Task SendInvitationAsync_Success_ReturnsTrue()
        {
            // Arrange
            var invitationRequest = new InvitationRequestModel
            {
                UserEmail = "<EMAIL>",
                Name = "Test User",
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "https://example.com/logo.png",
                AgencyColour = "#FF0000",
                PortfolioId = "portfolio123"
            };

            var userInvite = new UserInvites
            {
                UserEmail = invitationRequest.UserEmail,
                Name = invitationRequest.Name,
                AgencyId = invitationRequest.AgencyId,
                AgencyName = invitationRequest.AgencyName,
                AgencyLogo = invitationRequest.AgencyLogo,
                AgencyColour = invitationRequest.AgencyColour,
                PortfolioId = invitationRequest.PortfolioId,
                InviteCode = "ABC12345",
                InviteLink = "https://example.com/invite?code=ABC12345",
                IsActive = true,
                DataSourceId = 1
            };

            var dataSource = new DataSourceEntity
            {
                DataSourceId = 1,
                Name = "TestDataSource",
                AccessKey = "test-key",
                AccessSecret = "test-secret"
            };

            // Setup mapper
            _mockMapper.Setup(m => m.Map<UserInvites>(It.IsAny<InvitationRequestModel>()))
                .Returns(userInvite);

            // Setup data source repository
            _mockDataSourceRepository.Setup(d => d.GetUserDataSource(It.IsAny<string>()))
                .ReturnsAsync(dataSource);

            // Setup invitation repository
            _mockInvitationRepository.Setup(r => r.AddAsync(It.IsAny<UserInvites>()))
                .ReturnsAsync(1);

            // Act
            var result = await _invitationService.SendInvitationAsync(invitationRequest);

            // Assert
            Assert.True(result);
            _mockInvitationRepository.Verify(r => r.AddAsync(It.IsAny<UserInvites>()), Times.Once);
        }
       

        [Fact]
        public async Task SendInvitationAsync_ExistingInvitation_ResendsEmail_ReturnsTrue()
        {
            // Arrange
            var invitationRequest = new InvitationRequestModel
            {
                UserEmail = "<EMAIL>",
                Name = "Test User",
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "https://example.com/logo.png",
                AgencyColour = "#FF0000",
                PortfolioId = "portfolio123"
            };

            var existingInvite = new UserInvites
            {
                UserEmail = invitationRequest.UserEmail,
                Name = invitationRequest.Name,
                AgencyId = invitationRequest.AgencyId,
                AgencyName = invitationRequest.AgencyName,
                AgencyLogo = invitationRequest.AgencyLogo,
                AgencyColour = invitationRequest.AgencyColour,
                PortfolioId = invitationRequest.PortfolioId,
                InviteCode = "ABC12345",
                InviteLink = "https://example.com/invite?code=ABC12345",
                IsActive = true,
                DataSourceId = 1
            };

            // Setup invitation repository to return existing invitation
            _mockInvitationRepository.Setup(r => r.GetInvitationByPortfolioIdAndEmail(
                    It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync(existingInvite);

            // Setup email service
            _mockEmailService.Setup(e => e.SendEmailAsync(It.IsAny<EmailMessage>()))
                .ReturnsAsync(true);

            // Act
            var result = await _invitationService.SendInvitationAsync(invitationRequest);

            // Assert
            Assert.True(result);
            _mockInvitationRepository.Verify(r => r.GetInvitationByPortfolioIdAndEmail(
                It.IsAny<string>(), It.IsAny<string>()), Times.Once);
            _mockInvitationRepository.Verify(r => r.AddAsync(It.IsAny<UserInvites>()), Times.Never);
            _mockEmailServiceFactory.Verify(f => f.GetEmailService(It.IsAny<int>(), It.IsAny<int>()), Times.Once);
        }

        [Fact]
        public async Task SendInvitationAsync_DataSourceNotFound_ThrowsUnauthorizedAccessException()
        {
            // Arrange
            var invitationRequest = new InvitationRequestModel
            {
                UserEmail = "<EMAIL>",
                Name = "Test User",
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "https://example.com/logo.png",
                AgencyColour = "#FF0000",
                PortfolioId = "portfolio123"
            };

            var userInvite = new UserInvites
            {
                UserEmail = invitationRequest.UserEmail,
                Name = invitationRequest.Name,
                AgencyId = invitationRequest.AgencyId,
                AgencyName = invitationRequest.AgencyName,
                AgencyLogo = invitationRequest.AgencyLogo,
                AgencyColour = invitationRequest.AgencyColour,
                PortfolioId = invitationRequest.PortfolioId,
                InviteCode = "ABC12345",
                InviteLink = "https://example.com/invite?code=ABC12345",
                IsActive = true
            };

            // Setup invitation repository to return no existing invitation
            _mockInvitationRepository.Setup(r => r.GetInvitationByPortfolioIdAndEmail(
                    It.IsAny<string>(), It.IsAny<string>()))
                .ReturnsAsync((UserInvites)null);

            // Setup mapper
            _mockMapper.Setup(m => m.Map<UserInvites>(It.IsAny<InvitationRequestModel>()))
                .Returns(userInvite);

            // Setup data source repository to return null
            _mockDataSourceRepository.Setup(d => d.GetUserDataSource(It.IsAny<string>()))
                .ReturnsAsync((DataSourceEntity)null);

            // Act & Assert
            await Assert.ThrowsAsync<UnauthorizedAccessException>(() => 
                _invitationService.SendInvitationAsync(invitationRequest));
            
            _mockDataSourceRepository.Verify(d => d.GetUserDataSource(It.IsAny<string>()), Times.Once);
            _mockInvitationRepository.Verify(r => r.AddAsync(It.IsAny<UserInvites>()), Times.Never);
        }

        [Fact]
        public async Task AcceptInvitationAsync_Success_ReturnsOne()
        {
            // Arrange
            var acceptInvitation = new AcceptInvitationModel
            {
                UserEmail = "<EMAIL>",
                UserId = 1,
                ProviderId = "provider123",
                InviteCode = "ABC12345",
                ProviderType = "AzureAD"
            };

            var invitationDetails = new ViewUserInvites
            {
                UserEmail = "<EMAIL>",
                Name = "Test User",
                InviteCode = "ABC12345",
                IsActive = true,
                DataSourceId = 1,
                PortfolioId = "portfolio123",
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "https://example.com/logo.png",
                AgencyColour = "#FF0000",
            };

            // Create a valid manifest JSON that matches what the service expects
            string manifestJson = @"{
               ""baseUrl"": ""https://api.example.com"",
               ""property:associatePortfolio"": {
                   ""endpoint"": ""/properties"",
                   ""method"": ""POST"",
                   ""params"": {
                       ""portfolioId"": ""string"",
                       ""entraId"": ""string"",
                       ""accessToken"": ""string""
                   }
               },
               ""property:list"": {
                   ""endpoint"": ""/properties/list"",
                   ""method"": ""GET"",
                   ""params"": {}
               },
               ""ownership:list"": {
                   ""endpoint"": ""/ownership/list"",
                   ""method"": ""GET"",
                   ""params"": {}
               },
               ""tenancies-tenant:list"": {
                   ""endpoint"": ""/tenancies/tenant/list"",
                   ""method"": ""GET"",
                   ""params"": {}
               },
               ""tenancies-owner:list"": {
                   ""endpoint"": ""/tenancies/owner/list"",
                   ""method"": ""GET"",
                   ""params"": {}
               },
               ""maintenance:list"": {
                   ""endpoint"": ""/maintenance/list"",
                   ""method"": ""GET"",
                   ""params"": {}
                   },
               ""inspection:list"": {
                   ""endpoint"": ""/inspection/list"",
                   ""method"": ""GET"",
                   ""params"": {}
               },
               ""compliance:list"": {
		            ""endpoint"": ""/compliance/list"",
                   ""method"": ""GET"",
                  ""params"": {}
               },
               ""financial:list"": {
		            ""endpoint"": ""/financial/list"",
                   ""method"": ""GET"",
                  ""params"": {}
               },
               ""documents:owner:list"": {
		            ""endpoint"": ""/documents/owner/list"",
                   ""method"": ""GET"",
                  ""params"": {}
               },
               ""documents:tenant:list"": {
		            ""endpoint"": ""/documents/tenant/list"",
                   ""method"": ""GET"",
                  ""params"": {}
               }

           }";

            var dataSource = new DataSourceEntity
            {
                DataSourceId = 1,
                Name = "TestDataSource",
                AccessKey = "test-key",
                AccessSecret = "test-secret",
                ManifestJson = manifestJson
            };

            // Create a valid response from the proxy service that matches what ProcessProxyResponse expects
            var jsonResponse = @"[{
               ""ptPropertyName"": ""Property 1"",
               ""srcServiceId"": ""1"",
               ""occupancyType"": ""Residential"",
               ""ptSuburb"": ""Test Suburb"",
               ""propertyType"": ""House"",
               ""lotNumber"": ""123"",
               ""occupancyStatus"": ""Occupied"",
               ""srcAgencyId"": ""1"",
               ""isActive"": true,
               ""bedrooms"": 3,
               ""bathRooms"": 2,
               ""carSpaces"": 1,
               ""floorArea"": 150,
               ""landArea"": 500,
               ""description"": ""Test property"",
               ""propertyRelationshipId"": 1,
               ""propertyRelationshipName"": ""Owner""
           }]";

            var proxyResponse = new OkObjectResult(JsonDocument.Parse(jsonResponse)) { StatusCode = 200 };

            var property = new UserProperties
            {
                PropertyId = 1,
                PropertyName = "Property 1",
                UserId = 1,
                DataSourceId = 1,
            };

            // Setup invitation repository
            _mockInvitationRepository.Setup(r => r.GetInvitationDetailsById(It.IsAny<string>()))
                .ReturnsAsync(invitationDetails);

            // Setup data source repository
            _mockDataSourceRepository.Setup(d => d.GetByIdAsync(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(dataSource);

            // Setup PTService for CreateAssociatePortfolioProxyRequest
            var associatePortfolioProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/properties",
                Method = "POST",
                RequestId = "test-request-id",
                Body = new AssociatePortfolioModel
                {
                    PortfolioId = invitationDetails.PortfolioId,
                    UserId = acceptInvitation.ProviderId
                }
            };

            _mockPTService.Setup(p => p.CreateAssociatePortfolioProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()))
                .Returns(associatePortfolioProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetPropertiesProxyRequest)
            var getPropertiesProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/properties/list",
                Method = "GET",
                RequestId = "test-request-id-2"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.OwnerPropertyList,
                    It.IsAny<string[]>(),
                    It.IsAny<string>()))
                .Returns(getPropertiesProxyRequest);

            var getPropertiesProxyRequestTenant = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/properties/list",
                Method = "GET",
                RequestId = "test-request-id-2"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.TenantPropertyList,
                    It.IsAny<string[]>(),
                    It.IsAny<string>()))
                .Returns(getPropertiesProxyRequestTenant);

            // Setup PTService for CreateProxyRequest (replaces CreateGetOwnershipProxyRequest)
            var getOwnershipProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/ownership/list",
                Method = "GET",
                RequestId = "test-request-id-ownership"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.ManagementList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getOwnershipProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetTenanciesTenantProxyRequest)
            var getTenanciesTenantProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/tenancies/tenant/list",
                Method = "GET",
                RequestId = "test-request-id-3"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.TenanciesTenantList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getTenanciesTenantProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetTenanciesOwnerProxyRequest)
            var getTenanciesOwnerProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/tenancies/owner/list",
                Method = "GET",
                RequestId = "test-request-id-4"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.TenanciesOwnerList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getTenanciesOwnerProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetMaintenanceProxyRequest)
            var getMaintenanceProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/maintenance/list",
                Method = "GET",
                RequestId = "test-request-id-5"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.MaintenanceList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getMaintenanceProxyRequest);

            var getInspectionProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/inspection/list",
                Method = "GET",
                RequestId = "test-request-id-5"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.InspectionList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getInspectionProxyRequest);

            var getComplianceProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/compliance/list",
                Method = "GET",
                RequestId = "test-request-id-5"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.ComplianceList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getComplianceProxyRequest);

            var getFinancialProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/financial/list",
                Method = "GET",
                RequestId = "test-request-id-3"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.FinancialList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getFinancialProxyRequest);

            var getDocProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/maintenance/list",
                Method = "GET",
                RequestId = "test-request-id-5"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.DocumentList,
                    It.IsAny<string[]>(),
                    It.IsAny<string>()))
                .Returns(getDocProxyRequest);

            // Setup PTService for ProcessGetPropertiesProxyResponse
            var properties = new[] { property }.ToList();
            _mockPTService.Setup(p => p.ProcessGetPropertiesProxyResponse(
                    It.IsAny<IActionResult>(),
                    It.IsAny<DataSourceEntity>(),
                    It.IsAny<AcceptInvitationModel>()))
                .ReturnsAsync((true, properties));

            // Setup for background processing method
            _mockPTService.Setup(p => p.ProcessGetPropertiesOtherDataResponse(
                    It.IsAny<PropertyTreeResponseBundle>(),
                    It.IsAny<DataSourceEntity>(),
                    It.IsAny<List<UserProperties>>(),
                    It.IsAny<string>()))
                .ReturnsAsync(1); // Return success value

            // Setup proxy service for all three calls
            _mockProxyService.Setup(p => p.ForwardRequestAsync(
                    It.IsAny<ProxyRequestModel>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()))
                .ReturnsAsync(proxyResponse);

            // Setup property repository
            _mockPropertyRepository.Setup(p => p.AddProperty(It.IsAny<UserProperties>()))
                .ReturnsAsync(1);

            // Setup invitation repository update
            _mockInvitationRepository.Setup(r => r.UpdateUserInvites(
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>()))
                .ReturnsAsync(1);

            // Act
            var result = await _invitationService.AcceptInvitationAsync(acceptInvitation);

            // Assert
            Assert.Equal(1, result.Item1);
            _mockInvitationRepository.Verify(r => r.GetInvitationDetailsById(It.IsAny<string>()), Times.Once);
            _mockDataSourceRepository.Verify(d => d.GetByIdAsync(It.IsAny<int>(), It.IsAny<string>()), Times.Once);

            // Verify PTService methods were called
            _mockPTService.Verify(p => p.CreateAssociatePortfolioProxyRequest(
                It.IsAny<DataSourceManifest>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);

            _mockPTService.Verify(p => p.CreateProxyRequest(
                It.IsAny<DataSourceManifest>(),
                IntegrationEndPointsType.OwnerPropertyList,
                It.IsAny<string[]>(),
                It.IsAny<string>()), Times.Once);

            // Remove verifications for background operations that happen in Task.Run
            // These may not complete before the test ends and could cause intermittent failures

            _mockPTService.Verify(p => p.ProcessGetPropertiesProxyResponse(
                It.IsAny<IActionResult>(),
                It.IsAny<DataSourceEntity>(),
                It.IsAny<AcceptInvitationModel>()), Times.AtLeast(2 ));

            // Only verify the synchronous proxy calls (Associate Portfolio + Get Properties)
            // The other 8 calls happen in background Task.Run and may not complete before test ends
            _mockProxyService.Verify(p => p.ForwardRequestAsync(
                It.IsAny<ProxyRequestModel>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.AtLeast(2));

            _mockInvitationRepository.Verify(r => r.UpdateUserInvites(
                It.IsAny<string>(), It.IsAny<string>(), It.IsAny<string>(), It.IsAny<bool>()), Times.Once);

            // Verify API tracking was called for the synchronous operations
            _mockAPITracking.Verify(a => a.LogAPITrackingDetails(It.IsAny<APITrackingDetail>()), Times.AtLeast(2));
        }

        [Fact]
        public async Task AcceptInvitationAsync_InvitationNotFound_ReturnsNegativeTwo()
        {
            // Arrange
            var acceptInvitation = new AcceptInvitationModel
            {
                UserEmail = "<EMAIL>",
                UserId = 1,
                ProviderId = "provider123",
                InviteCode = "INVALID",
                ProviderType = "AzureAD"
            };

            // Setup invitation repository to return null
            _mockInvitationRepository.Setup(r => r.GetInvitationDetailsById(It.IsAny<string>()))
                .ReturnsAsync((ViewUserInvites)null);

            // Act
            var result = await _invitationService.AcceptInvitationAsync(acceptInvitation);

            // Assert
            Assert.Equal(-2, result.Item1);
            _mockInvitationRepository.Verify(r => r.GetInvitationDetailsById(It.IsAny<string>()), Times.Once);
            _mockDataSourceRepository.Verify(d => d.GetByIdAsync(It.IsAny<int>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task AcceptInvitationAsync_DataSourceNotFound_ReturnsNegativeThree()
        {
            // Arrange
            var acceptInvitation = new AcceptInvitationModel
            {
                UserEmail = "<EMAIL>",
                UserId = 1,
                ProviderId = "provider123",
                InviteCode = "ABC12345",
                ProviderType = "AzureAD"
            };

            var invitationDetails = new ViewUserInvites
            {
                UserEmail = "<EMAIL>",
                Name = "Test User",
                InviteCode = "ABC12345",
                IsActive = true,
                DataSourceId = 1,
                PortfolioId = "portfolio123",
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "https://example.com/logo.png",
                AgencyColour = "#FF0000",
            };

            // Setup invitation repository
            _mockInvitationRepository.Setup(r => r.GetInvitationDetailsById(It.IsAny<string>()))
                .ReturnsAsync(invitationDetails);

            // Setup data source repository to return null
            _mockDataSourceRepository.Setup(d => d.GetByIdAsync(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync((DataSourceEntity)null);

            // Act
            var result = await _invitationService.AcceptInvitationAsync(acceptInvitation);

            // Assert
            Assert.Equal(-3, result.Item1);
            _mockInvitationRepository.Verify(r => r.GetInvitationDetailsById(It.IsAny<string>()), Times.Once);
            _mockDataSourceRepository.Verify(d => d.GetByIdAsync(It.IsAny<int>(), It.IsAny<string>()), Times.Once);
            _mockProxyService.Verify(p => p.ForwardRequestAsync(It.IsAny<ProxyRequestModel>(), It.IsAny<string>(), It.IsAny<string>()), Times.Never);
        }

        [Fact]
        public async Task AcceptInvitationAsync_ProxyServiceFails_ReturnsNegativeOne()
        {
            // Arrange
            var acceptInvitation = new AcceptInvitationModel
            {
                UserEmail = "<EMAIL>",
                UserId = 1,
                ProviderId = "provider123",
                InviteCode = "ABC12345",
                ProviderType = "AzureAD"
            };

            var invitationDetails = new ViewUserInvites
            {
                UserEmail = "<EMAIL>",
                Name = "Test User",
                InviteCode = "ABC12345",
                IsActive = true,
                DataSourceId = 1,
                PortfolioId = "portfolio123",
                AgencyId = "agency123",
                AgencyName = "Test Agency",
                AgencyLogo = "https://example.com/logo.png",
                AgencyColour = "#FF0000",
            };

            // Create a valid manifest JSON that matches what the service expects
            string manifestJson = @"{
               ""baseUrl"": ""https://api.example.com"",
               ""property:associatePortfolio"": {
                   ""endpoint"": ""/properties"",
                   ""method"": ""POST"",
                   ""params"": {
                       ""portfolioId"": ""string"",
                       ""entraId"": ""string"",
                       ""accessToken"": ""string""
                   }
               },
               ""property:list"": {
                   ""endpoint"": ""/properties/list"",
                   ""method"": ""GET"",
                   ""params"": {}
               }
           }";

            var dataSource = new DataSourceEntity
            {
                DataSourceId = 1,
                Name = "TestDataSource",
                AccessKey = "test-key",
                AccessSecret = "test-secret",
                ManifestJson = manifestJson
            };

            // Setup invitation repository
            _mockInvitationRepository.Setup(r => r.GetInvitationDetailsById(It.IsAny<string>()))
                .ReturnsAsync(invitationDetails);

            // Setup data source repository
            _mockDataSourceRepository.Setup(d => d.GetByIdAsync(It.IsAny<int>(), It.IsAny<string>()))
                .ReturnsAsync(dataSource);

            // Setup PTService for CreateAssociatePortfolioProxyRequest
            var associatePortfolioProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/properties",
                Method = "POST",
                RequestId = "test-request-id",
                Body = new AssociatePortfolioModel
                {
                    PortfolioId = invitationDetails.PortfolioId,
                    UserId = acceptInvitation.ProviderId
                }
            };

            _mockPTService.Setup(p => p.CreateAssociatePortfolioProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    It.IsAny<string>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()))
                .Returns(associatePortfolioProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetPropertiesProxyRequest)
            var getPropertiesProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/properties/list",
                Method = "GET",
                RequestId = "test-request-id-2"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.OwnerPropertyList,
                    It.IsAny<string[]>(),
                    It.IsAny<string>()))
                .Returns(getPropertiesProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetOwnershipProxyRequest)
            var getOwnershipProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/ownership/list",
                Method = "GET",
                RequestId = "test-request-id-ownership"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.ManagementList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getOwnershipProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetTenanciesTenantProxyRequest)
            var getTenanciesTenantProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/tenancies/tenant/list",
                Method = "GET",
                RequestId = "test-request-id-3"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.TenanciesTenantList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getTenanciesTenantProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetTenanciesOwnerProxyRequest)
            var getTenanciesOwnerProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/tenancies/owner/list",
                Method = "GET",
                RequestId = "test-request-id-4"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.TenanciesOwnerList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getTenanciesOwnerProxyRequest);

            // Setup PTService for CreateProxyRequest (replaces CreateGetMaintenanceProxyRequest)
            var getMaintenanceProxyRequest = new ProxyRequestModel
            {
                FullUrl = "https://api.example.com/maintenance/list",
                Method = "GET",
                RequestId = "test-request-id-5"
            };
            _mockPTService.Setup(p => p.CreateProxyRequest(
                    It.IsAny<DataSourceManifest>(),
                    IntegrationEndPointsType.MaintenanceList,
                    It.IsAny<string>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>(),
                    It.IsAny<string[]>()))
                .Returns(getMaintenanceProxyRequest);

            // Setup proxy service to throw exception on the first call
            _mockProxyService.Setup(p => p.ForwardRequestAsync(
                    It.IsAny<ProxyRequestModel>(),
                    It.IsAny<string>(),
                    It.IsAny<string>()))
                .ThrowsAsync(new Exception("Proxy service error"));

            // Act
            var result = await _invitationService.AcceptInvitationAsync(acceptInvitation);

            // Assert
            Assert.Equal(-1, result.Item1);
            _mockInvitationRepository.Verify(r => r.GetInvitationDetailsById(It.IsAny<string>()), Times.Once);
            _mockDataSourceRepository.Verify(d => d.GetByIdAsync(It.IsAny<int>(), It.IsAny<string>()), Times.Once);

            // Verify PTService methods were called
            _mockPTService.Verify(p => p.CreateAssociatePortfolioProxyRequest(
                It.IsAny<DataSourceManifest>(),
                It.IsAny<string>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);

            // Verify proxy service was called once and threw an exception
            _mockProxyService.Verify(p => p.ForwardRequestAsync(
                It.IsAny<ProxyRequestModel>(),
                It.IsAny<string>(),
                It.IsAny<string>()), Times.Once);

            // The second PTService method should not be called because the first proxy call throws an exception
            _mockPTService.Verify(p => p.CreateProxyRequest(
                It.IsAny<DataSourceManifest>(),
                IntegrationEndPointsType.OwnerPropertyList,
                It.IsAny<string[]>(),
                It.IsAny<string>()), Times.Never);

            // ProcessGetPropertiesProxyResponse should not be called
            _mockPTService.Verify(p => p.ProcessGetPropertiesProxyResponse(
                It.IsAny<IActionResult>(),
                It.IsAny<DataSourceEntity>(),
                It.IsAny<AcceptInvitationModel>()), Times.Never);
        }

        [Fact]
        public async Task ForwardRequestAsync_ReturnsRawContent_WhenJsonParsingFails()
        {
            // Arrange
            var invalidJson = "not a json string";
            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(invalidJson, Encoding.UTF8, "application/json")
                });

            var httpClient = new HttpClient(handlerMock.Object);
            var httpClientFactoryManagerMock = new Mock<IHttpClientFactoryManager>();
            httpClientFactoryManagerMock.Setup(f => f.GetHttpClient()).Returns(httpClient);

            var loggerMock = new Mock<ILogger<ProxyService>>();
            var service = new ProxyService(httpClientFactoryManagerMock.Object, loggerMock.Object);

            var request = new ProxyRequestModel
            {
                FullUrl = "http://localhost/api/test",
                Method = "GET",
                RequestId = "req-raw"
            };

            // Act
            var result = await service.ForwardRequestAsync(request, "Bearer testtoken");

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(200, objectResult.StatusCode);
            Assert.NotNull(objectResult.Value);
            var value = objectResult.Value.ToString();
            Assert.Contains("not a json string", value);
            Assert.Contains("statusCode", value);
            Assert.Contains("content", value);
        }

        [Fact]
        public async Task ForwardRequestAsync_ReturnsNoContent_WhenResponseBodyIsEmpty()
        {
            // Arrange
            var emptyContent = ""; // Simulate empty response body
            var handlerMock = new Mock<HttpMessageHandler>();
            handlerMock.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent(emptyContent, Encoding.UTF8, "application/json")
                });

            var httpClient = new HttpClient(handlerMock.Object);
            var httpClientFactoryManagerMock = new Mock<IHttpClientFactoryManager>();
            httpClientFactoryManagerMock.Setup(f => f.GetHttpClient()).Returns(httpClient);

            var loggerMock = new Mock<ILogger<ProxyService>>();
            var service = new ProxyService(httpClientFactoryManagerMock.Object, loggerMock.Object);

            var request = new ProxyRequestModel
            {
                FullUrl = "http://localhost/api/test",
                Method = "GET",
                RequestId = "req-empty"
            };

            // Act
            var result = await service.ForwardRequestAsync(request, "Bearer testtoken");

            // Assert
            var objectResult = Assert.IsType<ObjectResult>(result);
            Assert.Equal(200, objectResult.StatusCode);
            Assert.NotNull(objectResult.Value);
            var value = objectResult.Value.ToString();
            Assert.Contains("No content", value);
            Assert.Contains("statusCode", value);
            Assert.Contains("content", value);
        }

        [Fact]
        public async Task ForwardRequestAsync_AddsHeadersAndBody_WhenPresent()
        {
            // Arrange
            var handlerMock = new Mock<HttpMessageHandler>();
            HttpRequestMessage? capturedRequest = null;
            handlerMock.Protected()
                .Setup<Task<HttpResponseMessage>>(
                    "SendAsync",
                    ItExpr.IsAny<HttpRequestMessage>(),
                    ItExpr.IsAny<CancellationToken>())
                .Callback<HttpRequestMessage, CancellationToken>((req, _) => capturedRequest = req)
                .ReturnsAsync(new HttpResponseMessage
                {
                    StatusCode = HttpStatusCode.OK,
                    Content = new StringContent("{\"result\":\"ok\"}", Encoding.UTF8, "application/json")
                });

            var httpClient = new HttpClient(handlerMock.Object);
            var httpClientFactoryManagerMock = new Mock<IHttpClientFactoryManager>();
            httpClientFactoryManagerMock.Setup(f => f.GetHttpClient()).Returns(httpClient);

            var loggerMock = new Mock<ILogger<ProxyService>>();
            var service = new ProxyService(httpClientFactoryManagerMock.Object, loggerMock.Object);

            var headers = new Dictionary<string, string>
           {
               { "X-Test-Header", "HeaderValue" }
           };
            var bodyObj = new { foo = "bar" };

            var request = new ProxyRequestModel
            {
                FullUrl = "http://localhost/api/test",
                Method = "POST",
                RequestId = "req-headers-body",
                Headers = headers,
                Body = bodyObj
            };

            // Act
            var result = await service.ForwardRequestAsync(request, "Bearer testtoken");

            // Assert
            Assert.NotNull(capturedRequest);
            Assert.True(capturedRequest.Headers.Contains("X-Test-Header"));
            Assert.Equal("HeaderValue", capturedRequest.Headers.GetValues("X-Test-Header").First());
            Assert.NotNull(capturedRequest.Content);
            var content = await capturedRequest.Content.ReadAsStringAsync();
            Assert.Contains("\"foo\":\"bar\"", content);
            Assert.Equal("application/json", capturedRequest.Content.Headers.ContentType.MediaType);
        }

        [Theory]
        [InlineData(IntegrationEndPointsType.MaintenanceList, "2024-01-01", new[] { "m1" }, null, null, typeof(ManagementRequestModel))]
        [InlineData(IntegrationEndPointsType.ManagementList, "2024-01-01", new[] { "m2" }, null, null, typeof(ManagementRequestModel))]
        [InlineData(IntegrationEndPointsType.FinancialList, "2024-01-01", new[] { "m3" }, null, null, typeof(ManagementRequestModel))]
        [InlineData(IntegrationEndPointsType.ComplianceList, "2024-01-01", new[] { "m4" }, null, null, typeof(ManagementRequestModel))]
        [InlineData(IntegrationEndPointsType.InspectionList, "2024-01-01", null, new[] { "t1" }, null, typeof(TenancyRequestModel))]
        [InlineData(IntegrationEndPointsType.TenanciesTenantList, "2024-01-01", null, new[] { "t2" }, null, typeof(TenancyRequestModel))]
        [InlineData(IntegrationEndPointsType.TenanciesOwnerList, "2024-01-01", null, new[] { "t3" }, null, typeof(TenancyRequestModel))]
        [InlineData(IntegrationEndPointsType.DocumentList, "2024-01-01", new[] { "m5" }, new[] { "t4" }, null, typeof(DocumentRequestModel))]
        [InlineData(IntegrationEndPointsType.AgencyList, "2024-01-01", null, null, new[] { "a1" }, typeof(AgencyRequestModel))]
        public void CreateRequestBody_ReturnsExpectedModelType(
        IntegrationEndPointsType sectionType,
        string modifiedSince,
        string[] managementIds,
        string[] tenancyIds,
        string[] agencyIds,
        Type expectedType)
        {
            // Arrange
            var method = typeof(PropertTreeService)
                .GetMethod("CreateRequestBody", BindingFlags.Static | BindingFlags.NonPublic);

            // Act
            var result = method.Invoke(null, new object[] { sectionType, modifiedSince, managementIds, tenancyIds, agencyIds });

            // Assert
            Assert.NotNull(result);
            Assert.IsType(expectedType, result);

            // Additional property checks (optional)
            if (result is ManagementRequestModel m)
            {
                Assert.Equal(managementIds ?? Array.Empty<string>(), m.ManagementIds);
                Assert.Equal(modifiedSince, m.ModifiedSince);
            }
            if (result is TenancyRequestModel t)
            {
                Assert.Equal(tenancyIds ?? Array.Empty<string>(), t.TenancyIds);
                Assert.Equal(modifiedSince, t.ModifiedSince);
            }
            if (result is DocumentRequestModel d)
            {
                Assert.Equal(managementIds ?? Array.Empty<string>(), d.ManagementIds);
                Assert.Equal(tenancyIds ?? Array.Empty<string>(), d.TenancyIds);
                Assert.Equal(modifiedSince, d.ModifiedSince);
            }
            if (result is AgencyRequestModel a)
            {
                Assert.Equal(agencyIds ?? Array.Empty<string>(), a.AgencyIds);
            }
        }

        [Fact]
        public void CreateRequestBody_ThrowsArgumentException_ForUnknownType()
        {
            // Arrange
            var method = typeof(PropertTreeService)
                .GetMethod("CreateRequestBody", BindingFlags.Static | BindingFlags.NonPublic);

            var ex = Assert.Throws<TargetInvocationException>(() =>
                method.Invoke(null, new object[] { (IntegrationEndPointsType)999, "2024-01-01", Array.Empty<string>(), Array.Empty<string>(), Array.Empty<string>() })
            );
            Assert.IsType<ArgumentException>(ex.InnerException);
        }

        [Theory]
        [InlineData("UserProperties", "PropertyTreeUserPropertiesMappingProfile")]
        [InlineData("TenanciesTenant", "TenanciesTenantMappingProfile")]
        [InlineData("TenanciesOwner", "TenanciesOwnerMappingProfile")]
        [InlineData("MaintenanceDetail", "MaintenanceDetailMappingProfile")]
        [InlineData("InspectionDetail", "InspectionDetailMappingProfile")]
        [InlineData("ComplianceDetail", "ComplianceDetailMappingProfile")]
        [InlineData("FinancialDetail", "FinancialDetailMappingProfile")]
        [InlineData("DocumentDetail", "DocumentDetailMappingProfile")]
        [InlineData("PropertyManagerInformation", "ManagementMappingProfile")]
        public void GetProfileNameForType_ReturnsExpectedProfileName(string typeName, string expectedProfile)
        {
            // Arrange
            var method = typeof(PropertTreeService)
                .GetMethod("GetProfileNameForType", BindingFlags.Static | BindingFlags.NonPublic);

            // Dynamically create a dummy type with the given name if not found in the assembly
            Type type = AppDomain.CurrentDomain
                .GetAssemblies()
                .SelectMany(a => a.GetTypes())
                .FirstOrDefault(t => t.Name == typeName);

            Assert.NotNull(type);

            // Make a generic method for the type
            var genericMethod = method.MakeGenericMethod(type);

            // Act
            var result = genericMethod.Invoke(null, null);

            // Assert
            Assert.Equal(expectedProfile, result);
        }

        [Fact]
        public void GetProfileNameForType_ThrowsArgumentException_ForUnknownType()
        {
            // Arrange
            var method = typeof(PropertTreeService)
                .GetMethod("GetProfileNameForType", BindingFlags.Static | BindingFlags.NonPublic);

            // Use a type that is not mapped
            var genericMethod = method.MakeGenericMethod(typeof(string));

            // Act & Assert
            var ex = Assert.Throws<TargetInvocationException>(() => genericMethod.Invoke(null, null));
            Assert.IsType<ArgumentException>(ex.InnerException);
            Assert.Contains("No profile mapping defined for type String", ex.InnerException.Message);
        }

        [Fact]
        public void ValidateData_MaintenanceDetail_ValidAndInvalid()
        {
            // Arrange
            var valid = new MaintenanceDetail { SRCManagementId = "m1", SRCPropertyId = "p1", SRCJobId = "j1" };
            var invalid = new MaintenanceDetail { SRCManagementId = "", SRCPropertyId = "p1", SRCJobId = "j1" };
            var data = new List<MaintenanceDetail> { valid, invalid };

            // Act
            var method = typeof(PropertTreeService)
                .GetMethod("ValidateData", BindingFlags.Static | BindingFlags.NonPublic)
                ?.MakeGenericMethod(typeof(MaintenanceDetail));
            var invokeResult = method?.Invoke(null, new object[] { data });
            Assert.NotNull(invokeResult);
            var result = ((List<MaintenanceDetail> validData, List<MaintenanceDetail> invalidData))invokeResult!;

            // Assert
            Assert.Single(result.validData);
            Assert.Single(result.invalidData);
            Assert.Equal(valid, result.validData[0]);
            Assert.Equal(invalid, result.invalidData[0]);
        }

        [Fact]
        public void ValidateData_ComplianceDetail_ValidAndInvalid()
        {
            var valid = new ComplianceDetail { SRCManagementId = "m1", SRCPropertyId = "p1", SRCComplianceId = "c1" };
            var invalid = new ComplianceDetail { SRCManagementId = "m1", SRCPropertyId = "", SRCComplianceId = "c1" };
            var data = new List<ComplianceDetail> { valid, invalid };

            var method = typeof(PropertTreeService)
                .GetMethod("ValidateData", BindingFlags.Static | BindingFlags.NonPublic)
                ?.MakeGenericMethod(typeof(ComplianceDetail));
            var invokeResult = method?.Invoke(null, new object[] { data });
            Assert.NotNull(invokeResult);
            var result = ((List<ComplianceDetail> validData, List<ComplianceDetail> invalidData))invokeResult!;

            Assert.Single(result.validData);
            Assert.Single(result.invalidData);
        }

        [Fact]
        public void ValidateData_TenanciesTenant_ValidAndInvalid()
        {
            var valid = new TenanciesTenant { SRCManagementId = "m1", SRCPropertyId = "p1", SRCTenancyId = "t1", TenancyName = "Tenant" };
            var invalid = new TenanciesTenant { SRCManagementId = "m1", SRCPropertyId = "p1", SRCTenancyId = "", TenancyName = "Tenant" };
            var data = new List<TenanciesTenant> { valid, invalid };

            var method = typeof(PropertTreeService)
                .GetMethod("ValidateData", BindingFlags.Static | BindingFlags.NonPublic)
                ?.MakeGenericMethod(typeof(TenanciesTenant));
            var invokeResult = method?.Invoke(null, new object[] { data });
            Assert.NotNull(invokeResult);
            var result = ((List<TenanciesTenant> validData, List<TenanciesTenant> invalidData))invokeResult!;

            Assert.Single(result.validData);
            Assert.Single(result.invalidData);
        }

        [Fact]
        public void ValidateData_UserProperties_ValidAndInvalid()
        {
            var valid = new UserProperties { SRCEntitytId = "e1", SRCManagementId = "m1", SRCAgencyId = "a1" };
            var invalid = new UserProperties { SRCEntitytId = null, SRCManagementId = "m1", SRCAgencyId = "a1" };
            var data = new List<UserProperties> { valid, invalid };

            var method = typeof(PropertTreeService)
                .GetMethod("ValidateData", BindingFlags.Static | BindingFlags.NonPublic)
                ?.MakeGenericMethod(typeof(UserProperties));
            var invokeResult = method?.Invoke(null, new object[] { data });
            Assert.NotNull(invokeResult);
            var result = ((List<UserProperties> validData, List<UserProperties> invalidData))invokeResult!;

            Assert.Single(result.validData);
            Assert.Single(result.invalidData);
        }

        [Fact]
        public void ValidateData_UnknownType_DefaultsToValid()
        {
            var data = new List<string?> { "abc", null };
            var method = typeof(PropertTreeService)
                .GetMethod("ValidateData", BindingFlags.Static | BindingFlags.NonPublic)
                ?.MakeGenericMethod(typeof(string));
            var invokeResult = method?.Invoke(null, new object[] { data });
            Assert.NotNull(invokeResult);
            var result = ((List<string?> validData, List<string?> invalidData))invokeResult!;

            Assert.Equal(2, result.validData.Count);
            Assert.Empty(result.invalidData);
        }

        [Theory]
        [InlineData("m1", "p1", "j1", true)]
        [InlineData(null, "p1", "j1", false)]
        [InlineData("m1", null, "j1", false)]
        [InlineData("m1", "p1", null, false)]
        public void IsValidData_MaintenanceDetail(string managementId, string propertyId, string jobId, bool expected)
        {
            var detail = new MaintenanceDetail { SRCManagementId = managementId, SRCPropertyId = propertyId, SRCJobId = jobId };
            var result = InvokeIsValidData(detail);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("m1", "p1", "c1", true)]
        [InlineData(null, "p1", "c1", false)]
        [InlineData("m1", null, "c1", false)]
        [InlineData("m1", "p1", null, false)]
        public void IsValidData_ComplianceDetail(string managementId, string propertyId, string complianceId, bool expected)
        {
            var detail = new ComplianceDetail { SRCManagementId = managementId, SRCPropertyId = propertyId, SRCComplianceId = complianceId };
            var result = InvokeIsValidData(detail);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("m1", "p1", "t1", "Tenant", true)]
        [InlineData(null, "p1", "t1", "Tenant", false)]
        [InlineData("m1", null, "t1", "Tenant", false)]
        [InlineData("m1", "p1", null, "Tenant", false)]
        [InlineData("m1", "p1", "t1", null, false)]
        public void IsValidData_TenanciesTenant(string managementId, string propertyId, string tenancyId, string tenancyName, bool expected)
        {
            var detail = new TenanciesTenant { SRCManagementId = managementId, SRCPropertyId = propertyId, SRCTenancyId = tenancyId, TenancyName = tenancyName };
            var result = InvokeIsValidData(detail);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("m1", "p1", "t1", "Owner", true)]
        [InlineData(null, "p1", "t1", "Owner", false)]
        [InlineData("m1", null, "t1", "Owner", false)]
        [InlineData("m1", "p1", null, "Owner", false)]
        [InlineData("m1", "p1", "t1", null, false)]
        public void IsValidData_TenanciesOwner(string managementId, string propertyId, string tenancyId, string tenancyName, bool expected)
        {
            var detail = new TenanciesOwner { SRCManagementId = managementId, SRCPropertyId = propertyId, SRCTenancyId = tenancyId, TenancyName = tenancyName };
            var result = InvokeIsValidData(detail);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("i1", "p1", "t1", true)]
        [InlineData(null, "p1", "t1", false)]
        [InlineData("i1", null, "t1", false)]
        [InlineData("i1", "p1", null, false)]
        public void IsValidData_InspectionDetail(string inspectionId, string propertyId, string tenancyId, bool expected)
        {
            var detail = new InspectionDetail { SRCInspectionId = inspectionId, SRCPropertyId = propertyId, SRCTenancyId = tenancyId };
            var result = InvokeIsValidData(detail);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("m1", "p1", "a1", true)]
        [InlineData(null, "p1", "a1", false)]
        [InlineData("m1", null, "a1", false)]
        [InlineData("m1", "p1", null, false)]
        public void IsValidData_PropertyManagerInformation(string managementId, string propertyId, string agencyId, bool expected)
        {
            var detail = new PropertyManagerInformation { SRCManagementId = managementId, SRCPropertyId = propertyId, SRCAgencyId = agencyId };
            var result = InvokeIsValidData(detail);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("m1", "p1", true)]
        [InlineData(null, "p1", false)]
        [InlineData("m1", null, false)]
        public void IsValidData_FinancialDetail(string managementId, string propertyId, bool expected)
        {
            var detail = new FinancialDetail { SRCManagementId = managementId, SRCPropertyId = propertyId };
            var result = InvokeIsValidData(detail);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("e1", "m1", "a1", true)]
        [InlineData(null, "m1", "a1", false)]
        [InlineData("e1", null, "a1", false)]
        [InlineData("e1", "m1", null, false)]
        public void IsValidData_UserProperties(string entityId, string managementId, string agencyId, bool expected)
        {
            var property = new UserProperties { SRCEntitytId = entityId, SRCManagementId = managementId, SRCAgencyId = agencyId };
            var result = InvokeIsValidData(property);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("mri1", "a1", true)]
        [InlineData(null, "a1", false)]
        [InlineData("mri1", null, false)]
        public void IsValidData_AgencyDetails(string mriId, string agencyId, bool expected)
        {
            var agency = new AgencyDetails { MriId = mriId, AgencyId = agencyId };
            var result = InvokeIsValidData(agency);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("a1", true)]
        [InlineData(null, false)]
        public void IsValidData_AgencyPartners(string agencyId, bool expected)
        {
            var partner = new AgencyPartners { AgencyId = agencyId };
            var result = InvokeIsValidData(partner);
            Assert.Equal(expected, result);
        }

        [Theory]
        [InlineData("d1", "doc", "link", true)]
        [InlineData(null, "doc", "link", false)]
        [InlineData("d1", null, "link", false)]
        [InlineData("d1", "doc", null, false)]
        public void IsValidData_DocumentDetail(string documentId, string documentName, string documentLink, bool expected)
        {
            var doc = new DocumentDetail { SRCDocumentId = documentId, DocumentName = documentName, DocumentLink = documentLink };
            var result = InvokeIsValidData(doc);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void IsValidData_UnknownType_DefaultsToTrue()
        {
            var someObj = new { Foo = "bar" };
            var result = InvokeIsValidData(someObj);
            Assert.True(result);
        }

        [Theory]
        [InlineData("management", APIDetail.GetManagement)]
        [InlineData("compliance", APIDetail.GetCompliance)]
        [InlineData("tenancies tenant", APIDetail.GetTenanciesTenant)]
        [InlineData("inspections", APIDetail.GetInspections)]
        [InlineData("maintenance", APIDetail.GetMaintenance)]
        [InlineData("financials", APIDetail.GetFinancials)]
        [InlineData("tenancies owner", APIDetail.GetTenanciesOwner)]
        [InlineData("agency", APIDetail.GetAgencyDetails)]
        [InlineData("unknown", APIDetail.GetProperties)]
        [InlineData("", APIDetail.GetProperties)]
        [InlineData(null, APIDetail.GetProperties)]
        public void GetAPIDetailForDataType_ReturnsExpectedEnum(string dataTypeName, APIDetail expected)
        {
            // Arrange
            var method = typeof(PropertTreeService)
                .GetMethod("GetAPIDetailForDataType", BindingFlags.Static | BindingFlags.NonPublic);

            // Act
            var resultObj = method.Invoke(null, new object[] { dataTypeName });
            Assert.NotNull(resultObj);
            APIDetail result = (APIDetail)resultObj;

            // Assert
            Assert.NotNull(resultObj);
            Assert.Equal(expected, result);
        }

        [Fact]
        public void CreateProxyRequestForSync_UnknownColumn_ThrowsArgumentException()
        {
            // Arrange
            var service = CreateService();
            var method = typeof(PropertTreeService).GetMethod("CreateProxyRequestForSync", BindingFlags.Instance | BindingFlags.NonPublic);
            var manifest = new DataSourceManifest();
            var syncConfig = new PropertTreeService.SyncConfig
            {
                ColumnName = "UnknownColumn",
                SectionType = IntegrationEndPointsType.OwnerPropertyList
            };
            var ids = new[] { "x1" };

            // Act & Assert
            var ex = Assert.Throws<TargetInvocationException>(() =>
                method.Invoke(service, new object[] { manifest, syncConfig, ids })
            );
            Assert.IsType<ArgumentException>(ex.InnerException);
            Assert.Contains("Unsupported column name", ex.InnerException.Message);
        }

        [Fact]
        public async Task ProcessTenanciesOwnerDataSync_CallsSyncDataGeneric_AndReturnsTrue()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<PropertTreeService>>();
            var mockMapper = new Mock<IMapper>();
            var mockPropertyRepo = new Mock<IPropertyRepository>();
            var mockUserRepo = new Mock<IUserRepository>();
            var mockProxyService = new Mock<IProxyService>();
            var mockApiTracking = new Mock<IAPITrackingService>();
            var mockDataSourceRepo = new Mock<IDataSourceRepository>();
            var mockConfig = new Mock<Microsoft.Extensions.Configuration.IConfiguration>();
            var mockIntegrationRepo = new Mock<IIntegrationRepository>();
            var notificationService = new Mock<INotificationService>();

            var service = new PropertTreeService(
                mockLogger.Object,
                mockMapper.Object,
                mockPropertyRepo.Object,
                mockUserRepo.Object,
                mockProxyService.Object,
                new TaskContext(),
                mockApiTracking.Object,
                mockDataSourceRepo.Object,
                mockConfig.Object,
                mockIntegrationRepo.Object,
                notificationService.Object,
                Mock.Of<IBatchOperationLogger>()
            );

            var config = new BatchProcessingConfig
            {
                BatchSize = 1,
                DelayBetweenBatches = 0,
                DelayBetweenRequests = 0,
                DataSourceId = 1,
                DataSourceName = "TestSource",
                Manifest = new DataSourceManifest()
            };

            // Use reflection to invoke the private method
            var method = typeof(PropertTreeService).GetMethod("ProcessTenanciesOwnerDataSync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            // Act
            var resultTask = (Task<bool>)method.Invoke(service, new object[] { config });
            var result = await resultTask;

            // Assert
            Assert.IsType<bool>(result);
        }

        [Fact]
        public async Task ProcessFinancialsDataSync_CallsSyncDataGeneric_AndReturnsBool()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<PropertTreeService>>();
            var mockMapper = new Mock<IMapper>();
            var mockPropertyRepo = new Mock<IPropertyRepository>();
            var mockUserRepo = new Mock<IUserRepository>();
            var mockProxyService = new Mock<IProxyService>();
            var mockApiTracking = new Mock<IAPITrackingService>();
            var mockDataSourceRepo = new Mock<IDataSourceRepository>();
            var mockConfig = new Mock<Microsoft.Extensions.Configuration.IConfiguration>();
            var mockIntegrationRepo = new Mock<IIntegrationRepository>();
            var notificationService = new Mock<INotificationService>();

            var service = new PropertTreeService(
                mockLogger.Object,
                mockMapper.Object,
                mockPropertyRepo.Object,
                mockUserRepo.Object,
                mockProxyService.Object,
                new TaskContext(),
                mockApiTracking.Object,
                mockDataSourceRepo.Object,
                mockConfig.Object,
                mockIntegrationRepo.Object,
                notificationService.Object,
                Mock.Of<IBatchOperationLogger>()
            );

            var config = new BatchProcessingConfig
            {
                BatchSize = 1,
                DelayBetweenBatches = 0,
                DelayBetweenRequests = 0,
                DataSourceId = 1,
                DataSourceName = "TestSource",
                Manifest = new DataSourceManifest()
            };

            // Use reflection to invoke the private method
            var method = typeof(PropertTreeService).GetMethod("ProcessFinancialsDataSync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            // Act
            var resultTask = (Task<bool>)method.Invoke(service, new object[] { config });
            var result = await resultTask;

            // Assert
            Assert.IsType<bool>(result);
        }

        [Fact]
        public async Task ProcessMaintenanceDataSync_CallsSyncDataGeneric_AndReturnsBool()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<PropertTreeService>>();
            var mockMapper = new Mock<IMapper>();
            var mockPropertyRepo = new Mock<IPropertyRepository>();
            var mockUserRepo = new Mock<IUserRepository>();
            var mockProxyService = new Mock<IProxyService>();
            var mockApiTracking = new Mock<IAPITrackingService>();
            var mockDataSourceRepo = new Mock<IDataSourceRepository>();
            var mockConfig = new Mock<Microsoft.Extensions.Configuration.IConfiguration>();
            var mockIntegrationRepo = new Mock<IIntegrationRepository>();
            var notificationService = new Mock<INotificationService>();

            var service = new PropertTreeService(
                mockLogger.Object,
                mockMapper.Object,
                mockPropertyRepo.Object,
                mockUserRepo.Object,
                mockProxyService.Object,
                new TaskContext(),
                mockApiTracking.Object,
                mockDataSourceRepo.Object,
                mockConfig.Object,
                mockIntegrationRepo.Object,
                notificationService.Object,
                Mock.Of<IBatchOperationLogger>()
            );

            var config = new BatchProcessingConfig
            {
                BatchSize = 1,
                DelayBetweenBatches = 0,
                DelayBetweenRequests = 0,
                DataSourceId = 1,
                DataSourceName = "TestSource",
                Manifest = new DataSourceManifest()
            };

            // Use reflection to invoke the private method
            var method = typeof(PropertTreeService).GetMethod("ProcessMaintenanceDataSync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            // Act
            var resultTask = (Task<bool>)method.Invoke(service, new object[] { config });
            var result = await resultTask;

            // Assert
            Assert.IsType<bool>(result);
        }

        [Fact]
        public async Task ProcessInspectionsDataSync_CallsSyncDataGeneric_AndReturnsBool()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<PropertTreeService>>();
            var mockMapper = new Mock<IMapper>();
            var mockPropertyRepo = new Mock<IPropertyRepository>();
            var mockUserRepo = new Mock<IUserRepository>();
            var mockProxyService = new Mock<IProxyService>();
            var mockApiTracking = new Mock<IAPITrackingService>();
            var mockDataSourceRepo = new Mock<IDataSourceRepository>();
            var mockConfig = new Mock<Microsoft.Extensions.Configuration.IConfiguration>();
            var mockIntegrationRepo = new Mock<IIntegrationRepository>();
            var notificationService = new Mock<INotificationService>();

            var service = new PropertTreeService(
                mockLogger.Object,
                mockMapper.Object,
                mockPropertyRepo.Object,
                mockUserRepo.Object,
                mockProxyService.Object,
                new TaskContext(),
                mockApiTracking.Object,
                mockDataSourceRepo.Object,
                mockConfig.Object,
                mockIntegrationRepo.Object,
                notificationService.Object,
                Mock.Of<IBatchOperationLogger>()
            );

            var config = new BatchProcessingConfig
            {
                BatchSize = 1,
                DelayBetweenBatches = 0,
                DelayBetweenRequests = 0,
                DataSourceId = 1,
                DataSourceName = "TestSource",
                Manifest = new DataSourceManifest()
            };

            // Use reflection to invoke the private method
            var method = typeof(PropertTreeService).GetMethod("ProcessInspectionsDataSync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            // Act
            var resultTask = (Task<bool>)method.Invoke(service, new object[] { config });
            var result = await resultTask;

            // Assert
            Assert.IsType<bool>(result);
        }

        [Fact]
        public async Task ProcessTenanciesTenantDataSync_CallsSyncDataGeneric_AndReturnsBool()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<PropertTreeService>>();
            var mockMapper = new Mock<IMapper>();
            var mockPropertyRepo = new Mock<IPropertyRepository>();
            var mockUserRepo = new Mock<IUserRepository>();
            var mockProxyService = new Mock<IProxyService>();
            var mockApiTracking = new Mock<IAPITrackingService>();
            var mockDataSourceRepo = new Mock<IDataSourceRepository>();
            var mockConfig = new Mock<Microsoft.Extensions.Configuration.IConfiguration>();
            var mockIntegrationRepo = new Mock<IIntegrationRepository>();
            var notificationService = new Mock<INotificationService>();

            var service = new PropertTreeService(
                mockLogger.Object,
                mockMapper.Object,
                mockPropertyRepo.Object,
                mockUserRepo.Object,
                mockProxyService.Object,
                new TaskContext(),
                mockApiTracking.Object,
                mockDataSourceRepo.Object,
                mockConfig.Object,
                mockIntegrationRepo.Object,
                notificationService.Object,
                Mock.Of<IBatchOperationLogger>()
            );

            var config = new BatchProcessingConfig
            {
                BatchSize = 1,
                DelayBetweenBatches = 0,
                DelayBetweenRequests = 0,
                DataSourceId = 1,
                DataSourceName = "TestSource",
                Manifest = new DataSourceManifest()
            };

            // Use reflection to invoke the private method
            var method = typeof(PropertTreeService).GetMethod("ProcessTenanciesTenantDataSync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            // Act
            var resultTask = (Task<bool>)method.Invoke(service, new object[] { config });
            var result = await resultTask;

            // Assert
            Assert.IsType<bool>(result);
        }

        [Fact]
        public async Task ProcessComplianceDataSync_CallsSyncDataGeneric_AndReturnsBool()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<PropertTreeService>>();
            var mockMapper = new Mock<IMapper>();
            var mockPropertyRepo = new Mock<IPropertyRepository>();
            var mockUserRepo = new Mock<IUserRepository>();
            var mockProxyService = new Mock<IProxyService>();
            var mockApiTracking = new Mock<IAPITrackingService>();
            var mockDataSourceRepo = new Mock<IDataSourceRepository>();
            var mockConfig = new Mock<Microsoft.Extensions.Configuration.IConfiguration>();
            var mockIntegrationRepo = new Mock<IIntegrationRepository>();
            var notificationService = new Mock<INotificationService>();

            var service = new PropertTreeService(
                mockLogger.Object,
                mockMapper.Object,
                mockPropertyRepo.Object,
                mockUserRepo.Object,
                mockProxyService.Object,
                new TaskContext(),
                mockApiTracking.Object,
                mockDataSourceRepo.Object,
                mockConfig.Object,
                mockIntegrationRepo.Object,
                notificationService.Object,
                Mock.Of<IBatchOperationLogger>()
            );

            var config = new BatchProcessingConfig
            {
                BatchSize = 1,
                DelayBetweenBatches = 0,
                DelayBetweenRequests = 0,
                DataSourceId = 1,
                DataSourceName = "TestSource",
                Manifest = new DataSourceManifest()
            };

            // Use reflection to invoke the private method
            var method = typeof(PropertTreeService).GetMethod("ProcessComplianceDataSync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            // Act
            var resultTask = (Task<bool>)method.Invoke(service, new object[] { config });
            var result = await resultTask;

            // Assert
            Assert.IsType<bool>(result);
        }

        [Fact]
        public async Task ProcessManagementDataSync_CallsSyncDataGeneric_AndReturnsBool()
        {
            // Arrange
            var mockLogger = new Mock<ILogger<PropertTreeService>>();
            var mockMapper = new Mock<IMapper>();
            var mockPropertyRepo = new Mock<IPropertyRepository>();
            var mockUserRepo = new Mock<IUserRepository>();
            var mockProxyService = new Mock<IProxyService>();
            var mockApiTracking = new Mock<IAPITrackingService>();
            var mockDataSourceRepo = new Mock<IDataSourceRepository>();
            var mockConfig = new Mock<Microsoft.Extensions.Configuration.IConfiguration>();
            var mockIntegrationRepo = new Mock<IIntegrationRepository>();
            var notificationService = new Mock<INotificationService>();

            var service = new PropertTreeService(
                mockLogger.Object,
                mockMapper.Object,
                mockPropertyRepo.Object,
                mockUserRepo.Object,
                mockProxyService.Object,
                new TaskContext(),
                mockApiTracking.Object,
                mockDataSourceRepo.Object,
                mockConfig.Object,
                mockIntegrationRepo.Object,
                notificationService.Object,
                Mock.Of<IBatchOperationLogger>()
            );

            var config = new BatchProcessingConfig
            {
                BatchSize = 1,
                DelayBetweenBatches = 0,
                DelayBetweenRequests = 0,
                DataSourceId = 1,
                DataSourceName = "TestSource",
                Manifest = new DataSourceManifest()
            };

            // Use reflection to invoke the private method
            var method = typeof(PropertTreeService).GetMethod("ProcessManagementDataSync", System.Reflection.BindingFlags.Instance | System.Reflection.BindingFlags.NonPublic);

            // Act
            var resultTask = (Task<bool>)method.Invoke(service, new object[] { config });
            var result = await resultTask;

            // Assert
            Assert.IsType<bool>(result);
        }
        private static bool InvokeIsValidData<T>(T item)
        {
            var method = typeof(PropertTreeService)
                .GetMethod("IsValidData", System.Reflection.BindingFlags.Static | System.Reflection.BindingFlags.NonPublic)
                ?.MakeGenericMethod(typeof(T));
            return (bool)method!.Invoke(null, [item])!;
        }
        private PropertTreeService CreateService()
        {
            return new PropertTreeService(
                Mock.Of<Microsoft.Extensions.Logging.ILogger<PropertTreeService>>(),
                Mock.Of<AutoMapper.IMapper>(),
                Mock.Of<IPropertyRepository>(),
                Mock.Of<IUserRepository>(),
                Mock.Of<IProxyService>(),
                new TaskContext(),
                Mock.Of<IAPITrackingService>(),
                Mock.Of<IDataSourceRepository>(),
                Mock.Of<Microsoft.Extensions.Configuration.IConfiguration>(),
                Mock.Of<IIntegrationRepository>(),
                Mock.Of<INotificationService>(),
                Mock.Of<IBatchOperationLogger>()
            );
        }
    }
}

